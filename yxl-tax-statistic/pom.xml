<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cc.buyhoo.tax</groupId>
        <artifactId>yxl-statistic</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>cc.buyhoo.tax</groupId>
    <artifactId>yxl-tax-statistic</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>yxl-tax-statistic</name>
    <description>yxl-tax-statistic</description>
    <properties>
        <java.version>17</java.version>
        <yxl.version>2.1.0</yxl.version>
        <satoken.version>1.35.0.RC</satoken.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <tax-statistic.version>1.0.0</tax-statistic.version>
        <rocketmq.version>2.3.1</rocketmq.version>
    </properties>
    <dependencies>


        <!-- 短信验证码、邮件发送 -->
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-smsg</artifactId>
            <version>${yxl.version}</version>
        </dependency>

        <!-- 邮件发送 -->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.7</version>
        </dependency>
        <!-- 短信验证码、邮件发送结束 -->

        <!-- 文件格式转换 -->
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>core-renderer</artifactId>
            <version>R8</version>
            <exclusions>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcprov-jdk14</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version>
        </dependency>

        <!-- 文件格式转换结束 -->

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-io</artifactId>
            <version>1.3.2</version>
        </dependency>

        <!-- FTP -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.53</version>
        </dependency>

        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-core</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-datasource</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-dubbo</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-bankpay</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-minio</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <!-- redis 缓存操作 -->
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-redis</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-web</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-captcha</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.tax</groupId>
            <artifactId>yxl-tax-statistic-dubbo-api</artifactId>
            <version>${tax-statistic.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-job</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.upgrade</groupId>
            <artifactId>yxl-upgrade-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- Sa-Token 权限认证-->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${satoken.version}</version>
        </dependency>
        <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
            <version>${satoken.version}</version>
        </dependency>
        <!-- Sa-Token 整合 jwt -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
            <version>${satoken.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 阿里巴巴 Excel 工具 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-baidu</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-dingdingtalk</artifactId>
            <version>${yxl.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>
