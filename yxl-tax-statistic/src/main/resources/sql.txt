2024年6月27日，招商银行代发工资功能修改数据库记录
1、ALTER TABLE `yxl_tax_statistic`.`bus_shop_bill_detail`
ADD COLUMN `bill_type` smallint(2) NULL DEFAULT 1 COMMENT '转账方式：1、转账；2、代发工资' AFTER `modify_time`;
2、CREATE TABLE `sys_company_bank_account` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `company_id` bigint(20) DEFAULT NULL COMMENT '所属企业ID',
    `is_default` smallint(1) DEFAULT NULL COMMENT '1、默认账户；0、非默认账户',
    `bank_id` bigint(20) DEFAULT NULL COMMENT '所属银行ID，见表bank_list',
    `bank_card` varchar(30) DEFAULT NULL COMMENT '银行账号',
    `bank_name` varchar(50) DEFAULT NULL COMMENT '开户行名称',
    `bank_lhh` varchar(12) DEFAULT NULL COMMENT '联行号，备用',
    `bank_branch` varchar(20) NOT NULL COMMENT '所在银行的支行号，见bank_branch_list表',
    `symmetric_key` varchar(255) DEFAULT NULL COMMENT '对称秘钥',
    `private_key` varchar(255) DEFAULT NULL COMMENT '银行私钥',
    `public_key` varchar(255) DEFAULT NULL COMMENT '银行公钥',
    `uid` varbinary(50) DEFAULT NULL COMMENT '银行分配的员工ID',
    `busmod` varchar(50) DEFAULT NULL COMMENT '业务ID，招商专用，似乎不该存在这里',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` bigint(20) DEFAULT NULL COMMENT '最后修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '最后修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `company_id` (`company_id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
3、CREATE TABLE `sys_bank_list` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `bank_name` varchar(100) DEFAULT NULL COMMENT '银行名称',
    `bank_code` varchar(20) DEFAULT NULL COMMENT '分配的银行简称',
    `bank_logo` varchar(255) DEFAULT NULL COMMENT '银行logo存放地址',
    `del_flag` int(1) DEFAULT NULL COMMENT '删除标记：0、删除；1、未删除；',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '最后修改人',
    `modify_datetime` datetime DEFAULT NULL COMMENT '最后修改时间',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行列表，包含银行简称、银行名称、logo等信息';
4、CREATE TABLE `sys_bank_branch_list` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `branch_id` varchar(20) DEFAULT NULL COMMENT '银行内部分配的分支ID',
    `bank_id` bigint(20) DEFAULT NULL COMMENT '分支所属银行ID',
    `branch_name` varchar(100) DEFAULT NULL COMMENT '银行分支的名称',
    `del_flg` smallint(1) DEFAULT NULL COMMENT '删除标记：1、未删除；0、已删除；',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '最后修改人',
    `modify_datetime` datetime DEFAULT NULL COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `bank_id` (`bank_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行内部分配的银行分支编号';

 5、ALTER TABLE `yxl_tax_statistic`.`bus_shop`
   DROP COLUMN `bank_city`,
   ADD COLUMN `bank_city` varchar(100) NULL COMMENT '开户行所在地（非银联卡必传）' AFTER `bank_name`;