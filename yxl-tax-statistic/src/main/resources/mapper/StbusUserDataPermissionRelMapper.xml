<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.StbusUserDataPermissionRelMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.StbusUserDataPermissionRelEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="level" property="level" jdbcType="TINYINT"/>
        <result column="data_permission" property="dataPermission" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_by" property="modifyBy" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `user_id`, `level`, `data_permission`, `del_flag`, `create_by`, 
		`create_time`, `modify_by`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="userId != null">
            and user_id=#{userId}
		</if>
        <if test="level != null">
            and level=#{level}
		</if>
        <if test="dataPermission != null  and dataPermission != ''">
            and data_permission=#{dataPermission}
		</if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
		</if>
        <if test="createBy != null">
            and create_by=#{createBy}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyBy != null">
            and modify_by=#{modifyBy}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.entity.StbusUserDataPermissionRelEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from stbus_user_data_permission_rel
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>