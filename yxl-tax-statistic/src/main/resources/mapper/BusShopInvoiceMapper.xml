<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusShopInvoiceMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopInvoiceEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="invoice_type" property="invoiceType" jdbcType="INTEGER"/>
        <result column="invoice_code" property="invoiceCode" jdbcType="VARCHAR"/>
        <result column="machine_code" property="machineCode" jdbcType="VARCHAR"/>
        <result column="invoice_number" property="invoiceNumber" jdbcType="VARCHAR"/>
        <result column="invoice_kind" property="invoiceKind" jdbcType="INTEGER"/>
        <result column="invoice_date" property="invoiceDate" jdbcType="TIMESTAMP"/>
        <result column="check_code" property="checkCode" jdbcType="VARCHAR"/>
        <result column="purchase_name" property="purchaseName" jdbcType="VARCHAR"/>
        <result column="purchase_identity" property="purchaseIdentity" jdbcType="VARCHAR"/>
        <result column="purchase_address" property="purchaseAddress" jdbcType="VARCHAR"/>
        <result column="purchase_phone" property="purchasePhone" jdbcType="VARCHAR"/>
        <result column="purchase_bank" property="purchaseBank" jdbcType="VARCHAR"/>
        <result column="purchase_bank_no" property="purchaseBankNo" jdbcType="VARCHAR"/>
        <result column="sale_name" property="saleName" jdbcType="VARCHAR"/>
        <result column="sale_identity" property="saleIdentity" jdbcType="VARCHAR"/>
        <result column="sale_address" property="saleAddress" jdbcType="VARCHAR"/>
        <result column="sale_phone" property="salePhone" jdbcType="VARCHAR"/>
        <result column="sale_bank" property="saleBank" jdbcType="VARCHAR"/>
        <result column="sale_bank_no" property="saleBankNo" jdbcType="VARCHAR"/>
        <result column="order_money" property="orderMoney" jdbcType="DECIMAL"/>
        <result column="tax_money" property="taxMoney" jdbcType="DECIMAL"/>
        <result column="order_tax_money" property="orderTaxMoney" jdbcType="DECIMAL"/>
        <result column="payee" property="payee" jdbcType="VARCHAR"/>
        <result column="checker" property="checker" jdbcType="VARCHAR"/>
        <result column="invoice_man" property="invoiceMan" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="bill_number" property="billNumber" jdbcType="VARCHAR"/>
        <result column="periods_level" property="periodsLevel" jdbcType="INTEGER" />
        <result column="receive_msg" property="receiveMsg" jdbcType="VARCHAR" />
        <result column="notes" property="notes" jdbcType="VARCHAR" />
        <result column="purchase_person_flag" property="purchasePersonFlag" jdbcType="INTEGER" />
        <result column="sale_person_flag" property="salePersonFlag" jdbcType="INTEGER" />
        <result column="purchase_bank_flag" property="purchaseBankFlag" jdbcType="INTEGER" />
        <result column="sale_bank_flag" property="saleBankFlag" jdbcType="INTEGER" />
        <result column="apply_flag" property="applyFlag" jdbcType="INTEGER" />
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `shop_unique`, `invoice_type`, `invoice_code`, `machine_code`, 
		`invoice_number`, `invoice_kind`, `invoice_date`, `check_code`, `purchase_name`, `purchase_identity`, 
		`purchase_address`, `purchase_phone`, `purchase_bank`, `purchase_bank_no`, `sale_name`, `sale_identity`, 
		`sale_address`, `sale_phone`, `sale_bank`, `sale_bank_no`, `order_money`, `tax_money`, 
		`order_tax_money`, `payee`, `checker`, `invoice_man`, `create_user`, `create_time`, 
		`modify_user`, `modify_time`, `status`, `sale_list_unique`, `notes`, `purchase_person_flag`, `sale_person_flag`
            , `purchase_bank_flag`, `sale_bank_flag`, `apply_flag`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="saleListUnique != null and saleListUnique != ''">
            and sale_list_unique=#{saleListUnique}
		</if>
        <if test="invoiceType != null">
            and invoice_type=#{invoiceType}
		</if>
        <if test="invoiceCode != null  and invoiceCode != ''">
            and invoice_code=#{invoiceCode}
		</if>
        <if test="machineCode != null  and machineCode != ''">
            and machine_code=#{machineCode}
		</if>
        <if test="invoiceNumber != null  and invoiceNumber != ''">
            and invoice_number=#{invoiceNumber}
		</if>
        <if test="invoiceKind != null">
            and invoice_kind=#{invoiceKind}
		</if>
        <if test="invoiceDate != null  and invoiceDate != ''">
            and invoice_date=#{invoiceDate}
		</if>
        <if test="checkCode != null  and checkCode != ''">
            and check_code=#{checkCode}
		</if>
        <if test="purchaseName != null  and purchaseName != ''">
            and purchase_name=#{purchaseName}
		</if>
        <if test="purchaseIdentity != null  and purchaseIdentity != ''">
            and purchase_identity=#{purchaseIdentity}
		</if>
        <if test="purchaseAddress != null  and purchaseAddress != ''">
            and purchase_address=#{purchaseAddress}
		</if>
        <if test="purchasePhone != null  and purchasePhone != ''">
            and purchase_phone=#{purchasePhone}
		</if>
        <if test="purchaseBank != null  and purchaseBank != ''">
            and purchase_bank=#{purchaseBank}
		</if>
        <if test="purchaseBankNo != null  and purchaseBankNo != ''">
            and purchase_bank_no=#{purchaseBankNo}
		</if>
        <if test="saleName != null  and saleName != ''">
            and sale_name=#{saleName}
		</if>
        <if test="saleIdentity != null  and saleIdentity != ''">
            and sale_identity=#{saleIdentity}
		</if>
        <if test="saleAddress != null  and saleAddress != ''">
            and sale_address=#{saleAddress}
		</if>
        <if test="salePhone != null  and salePhone != ''">
            and sale_phone=#{salePhone}
		</if>
        <if test="saleBank != null  and saleBank != ''">
            and sale_bank=#{saleBank}
		</if>
        <if test="saleBankNo != null  and saleBankNo != ''">
            and sale_bank_no=#{saleBankNo}
		</if>
        <if test="orderMoney != null">
            and order_money=#{orderMoney}
		</if>
        <if test="taxMoney != null">
            and tax_money=#{taxMoney}
		</if>
        <if test="orderTaxMoney != null">
            and order_tax_money=#{orderTaxMoney}
		</if>
        <if test="payee != null  and payee != ''">
            and payee=#{payee}
		</if>
        <if test="checker != null  and checker != ''">
            and checker=#{checker}
		</if>
        <if test="invoiceMan != null  and invoiceMan != ''">
            and invoice_man=#{invoiceMan}
		</if>
        <if test="status != null">
            and status=#{status}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
		<if test="notes != null">
            and notes=#{notes}
		</if>
		<if test="purchasePersonFlag != null">
            and purchase_person_flag=#{purchasePersonFlag}
		</if>
		<if test="salePersonFlag != null">
            and sale_person_flag=#{salePersonFlag}
		</if>
		<if test="purchaseBankFlag != null">
            and purchase_bank_flag=#{purchaseBankFlag}
		</if>
		<if test="saleBankFlag != null">
            and sale_bank_flag=#{saleBankFlag}
		</if>
		<if test="applyFlag != null">
            and apply_flag=#{applyFlag}
		</if>
    </sql>

</mapper>