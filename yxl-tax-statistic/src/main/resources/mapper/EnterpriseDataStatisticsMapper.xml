<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.EnterpriseDataStatisticsMapper">

    <select id="getShopGroupedByTownCode" resultType="cc.buyhoo.tax.result.enterpriseDataStatistics.Statistics">
        --统计商家数量
        SELECT COUNT(DISTINCT s.id) AS shopCount
        --统计今日新增的商家数量,
        SUM(CASE WHEN DATE(s.create_time)=CURDATE() THEN 1 ELSE 0 END ) AS todayAddedShops
        -- //统计一般纳税人数量,统计shop_nature字段为0的不重复的商家数量
        COUNT(DISTINCT CASE WHEN s.shop_nature = 0 THEN s.id ELSE NULL END ) AS yiBanNaShuiRenTongJi
        --//统计今日新增的一般纳税人数量
        SUM(CASE WHEN DATE(s.shop_nature = 0 AND DATE(s.create_time) )=CURDATE() THEN 1 ELSE 0 END ) AS
        todayNewGenerslTaxpayerCount
        --//统计小规模商家数
        COUNT(DISTINCT CASE WHEN s.shop_nature = 2 THEN s.id ELSE NULL END) AS
        --//统计今日新增的小规模商家数
        SUM(CASE WHEN DATE(s.shop_nature = 2 AND DATE(s.create_time) )=CURDATE() THEN 1 ELSE 0 END ) AS todayNewGenerslTaxpayerCount
        --//统计个体工商户的商家数
        COUNT(DISTINCT CASE WHEN s.shop_nature = 1 THEN s.id ELSE NULL END ) AS
        --//统计今日新增的小规模商家数
        SUM(CASE WHEN DATE(s.shop_nature = 1 AND DATE(s.create_time))=CURDATE() THEN 1 ELSE 0 END ) AS todayNewGenerslTaxpayerCount
        --记录总开票额
        SUM(si.tax_money)
        --记录今日开票额
        SUM(CASE WHEN DATE(si.invoice_date) = CURDATE() THEN si.tax_money ELSE 0 END )
        --统计开票单数
        COUNT(DISTINCT CASE WHEN si.status = 1 THEN si.id ELSE NULL END)
        --统计今日开票单数
        SUM(CASE WHEN DATE(si.status = 1 AND DATE(si.invoice_date) ) = CURDATE() THEN 1 ELSE 0 END )
        --统计订单数
        COUNT (DISTINCT CASE WHEN sl.sale_list_unique  THEN sl.id ELSE NULL END)
        --统计今日订单数
        COUNT(DISTINCT CASE WHEN sale_list_datetime = CURDATE() THEN sl.id ELSE NULL END)
        FROM bus_shop s
        LEFT JOIN sale_list sl ON s.id = sl.shop_unique
        LEFT JOIN bus_shop_invoice si ON s.id = si.shop_unique
            <where >
            <if test="companyId != null">
                AND s.company_id = #{companyId}
            </if>
            </where>
        --//先不写这个开票额或开票单数
        <foreach collection="shopIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <!--根据企业id来查询企业下的所有的区县和街道编码-->
    <select id="DistrictAndCountyStreet" resultType="java.lang.String">
        SELECT
            town_code,
            county_code
        FROM bus_shop
        WHERE company_id = #{companyId}
    </select>




    <!--订单 查询销售额,今日销售额,订单数,今日订单数-->
    <select id="selectOrderForm" resultType="cc.buyhoo.tax.result.enterpriseDataStatistics.Statistics">
        SELECT
        bs.town_code AS townCode,
        SUM(sl.sale_list_actually_received) AS salesAmount,
        SUM(CASE WHEN DATE(sl.sale_list_datetime) = CURDATE() THEN sl.sale_list_actually_received ELSE 0 END)
        AS todaySalesAmount,
        COUNT(sl.sale_type) AS orderCount,
        SUM(CASE WHEN sale_type  AND DATE(sale_list_datetime) = CURDATE() THEN 1 ELSE 0 END) AS todayOrderCount

        FROM sale_list sl
        LEFT JOIN bus_shop bs ON sl.shop_unique = bs.shop_unique
        <where>

            <if test="companyId != null and companyId != '' ">
                AND sl.company_id = #{companyId}
            </if>
            <if test="createTime != null and createTime != '' ">
                AND sl.create_time >= CONCAT(#{createTime}
            </if>
            <if test="modifyTime != null and modifyTime != '' ">
                AND sl.create_time <![CDATA[ <= ]]> CONCAT(#{modifyTime})
            </if>
        </where>
        GROUP BY
        bs.town_code
    </select>



    <!--  //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数-->
    <select id="selectmakeOutAnInvoice" resultType="cc.buyhoo.tax.result.enterpriseDataStatistics.Statistics">
        SELECT
             bs.town_code AS townCode,
            SUM( bsi.tax_money) AS invoiceAmount,
            SUM(CASE WHEN DATE(bsi.invoice_date) = CURDATE() THEN bsi.tax_money ELSE 0 END) AS todayInvoiceAmount,
            COUNT(sale_list_unique AND status = 1) AS invoiceCount,
            SUM(CASE WHEN sale_list_unique AND status = 1 AND DATE(bsi.invoice_date) = CURDATE() THEN 1 ELSE 0 END)
            AS todayInvoiceCount

        FROM bus_shop_invoice bsi
        LEFT JOIN bus_shop bs ON bsi.shop_unique = bs.shop_unique
        <where>

            <if test="companyId != null and companyId != '' ">
                AND  bs.company_id = #{companyId}
            </if>
            <if test="createTime != null and createTime != '' ">
                AND bs.create_time >= CONCAT(#{createTime}
            </if>
            <if test="modifyTime != null and modifyTime != '' ">
                AND bs.create_time <![CDATA[ <= ]]> CONCAT(#{modifyTime})
            </if>
        </where>
        GROUP BY
        bs.town_code
    </select>

    <!--//查询每个门店下的bus_shop的 : 商户数,今日新增商户数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数,今日新增个体工商户数
     * -->
    <select id="selectMerchant" resultType="cc.buyhoo.tax.result.enterpriseDataStatistics.Statistics">
            SELECT
                bs.town_code AS townCode,
                COUNT(bs.shop_unique) AS merchantCount,
                SUM(CASE WHEN DATE(bs.create_time) = CURDATE() THEN 1 ELSE 0 END) AS todayNewMerchantCount,
                SUM(CASE WHEN bs.shop_nature = 0 THEN 1 ELSE 0 END ) AS generalTaxpayerCount,
                SUM(CASE WHEN DATE(bs.create_time) = CURDATE() AND bs.shop_nature = 0  THEN 1 ELSE 0 END) AS todayNewGeneralTaxpayerCount,

                SUM( CASE WHEN bs.shop_nature = 2 THEN 1 ELSE 0 END) AS smallScaleTaxpayerCount,
                SUM(CASE WHEN DATE(bs.create_time) = CURDATE() AND bs.shop_nature = 2 THEN 1 ELSE 0 END) AS todayNewSmallScaleTaxpayerCount,

                SUM(CASE WHEN bs.shop_nature = 1 THEN 1 ELSE 0 END) AS individualBusinessCount,
                SUM(CASE WHEN DATE(bs.create_time) = CURDATE() AND bs.shop_nature = 1 THEN 1 ELSE 0 END) AS todayNewIndividualBusinessCount

            FROM bus_shop bs
            <where>

                <if test="companyId != null and companyId != '' ">
                    AND bs.company_id = #{companyId}
                </if>
                <if test="createTime != null and createTime != '' ">
                    AND  bs.create_time >= CONCAT(#{createTime})
                </if>
                <if test="modifyTime != null and modifyTime != '' ">
                    bs.create_time <![CDATA[ <= ]]> CONCAT(#{modifyTime})
                </if>
            </where>
             GROUP BY
            bs.town_code
    </select>
</mapper>