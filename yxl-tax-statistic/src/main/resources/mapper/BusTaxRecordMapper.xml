<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusTaxRecordMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusTaxRecordEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="tax_type" property="taxType" jdbcType="VARCHAR"/>
        <result column="target_amount" property="targetAmount" jdbcType="DECIMAL"/>
        <result column="tax_amount" property="taxAmount" jdbcType="DECIMAL"/>
        <result column="tax_date" property="taxDate" jdbcType="VARCHAR"/>
        <result column="tax_status" property="taxStatus" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
    </resultMap>
</mapper>