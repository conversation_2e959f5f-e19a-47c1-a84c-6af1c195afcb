<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusContractMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusContractEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="contractNo" column="contract_no"/>
        <result property="shopUnique" column="shop_unique"/>
        <result property="contractName" column="contract_name"/>
        <result property="contractType" column="contract_type"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="contractStatus" column="contract_status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="signTime" column="sign_time"/>
        <result property="orderNo" column="order_no"/>
        <result property="fileUrl" column="file_url"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>


    <sql id="baseColumns">
        id,company_id,contract_no,shop_unique,contract_name,contract_type,total_amount,contract_status,start_time,end_time,sign_time,order_no,file_url,del_flag,create_user,create_time,modify_user,modify_time
    </sql>
    <sql id="whereSql">
        <if test="id != null ">
            and a.id = #{id},
        </if>
        <if test="companyId != null ">
            and a.company_id = #{companyId},
        </if>
        <if test="contractNo != null  and contractNo.trim() != ''">
            and a.contract_no = #{contractNo},
        </if>
        <if test="shopUnique != null ">
            and a.shop_unique = #{shopUnique},
        </if>
        <if test="contractName != null  and contractName.trim() != ''">
            and a.contract_name = #{contractName},
        </if>
        <if test="contractType != null ">
            and a.contract_type = #{contractType},
        </if>
        <if test="totalAmount != null ">
            and a.total_amount = #{totalAmount},
        </if>
        <if test="contractStatus != null ">
            and a.contract_status = #{contractStatus},
        </if>
        <if test="signTime != null ">
            and a.sign_time = #{signTime},
        </if>
        <if test="orderNo != null  and orderNo.trim() != ''">
            and INSTR(a.order_no, #{orderNo}) > 0,
        </if>
        <if test="fileUrl != null  and fileUrl.trim() != ''">
            and a.file_url = #{fileUrl},
        </if>
        <if test="delFlag != null ">
            and a.del_flag = #{delFlag},
        </if>
        <if test="createUser != null ">
            and a.create_user = #{createUser},
        </if>
        <if test="modifyUser != null ">
            and a.modify_user = #{modifyUser},
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusContractEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_contract
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>