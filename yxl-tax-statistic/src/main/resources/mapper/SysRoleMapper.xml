<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SysRoleMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysRoleEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="role_type" property="roleType" jdbcType="INTEGER"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="data_scope" property="dataScope" jdbcType="INTEGER"/>
        <result column="data_scope_dept_ids" property="dataScopeDeptIds" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `role_type`, `role_name`, `data_scope`, `data_scope_dept_ids`, 
		`remark`, `del_flag`, `create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
        </if>
        <if test="companyId != null">
            and company_id=#{companyId}
        </if>
        <if test="roleType != null">
            and role_type=#{roleType}
        </if>
        <if test="roleName != null  and roleName != ''">
            and role_name=#{roleName}
        </if>
        <if test="dataScope != null">
            and data_scope=#{dataScope}
        </if>
        <if test="dataScopeDeptIds != null  and dataScopeDeptIds != ''">
            and data_scope_dept_ids=#{dataScopeDeptIds}
        </if>
        <if test="remark != null  and remark != ''">
            and remark=#{remark}
        </if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
        </if>
        <if test="createUser != null">
            and create_user=#{createUser}
        </if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
        </if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
        </if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
        </if>
    </sql>

    <!--删除角色权限-->
    <insert id="deleteRoleMenuByRoleId" parameterType="Long">
        delete from sys_role_menu where role_id=#{roleId}
    </insert>

    <!--根据角色编号查询用户-->
    <select id="findUserByRoleId" parameterType="Long" resultType="cc.buyhoo.tax.entity.SysUserRoleEntity">
        SELECT
            user_id AS userId,
            role_id AS roleId
        FROM
            sys_user_role
        WHERE
            role_id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <!--保存用户角色-->
    <insert id="insertUserRole" parameterType="cc.buyhoo.tax.entity.SysUserRoleEntity">
        insert into sys_user_role(user_id,role_id) values (#{userId},#{roleId})
    </insert>

    <!--根据用户编号删除角色-->
    <delete id="deleteUserRoleByUserId" parameterType="Long">
        delete from sys_user_role where user_id=#{userId}
    </delete>

    <!--根据菜单编号删除菜单-->
    <delete id="deleteRoleMenuByMenuId" parameterType="Long">
        delete from sys_role_menu where menu_id=#{menuId}
    </delete>

</mapper>