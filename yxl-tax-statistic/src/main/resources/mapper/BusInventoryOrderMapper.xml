<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusInventoryOrderMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusInventoryOrderEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="total_money" property="totalMoney" jdbcType="DECIMAL"/>
        <result column="cost_money" property="costMoney" jdbcType="DECIMAL"/>
        <result column="profit_money" property="profitMoney" jdbcType="DECIMAL"/>
        <result column="inventory_type" property="inventoryType" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `order_no`, `total_money`, `cost_money`, `profit_money`, `inventory_type`, `create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
        </if>
        <if test="companyId != null">
            and company_id=#{companyId}
        </if>
        <if test="orderNo != null  and orderNo != ''">
            and order_no=#{orderNo}
        </if>
        <if test="totalMoney != null">
            and total_money=#{totalMoney}
        </if>
        <if test="inventoryType != null">
            and inventory_type=#{inventoryType}
        </if>
        <if test="createUser != null">
            and create_user=#{createUser}
        </if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
        </if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
        </if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
        </if>
    </sql>

</mapper>