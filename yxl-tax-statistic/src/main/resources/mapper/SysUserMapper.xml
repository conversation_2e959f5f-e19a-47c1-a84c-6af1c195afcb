<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SysUserMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysUserEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="INTEGER"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="enable_status" property="enableStatus" jdbcType="INTEGER"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `username`, `password`, `user_type`, `mobile`,
		`email`, `avatar`, `enable_status`, `del_flag`, `remark`, `create_user`,
		`create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
        </if>
        <if test="companyId != null">
            and company_id=#{companyId}
        </if>
        <if test="username != null  and username != ''">
            and username=#{username}
        </if>
        <if test="password != null  and password != ''">
            and password=#{password}
        </if>
        <if test="userType != null">
            and user_type=#{userType}
        </if>
        <if test="mobile != null  and mobile != ''">
            and mobile=#{mobile}
        </if>
        <if test="email != null  and email != ''">
            and email=#{email}
        </if>
        <if test="avatar != null  and avatar != ''">
            and avatar=#{avatar}
        </if>
        <if test="enableStatus != null">
            and enable_status=#{enableStatus}
        </if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
        </if>
        <if test="remark != null  and remark != ''">
            and remark=#{remark}
        </if>
        <if test="createUser != null">
            and create_user=#{createUser}
        </if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
        </if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
        </if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
        </if>
    </sql>

    <!--根据用户编号查询角色编号-->
    <select id="getRoleIdByUserId" parameterType="long" resultType="long">
        select role_id from sys_user_role where user_id=#{userId} limit 1
    </select>

</mapper>