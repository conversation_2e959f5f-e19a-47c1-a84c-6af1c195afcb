<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnListDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnListDetailEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="ret_list_detail_id" property="retListDetailId" jdbcType="INTEGER"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="goods_barcode" property="goodsBarcode" jdbcType="VARCHAR"/>
        <result column="goods_id" property="goodsId" jdbcType="BIGINT"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="ret_list_detail_count" property="retListDetailCount" jdbcType="DECIMAL"/>
        <result column="ret_list_detail_price" property="retListDetailPrice" jdbcType="DECIMAL"/>
        <result column="handle_way" property="handleWay" jdbcType="SMALLINT"/>
        <result column="ret_list_origin_price" property="retListOriginPrice" jdbcType="DECIMAL"/>
        <result column="ret_list_unique" property="retListUnique" jdbcType="VARCHAR"/>
        <result column="rsale_list_detail_id" property="rsaleListDetailId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`,`company_id`, `ret_list_detail_id`, `sale_list_unique`, `goods_barcode`, `goods_name`, `ret_list_detail_count`,
		`ret_list_detail_price`, `handle_way`, `ret_list_origin_price`, `ret_list_unique`, `rsale_list_detail_id`, `create_time`, `modify_time`,
        `goods_id`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="retListDetailId != null">
            and ret_list_detail_id=#{retListDetailId}
		</if>
        <if test="saleListUnique != null  and saleListUnique != ''">
            and sale_list_unique=#{saleListUnique}
		</if>
        <if test="goodsBarcode != null  and goodsBarcode != ''">
            and goods_barcode=#{goodsBarcode}
		</if>
        <if test="goodsId != null">
            and goods_id=#{goodsId}
        </if>
        <if test="goodsName != null  and goodsName != ''">
            and goods_name=#{goodsName}
		</if>
        <if test="retListDetailCount != null">
            and ret_list_detail_count=#{retListDetailCount}
		</if>
        <if test="retListDetailPrice != null">
            and ret_list_detail_price=#{retListDetailPrice}
		</if>
        <if test="handleWay != null">
            and handle_way=#{handleWay}
		</if>
        <if test="retListOriginPrice != null">
            and ret_list_origin_price=#{retListOriginPrice}
		</if>
        <if test="retListUnique != null  and retListUnique != ''">
            and ret_list_unique=#{retListUnique}
		</if>
        <if test="rsaleListDetailId != null">
            and rsale_list_detail_id=#{rsaleListDetailId}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>