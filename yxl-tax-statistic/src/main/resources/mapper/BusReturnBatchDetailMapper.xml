<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnBatchDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnBatchDetailEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="batch_id" property="batchId" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="category_two_id" property="categoryTwoId" jdbcType="BIGINT"/>
        <result column="goods_id" property="goodsId" jdbcType="BIGINT"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="goods_barcode" property="goodsBarcode" jdbcType="VARCHAR"/>
        <result column="goods_count" property="goodsCount" jdbcType="DECIMAL"/>
        <result column="total_money" property="totalMoney" jdbcType="DECIMAL"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `batch_id`, `category_id`, `category_two_id`, `goods_id`, `goods_name`, 
		`goods_barcode`, `goods_count`, `total_money`, `company_id`, `create_time`, `modify_time`, `order_id`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="batchId != null">
            and batch_id=#{batchId}
		</if>
        <if test="orderId != null">
            and order_id=#{orderId}
		</if>
        <if test="categoryId != null">
            and category_id=#{categoryId}
		</if>
        <if test="categoryTwoId != null">
            and category_two_id=#{categoryTwoId}
		</if>
        <if test="goodsId != null">
            and goods_id=#{goodsId}
		</if>
        <if test="goodsName != null  and goodsName != ''">
            and goods_name=#{goodsName}
		</if>
        <if test="goodsBarcode != null  and goodsBarcode != ''">
            and goods_barcode=#{goodsBarcode}
		</if>
        <if test="goodsCount != null">
            and goods_count=#{goodsCount}
		</if>
        <if test="totalMoney != null">
            and total_money=#{totalMoney}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>