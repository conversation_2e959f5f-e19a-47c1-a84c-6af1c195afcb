<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnListPaydetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnListPaydetailEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="ret_list_unique" property="retListUnique" jdbcType="VARCHAR"/>
        <result column="pay_type" property="payType" jdbcType="SMALLINT"/>
        <result column="pay_money" property="payMoney" jdbcType="DECIMAL"/>
        <result column="service_type" property="serviceType" jdbcType="SMALLINT"/>
        <result column="mch_id" property="mchId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `sale_list_unique`, `ret_list_unique`, `pay_type`, `pay_money`, `service_type`,
		`mch_id`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="saleListUnique != null  and saleListUnique != ''">
            and sale_list_unique=#{saleListUnique}
		</if>
        <if test="retListUnique != null  and retListUnique != ''">
            and ret_list_unique=#{retListUnique}
		</if>
        <if test="payType != null">
            and pay_type=#{payType}
		</if>
        <if test="payMoney != null">
            and pay_money=#{payMoney}
		</if>
        <if test="serviceType != null">
            and service_type=#{serviceType}
		</if>
        <if test="mchId != null  and mchId != ''">
            and mch_id=#{mchId}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>