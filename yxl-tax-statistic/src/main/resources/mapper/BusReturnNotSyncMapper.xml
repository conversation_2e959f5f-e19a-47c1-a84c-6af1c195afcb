<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnNotSyncMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnNotSyncEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="sync_type" property="syncType" jdbcType="INTEGER"/>
        <result column="ret_list_unique" property="retListUnique" jdbcType="VARCHAR"/>
        <result column="ret_list_detail_id" property="retListDetailId" jdbcType="BIGINT"/>
        <result column="goods_id" property="goodsId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `sync_type`, `ret_list_unique`, `ret_list_detail_id`, `goods_id`,
		`status`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="syncType != null">
            and sync_type=#{syncType}
		</if>
        <if test="retListUnique != null">
            and ret_list_unique=#{retListUnique}
		</if>
        <if test="retListDetailId != null">
            and ret_list_detail_id=#{retListDetailId}
		</if>
        <if test="goodsId != null">
            and goods_id=#{goodsId}
		</if>
        <if test="status != null">
            and status=#{status}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>