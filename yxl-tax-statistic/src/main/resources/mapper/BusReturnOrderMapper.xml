<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnOrderMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnOrderEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="total_money" property="totalMoney" jdbcType="DECIMAL"/>
        <result column="online_money" property="onlineMoney" jdbcType="DECIMAL"/>
        <result column="cash_money" property="cashMoney" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `order_no`, `total_money`, `online_money`, `cash_money`, 
		`create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="orderNo != null  and orderNo != ''">
            and order_no=#{orderNo}
		</if>
        <if test="totalMoney != null">
            and total_money=#{totalMoney}
		</if>
        <if test="onlineMoney != null">
            and online_money=#{onlineMoney}
		</if>
        <if test="cashMoney != null">
            and cash_money=#{cashMoney}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>