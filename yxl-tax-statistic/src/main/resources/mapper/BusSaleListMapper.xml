<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusSaleListMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusSaleListEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="shop_name" property="shopName" jdbcType="VARCHAR"/>
        <result column="sale_type" property="saleType" jdbcType="INTEGER"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="cus_unique" property="cusUnique" jdbcType="VARCHAR"/>
        <result column="sale_list_name" property="saleListName" jdbcType="VARCHAR"/>
        <result column="sale_list_total" property="saleListTotal" jdbcType="DECIMAL"/>
        <result column="sale_list_actually_received" property="saleListActuallyReceived" jdbcType="DECIMAL"/>
        <result column="sale_list_service_fee" property="saleListServiceFee" jdbcType="DECIMAL"/>
        <result column="trade_no" property="tradeNo" jdbcType="VARCHAR"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="sale_list_datetime" property="saleListDatetime" jdbcType="TIMESTAMP"/>
        <result column="sale_list_state" property="saleListState" jdbcType="INTEGER"/>
        <result column="sale_list_payment" property="saleListPayment" jdbcType="INTEGER"/>
        <result column="settled_status" property="settledStatus" jdbcType="INTEGER"/>
        <result column="profit_status" property="profitStatus" jdbcType="INTEGER"/>
        <result column="profit_total" property="profitTotal" jdbcType="DECIMAL"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="order_type" property="orderType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `shop_unique`, `shop_name`, `sale_type`, `sale_list_unique`, `cus_unique`,
		`sale_list_name`, `sale_list_total`, `sale_list_actually_received`, `sale_list_service_fee`, `trade_no`, `pay_time`,
        `sale_list_datetime`, `sale_list_state`, `sale_list_payment`, `settled_status`, `profit_status`, `profit_total`, `contract_no`, `order_type`,  `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="shopName != null  and shopName != ''">
            and shop_name=#{shopName}
		</if>
        <if test="saleType != null">
            and sale_type=#{saleType}
		</if>
        <if test="saleListUnique != null">
            and sale_list_unique=#{saleListUnique}
		</if>
		<if test="cusUnique != null and cusUnique != ''">
            and cus_unique=#{cusUnique}
		</if>
        <if test="saleListName != null  and saleListName != ''">
            and sale_list_name=#{saleListName}
		</if>
        <if test="saleListTotal != null">
            and sale_list_total=#{saleListTotal}
		</if>
        <if test="saleListActuallyReceived != null">
            and sale_list_actually_received=#{saleListActuallyReceived}
		</if>
        <if test="saleListServiceFee != null">
            and sale_list_service_fee=#{saleListServiceFee}
		</if>
        <if test="tradeNo != null  and tradeNo != ''">
            and trade_no=#{tradeNo}
		</if>
        <if test="payTime != null  and payTime != ''">
            and pay_time=#{payTime}
		</if>
        <if test="saleListDatetime != null  and saleListDatetime != ''">
            and sale_list_datetime=#{saleListDatetime}
		</if>
        <if test="saleListState != null">
            and sale_list_state=#{saleListState}
		</if>
        <if test="saleListPayment != null">
            and sale_list_payment=#{saleListPayment}
		</if>
        <if test="settledStatus != null">
            and settled_status=#{settledStatus}
		</if>
        <if test="profitStatus != null">
            and profit_status=#{profitStatus}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>
    <select id="selectTotalAmount" resultType="java.math.BigDecimal">
        select
            sum(sale_list_actually_received) as totalAmount
        from sale_list
        <where>
            and company_id = #{params.companyId}
            <if test="params.createTimes != null and params.createTimes.length == 2">
                and sale_list_datetime between #{params.createTimes[0]} and #{params.createTimes[1]}
            </if>
            <if test="params.selectPayType != null and 1 == params.selectPayType">
                and sale_list_payment != 1
            </if>
        </where>
    </select>
    <select id="selectOnlineMoney" resultType="java.util.Map">
        select
            sl.shop_unique,
            bs.has_service_fee,
            sum(sl.sale_list_service_fee) as fee,
            sum(sl.profit_total) as profitTotal,
            sum(slpd.pay_money) as pay_money
        from
            sale_list sl
        inner join sale_list_pay_detail slpd on sl.company_id = slpd.company_id and sl.sale_list_unique = slpd.sale_list_unique
        inner join bus_shop bs on sl.shop_unique = bs.shop_unique
        where sl.company_id = #{companyId}
          and sl.settled_status = 0
          and slpd.server_type != 0
        and sl.sale_list_datetime between #{startTime} and #{endTime}
        group by sl.shop_unique, bs.has_service_fee
    </select>
    <update id="updateSettledStatus">
        update sale_list set settled_status = 1
        where company_id = #{companyId}
        and settled_status = 0
        and sale_list_datetime between #{startTime} and #{endTime}
    </update>
    <update id="updateProfitStatus">

    </update>
    <update id="updateBatchProfitRecords">
        <foreach collection="list" item="item" separator=";">
            update sale_list set profit_status = 1, profit_total = #{item.profitTotal} where id = #{item.id}
        </foreach>
    </update>
    <select id="selectTaxSyncCount" resultType="java.util.Map">
        select
            count(sl.sale_list_unique) as count,
            sum(sl.sale_list_actually_received) as amount
        from
            sale_list sl
        where sl.sale_list_datetime between #{startTime} and #{endTime}
    </select>
    <select id="selectTaxCount" resultType="java.util.Map">
        select
            count(sl.sale_list_unique) as count
        from
            sale_list sl
            inner join sale_list_pay_detail slpd on sl.company_id = slpd.company_id and sl.sale_list_unique = slpd.sale_list_unique
        where sl.shop_unique = #{shopUnique}
          and slpd.server_type != 0
          and sl.sale_list_datetime between #{startTime} and #{endTime}
    </select>
    <select id="selectTaxCountGroup" resultType="java.util.Map">
        select
            sl.shop_unique as shopUnique,
            count(sl.sale_list_unique) as count,
            sum(sl.sale_list_actually_received) as amount,
            sum(sl.sale_list_service_fee) as fee
        from
            sale_list sl
            inner join sale_list_pay_detail slpd on sl.company_id = slpd.company_id and sl.sale_list_unique = slpd.sale_list_unique
            inner join sys_company sc on sl.company_id = sc.id
        where slpd.server_type != 0
            and sl.sale_type != 0
            and sl.settled_status = 1
          and sl.sale_list_datetime between #{startTime} and #{endTime}
            and sc.statistics_status = 1
            group by sl.shop_unique
    </select>
    <select id="saleListSyncInfo" resultMap="saleListSyncInfo">
        select
            sl.shop_unique,
            bs.shop_name,
            sl.sale_list_unique,
            sl.sale_list_actually_received
        from
            sale_list sl
            left join
            bus_shop bs on sl.shop_unique = bs.shop_unique
            inner join sys_company sc on sl.company_id = sc.id
        where sl.sale_list_datetime between #{startTime} and #{endTime}
        and sc.statistics_status = 1
    </select>
    <resultMap id="saleListSyncInfo" type="cc.buyhoo.tax.result.saleList.SaleListSyncDto">
        <result column="shop_unique" property="shopUnique"/>
        <result column="shop_name" property="shopName" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="sale_list_actually_received" property="saleListActuallyReceived" />
    </resultMap>
</mapper>