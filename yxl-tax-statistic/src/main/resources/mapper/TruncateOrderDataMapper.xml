<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.TruncateOrderDataMapper">

    <!-- 清空订单数据 -->
    <update id="truncate1" >
        TRUNCATE TABLE bus_inventory_batch
    </update>
    <update id="truncate2">TRUNCATE TABLE bus_inventory_batch_detail</update>
    <update id="truncate3">TRUNCATE TABLE bus_inventory_not_sync</update>
    <update id="truncate4">TRUNCATE TABLE bus_inventory_order</update>
    <update id="truncate5">TRUNCATE TABLE bus_inventory_order_category</update>
    <update id="truncate6">TRUNCATE TABLE bus_shop_bill</update>
    <update id="truncate7">TRUNCATE TABLE bus_shop_bill_detail</update>
    <update id="truncate8">TRUNCATE TABLE bus_shop_bill_log</update>
    <update id="truncate9">TRUNCATE TABLE sale_list</update>
    <update id="truncate10">TRUNCATE TABLE sale_list_detail</update>
    <update id="truncate11">TRUNCATE TABLE sale_list_pay_detail</update>
    <update id="truncate12">TRUNCATE TABLE return_list</update>
    <update id="truncate13">TRUNCATE TABLE return_list_detail</update>
    <update id="truncate14">TRUNCATE TABLE return_list_paydetail</update>
    <update id="truncate15">TRUNCATE TABLE bus_return_batch</update>
    <update id="truncate16">TRUNCATE TABLE bus_return_batch_detail</update>
    <update id="truncate17">TRUNCATE TABLE bus_return_not_sync</update>
    <update id="truncate18">TRUNCATE TABLE bus_return_order</update>
    <update id="truncate19">TRUNCATE TABLE bus_return_order_category</update>
    <update id="truncate20">TRUNCATE TABLE bus_shop_invoice</update>
    <update id="truncate21">TRUNCATE TABLE bus_shop_service_fee</update>
</mapper>