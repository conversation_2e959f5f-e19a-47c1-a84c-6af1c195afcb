<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusShopCoverChargeMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopCoverChargeEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="cover_charge" property="coverCharge" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `shop_unique`, `stat_date`, `cover_charge`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="statDate != null  and statDate != ''">
            and stat_date=#{statDate}
		</if>
        <if test="coverCharge != null">
            and cover_charge=#{coverCharge}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.entity.BusShopCoverChargeEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from bus_shop_cover_charge
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>