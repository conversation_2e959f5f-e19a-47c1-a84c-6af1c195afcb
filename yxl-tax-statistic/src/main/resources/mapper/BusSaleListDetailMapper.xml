<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusSaleListDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusSaleListDetailEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sale_list_detail_id" property="saleListDetailId" jdbcType="BIGINT"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="goods_id" property="goodsId" jdbcType="BIGINT"/>
        <result column="goods_barcode" property="goodsBarcode" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="sale_list_detail_count" property="saleListDetailCount" jdbcType="DECIMAL"/>
        <result column="sale_list_detail_price" property="saleListDetailPrice" jdbcType="DECIMAL"/>
        <result column="sale_list_detail_subtotal" property="saleListDetailSubtotal" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `sale_list_detail_id`, `sale_list_unique`, `goods_id`, `goods_barcode`, `goods_name`,
		`sale_list_detail_count`, `sale_list_detail_price`, `sale_list_detail_subtotal`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="saleListDetailId != null">
            and sale_list_detail_id=#{saleListDetailId}
		</if>
        <if test="saleListUnique != null">
            and sale_list_unique=#{saleListUnique}
		</if>
        <if test="goodsId != null">
            and goods_id=#{goodsId}
		</if>
        <if test="goodsBarcode != null  and goodsBarcode != ''">
            and goods_barcode=#{goodsBarcode}
		</if>
        <if test="goodsName != null  and goodsName != ''">
            and goods_name=#{goodsName}
		</if>
        <if test="saleListDetailCount != null">
            and sale_list_detail_count=#{saleListDetailCount}
		</if>
        <if test="saleListDetailPrice != null">
            and sale_list_detail_price=#{saleListDetailPrice}
		</if>
        <if test="saleListDetailSubtotal != null">
            and sale_list_detail_subtotal=#{saleListDetailSubtotal}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <select id="querySaleListDetailForInvoice" resultMap="generateQdInvoiceGoodsEntity">
        SELECT
            sld.sale_list_detail_subtotal,
            sld.goods_name AS detail_name,
            0 AS rowKind,
            1 AS priceKind,
            1 AS number,
            1 AS originalRowNumber,
            bgc.category_no,
            bgc.goods_name,
            bgc.tax_rate,
            bgc.unit,
            @rank := @rank + 1 AS rowNumbe
        FROM
            sale_list_detail sld LEFT JOIN bus_goods bg ON sld.goods_id = bg.goods_id
        LEFT JOIN bus_goods_category bgc ON bg.category_id = bgc.id,
            (SELECT @rank := 0) AS r
        WHERE sld.sale_list_unique = #{saleListUnique}
    </select>

    <resultMap id="generateQdInvoiceGoodsEntity" type="cc.buyhoo.tax.entity.invoice.InvoiceGoodsExternalEntity">
        <id column="rowNumbe" property="rowNumbe"/>
        <result column="detail_name" property="detailName"/>
        <result column="rowKind" property="rowKind"/>
        <result column="category_no" property="goodsTaxNo"/>
        <result column="number" property="number"/>
        <result column="originalRowNumber" property="originalRowNumber"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="priceKind" property="priceKind" />
        <result column="tax_rate" property="taxRate"/>
        <result column="sale_list_detail_subtotal" property="amount"/>
    </resultMap>
</mapper>