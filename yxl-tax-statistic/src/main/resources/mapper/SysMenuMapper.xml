<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SysMenuMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysMenuEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="menu_name" property="menuName" jdbcType="VARCHAR"/>
        <result column="permission" property="permission" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="component_name" property="componentName" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="hidden" property="hidden" jdbcType="INTEGER"/>
        <result column="menu_level" property="menuLevel" jdbcType="INTEGER"/>
        <result column="built_in_system" property="builtInSystem" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `parent_id`, `menu_name`, `permission`, `type`, `path`, 
		`component`, `component_name`, `sort`, `icon`, `hidden`, `menu_level`, `built_in_system`,
        `create_time`,`create_user`,`modify_time`,`modify_user`,`del_flag`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="parentId != null">
            and parent_id=#{parentId}
		</if>
        <if test="menuName != null  and menuName != ''">
            and menu_name=#{menuName}
		</if>
        <if test="permission != null  and permission != ''">
            and permission=#{permission}
		</if>
        <if test="type != null">
            and type=#{type}
		</if>
        <if test="path != null  and path != ''">
            and path=#{path}
		</if>
        <if test="component != null  and component != ''">
            and component=#{component}
		</if>
        <if test="componentName != null  and componentName != ''">
            and component_name=#{componentName}
		</if>
        <if test="sort != null">
            and sort=#{sort}
		</if>
        <if test="icon != null  and icon != ''">
            and icon=#{icon}
		</if>
        <if test="hidden != null">
            and hidden=#{hidden}
		</if>
        <if test="menuLevel != null">
            and menu_level=#{menuLevel}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!--根据角色查询权限-->
    <select id="getMenuIdByRoleId" parameterType="long" resultType="long">
        select menu_id from sys_role_menu where role_id=#{roleId}
    </select>

</mapper>