<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusShopInvoiceGoodsServiceMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopInvoiceGoodsServiceEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="category_no" property="categoryNo" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="tax_item" property="taxItem" jdbcType="VARCHAR"/>
        <result column="goods_spec" property="goodsSpec" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="VARCHAR"/>
        <result column="tax_rate" property="taxRate" jdbcType="VARCHAR"/>
        <result column="enable_status" property="enableStatus" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `category_no`, `goods_name`, `tax_item`, `goods_spec`, 
		`goods_unit`, `tax_rate`, `enable_status`, `create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="categoryNo != null  and categoryNo != ''">
            and category_no=#{categoryNo}
		</if>
        <if test="goodsName != null  and goodsName != ''">
            and goods_name=#{goodsName}
		</if>
        <if test="taxItem != null  and taxItem != ''">
            and tax_item=#{taxItem}
		</if>
        <if test="goodsSpec != null  and goodsSpec != ''">
            and goods_spec=#{goodsSpec}
		</if>
        <if test="goodsUnit != null  and goodsUnit != ''">
            and goods_unit=#{goodsUnit}
		</if>
        <if test="taxRate != null  and taxRate != ''">
            and tax_rate=#{taxRate}
		</if>
        <if test="enableStatus != null">
            and enable_status=#{enableStatus}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>