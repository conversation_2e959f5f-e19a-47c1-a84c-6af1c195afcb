<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusGoodsCategoryMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusGoodsCategoryEntity">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="categoryName" column="category_name"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="companyId" column="company_id"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="categoryType" column="category_type"/>
        <result property="categoryNo" column="category_no"/>
        <result property="goodsName" column="goods_name"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="unit" column="unit"/>
    </resultMap>


    <sql id="baseColumns">
        a.id,a.parent_id,a.ancestors,a.category_name,a.enable_status,a.company_id,a.remark,a.create_user,a.create_time,a.modify_user,a.modify_time,a.del_flag,a.category_type,category_no,goods_name,tax_rate,unit
    </sql>
    <sql id="whereSql">
        <if test="params.id != null ">
            and a.id = #{params.id}
        </if>
        <if test="params.parentId != null ">
            and a.parent_id = #{params.parentId}
        </if>
        <if test="params.ancestors != null  and params.ancestors.trim() != ''">
            and a.ancestors = #{params.ancestors}
        </if>
        <if test="params.categoryName != null  and params.categoryName.trim() != ''">
            and a.category_name = #{params.categoryName}
        </if>
        <if test="params.enableStatus != null ">
            and a.enable_status = #{params.enableStatus}
        </if>
        <if test="params.companyId != null">
            and a.company_id = #{params.companyId}
        </if>
        <if test="params.remark != null  and params.remark.trim() != ''">
            and a.remark = #{params.remark}
        </if>
        <if test="params.createUser != null ">
            and a.create_user = #{params.createUser}
        </if>
        <if test="params.createTime != null ">
            and a.create_time = #{params.createTime}
        </if>
        <if test="params.modifyUser != null ">
            and a.modify_user = #{params.modifyUser}
        </if>
        <if test="params.modifyTime != null ">
            and a.modify_time = #{params.modifyTime}
        </if>
        <if test="params.delFlag != null ">
            and a.del_flag = #{params.delFlag}
        </if>
        <if test="params.categoryType != null ">
            and a.category_type = #{params.categoryType}
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusGoodsCategoryEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_goods_category a
        <where>
            <include refid="whereSql"/>
        </where>
    </select>

</mapper>