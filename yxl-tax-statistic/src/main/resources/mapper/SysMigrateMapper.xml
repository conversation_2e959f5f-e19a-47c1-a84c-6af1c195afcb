<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.SysMigrateMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysMigrateEntity">
        <result property="id" column="id"/>
        <result property="inCompanyId" column="in_company_id"/>
        <result property="outCompanyId" column="out_company_id"/>
        <result property="shopUnique" column="shop_unique"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditContent" column="audit_content"/>
        <result property="auditUser" column="audit_user"/>
        <result property="auditTime" column="audit_time"/>
        <result property="remark" column="remark"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="baseColumns">
        id,in_company_id,out_company_id,shop_unique,audit_status,audit_content,audit_user,audit_time,remark,enable_status,create_user,create_time,modify_user,modify_time,del_flag
    </sql>
    <sql id="whereSql">
        <if test="id != null ">
            and a.id = #{id},
        </if>
        <if test="inCompanyId != null ">
            and a.in_company_id = #{inCompanyId},
        </if>
        <if test="outCompanyId != null ">
            and a.out_company_id = #{outCompanyId},
        </if>
        <if test="shopUnique != null ">
            and a.shop_unique = #{shopUnique},
        </if>
        <if test="auditStatus != null ">
            and a.audit_status = #{auditStatus},
        </if>
        <if test="auditContent != null  and auditContent.trim() != ''">
            and a.audit_content = #{auditContent},
        </if>
        <if test="auditUser != null ">
            and a.audit_user = #{auditUser},
        </if>
        <if test="auditTime != null ">
            and a.audit_time = #{auditTime},
        </if>
        <if test="remark != null  and remark.trim() != ''">
            and a.remark = #{remark},
        </if>
        <if test="enableStatus != null ">
            and a.enable_status = #{enableStatus},
        </if>
        <if test="createUser != null ">
            and a.create_user = #{createUser},
        </if>
        <if test="createTime != null ">
            and a.create_time = #{createTime},
        </if>
        <if test="modifyUser != null ">
            and a.modify_user = #{modifyUser},
        </if>
        <if test="modifyTime != null ">
            and a.modify_time = #{modifyTime},
        </if>
        <if test="delFlag != null ">
            and a.del_flag = #{delFlag},
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.SysMigrateEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from sys_migrate
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
    <select id="selectPageList" resultType="cc.buyhoo.tax.result.sysMigrate.SysMigrateDto">
        select
            a.id,
            a.shop_unique,
            a.in_company_id,
            c.company_name as in_company_name,
            a.out_company_id,
            d.company_name as out_company_name,
            a.audit_status,
            a.audit_user,
            a.audit_time,
            a.create_user,
            a.create_time,
            ROUND(IFNULL(c.target_amount,0)/10000,2) as target_amount,
            c.tax_type,
            mt.market_name,
            s.shop_name
        from sys_migrate a
        left join sys_company c on a.in_company_id = c.id
        left join sys_company d on a.out_company_id = d.id
        left join sys_market mt on c.market_id = mt.id
        left join bus_shop s on a.shop_unique = s.shop_unique
        <where>
            and a.del_flag = 0
            <if test="params.inCompanyId != null">
                and a.in_company_id = #{params.inCompanyId}
            </if>
            <if test="params.outCompanyId != null">
                and a.out_company_id = #{params.outCompanyId}
            </if>
            <if test="params.shopName != null and params.shopName.trim() != ''">
                and INSTR(s.shop_name, #{params.shopName}) > 0
            </if>
            <if test="params.auditStatus != null">
                and a.audit_status = #{params.auditStatus}
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="selectList" resultType="cc.buyhoo.tax.result.sysMigrate.SysMigrateDto">
        select
        a.id,
        a.shop_unique,
        a.in_company_id,
        c.company_name as in_company_name,
        a.out_company_id,
        d.company_name as out_company_name,
        a.audit_status,
        a.audit_user,
        a.audit_time,
        a.create_user,
        a.create_time,
        c.target_amount,
        c.tax_type,
        mt.market_name,
        s.shop_name
        from sys_migrate a
        left join sys_company c on a.in_company_id = c.id
        left join sys_company d on a.out_company_id = d.id
        left join sys_market mt on c.market_id = mt.id
        left join bus_shop s on a.shop_unique = s.shop_unique
        <where>
            and a.del_flag = 0
            <if test="params.inCompanyId != null">
                and a.in_company_id = #{params.inCompanyId}
            </if>
            <if test="params.outCompanyId != null">
                and a.out_company_id = #{params.outCompanyId}
            </if>
            <if test="params.shopName != null and params.shopName.trim() != ''">
                and INSTR(s.shop_name, #{params.shopName}) > 0
            </if>
            <if test="params.auditStatus != null">
                and a.audit_status = #{params.auditStatus}
            </if>
        </where>
        order by a.id desc
    </select>
    <select id="selectDetailById" resultType="cc.buyhoo.tax.result.sysMigrate.SysMigrateDetailResult">
        select
        a.id,
        c.company_name as inCompanyName,
        d.company_name as outCompanyName,
        a.audit_status as auditStatus,
        au.username as auditUserName,
        a.audit_time as auditTime,
        u.username as createUserName,
        a.create_time as createTime,
        ROUND(IFNULL(c.target_amount,0)/10000,2) as targetAmount,
        c.tax_type as taxType,
        mt.market_name as marketName,
        s.shop_name as shopName,
        a.remark as remark
        from sys_migrate a
        left join sys_company c on a.in_company_id = c.id
        left join sys_company d on a.out_company_id = d.id
        left join sys_market mt on c.market_id = mt.id
        left join bus_shop s on a.shop_unique = s.shop_unique
        left join sys_user u on a.create_user = u.id
        left join sys_user au on a.audit_user = au.id
        where a.id = #{id}
    </select>
</mapper>