<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.TradeRecordMapper">
    <resultMap id="tradeRecord" type="cc.buyhoo.tax.entity.TradeRecordEntity">
        <id property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="companyBankName" column="company_bank_name"/>
        <result property="companyBankAccount" column="company_bank_account"/>
        <result property="tradeDate" column="trade_date"/>
        <result property="tradeTime" column="trade_time"/>
        <result property="tradeAmount" column="trade_amount"/>
        <result property="tradeWay" column="trade_way"/>
        <result property="tradeOtherAccount" column="trade_other_account"/>
        <result property="tradeOtherName" column="trade_other_name"/>
        <result property="companyBalance" column="company_balance"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="tradeType" column="trade_type"/>
        <result property="tradeAbstract" column="trade_abstract"/>
        <result property="creditMark" column="credit_mark"/>
        <result property="businessNum" column="business_num"/>
        <result property="bankBranchId" column="bank_branch_id"/>
        <result property="fromerBankCode" column="fromer_bank_code"/>
        <result property="fromerBankName" column="fromer_bank_name"/>
        <result property="fromerBankAddress" column="fromer_bank_address"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="caeate_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="baseSql">
        SELECT
            id,
            company_id,
            company_bank_name,
            company_bank_account,
            trade_date,
            trade_time,
            trade_amount,
            trade_way,
            trade_other_account,
            trade_other_name,
            company_balance,
            serial_number,
            trade_type,
            trade_abstract,
            credit_mark,
            business_num,
            bank_branch_id,
            fromer_bank_code,
            fromer_bank_name,
            fromer_bank_address,
            create_user,
            create_time,
            modify_user,
            modify_time
        FROM trade_record
    </sql>
</mapper>