<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.SysCityInfoMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysCityInfoEntity">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="grade" column="grade"/>
        <result property="name" column="name"/>
        <result property="abbr" column="abbr"/>
        <result property="initial" column="initial"/>
        <result property="full" column="full"/>
        <result property="hot" column="hot"/>
    </resultMap>


    <sql id="baseColumns">
        id,pid,grade,name,abbr,initial,full,hot
    </sql>
    <sql id="whereSql">
        <if test="id != null ">
            and a.id = #{id},
        </if>
        <if test="pid != null ">
            and a.pid = #{pid},
        </if>
        <if test="grade != null ">
            and a.grade = #{grade},
        </if>
        <if test="name != null  and name.trim() != ''">
            and a.name = #{name},
        </if>
        <if test="abbr != null  and abbr.trim() != ''">
            and a.abbr = #{abbr},
        </if>
        <if test="initial != null  and initial.trim() != ''">
            and a.initial = #{initial},
        </if>
        <if test="full != null  and full.trim() != ''">
            and a.full = #{full},
        </if>
        <if test="hot != null ">
            and a.hot = #{hot},
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.SysCityInfoEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from city_info
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>