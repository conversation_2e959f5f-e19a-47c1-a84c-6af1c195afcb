<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusInventoryNotSyncMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusInventoryNotSyncEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="sync_type" property="syncType" jdbcType="INTEGER"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="BIGINT"/>
        <result column="sale_list_detail_id" property="saleListDetailId" jdbcType="BIGINT"/>
        <result column="goods_id" property="goodsId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `sync_type`, `sale_list_unique`, `sale_list_detail_id`, `goods_id`, 
		`status`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="syncType != null">
            and sync_type=#{syncType}
		</if>
        <if test="saleListUnique != null">
            and sale_list_unique=#{saleListUnique}
		</if>
        <if test="saleListDetailId != null">
            and sale_list_detail_id=#{saleListDetailId}
		</if>
        <if test="goodsId != null">
            and goods_id=#{goodsId}
		</if>
        <if test="status != null">
            and status=#{status}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>