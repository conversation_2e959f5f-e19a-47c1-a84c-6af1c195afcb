<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusShopMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopEntity">
        <result property="id" column="id"/>
        <result property="shopUnique" column="shop_unique"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopType" column="shop_type"/>
        <result property="address" column="address"/>
        <result property="shopPhone" column="shop_phone"/>
        <result property="countyCode" column="county_code"/>
        <result property="townCode" column="town_code"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="companyId" column="company_id"/>
        <result property="invitationCode" column="invitation_code"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankCard" column="bank_card"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="legalPhone" column="legal_phone"/>
        <result property="remark" column="remark"/>
        <result property="bindStatus" column="bind_status"/>
        <result property="cooperateType" column="cooperate_type"/>
        <result property="subAccount" column="sub_account"/>
        <result property="serviceFeeRate" column="service_fee_rate"/>
        <result property="hasServiceFee" column="has_service_fee"/>
        <result property="syncCanyinData" column="sync_canyin_data"/>
        <result property="syncBuyhooData" column="sync_buyhoo_data"/>
        <result property="contractUrl" column="contract_url"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>


    <sql id="baseColumns">
        a.id,a.shop_unique,a.shop_name,a.shop_type,a.address,a.shop_phone,a.county_code,a.town_code,a.enable_status,a.company_id,a.invitation_code,a.bank_name,a.bank_card,a.legal_person,a.legal_phone,a.remark,a.bind_status,a.cooperate_type,a.sub_account,a.service_fee_rate,a.has_service_fee,a.sync_canyin_data,a.sync_buyhoo_data,a.contract_url,a.del_flag,a.create_user,a.create_time,a.modify_user,a.modify_time
    </sql>
    <sql id="whereSql">
        <if test="params.id != null ">
            and a.id = #{params.id}
        </if>
        <if test="params.shopUnique != null ">
            and a.shop_unique = #{params.shopUnique}
        </if>
        <if test="params.shopName != null  and params.shopName.trim() != ''">
            and a.shop_name = #{params.shopName}
        </if>
        <if test="params.shopType != null ">
            and a.shop_type = #{params.shopType}
        </if>
        <if test="params.address != null  and params.address.trim() != ''">
            and a.address = #{params.address}
        </if>
        <if test="params.shopPhone != null  and params.shopPhone.trim() != ''">
            and a.shop_phone = #{params.shopPhone}
        </if>
        <if test="params.countyCode != null  and params.countyCode.trim() != ''">
            and a.county_code = #{params.countyCode}
        </if>
        <if test="params.townCode != null  and params.townCode.trim() != ''">
            and a.town_code = #{params.townCode}
        </if>
        <if test="params.enableStatus != null ">
            and a.enable_status = #{params.enableStatus}
        </if>
        <if test="params.companyId != null ">
            and a.company_id = #{params.companyId}
        </if>
        <if test="params.invitationCode != null  and params.invitationCode.trim() != ''">
            and a.invitation_code = #{params.invitationCode}
        </if>
        <if test="params.bankName != null  and params.bankName.trim() != ''">
            and a.bank_name = #{params.bankName}
        </if>
        <if test="params.bankCard != null  and params.bankCard.trim() != ''">
            and a.bank_card = #{params.bankCard}
        </if>
        <if test="params.legalPerson != null  and params.legalPerson.trim() != ''">
            and a.legal_person = #{params.legalPerson}
        </if>
        <if test="params.legalPhone != null  and params.legalPhone.trim() != ''">
            and a.legal_phone = #{params.legalPhone}
        </if>
        <if test="params.remark != null  and params.remark.trim() != ''">
            and a.remark = #{params.remark}
        </if>
        <if test="params.bindStatus != null">
            and a.bind_status = #{params.bindStatus}
        </if>
        <if test="params.cooperateType != null">
            and a.cooperate_type = #{params.cooperateType}
        </if>
        <if test="params.hasServiceFee != null">
            and a.has_service_fee = #{params.hasServiceFee}
        </if>
        <if test="params.syncCanyinData != null">
            and a.sync_canyin_data = #{params.syncCanyinData}
        </if>
        <if test="params.syncBuyhooData != null">
            and a.sync_buyhoo_data = #{params.syncBuyhooData}
        </if>
        <if test="params.delFlag != null ">
            and a.del_flag = #{params.delFlag}
        </if>
        <if test="params.createUser != null ">
            and a.create_user = #{params.createUser}
        </if>
        <if test="params.modifyUser != null ">
            and a.modify_user = #{params.modifyUser}
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusShopEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_shop a
        <where>
            <include refid="whereSql"/>
        </where>
    </select>

    <select id="queryAmount">
        select shop_unique, sum(sl.sale_list_actually_received) as completedAmount from sale_list sl left join bus_shop bs on sl.shop_unique = bs.shop_unique and sl.company_id = bs.company_id
        left join sys_company sc on bs.company_id = sc.id group by sl.shop_unique
    </select>
</mapper>