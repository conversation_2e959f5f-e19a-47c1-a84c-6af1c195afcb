<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnOrderCategoryMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnOrderCategoryEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="return_order_id" property="returnOrderId" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="category_two_id" property="categoryTwoId" jdbcType="BIGINT"/>
        <result column="total_goods_count" property="totalGoodsCount" jdbcType="DECIMAL"/>
        <result column="total_money" property="totalMoney" jdbcType="DECIMAL"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `return_order_id`, `category_id`, `category_two_id`, `total_goods_count`, `total_money`, 
		`company_id`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="returnOrderId != null">
            and return_order_id=#{returnOrderId}
		</if>
        <if test="categoryId != null">
            and category_id=#{categoryId}
		</if>
        <if test="categoryTwoId != null">
            and category_two_id=#{categoryTwoId}
		</if>
        <if test="totalGoodsCount != null">
            and total_goods_count=#{totalGoodsCount}
		</if>
        <if test="totalMoney != null">
            and total_money=#{totalMoney}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>