<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusSyncRecordMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusSyncRecordEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="syncType" column="sync_type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="saleListCount" column="sale_list_count"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>


    <sql id="baseColumns">
        a.id,a.company_id,a.sync_type,a.start_time,a.end_time,a.sale_list_count,a.status,a.del_flag,a.create_user,a.create_time,a.modify_user,a.modify_time
    </sql>
    <sql id="whereSql">
        <if test="params.id != null">
            and a.id = #{params.id}
        </if>
        <if test="params.companyId != null">
            and a.company_id = #{params.companyId}
        </if>
        <if test="params.syncType != null">
            and a.sync_type = #{params.syncType}
        </if>
        <if test="params.startTime != null">
            and a.start_time = #{params.startTime}
        </if>
        <if test="params.endTime != null">
            and a.end_time = #{params.endTime}
        </if>
        <if test="params.saleListCount != null">
            and a.sale_list_count = #{params.saleListCount}
        </if>
        <if test="params.status != null">
            and a.status = #{params.status}
        </if>
        <if test="params.delFlag != null">
            and a.del_flag = #{params.delFlag}
        </if>
        <if test="params.createUser != null">
            and a.create_user = #{params.createUser}
        </if>
        <if test="params.createTime != null">
            and a.create_time = #{params.createTime}
        </if>
        <if test="params.modifyUser != null">
            and a.modify_user = #{params.modifyUser}
        </if>
        <if test="params.modifyTime != null">
            and a.modify_time = #{params.modifyTime}
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusSyncRecordEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_sync_record a
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>