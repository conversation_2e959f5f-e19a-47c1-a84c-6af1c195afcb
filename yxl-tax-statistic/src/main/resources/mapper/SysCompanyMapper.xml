<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.SysCompanyMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysCompanyEntity">
        <result property="id" column="id"/>
        <result property="parentCompanyId" column="parent_company_id"/>
        <result property="companyType" column="company_type"/>
        <result property="companyName" column="company_name"/>
        <result property="licenseNumber" column="license_number"/>
        <result property="deptId" column="dept_id"/>
        <result property="address" column="address"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactMobile" column="contact_mobile"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="invitationCode" column="invitation_code"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankCard" column="bank_card"/>
        <result property="targetAmount" column="target_amount"/>
        <result property="taxType" column="tax_type"/>
        <result property="invoiceFlag" column="invoice_flag"/>
        <result property="transferAccountAudit" column="transfer_account_audit"/>
        <result property="payFeeRate" column="pay_fee_rate"/>
        <result property="vatRate" column="vat_rate"/>
        <result property="industryId" column="industry_id"/>
        <result property="marketId" column="market_id"/>
        <result property="statisticGrade" column="statistic_grade"/>
        <result property="statisticAmount" column="statistic_amount"/>
        <result property="statisticStatus" column="statistic_status"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="baseColumns">
        id,parent_company_id,company_type,company_name,license_number,dept_id,address,contact_name,contact_mobile,enable_status,invitation_code,bank_name,bank_card,target_amount,tax_type,invoice_flag,transfer_account_audit,pay_fee_rate,vat_rate,industry_id,market_id,statistic_grade,statistic_amount,statistic_status,remark,create_user,create_time,modify_user,modify_time,del_flag
    </sql>
    <sql id="whereSql">
        <if test="id != null ">
            and a.id = #{id},
        </if>
        <if test="parentCompanyId != null ">
            and a.parent_company_id = #{parentCompanyId},
        </if>
        <if test="companyType != null ">
            and a.company_type = #{companyType},
        </if>
        <if test="companyName != null  and companyName.trim() != ''">
            and a.company_name = #{companyName},
        </if>
        <if test="licenseNumber != null  and licenseNumber.trim() != ''">
            and a.license_number = #{licenseNumber},
        </if>
        <if test="deptId != null ">
            and a.dept_id = #{deptId},
        </if>
        <if test="address != null  and address.trim() != ''">
            and a.address = #{address},
        </if>
        <if test="contactName != null  and contactName.trim() != ''">
            and a.contact_name = #{contactName},
        </if>
        <if test="contactMobile != null  and contactMobile.trim() != ''">
            and a.contact_mobile = #{contactMobile},
        </if>
        <if test="enableStatus != null ">
            and a.enable_status = #{enableStatus},
        </if>
        <if test="invitationCode != null  and invitationCode.trim() != ''">
            and a.invitation_code = #{invitationCode},
        </if>
        <if test="bankName != null  and bankName.trim() != ''">
            and a.bank_name = #{bankName},
        </if>
        <if test="bankCard != null  and bankCard.trim() != ''">
            and a.bank_card = #{bankCard},
        </if>
        <if test="targetAmount != null ">
            and a.target_amount = #{targetAmount},
        </if>
        <if test="taxType != null  and taxType.trim() != ''">
            and a.tax_type = #{taxType},
        </if>
        <if test="invoiceFlag != null ">
            and a.invoice_flag = #{invoiceFlag},
        </if>
        <if test="transferAccountAudit != null ">
            and a.transfer_account_audit = #{transferAccountAudit},
        </if>
        <if test="payFeeRate != null ">
            and a.pay_fee_rate = #{payFeeRate},
        </if>
        <if test="vatRate != null ">
            and a.vat_rate = #{vatRate},
        </if>
        <if test="industryId != null ">
            and a.industry_id = #{industryId},
        </if>
        <if test="marketId != null ">
            and a.market_id = #{marketId},
        </if>
        <if test="statisticGrade != null ">
            and a.statistic_grade = #{statisticGrade},
        </if>
        <if test="statisticAmount != null ">
            and a.statistic_amount = #{statisticAmount},
        </if>
        <if test="statisticStatus != null ">
            and a.statistic_status = #{statisticStatus},
        </if>
        <if test="remark != null  and remark.trim() != ''">
            and a.remark = #{remark},
        </if>
        <if test="createUser != null ">
            and a.create_user = #{createUser},
        </if>
        <if test="createTime != null ">
            and a.create_time = #{createTime},
        </if>
        <if test="modifyUser != null ">
            and a.modify_user = #{modifyUser},
        </if>
        <if test="modifyTime != null ">
            and a.modify_time = #{modifyTime},
        </if>
        <if test="delFlag != null ">
            and a.del_flag = #{delFlag},
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.SysCompanyEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from sys_company
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>