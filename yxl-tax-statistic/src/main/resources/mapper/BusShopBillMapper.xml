<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusShopBillMapper">

    <!-- 查询有需要打款的企业信息 -->
    <select id="queryCompanyListForPay" resultType="long">
        SELECT
            company_id
        FROM
            bus_shop_bill bsb
        WHERE
            unsettled_amount > 0
        GROUP BY company_id
    </select>
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopBillEntity">
        <result property="id" column="id"/>
        <result property="shopUnique" column="shop_unique"/>
        <result property="settledAmount" column="settled_amount"/>
        <result property="unsettledAmount" column="unsettled_amount"/>
        <result property="settledingAmount" column="settleding_amount"/>
        <result property="settledType" column="settled_type"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>


    <sql id="baseColumns">
        a.id,a.shop_unique,a.settled_amount,a.unsettled_amount,a.settleding_amount,a.settled_type,a.remark,a.del_flag,a.create_user,a.create_time,a.modify_user,a.modify_time
    </sql>
    <sql id="whereSql">
        <if test="params.id != null ">
            and a.id = #{params.id}
        </if>
        <if test="params.shopUnique != null ">
            and a.shop_unique = #{params.shopUnique}
        </if>
        <if test="params.settledAmount != null ">
            and a.settled_amount = #{params.settledAmount}
        </if>
        <if test="params.unsettledAmount != null ">
            and a.unsettled_amount = #{params.unsettledAmount}
        </if>
        <if test="params.settledingAmount != null ">
            and a.settleding_amount = #{params.settledingAmount}
        </if>
        <if test="params.settledType != null  and params.settledType.trim() != ''">
            and a.settled_type = #{params.settledType}
        </if>
        <if test="params.remark != null  and params.remark.trim() != ''">
            and a.remark = #{params.remark}
        </if>
        <if test="params.delFlag != null ">
            and a.del_flag = #{params.delFlag}
        </if>
        <if test="params.createUser != null ">
            and a.create_user = #{params.createUser}
        </if>
        <if test="params.modifyUser != null ">
            and a.modify_user = #{params.modifyUser}
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusShopBillEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_shop_bill a
        <where>
            <include refid="whereSql"/>
        </where>
    </select>

    <resultMap id="pageResult" type="cc.buyhoo.tax.result.busShopBill.BusShopBillDto" extends="BaseResultMap">
        <result property="companyId" column="company_id"/>
        <result property="companyName" column="company_name"/>
        <result property="shopName" column="shop_name"/>
    </resultMap>
    <select id="selectPageList" resultType="cc.buyhoo.tax.result.busShopBill.BusShopBillDto">
        select
            <include refid="baseColumns"/>
            ,s.shop_name
            ,s.shop_phone
            ,s.company_id
            ,c.company_name
        from bus_shop s
        left join bus_shop_bill a on a.shop_unique = s.shop_unique
        left join sys_company c on c.id = s.company_id
        <where>
            and s.del_flag = '0'
            and a.del_flag = '0'
            <if test="params.companyId != null">
                and s.company_id = #{params.companyId}
            </if>
            <if test="params.shopName != null and params.shopName.trim() != ''">
                and INSTR(s.shop_name, #{params.shopName}) > 0
            </if>
            <if test="params.shopPhone != null and params.shopPhone.trim() != ''">
                and s.shop_phone = #{params.shopPhone}
            </if>
        </where>
        order by s.id asc
    </select>
    <select id="selectExportList" resultType="cc.buyhoo.tax.result.busShopBill.BusShopBillExcel">
        select
        s.shop_unique as shopUnique,
        s.shop_name as shopName,
        s.shop_phone as shopPhone,
        s.bank_name as bankName,
        s.bank_card as bankCard,
        a.unsettled_amount as unsettledAmount,
        c.company_name as companyName,
        c.bank_name as companyBankName,
        c.bank_card as companyBankCard
        from bus_shop s
        left join bus_shop_bill a on a.shop_unique = s.shop_unique
        left join sys_company c on c.id = s.company_id
        <where>
            and s.del_flag = '0'
            and a.del_flag = '0'
            and a.unsettled_amount > 0
            <if test="params.companyId != null">
                and s.company_id = #{params.companyId}
            </if>
            <if test="params.shopName != null and params.shopName.trim() != ''">
                and INSTR(s.shop_name, #{params.shopName}) > 0
            </if>
            <if test="params.shopPhone != null and params.shopPhone.trim() != ''">
                and s.shop_phone = #{params.shopPhone}
            </if>
            <if test="params.billIds != null and params.billIds.size() > 0">
                and a.id in
                <foreach collection="params.billIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by s.shop_name asc
    </select>

</mapper>