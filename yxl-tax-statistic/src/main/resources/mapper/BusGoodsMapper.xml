<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusGoodsMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusGoodsEntity">
        <result property="id" column="id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="shopUnique" column="shop_unique"/>
        <result property="goodsBarcode" column="goods_barcode"/>
        <result property="goodsName" column="goods_name"/>
        <result property="goodsAlias" column="goods_alias"/>
        <result property="goodsInPrice" column="goods_in_price"/>
        <result property="goodsSalePrice" column="goods_sale_price"/>
        <result property="goodsWebSalePrice" column="goods_web_sale_price"/>
        <result property="goodsLife" column="goods_life"/>
        <result property="goodsStandard" column="goods_standard"/>
        <result property="goodsUnit" column="goods_unit"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryTwoId" column="category_two_id"/>
        <result property="companyId" column="company_id"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="baseColumns">
        a.id,a.goods_id, a.shop_unique,a.goods_barcode,a.goods_name,a.goods_alias,a.goods_in_price,a.goods_sale_price,a.goods_web_sale_price,a.goods_life,a.goods_standard,a.goods_unit,a.category_id,a.category_two_id,a.company_id,a.remark,a.create_user,a.create_time,a.modify_user,a.modify_time,a.del_flag
    </sql>
    <sql id="whereSql">
        <if test="params.id != null ">
            and a.id = #{params.id}
        </if>
        <if test="params.goodsId != null ">
            and a.goods_id = #{params.goodsId}
        </if>
        <if test="params.shopUnique != null ">
            and a.shop_unique = #{params.shopUnique}
        </if>
        <if test="params.goodsBarcode != null  and params.goodsBarcode.trim() != ''">
            and a.goods_barcode = #{params.goodsBarcode}
        </if>
        <if test="params.goodsName != null  and params.goodsName.trim() != ''">
            and a.goods_name = #{params.goodsName}
        </if>
        <if test="params.goodsAlias != null  and params.goodsAlias.trim() != ''">
            and a.goods_alias = #{params.goodsAlias}
        </if>
        <if test="params.goodsInPrice != null ">
            and a.goods_in_price = #{params.goodsInPrice}
        </if>
        <if test="params.goodsSalePrice != null ">
            and a.goods_sale_price = #{params.goodsSalePrice}
        </if>
        <if test="params.goodsWebSalePrice != null ">
            and a.goods_web_sale_price = #{params.goodsWebSalePrice}
        </if>
        <if test="params.goodsLife != null ">
            and a.goods_life = #{params.goodsLife}
        </if>
        <if test="params.goodsStandard != null  and params.goodsStandard.trim() != ''">
            and a.goods_standard = #{params.goodsStandard}
        </if>
        <if test="params.goodsUnit != null  and params.goodsUnit.trim() != ''">
            and a.goods_unit = #{params.goodsUnit}
        </if>
        <if test="params.categoryId != null ">
            and a.category_id = #{params.categoryId}
        </if>
        <if test="params.categoryTwoId != null ">
            and a.category_two_id = #{params.categoryTwoId}
        </if>
        <if test="params.companyId != null ">
            and a.company_id = #{params.companyId}
        </if>
        <if test="params.remark != null  and params.remark.trim() != ''">
            and a.remark = #{params.remark}
        </if>
        <if test="params.createUser != null ">
            and a.create_user = #{params.createUser}
        </if>
        <if test="params.createTime != null ">
            and a.create_time = #{params.createTime}
        </if>
        <if test="params.modifyUser != null ">
            and a.modify_user = #{params.modifyUser}
        </if>
        <if test="params.modifyTime != null ">
            and a.modify_time = #{params.modifyTime}
        </if>
        <if test="params.delFlag != null ">
            and a.del_flag = #{params.delFlag}
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusGoodsEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_goods a
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>