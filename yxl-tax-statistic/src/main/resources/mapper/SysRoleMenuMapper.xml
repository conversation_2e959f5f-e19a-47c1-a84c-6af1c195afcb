<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SysRoleMenuMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysRoleMenuEntity">
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="menu_id" property="menuId" jdbcType="BIGINT"/>
    </resultMap>


    <!--批量添加角色权限-->
    <insert id="insertBatch" parameterType="cc.buyhoo.tax.entity.SysRoleMenuEntity">
        insert into sys_role_menu (`role_id`, `menu_id`)
        values
        <foreach collection ="list" item="v" separator =",">
            (#{v.roleId}, #{v.menuId})
        </foreach>
    </insert>
</mapper>