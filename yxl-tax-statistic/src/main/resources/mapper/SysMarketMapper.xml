<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.SysMarketMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysMarketEntity">
        <result property="id" column="id"/>
        <result property="marketName" column="market_name"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="districtId" column="district_id"/>
        <result property="cityInfo" column="city_info"/>
        <result property="address" column="address"/>
        <result property="managementModel" column="management_model"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="personMobile" column="person_mobile"/>
        <result property="introduction" column="introduction"/>
        <result property="remark" column="remark"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="baseColumns">
        id,market_name,province_id,city_id,district_id,city_info,address,management_model,legal_person,person_mobile,introduction,remark,enable_status,create_user,create_time,modify_user,modify_time,del_flag
    </sql>
    <sql id="whereSql">
        <if test="id != null ">
            and a.id = #{id},
        </if>
        <if test="marketName != null  and marketName.trim() != ''">
            and a.market_name = #{marketName},
        </if>
        <if test="provinceId != null ">
            and a.province_id = #{provinceId},
        </if>
        <if test="cityId != null ">
            and a.city_id = #{cityId},
        </if>
        <if test="districtId != null ">
            and a.district_id = #{districtId},
        </if>
        <if test="address != null  and address.trim() != ''">
            and a.address = #{address},
        </if>
        <if test="managementModel != null ">
            and a.management_model = #{managementModel},
        </if>
        <if test="legalPerson != null  and legalPerson.trim() != ''">
            and a.legal_person = #{legalPerson},
        </if>
        <if test="personMobile != null  and personMobile.trim() != ''">
            and a.person_mobile = #{personMobile},
        </if>
        <if test="introduction != null  and introduction.trim() != ''">
            and a.introduction = #{introduction},
        </if>
        <if test="remark != null  and remark.trim() != ''">
            and a.remark = #{remark},
        </if>
        <if test="enableStatus != null ">
            and a.enable_status = #{enableStatus},
        </if>
        <if test="createUser != null ">
            and a.create_user = #{createUser},
        </if>
        <if test="createTime != null ">
            and a.create_time = #{createTime},
        </if>
        <if test="modifyUser != null ">
            and a.modify_user = #{modifyUser},
        </if>
        <if test="modifyTime != null ">
            and a.modify_time = #{modifyTime},
        </if>
        <if test="delFlag != null ">
            and a.del_flag = #{delFlag},
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.SysMarketEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from sys_market
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>