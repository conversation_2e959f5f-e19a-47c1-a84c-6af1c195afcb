<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusCompanyProfitRuleMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusCompanyProfitRuleEntity">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="ruleType" column="rule_type"/>
        <result property="amount" column="amount"/>
        <result property="firstProfitRate" column="first_profit_rate"/>
        <result property="secondProfitRate" column="second_profit_rate"/>
        <result property="monthProfitRate" column="month_profit_rate"/>
        <result property="profitRate" column="profit_rate"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>


    <sql id="baseColumns">
        a.id,a.company_id,a.rule_type,a.amount,a.first_profit_rate,a.second_profit_rate,a.month_profit_rate,a.profit_rate,a.enable_status,a.del_flag,a.create_user,a.create_time,a.modify_user,a.modify_time
    </sql>
    <sql id="whereSql">
        <if test="params.id != null">
            and a.id = #{params.id}
        </if>
        <if test="params.companyId != null">
            and a.company_id = #{params.companyId}
        </if>
        <if test="params.ruleType != null">
            and a.rule_type = #{params.ruleType}
        </if>
        <if test="params.amount != null">
            and a.amount = #{params.amount}
        </if>
        <if test="params.firstProfitRate != null">
            and a.first_profit_rate = #{params.firstProfitRate}
        </if>
        <if test="params.secondProfitRate != null">
            and a.second_profit_rate = #{params.secondProfitRate}
        </if>
        <if test="params.monthProfitRate != null">
            and a.month_profit_rate = #{params.monthProfitRate}
        </if>
        <if test="params.profitRate != null">
            and a.profit_rate = #{params.profitRate}
        </if>
        <if test="params.enableStatus != null">
            and a.enable_status = #{params.enableStatus}
        </if>
        <if test="params.delFlag != null">
            and a.del_flag = #{params.delFlag}
        </if>
        <if test="params.createUser != null">
            and a.create_user = #{params.createUser}
        </if>
        <if test="params.createTime != null">
            and a.create_time = #{params.createTime}
        </if>
        <if test="params.modifyUser != null">
            and a.modify_user = #{params.modifyUser}
        </if>
        <if test="params.modifyTime != null">
            and a.modify_time = #{params.modifyTime}
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusCompanyProfitRuleEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_company_profit_rule a
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>