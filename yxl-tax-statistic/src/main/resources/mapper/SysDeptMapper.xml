<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.SysDeptMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysDeptEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="leader" property="leader" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `parent_id`, `dept_name`, `sort`, `leader`, 
		`email`, `mobile`, `del_flag`, `create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="parentId != null">
            and parent_id=#{parentId}
		</if>
        <if test="deptName != null  and deptName != ''">
            and dept_name=#{deptName}
		</if>
        <if test="sort != null">
            and sort=#{sort}
		</if>
        <if test="leader != null  and leader != ''">
            and leader=#{leader}
		</if>
        <if test="email != null  and email != ''">
            and email=#{email}
		</if>
        <if test="mobile != null  and mobile != ''">
            and mobile=#{mobile}
		</if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>