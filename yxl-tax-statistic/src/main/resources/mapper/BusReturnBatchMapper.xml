<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnBatchMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnBatchEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="batch_no" property="batchNo" jdbcType="VARCHAR"/>
        <result column="total_money" property="totalMoney" jdbcType="DECIMAL"/>
        <result column="online_money" property="onlineMoney" jdbcType="DECIMAL"/>
        <result column="cash_money" property="cashMoney" jdbcType="DECIMAL"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="batch_date" property="batchDate" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `shop_unique`, `batch_no`, `total_money`, `online_money`, 
		`cash_money`, `order_id`, `batch_date`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="batchNo != null  and batchNo != ''">
            and batch_no=#{batchNo}
		</if>
        <if test="totalMoney != null">
            and total_money=#{totalMoney}
		</if>
        <if test="onlineMoney != null">
            and online_money=#{onlineMoney}
		</if>
        <if test="cashMoney != null">
            and cash_money=#{cashMoney}
		</if>
        <if test="orderId != null">
            and order_id=#{orderId}
		</if>
        <if test="batchDate != null  and batchDate != ''">
            and batch_date=#{batchDate}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>