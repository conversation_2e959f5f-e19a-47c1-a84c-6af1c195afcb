<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.BusShopBillDetailMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopBillDetailEntity">
        <result property="id" column="id"/>
        <result property="billId" column="bill_id"/>
        <result property="shopUnique" column="shop_unique"/>
        <result property="billNo" column="bill_no"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankCard" column="bank_card"/>
        <result property="settledAmount" column="settled_amount"/>
        <result property="settledType" column="settled_type"/>
        <result property="settledStatus" column="settled_status"/>
        <result property="failReason" column="fail_reason"/>
        <result property="purchaseNo" column="purchase_no"/>
        <result property="remark" column="remark"/>
        <result property="bthNbr" column="bth_nbr"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="billType" column="bill_type"/>
    </resultMap>


    <sql id="baseColumns">
        a.id,a.bill_id,a.shop_unique,a.bill_no,a.bank_name,a.bank_card,a.settled_amount,a.settled_type,a.settled_status,a.fail_reason,a.purchase_no,a.company_bank_name,
            a.company_bank_card,a.remark,a.bth_nbr,a.del_flag,a.create_user,a.create_time,a.modify_user,a.modify_time,a.bill_type
    </sql>
    <sql id="whereSql">
        <if test="params.id != null ">
            and a.id = #{params.id}
        </if>
        <if test="params.billId != null ">
            and a.bill_id = #{params.billId}
        </if>
        <if test="params.shopUnique != null ">
            and a.shop_unique = #{params.shopUnique}
        </if>
        <if test="params.billNo != null  and params.billNo.trim() != ''">
            and a.bill_no = #{params.billNo}
        </if>
        <if test="params.bankName != null  and params.bankName.trim() != ''">
            and a.bank_name = #{params.bankName}
        </if>
        <if test="params.bankCard != null  and params.bankCard.trim() != ''">
            and a.bank_card = #{params.bankCard}
        </if>
        <if test="params.settledAmount != null ">
            and a.settled_amount = #{params.settledAmount}
        </if>
        <if test="params.settledType != null  and params.settledType.trim() != ''">
            and a.settled_type = #{params.settledType}
        </if>
        <if test="params.settledStatus != null  and params.settledStatus.trim() != ''">
            and a.settled_status = #{params.settledStatus}
        </if>
        <if test="params.failReason != null  and params.failReason.trim() != ''">
            and a.fail_reason = #{params.failReason}
        </if>
        <if test="params.purchaseNo != null  and params.purchaseNo.trim() != ''">
            and a.purchase_no = #{params.purchaseNo}
        </if>
        <if test="params.remark != null  and params.remark.trim() != ''">
            and a.remark = #{params.remark}
        </if>
        <if test="params.bthNbr != null  and params.bthNbr.trim() != ''">
            and a.btn_nbr = #{params.bthNbr}
        </if>
        <if test="params.delFlag != null ">
            and a.del_flag = #{params.delFlag}
        </if>
        <if test="params.createUser != null ">
            and a.create_user = #{params.createUser}
        </if>
        <if test="params.modifyUser != null ">
            and a.modify_user = #{params.modifyUser}
        </if>
        <if test="params.billType != null">
            AND a.bill_type = #{params.billType}
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.BusShopBillDetailEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from bus_shop_bill_detail a
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
    <select id="selectPageList" resultType="cc.buyhoo.tax.result.busShopBill.BusShopBillDetailDto">
        select
            <include refid="baseColumns"/>
            ,s.shop_name
            ,c.company_name
        from bus_shop_bill_detail a
        left join bus_shop s on a.shop_unique = s.shop_unique
        left join sys_company c on s.company_id = c.id
        <where>
            <if test="params.billId != null ">
                and a.bill_id = #{params.billId}
            </if>
            <if test="params.shopUnique != null ">
                and a.shop_unique = #{params.shopUnique}
            </if>
            <if test="params.settledType != null  and params.settledType.trim() != ''">
                and a.settled_type = #{params.settledType}
            </if>
            <if test="params.settledStatus != null  and params.settledStatus.trim() != ''">
                and a.settled_status = #{params.settledStatus}
            </if>
            <if test="params.createTime != null and params.createTime.length == 2">
                and a.create_time between #{params.createTime[0]} and #{params.createTime[1]}
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>