<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusSaleListDisassembleMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusSaleListDisassembleEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="shop_name" property="shopName" jdbcType="VARCHAR"/>
        <result column="sale_type" property="saleType" jdbcType="INTEGER"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="cus_unique" property="cusUnique" jdbcType="VARCHAR"/>
        <result column="sale_list_name" property="saleListName" jdbcType="VARCHAR"/>
        <result column="sale_list_total" property="saleListTotal" jdbcType="DECIMAL"/>
        <result column="sale_list_actually_received" property="saleListActuallyReceived" jdbcType="DECIMAL"/>
        <result column="sale_list_service_fee" property="saleListServiceFee" jdbcType="DECIMAL"/>
        <result column="trade_no" property="tradeNo" jdbcType="VARCHAR"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="sale_list_datetime" property="saleListDatetime" jdbcType="TIMESTAMP"/>
        <result column="sale_list_state" property="saleListState" jdbcType="INTEGER"/>
        <result column="sale_list_payment" property="saleListPayment" jdbcType="INTEGER"/>
        <result column="settled_status" property="settledStatus" jdbcType="INTEGER"/>
        <result column="profit_status" property="profitStatus" jdbcType="INTEGER"/>
        <result column="profit_total" property="profitTotal" jdbcType="DECIMAL"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="order_type" property="orderType" jdbcType="VARCHAR"/>
        <result column="parent_list_unique" property="parentListUnique" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `shop_unique`, `shop_name`, `sale_type`, `sale_list_unique`, `cus_unique`,
		`sale_list_name`, `sale_list_total`, `sale_list_actually_received`, `sale_list_service_fee`, `trade_no`, `pay_time`,
        `sale_list_datetime`, `sale_list_state`, `sale_list_payment`, `settled_status`, `profit_status`, `profit_total`, `contract_no`, `order_type`, `parent_list_unique`,  `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="shopName != null  and shopName != ''">
            and shop_name=#{shopName}
		</if>
        <if test="saleType != null">
            and sale_type=#{saleType}
		</if>
        <if test="saleListUnique != null">
            and sale_list_unique=#{saleListUnique}
		</if>
		<if test="cusUnique != null and cusUnique != ''">
            and cus_unique=#{cusUnique}
		</if>
        <if test="saleListName != null  and saleListName != ''">
            and sale_list_name=#{saleListName}
		</if>
        <if test="saleListTotal != null">
            and sale_list_total=#{saleListTotal}
		</if>
        <if test="saleListActuallyReceived != null">
            and sale_list_actually_received=#{saleListActuallyReceived}
		</if>
        <if test="saleListServiceFee != null">
            and sale_list_service_fee=#{saleListServiceFee}
		</if>
        <if test="tradeNo != null  and tradeNo != ''">
            and trade_no=#{tradeNo}
		</if>
        <if test="payTime != null  and payTime != ''">
            and pay_time=#{payTime}
		</if>
        <if test="saleListDatetime != null  and saleListDatetime != ''">
            and sale_list_datetime=#{saleListDatetime}
		</if>
        <if test="saleListState != null">
            and sale_list_state=#{saleListState}
		</if>
        <if test="saleListPayment != null">
            and sale_list_payment=#{saleListPayment}
		</if>
        <if test="settledStatus != null">
            and settled_status=#{settledStatus}
		</if>
        <if test="profitStatus != null">
            and profit_status=#{profitStatus}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>
</mapper>