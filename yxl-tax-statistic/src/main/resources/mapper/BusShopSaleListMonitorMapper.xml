<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusShopSaleListMonitorMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopSaleListMonitorEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="buyhoo_sale_count" property="buyhooSaleCount" jdbcType="BIGINT"/>
        <result column="canyin_sale_count" property="canyinSaleCount" jdbcType="BIGINT"/>
        <result column="tax_sale_count" property="taxSaleCount" jdbcType="BIGINT"/>
        <result column="buyhoo_return_count" property="buyhooReturnCount" jdbcType="BIGINT"/>
        <result column="canyin_count" property="canyinReturnCount" jdbcType="BIGINT"/>
        <result column="tax_count" property="taxReturnCount" jdbcType="BIGINT"/>
        <result column="buyhoo_amount" property="buyhooAmount" jdbcType="DECIMAL"/>
        <result column="canyin_amount" property="canyinAmount" jdbcType="DECIMAL"/>
        <result column="tax_amount" property="taxAmount" jdbcType="DECIMAL"/>
        <result column="settled_status" property="settledStatus" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `shop_unique`, `stat_date`, `buyhoo_count`, `canyin_count`, 
		`tax_count`, `buyhoo_amount`, `canyin_amount`, `tax_amount`, `settled_status`, `remark`, 
		`bill_no`, `del_flag`, `create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="statDate != null  and statDate != ''">
            and stat_date=#{statDate}
		</if>
        <if test="buyhooCount != null">
            and buyhoo_count=#{buyhooCount}
		</if>
        <if test="canyinCount != null">
            and canyin_count=#{canyinCount}
		</if>
        <if test="taxCount != null">
            and tax_count=#{taxCount}
		</if>
        <if test="buyhooAmount != null">
            and buyhoo_amount=#{buyhooAmount}
		</if>
        <if test="canyinAmount != null">
            and canyin_amount=#{canyinAmount}
		</if>
        <if test="taxAmount != null">
            and tax_amount=#{taxAmount}
		</if>
        <if test="settledStatus != null">
            and settled_status=#{settledStatus}
		</if>
        <if test="remark != null  and remark != ''">
            and remark=#{remark}
		</if>
        <if test="billNo != null  and billNo != ''">
            and bill_no=#{billNo}
		</if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.entity.BusShopSaleListMonitorEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from bus_shop_sale_list_monitor
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>