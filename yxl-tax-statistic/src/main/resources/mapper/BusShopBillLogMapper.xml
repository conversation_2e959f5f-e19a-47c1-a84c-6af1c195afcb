<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusShopBillLogMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopBillLogEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="pay_type" property="payType" jdbcType="INTEGER"/>
        <result column="bus_id" property="busId" jdbcType="BIGINT"/>
        <result column="change_before" property="changeBefore" jdbcType="DECIMAL"/>
        <result column="change_money" property="changeMoney" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `shop_unique`, `company_id`, `pay_type`, `bus_id`, `change_before`, 
		`change_money`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="payType != null">
            and pay_type=#{payType}
		</if>
        <if test="busId != null">
            and bus_id=#{busId}
		</if>
        <if test="changeBefore != null">
            and change_before=#{changeBefore}
		</if>
        <if test="changeMoney != null">
            and change_money=#{changeMoney}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>