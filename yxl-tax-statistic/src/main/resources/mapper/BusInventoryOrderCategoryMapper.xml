<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusInventoryOrderCategoryMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusInventoryOrderCategoryEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="category_two_id" property="categoryTwoId" jdbcType="BIGINT"/>
        <result column="total_goods_count" property="totalGoodsCount" jdbcType="DECIMAL"/>
        <result column="total_money" property="totalMoney" jdbcType="DECIMAL"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `order_id`, `category_id`, `category_two_id`, `total_goods_count`, `total_money`,
		`company_id`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
        </if>
        <if test="orderId != null">
            and order_id=#{orderId}
        </if>
        <if test="categoryId != null">
            and category_id=#{categoryId}
        </if>
        <if test="categoryTwoId != null">
            and category_two_id=#{categoryTwoId}
        </if>
        <if test="totalGoodsCount != null">
            and total_goods_count=#{totalGoodsCount}
        </if>
        <if test="totalMoney != null">
            and total_money=#{totalMoney}
        </if>
        <if test="companyId != null">
            and company_id=#{companyId}
        </if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
        </if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
        </if>
    </sql>
    <select id="selectPageList" resultType="cc.buyhoo.tax.result.inventoryOrderCategory.InventoryOrderCategoryDto">
        select
            a.id, a.category_id, a.category_two_id, a.total_goods_count,
            c.category_name, c2.category_name as category_two_name, a.total_money
            from bus_inventory_order_category a
            left join bus_goods_category c on a.category_id = c.id
            left join bus_goods_category c2 on a.category_two_id = c2.id
            where a.company_id = #{params.companyId}
            <if test="params.orderId != null">
                and a.order_id = #{params.orderId}
            </if>
            order by a.id asc
    </select>

</mapper>