<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.StbusDataLevelMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.StbusDataLevelEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="level" property="level" jdbcType="TINYINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_by" property="modifyBy" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `level`, `name`, `parent_id`, `del_flag`, `create_by`, 
		`create_time`, `modify_by`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="level != null">
            and level=#{level}
		</if>
        <if test="name != null  and name != ''">
            and name=#{name}
		</if>
        <if test="parentId != null">
            and parent_id=#{parentId}
		</if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
		</if>
        <if test="createBy != null">
            and create_by=#{createBy}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyBy != null">
            and modify_by=#{modifyBy}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.entity.StbusDataLevelEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from stbus_data_level
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>