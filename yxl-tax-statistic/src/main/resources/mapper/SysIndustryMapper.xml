<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.dao.SysIndustryMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.SysIndustryEntity">
        <result property="id" column="id"/>
        <result property="industryName" column="industry_name"/>
        <result property="remark" column="remark"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="baseColumns">
        id,industry_name,remark,enable_status,create_user,create_time,modify_user,modify_time,del_flag
    </sql>
    <sql id="whereSql">
        <if test="id != null ">
            and a.id = #{id},
        </if>
        <if test="industryName != null  and industryName.trim() != ''">
            and a.industry_name = #{industryName},
        </if>
        <if test="remark != null  and remark.trim() != ''">
            and a.remark = #{remark},
        </if>
        <if test="enableStatus != null ">
            and a.enable_status = #{enableStatus},
        </if>
        <if test="createUser != null ">
            and a.create_user = #{createUser},
        </if>
        <if test="createTime != null ">
            and a.create_time = #{createTime},
        </if>
        <if test="modifyUser != null ">
            and a.modify_user = #{modifyUser},
        </if>
        <if test="modifyTime != null ">
            and a.modify_time = #{modifyTime},
        </if>
        <if test="delFlag != null ">
            and a.del_flag = #{delFlag},
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.entity.SysIndustryEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from sys_industry
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>