<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusSaleListPayDetailMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusSaleListPayDetailEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="pay_method" property="payMethod" jdbcType="INTEGER"/>
        <result column="pay_money" property="payMoney" jdbcType="DECIMAL"/>
        <result column="server_type" property="serverType" jdbcType="SMALLINT"/>
        <result column="mch_id" property="mchId" jdbcType="VARCHAR"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `sale_list_unique`, `pay_method`, `pay_money`, `server_type`, `mch_id`,
		`pay_time`, `create_time`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="saleListUnique != null">
            and sale_list_unique=#{saleListUnique}
		</if>
        <if test="payMethod != null">
            and pay_method=#{payMethod}
		</if>
        <if test="payMoney != null">
            and pay_money=#{payMoney}
		</if>
        <if test="serverType != null">
            and server_type=#{serverType}
		</if>
        <if test="mchId != null  and mchId != ''">
            and mch_id=#{mchId}
		</if>
        <if test="payTime != null  and payTime != ''">
            and pay_time=#{payTime}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>