<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusReturnListMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusReturnListEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="shop_name" property="shopName" jdbcType="VARCHAR"/>
        <result column="ret_list_datetime" property="retListDatetime" jdbcType="TIMESTAMP"/>
        <result column="ret_list_state" property="retListState" jdbcType="VARCHAR"/>
        <result column="ret_list_handlestate" property="retListHandlestate" jdbcType="VARCHAR"/>
        <result column="ret_money_type" property="retMoneyType" jdbcType="SMALLINT"/>
        <result column="ret_list_total_money" property="retListTotalMoney" jdbcType="DECIMAL"/>
        <result column="ret_back_datetime" property="retBackDatetime" jdbcType="TIMESTAMP"/>
        <result column="ret_list_unique" property="retListUnique" jdbcType="VARCHAR"/>
        <result column="ret_list_reason" property="retListReason" jdbcType="VARCHAR"/>
        <result column="ret_list_delfee" property="retListDelfee" jdbcType="DECIMAL"/>
        <result column="return_sale_list_service_fee" property="returnSaleListServiceFee" jdbcType="DECIMAL"/>
        <result column="settled_status" property="settledStatus" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `sale_list_unique`, `shop_unique`, `ret_list_datetime`, `ret_list_state`,
		`ret_list_handlestate`, `ret_money_type`, `ret_list_total_money`, `ret_back_datetime`, `ret_list_unique`, `ret_list_reason`,
		`ret_list_delfee`, `create_time`, `modify_time`, `shop_name`, `return_sale_list_service_fee`, `settled_status`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="saleListUnique != null  and saleListUnique != ''">
            and sale_list_unique=#{saleListUnique}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="shopName != null and shopName != ''">
            and shop_name=#{shopName}
		</if>
        <if test="retListDatetime != null  and retListDatetime != ''">
            and ret_list_datetime=#{retListDatetime}
		</if>
        <if test="retListState != null  and retListState != ''">
            and ret_list_state=#{retListState}
		</if>
        <if test="retListHandlestate != null  and retListHandlestate != ''">
            and ret_list_handlestate=#{retListHandlestate}
		</if>
        <if test="retMoneyType != null">
            and ret_money_type=#{retMoneyType}
		</if>
        <if test="retListTotalMoney != null">
            and ret_list_total_money=#{retListTotalMoney}
		</if>
        <if test="retBackDatetime != null  and retBackDatetime != ''">
            and ret_back_datetime=#{retBackDatetime}
		</if>
        <if test="retListUnique != null  and retListUnique != ''">
            and ret_list_unique=#{retListUnique}
		</if>
        <if test="retListReason != null  and retListReason != ''">
            and ret_list_reason=#{retListReason}
		</if>
        <if test="retListDelfee != null">
            and ret_list_delfee=#{retListDelfee}
		</if>
        <if test="returnSaleListServiceFee != null">
            and return_sale_list_service_fee=#{returnSaleListServiceFee}
		</if>
		<if test="settledStatus != null">
            and settled_status=#{settledStatus}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <select id="selectRetOnlineMoney" resultType="java.util.Map">
        select
            rl.shop_unique,
            sum(rl.ret_list_total_money) as ret_total_money,
            sum(rl.ret_list_delfee) as ret_delfee,
            sum(rl.return_sale_list_service_fee) as ret_service_fee
        from return_list rl
        inner join return_list_paydetail rlp on rl.ret_list_unique = rlp.ret_list_unique
        where rl.company_id = #{companyId}
            and rl.settled_status = 0
            and rlp.service_type != 1
            and rl.ret_list_datetime between #{startTime} and #{endTime}
        group by rl.shop_unique
    </select>

    <update id="updateSettledStatus">
        update
            return_list
        set settled_status = 1
        where company_id = #{companyId}
        and settled_status = 0
        and ret_list_datetime between #{startTime} and #{endTime}
    </update>
    <select id="selectTaxCount" resultType="java.util.Map">
        select
            count(rl.ret_list_unique) as count
        from
            return_list rl
            inner join return_list_paydetail rlp on rl.ret_list_unique = rlp.ret_list_unique
        where rl.shop_unique = #{shopUnique}
          and rlp.service_type != 1
          and rl.ret_list_datetime between #{startTime} and #{endTime}
    </select>
    <select id="selectTaxCountGroup" resultType="java.util.Map">
        select
            rl.shop_unique as shopUnique,
            count(rl.ret_list_unique) as count,
            sum(rl.ret_list_total_money) as ret_total_money,
            sum(rl.ret_list_delfee) as ret_delfee,
            sum(rl.return_sale_list_service_fee) as ret_service_fee
        from
            return_list rl
            inner join return_list_paydetail rlp on rl.ret_list_unique = rlp.ret_list_unique
            inner join sys_company sc on rl.company_id = sc.id
        where rlp.service_type != 1
            and rl.settled_status = 1
          and rl.ret_list_datetime between #{startTime} and #{endTime}
          and sc.statistics_status = 1
        group by rl.shop_unique
    </select>
    <select id="returnListSyncInfo" resultMap="returnListSyncInfo">
        select
            rl.shop_unique,
            bs.shop_name,
            rl.sale_list_unique,
            rl.ret_list_total_money
        from
            return_list rl
                left join
            bus_shop bs on rl.shop_unique = bs.shop_unique
                inner join sys_company sc on rl.company_id = sc.id
        where rl.ret_list_datetime between #{startTime} and #{endTime}
          and sc.statistics_status = 1
    </select>
    <resultMap id="returnListSyncInfo" type="cc.buyhoo.tax.result.returnList.ReturnListSyncDto">
        <result column="shop_unique" property="shopUnique"/>
        <result column="shop_name" property="shopName" />
        <result column="sale_list_unique" property="saleListUnique" />
        <result column="ret_list_total_money" property="retListTotalMoney" />
    </resultMap>
</mapper>