<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusShopInvoiceSettingMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopInvoiceSettingEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="company_tax_no" property="companyTaxNo" jdbcType="VARCHAR"/>
        <result column="company_address" property="companyAddress" jdbcType="VARCHAR"/>
        <result column="company_phone" property="companyPhone" jdbcType="VARCHAR"/>
        <result column="invoice_man" property="invoiceMan" jdbcType="VARCHAR"/>
        <result column="checker" property="checker" jdbcType="VARCHAR"/>
        <result column="payee" property="payee" jdbcType="VARCHAR"/>
        <result column="invoice_bank_name" property="invoiceBankName" jdbcType="VARCHAR"/>
        <result column="invoice_bank_card" property="invoiceBankCard" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `company_name`, `company_tax_no`, `company_address`, `company_phone`, 
		`invoice_man`, `checker`, `payee`, `invoice_bank_name`, `invoice_bank_card`, `create_user`, 
		`create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="companyName != null  and companyName != ''">
            and company_name=#{companyName}
		</if>
        <if test="companyTaxNo != null  and companyTaxNo != ''">
            and company_tax_no=#{companyTaxNo}
		</if>
        <if test="companyAddress != null  and companyAddress != ''">
            and company_address=#{companyAddress}
		</if>
        <if test="companyPhone != null  and companyPhone != ''">
            and company_phone=#{companyPhone}
		</if>
        <if test="invoiceMan != null  and invoiceMan != ''">
            and invoice_man=#{invoiceMan}
		</if>
        <if test="checker != null  and checker != ''">
            and checker=#{checker}
		</if>
        <if test="payee != null  and payee != ''">
            and payee=#{payee}
		</if>
        <if test="invoiceBankName != null  and invoiceBankName != ''">
            and invoice_bank_name=#{invoiceBankName}
		</if>
        <if test="invoiceBankCard != null  and invoiceBankCard != ''">
            and invoice_bank_card=#{invoiceBankCard}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

</mapper>