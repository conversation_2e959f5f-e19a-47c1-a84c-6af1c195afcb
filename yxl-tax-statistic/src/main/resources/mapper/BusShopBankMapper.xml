<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.dao.BusShopBankMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.entity.BusShopBankEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="enable_status" property="enableStatus" jdbcType="INTEGER"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
        <result column="bank_code" property="bankCode" jdbcType="VARCHAR"/>
        <result column="bank_city" property="bankCity" jdbcType="VARCHAR"/>
        <result column="bank_card" property="bankCard" jdbcType="VARCHAR"/>
        <result column="bank_id" property="bankId" jdbcType="BIGINT"/>
        <result column="legal_person" property="legalPerson" jdbcType="VARCHAR"/>
        <result column="legal_phone" property="legalPhone" jdbcType="VARCHAR"/>
        <result column="rcv_cust_type" property="rcvCustType" jdbcType="INTEGER"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `shop_unique`, `enable_status`, `company_id`, `bank_name`, `bank_city`, `bank_code`,
		`bank_card`, `bank_id`, `legal_person`, `legal_phone`, `rcv_cust_type`, `del_flag`, 
		`create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="enableStatus != null">
            and enable_status=#{enableStatus}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="bankName != null  and bankName != ''">
            and bank_name=#{bankName}
		</if>
        <if test="bankCode!= null  and bankCode!= ''">
            and bank_code=#{bankCode}
        </if>
        <if test="bankCity != null  and bankCity != ''">
            and bank_city=#{bankCity}
		</if>
        <if test="bankCard != null  and bankCard != ''">
            and bank_card=#{bankCard}
		</if>
        <if test="bankId != null">
            and bank_id=#{bankId}
		</if>
        <if test="legalPerson != null  and legalPerson != ''">
            and legal_person=#{legalPerson}
		</if>
        <if test="legalPhone != null  and legalPhone != ''">
            and legal_phone=#{legalPhone}
		</if>
        <if test="rcvCustType != null">
            and rcv_cust_type=#{rcvCustType}
		</if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.entity.BusShopBankEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from bus_shop_bank
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>