server:
  port: 14000
  servlet:
    context-path: /taxStatisticBack
spring:
  application:
    name: yxl-tax-statistic
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: @nacos.server-addr@
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        # 注册组
        group: @nacos.tax.group@
        namespace: @nacos.namespace@
      config:
        # 配置组
        group: @nacos.tax.group@
        namespace: @nacos.namespace@
  config:
    import:
      - optional:nacos:application.common.yaml
      - optional:nacos:dubbo-config.yaml
      - optional:nacos:${spring.application.name}.yaml
