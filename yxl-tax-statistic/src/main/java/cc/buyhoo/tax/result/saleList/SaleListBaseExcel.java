package cc.buyhoo.tax.result.saleList;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class SaleListBaseExcel implements Serializable {
    private static final long serialVersionUID = 4148234659399145807L;

    @ColumnWidth(50)
    @ExcelProperty("供货商名称")
    private String shopName;

    @ColumnWidth(30)
    @ExcelProperty("订单编号")
    private String saleListUnique;

    @ColumnWidth(20)
    @ExcelProperty("收款金额")
    private String saleListActuallyReceived;

    @ColumnWidth(20)
    @ExcelProperty("服务费")
    private BigDecimal saleListServiceFee;

    @ColumnWidth(30)
    @ExcelProperty("创建时间")
    private String createTime;

    @ColumnWidth(30)
    @ExcelProperty("支付时间")
    private BigDecimal payTime;

    @ColumnWidth(20)
    @ExcelProperty("交易手续费")
    private BigDecimal payFee;

    @ColumnWidth(20)
    @ExcelProperty("收款状态")
    private String saleListState;

    @ColumnWidth(30)
    @ExcelProperty("收款方式")
    private String saleListPayment;

    @ColumnWidth(10)
    @ExcelProperty("结算状态")
    private String settledStatus;

    @ColumnWidth(30)
    @ExcelProperty("成本金额")
    private BigDecimal profitTotal;
}
