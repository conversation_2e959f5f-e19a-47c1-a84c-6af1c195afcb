package cc.buyhoo.tax.result.sysMarket;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 市场列表返回值
 * @ClassName BusMarketDto
 * <AUTHOR>
 * @Date 2024/8/23 16:09
 **/
@Data
public class SysMarketDto implements Serializable {
    private static final long serialVersionUID = -4714247011801511540L;
    /**
     * 市场ID
     */
    private Long id;

    /**
     * 市场名称
     */
    private String marketName;

    /**
     * 所属省ID
     */
    private Long provinceId;

    /**
     * 所属市ID
     */
    private Long cityId;

    /**
     * 所属区ID
     */
    private Long districtId;

    /**
     * 所在地区ID数组
     */
    private Long[] cityInfoIds;
    /**
     * 所属地区
     */
    private String cityInfo;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经营模式：0-企业独营，1-企业联营
     */
    private Integer managementModel;

    /**
     * 负责人
     */
    private String legalPerson;

    /**
     * 负责人电话
     */
    private String personMobile;

    /**
     * 企业介绍
     */
    private String introduction;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
