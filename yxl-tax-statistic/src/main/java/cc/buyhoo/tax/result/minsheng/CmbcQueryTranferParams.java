package cc.buyhoo.tax.result.minsheng;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @ClassName CmbcTranferParams
 * @Description 民生银行单笔转账返回结果
 * <AUTHOR>
 * @Date 2025/4/2 19:27
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CmbcQueryTranferParams extends CmbcTranferBaseParams implements Serializable {
    private static final long serialVersionUID = -4804782024440588340L;


    /**
     * 客户端技术流水号，代表每一次请求的唯一标
     * 识，不可重复
     */
    private String trnId;
    /**
     *   客户端 cookie，响应时原值返回
     */
    private String cltcookie;

    /**
     * 银行渠道流水号，网银互联汇路不返回
     */
    private String svrId;
    /**
     * 客户业务流水号，代表每一次业务请求的唯一
     * 标识，同一业务请求不可重复
     */
    private String insId;
}