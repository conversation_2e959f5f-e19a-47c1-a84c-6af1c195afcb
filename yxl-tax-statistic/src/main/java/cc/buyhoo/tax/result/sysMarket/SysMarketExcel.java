package cc.buyhoo.tax.result.sysMarket;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 市场列表返回值
 * @ClassName BusMarketDto
 * <AUTHOR>
 * @Date 2024/8/23 16:09
 **/
@Data
public class SysMarketExcel implements Serializable {
    private static final long serialVersionUID = -4714247011801511540L;

    /**
     * 市场名称
     */
    @ColumnWidth(20)
    @ExcelProperty("市场名称")
    private String marketName;

    /**
     * 所属地区
     */
    @ColumnWidth(30)
    @ExcelProperty("所属地区")
    private String cityInfo;

    /**
     * 经营模式：0-企业独营，1-企业联营
     */
    @ColumnWidth(20)
    @ExcelProperty("经营模式")
    private String managementModelDesc;

    /**
     * 负责人
     */
    @ColumnWidth(15)
    @ExcelProperty("负责人")
    private String legalPerson;

    /**
     * 负责人电话
     */
    @ColumnWidth(20)
    @ExcelProperty("负责人电话")
    private String personMobile;

    /**
     * 有效状态:1正常0无效
     */
    @ColumnWidth(12)
    @ExcelProperty("有效状态")
    private String enableStatusDesc;

    /**
     * 创建时间
     */
    @ColumnWidth(20)
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
