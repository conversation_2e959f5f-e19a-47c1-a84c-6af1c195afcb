package cc.buyhoo.tax.result.external;

import lombok.Data;

import java.util.List;

/**
 * 开票信息查询结果
 */
@Data
public class GetInvoiceMsgResult {
    //开票企业ID，需要存在，开票成功后返回
    private Long companyId;
    //开票企业名称
    private String companyName;
    //开票企业税号
    private String companyTaxNo;
    //开票企业地址
    private String companyAddress;
    //开票企业电话
    private String companyPhone;
    //开票企业开户行
    private String invoiceBankName;
    //开票企业账户
    private String invoiceBankCard;
    //开票人
    private String invoiceMan;
    //复核人
    private String checker;
    //收款人
    private String payee;
    //开票期数
    private Integer periodsLevel;
    //店铺可用开票内容
    private List<InvoiceGoodsMsg> goodsMsgList;

}
