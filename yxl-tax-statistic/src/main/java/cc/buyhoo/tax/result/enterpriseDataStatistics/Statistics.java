package cc.buyhoo.tax.result.enterpriseDataStatistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName Statistics
 * @Description 企业数据统计实体类
 * <AUTHOR>
 * @Date 2025/6/16 下午2:14
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Statistics implements Serializable {

    /**
     * 销售额
     */
    private BigDecimal salesAmount;

    /**
     * 今日销售额
     */
    private BigDecimal todaySalesAmount;

    /**
     * 订单数
     */
    private Integer orderCount;

    /**
     * 今日订单数
     */
    private Integer todayOrderCount;

    /**
     * 开票额
     */
    private BigDecimal invoiceAmount;

    /**
     * 今日开票额
     */
    private BigDecimal todayInvoiceAmount;

    /**
     * 开票单数
     */
    private Integer invoiceCount;

    /**
     * 今日开票单数
     */
    private Integer todayInvoiceCount;

    /**
     * 商家数
     */
    private Integer merchantCount;

    /**
     * 今日新增商家数
     */
    private Integer todayNewMerchantCount;

    /**
     * 一般纳税人商家数
     */
    private Integer generalTaxpayerCount;

    /**
     * 今日新增一般纳税人
     */
    private Integer todayNewGeneralTaxpayerCount;

    /**
     * 小规模商家数
     */
    private Integer smallScaleTaxpayerCount;

    /**
     * 今日新增小规模纳税人
     */
    private Integer todayNewSmallScaleTaxpayerCount;

    /**
     * 个体工商户
     */
    private Integer individualBusinessCount;

    /**
     * 今日新增个体工商户
     */
    private Integer todayNewIndividualBusinessCount;

    /**
     * 街道编码
     */
    // jiedao
    private String townCode;
}

