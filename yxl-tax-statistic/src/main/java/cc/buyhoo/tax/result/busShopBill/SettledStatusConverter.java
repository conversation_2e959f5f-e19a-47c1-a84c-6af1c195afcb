package cc.buyhoo.tax.result.busShopBill;

import cc.buyhoo.tax.enums.SettledStatusEnum;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Description
 * @ClassName SettledStatusConverter
 * <AUTHOR>
 * @Date 2023/7/28 9:57
 **/
public class SettledStatusConverter implements Converter<String> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (StrUtil.isNotBlank(cellData.getStringValue()) && StrUtil.isNotBlank(SettledStatusEnum.getValueByLabel(cellData.getStringValue()))) {
            return SettledStatusEnum.getValueByLabel(cellData.getStringValue());
        }
        return SettledStatusEnum.UNSETTLED.getValue();
    }


    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (StrUtil.isNotBlank(value) && StrUtil.isNotBlank(SettledStatusEnum.getLabelByValue(value))) {
            return new WriteCellData<>(SettledStatusEnum.getLabelByValue(value));
        }
        return new WriteCellData<>(SettledStatusEnum.UNSETTLED.getLabel());
    }
}
