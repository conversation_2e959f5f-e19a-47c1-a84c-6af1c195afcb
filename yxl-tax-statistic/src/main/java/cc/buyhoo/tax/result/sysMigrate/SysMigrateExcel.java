package cc.buyhoo.tax.result.sysMigrate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 迁入迁出列表返回值
 *
 * @ClassName SysMigrateDto
 * <AUTHOR>
 * @Date 2024/8/29 14:24
 **/
@Data
public class SysMigrateExcel implements Serializable {
    private static final long serialVersionUID = -8201989078309738876L;

    /**
     * 商户名称
     */
    @ColumnWidth(20)
    @ExcelProperty("商户名称")
    private String shopName;

    /**
     * 迁入企业名称
     */
    @ColumnWidth(20)
    @ExcelProperty("迁入企业名称")
    private String inCompanyName;
    /**
     * 迁出企业名称
     */
    @ColumnWidth(20)
    @ExcelProperty("迁出企业名称")
    private String outCompanyName;
    /**
     * 商户编码
     *//*
    @ColumnWidth(20)
    @ExcelProperty("商户编码")
    private Long shopUnique;*/
    /**
     * 市场名称
     */
    @ColumnWidth(30)
    @ExcelProperty("市场名称")
    private String marketName;
    /**
     * 预计纳税目标金额(元)
     */
    @ColumnWidth(20)
    @ExcelProperty("企业经营目标（万元）")
    private BigDecimal targetAmount;

    /**
     * 纳统类型,1-按月,2-按季度,3-按年
     */
    @ColumnWidth(10)
    @ExcelProperty("经营周期")
    private String taxTypeDesc;
    /**
     * 审核状态：0-待审核，1-通过，2-不通过
     */
    @ColumnWidth(15)
    @ExcelProperty("审核状态")
    private String auditStatusDesc;

    /**
     * 申请时间
     */
    @ColumnWidth(20)
    @ExcelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 申请人
     */
    @ColumnWidth(20)
    @ExcelProperty("申请人")
    private String createUserName;
}
