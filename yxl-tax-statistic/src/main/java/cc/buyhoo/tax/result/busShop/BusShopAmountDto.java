package cc.buyhoo.tax.result.busShop;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName BusCompanyAmountDto
 * <AUTHOR>
 * @Date 2024/9/21 10:53
 */
@Data
public class BusShopAmountDto implements Serializable {
    /**
     * 店铺
     */
    private Long shopUnique;
    /**
     * 已完成金额
     */
    private BigDecimal completedAmount;
    /**
     * 期末预计完成金额
     */
    private BigDecimal expectedCompleteAmount;
    /**
     * 本月累计流水
     */
    private BigDecimal monthAccumulatedRevenue;
    /**
     * 本月联营订单总额
     */
    private BigDecimal jointVentureMonthAmount;
}
