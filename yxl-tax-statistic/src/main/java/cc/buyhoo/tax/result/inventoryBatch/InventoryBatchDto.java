package cc.buyhoo.tax.result.inventoryBatch;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 入库批次返回值
 * @ClassName InventoryBatchDto
 * <AUTHOR>
 * @Date 2023/7/31 15:38
 **/
@Data
public class InventoryBatchDto implements Serializable {
    private static final long serialVersionUID = 7986671720720230307L;

    private Long id;
    /**
     * 店铺编号
     */
    private Long shopUnique;
    /**
     * 批次单号
     */
    private String batchNo;
    /**
     * 销售金额
     */
    private BigDecimal totalMoney;
    /**
     * 成本
     */
    private BigDecimal costMoney;
    /**
     * 利润
     */
    private BigDecimal profitMoney;
    /**
     * 订单实际时间
     */
    private String batchDate;
    /**
     * 进货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 供货商名称
     */
    private String shopName;
    /**
     * 入库单类型:1收银订单同步2餐饮订单同步(仅统计数据使用)3手动录入
     */
    private Integer inventoryType;
}
