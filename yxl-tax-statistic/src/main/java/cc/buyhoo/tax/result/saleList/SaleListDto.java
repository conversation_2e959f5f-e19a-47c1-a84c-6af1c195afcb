package cc.buyhoo.tax.result.saleList;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SaleListDto {

    private Long id; //编号

    private String saleListName; //顾客姓名

    private String saleListUnique; //订单编号

    private BigDecimal saleListTotal; //应收金额

    private BigDecimal saleListActuallyReceived; //实收金额

    private String saleListDatetime; //下单时间

    private String shopName; //店铺名称

    private Long shopUnique;

    /**
     * 省市区县
     */
    private String areaName;
    /**
     * 镇
     */
    private String townName;

    /**
     * 企业性质
     */
    private String shopNatureDesc;
    /**
     * 合同编码
     */
    private String contractNo;

    /**
     * 交易手续费
     */
    private BigDecimal saleListServiceFee;

    /**
     * 采购成本
     */
    private BigDecimal profitTotal;

    /**
     * 订单类型：1-代收代付，2-联营订单
     */
    private String orderType;
    /**
     * 商品总数量
     */
    private BigDecimal goodsCount;
    /**
     * 支付方式
     */
    private String payTypeName;

    private String parentListUnique;


}
