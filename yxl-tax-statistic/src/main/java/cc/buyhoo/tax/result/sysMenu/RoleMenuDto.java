package cc.buyhoo.tax.result.sysMenu;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单树返回值
 */
@Data
public class RoleMenuDto implements Serializable {

    private static final long serialVersionUID = 84506684792618635L;
    /**
     * 菜单id
     */
    private Long id;

    /**
     * 父菜单id
     */
    @JsonIgnore
    private Long parentId;
    /**
     * 菜单标题
     */
    private String label;
    /**
     * 是否禁用
     */
    private boolean disabled = false;
    /**
     * 子菜单
     */
    private List<RoleMenuDto> children;
    @JsonIgnore
    public Long getParentId() {
        return parentId;
    }

    public Long getId() {
        return id;
    }

}
