package cc.buyhoo.tax.result.busShop;

import cc.buyhoo.common.annotation.Sensitive;
import cc.buyhoo.common.enums.SensitiveStrategyEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ShopExcelBase implements Serializable {
    public static final long serialVersionUID = 1L;
    /**
     * 供货商名称
     */
    @ColumnWidth(50)
    @ExcelProperty("供货商名称")
    private String shopName;

    /**
     * 联系电话
     */
    @ColumnWidth(12)
    @ExcelProperty("联系电话")
    @Sensitive(strategy = SensitiveStrategyEnum.PHONE)
    private String shopPhone;

    /**
     * 所属市场
     */
    @ColumnWidth(50)
    @ExcelProperty("所属市场")
    private String marketName;

    /**
     * 所属企业
     */
    @ColumnWidth(50)
    @ExcelProperty("所属企业")
    private String companyName;



    /**
     * 详细地址
     */
    @ColumnWidth(100)
    @ExcelProperty("详细地址")
    @Sensitive(strategy = SensitiveStrategyEnum.ADDRESS)
    private String address;


    /**
     * 合作方式
     */
    @ColumnWidth(10)
    @ExcelProperty("合作方式")
    private String cooperateTypeDesc;

    /**
     * 已完成金额
     */
    @ColumnWidth(10)
    @ExcelProperty("已完成金额")
    private BigDecimal completedAmount;

    /**
     * 期末预计完成金额
     */
    @ColumnWidth(10)
    @ExcelProperty("期末预计完成金额")
    private BigDecimal expectedCompleteAmount;

    /**
     * 本月累计流水
     */
    @ColumnWidth(10)
    @ExcelProperty("本月累计流水")
    private BigDecimal monthAccumulatedRevenue;
}
