package cc.buyhoo.tax.result.indexData;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 首页-订单统计
 * @ClassName SaleListDataResult
 * <AUTHOR>
 * @Date 2023/7/31 17:29
 **/
@Data
public class SaleListDataResult implements Serializable {

    /**
     * 商户数
     */
    private Long shopCount = 0L;
    /**
     * 总订单数
     */
    private Long saleListCount = 0L;
    /**
     * 联营订单数量
     */
    private Long joinSaleListCount = 0L;
    /**
     * 代收代付订单数量
     */
    private Long otherSaleListCount = 0L;

    /**
     * 交易流水(元)
     */
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     * 交易流水(万元)
     */
    private BigDecimal formatTotalAmount = BigDecimal.ZERO;
    /**
     * 营业收入(元)
     */
    private BigDecimal joinTotalAmount = BigDecimal.ZERO;
    /**
     * 营业收入(万元)
     */
    private BigDecimal formatJoinTotalAmount = BigDecimal.ZERO;
    /**
     * 不含税金额(元)
     */
    private BigDecimal excludingTaxTotalAmount = BigDecimal.ZERO;
    /**
     * 不含税金额(万元)
     */
    private BigDecimal formatExcludingTaxTotalAmount = BigDecimal.ZERO;
    /**
     * 代收代付金额(元)
     */
    private BigDecimal otherTotalAmount = BigDecimal.ZERO;
    /**
     * 代收代付金额(万元)
     */
    private BigDecimal formatOtherTotalAmount = BigDecimal.ZERO;

    /**
     * 支出统计(元)
     */
    private BigDecimal totalPayAmount = BigDecimal.ZERO;
    /**
     * 支出统计(万元)
     */
    private BigDecimal formatTotalPayAmount = BigDecimal.ZERO;
    /**
     * 营业成本(元)
     */
    private BigDecimal totalProfitAmount = BigDecimal.ZERO;

    /**
     * 营业成本(万元)
     */
    private BigDecimal formatTotalProfitAmount = BigDecimal.ZERO;

    /**
     * 交易手续费(元)
     */
    private BigDecimal payFee = BigDecimal.ZERO;
    /**
     * 交易手续费(万元)
     */
    private BigDecimal formatPayFee = BigDecimal.ZERO;

    /**
     * 其它服务费(元)
     */
    private BigDecimal saleListServiceFee = BigDecimal.ZERO;
    /**
     * 其它服务费(万元)
     */
    private BigDecimal formatSaleListServiceFee = BigDecimal.ZERO;
    /**
     * 营业利润(元)
     */
    private BigDecimal operatingProfit;
    /**
     * 营业利润(万元)
     */
    private BigDecimal formatOperatingProfit;
    /**
     * 毛利润(元)
     */
    private BigDecimal grossProfit;
    /**
     * 毛利润(万元)
     */
    private BigDecimal formatGrossProfit;
    /**
     * 毛利润率
     */
    private BigDecimal grossProfitRate;

    /**
     * 税费合计(元)
     */
    private BigDecimal totalTaxFee = BigDecimal.ZERO;
    /**
     * 税费合计(万元)
     */
    private BigDecimal formatTotalTaxFee = BigDecimal.ZERO;
    /**
     * 增值税(元)
     */
    private BigDecimal taxFee = BigDecimal.ZERO;
    /**
     * 增值税(万元)
     */
    private BigDecimal formatTaxFee = BigDecimal.ZERO;

    /**
     * 城市维护建设税(元)
     */
    private BigDecimal cityFee = BigDecimal.ZERO;
    /**
     * 城市维护建设税(万元)
     */
    private BigDecimal formatCityFee = BigDecimal.ZERO;
    /**
     * 教育附加费(元)
     */
    private BigDecimal eduFee = BigDecimal.ZERO;
    /**
     * 教育附加费(万元)
     */
    private BigDecimal formatEduFee = BigDecimal.ZERO;
    /**
     * 地方教育附加费(元)
     */
    private BigDecimal localEduFee = BigDecimal.ZERO;
    /**
     * 地方教育附加费(万元)
     */
    private BigDecimal formatLocalEduFee = BigDecimal.ZERO;
    /**
     * 印花税(元)
     */
    private BigDecimal stampTax = BigDecimal.ZERO;
    /**
     * 印花税(万元)
     */
    private BigDecimal formatStampTax = BigDecimal.ZERO;
    /**
     * 企业所得税(元)
     */
    private BigDecimal businessTax = BigDecimal.ZERO;
    /**
     * 企业所得税(万元)
     */
    private BigDecimal formatBusinessTax = BigDecimal.ZERO;
}
