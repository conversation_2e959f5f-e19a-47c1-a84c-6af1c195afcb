package cc.buyhoo.tax.result.returnList;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReturnListDto {

    private Long id;

    private String saleListUnique; //销售订单编号

    private String retListDatetime; //退货时间

    private BigDecimal retListTotalMoney; //退货金额

    private String retListUnique; //退货单号

    private String retListReason; //退款原因

    private String shopName; //店铺名称

    private Long shopUnique; //店铺编号

}
