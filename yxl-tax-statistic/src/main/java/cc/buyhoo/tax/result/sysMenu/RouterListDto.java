package cc.buyhoo.tax.result.sysMenu;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class RouterListDto {

    private String path; //路由菜单访问路径

    private String name; //路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)

    private String component; //组件路径

    private RouterMenuMeta meta; //页面元信息

    private List<RouterListDto> children; //子组件

    private Integer sort; //排序

    private Integer type; //菜单类型:1目录2菜单3按钮

    private Long id; //菜单id

    private Long parentId; //父菜单id

    private String permission; //菜单权限

    private Integer menuLevel; //菜单级别:0-普通,1-平台,2-内置

}
