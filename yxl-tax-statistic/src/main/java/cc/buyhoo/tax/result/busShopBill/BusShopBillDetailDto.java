package cc.buyhoo.tax.result.busShopBill;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商结算明细
 * @ClassName BusShopBillDto
 * <AUTHOR>
 * @Date 2023/7/27 18:21
 **/
@Data
public class BusShopBillDetailDto implements Serializable {
    private static final long serialVersionUID = 7223097441750157775L;

    /**
     * ID
     */
    private Long id;

    /**
     * 供货商名称
     */
    private String shopName;

    /**
     * 打款单号
     */
    private String billNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * 结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 结算方式，0-手动结算，1-自动结算
     */
    private String settledType;

    /**
     * 打款状态，YTJ(自定义状态:已提交)、AUT(等待审批)、NTE(终审完毕)、BNK(银行处理中)、FINS(银行支付成功)、FINF(银行支付失败)、FINB(银行支付被退票)、FINR(企业审批否决)、FIND(企业过期不审批)、FINC(企业撤销)、FINU(银行挂账)、OPR(数据接收中)、APW(银行人工审批)、WRF(属于银行处理中状态，可疑，表示状态未知，需要人工介入处理)
     */
    private String settledStatus;

    /**
     * 企业开户行
     */
    private String companyBankName;

    /**
     * 企业对公账户
     */
    private String companyBankCard;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 转账状态：1转账成功、2转账中、3转账失败
     */
    private Integer transferStatus;

}
