package cc.buyhoo.tax.result.inventoryOrder;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 入库单返回值
 * @ClassName InventoryOrderCategoryDto
 * <AUTHOR>
 * @Date 2023/7/30 16:45
 **/
@Data
public class InventoryOrderDto implements Serializable {
    private static final long serialVersionUID = -4804782024440588340L;

    private Long id;
    /**
     * 入库单号
     */
    private String orderNo;
    /**
     * 销售金额
     */
    private BigDecimal totalMoney;
    /**
     * 入库成本金额
     */
    private BigDecimal costMoney;
    /**
     * 利润
     */
    private BigDecimal profitMoney;
    /**
     * 入库时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;
}
