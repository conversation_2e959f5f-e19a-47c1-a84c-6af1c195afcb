package cc.buyhoo.tax.result.busShopInvoice;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AnalysisInvoiceDto {

    /**
     * 发票介质：1、电子发票；2、纸质发票
     */
    private Integer mediumType = 1;

    /**
     * 发票号码
     */
    private String invoiceNumber;
    /**
     * 发票种类:1普通发票2增值税发票3专用发票
     */
    private Integer invoiceKind;
    /**
     * 开票日期
     */
    private String invoiceDate;
    /**
     * 购买方名称
     */
    private String purchaseName;
    /**
     * 购买方纳税人识别号
     */
    private String purchaseIdentity;
    /**
     * 购买方地址
     */
    private String purchaseAddress;

    /**
     * 销售方名称
     */
    private String saleName;
    /**
     * 销售方纳税人识别号
     */
    private String saleIdentity;
    /**
     * 销售方地址
     */
    private String saleAddress;
    /**
     * 合计订单金额
     */
    private BigDecimal orderMoney;
    /**
     * 合计税额
     */
    private BigDecimal taxMoney;
    /**
     * 价税合计
     */
    private BigDecimal orderTaxMoney;
    /**
     * 开票人
     */
    private String invoiceMan;

}
