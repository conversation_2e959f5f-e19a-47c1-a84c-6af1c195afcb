package cc.buyhoo.tax.result.disassembleList;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class DisassembleDetailListDto implements Serializable {

    /**
     * 订单编号
     */
    private String saleListUnique;
    /**
     * 订单实收金额
     */
    private BigDecimal saleListActuallyReceived;
    /**
     * 采购成本
     */
    private BigDecimal profitTotal;
    /**
     * 商品总数量
     */
    private BigDecimal goodsCount;
    /**
     * 支付方式
     */
    private String payTypeName;
    /**
     * 交易手续费
     */
    private BigDecimal saleListServiceFee;
    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date saleListDatetime;
    /**
     * 订单类型：1-代收代付，2-联营订单
     */
    private String orderType;


}
