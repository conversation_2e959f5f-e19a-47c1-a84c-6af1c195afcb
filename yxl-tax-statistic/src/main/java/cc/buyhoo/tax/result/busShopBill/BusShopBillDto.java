package cc.buyhoo.tax.result.busShopBill;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商账单
 * @ClassName BusShopBillDto
 * <AUTHOR>
 * @Date 2023/7/27 18:21
 **/
@Data
public class BusShopBillDto implements Serializable {
    private static final long serialVersionUID = 7223097441750157775L;

    /**
     * ID
     */
    private Long id;

    /**
     * 商家唯一标识
     */
    private Long shopUnique;

    /**
     * 已结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 未结算金额
     */
    private BigDecimal unsettledAmount;

    /**
     * 结算中金额
     */
    private BigDecimal settledingAmount;

    /**
     * 供应商名称
     */
    private String shopName;
}
