package cc.buyhoo.tax.result.busShopInvoice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName ShopGoodQueryDto
 * <AUTHOR>
 * @Date 2024/6/17 16:56
 */
@Data
public class ShopGoodQueryDto implements Serializable {
    /**
     * 商品税收分类编码
     */
    private String taxClassificationCode;
    /**
     * 商品编码
     */
    private String goodsBarcode;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 税率百分比
     */
    private String taxRate;
    /**
     * 单位
     */
    private String unit;
    /**
     * 税率
     */
    private BigDecimal taxRatePer;
}
