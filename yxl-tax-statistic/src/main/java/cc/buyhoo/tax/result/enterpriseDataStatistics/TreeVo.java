package cc.buyhoo.tax.result.enterpriseDataStatistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName TreeVo
 * @Description 返回给前端的结果
 * <AUTHOR>
 * @Date 2025/6/16 下午2:07
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TreeVo implements Serializable {
    /**
     * tree的数据
     */
   // @ApiModelProperty(value = "tree数据")
    private List<TreeItemVO> items;
}