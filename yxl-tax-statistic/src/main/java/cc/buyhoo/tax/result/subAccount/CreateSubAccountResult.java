package cc.buyhoo.tax.result.subAccount;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CreateSubAccountResult
 * @Description 创建子账户返回信息
 * <AUTHOR>
 * @Date 2025/3/20 16:49
 * @Version 1.0
 */
@Data
public class CreateSubAccountResult implements Serializable {
    private static final long serialVersionUID = -500128977883975027L;


    /**
     * 子账簿账号
     */
    private String accNo;

}