package cc.buyhoo.tax.result.sysMigrate;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 迁入迁出详情返回值
 * @ClassName SysMigrateDetailResult
 * <AUTHOR>
 * @Date 2024/8/29 14:36
 **/
@Data
public class SysMigrateDetailResult implements Serializable {
    /**
     * ID
     */
    private Long id;
    /**
     * 企业名称
     */
    private String inCompanyName;
    /**
     * 迁出企业名称
     */
    private String outCompanyName;
    /**
     * 商户名称
     */
    private String shopName;

    /**
     * 审核状态：0-待审核，1-通过，2-不通过
     */
    private Integer auditStatus;

    /**
     * 审核状态：0-待审核，1-通过，2-不通过
     */
    private String auditStatusDesc;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 市场名称
     */
    private String marketName;
    /**
     * 预计纳税目标金额(元)
     */
    private BigDecimal targetAmount;

    /**
     * 纳统类型,1-按月,2-按季度,3-按年
     */
    private String taxType;
    /**
     * 审核人
     */
    private String auditUserName;
    /**
     * 创建人
     */
    private String createUserName;
}
