package cc.buyhoo.tax.result.sysCompany;

import cc.buyhoo.common.annotation.Sensitive;
import cc.buyhoo.common.enums.SensitiveStrategyEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业导出数据
 * @ClassName CompanyExcel
 * <AUTHOR>
 * @Date 2024-9-2 08:58:33
 **/
@Data
public class CompanyExcel implements Serializable {
    private static final long serialVersionUID = 4148234659399145807L;

    /**
     * 企业名称
     */
    @ColumnWidth(50)
    @ExcelProperty("企业名称")
    private String companyName;

    /**
     * 营业执照编号
     */
    @ColumnWidth(30)
    @ExcelProperty("营业执照编号")
    private String licenseNumber;

    /**
     * 注册地址
     */
    @ColumnWidth(50)
    @ExcelProperty("注册地址")
    private String address;

    /**
     * 行业名称
     */
    @ColumnWidth(30)
    @ExcelProperty("行业名称")
    private String industryName;

    /**
     * 市场名称
     */
    @ColumnWidth(30)
    @ExcelProperty("市场名称")
    private String marketName;

    /**
     * 邀请码
     */
    @ColumnWidth(20)
    @ExcelProperty("企业邀请码")
    private String invitationCode;

    /**
     * 联系人
     */
    @ColumnWidth(20)
    @ExcelProperty("联系人")
    private String contactName;

    /**
     * 联系电话
     */
    @ColumnWidth(15)
    @ExcelProperty("联系电话")
    private String contactMobile;

    /**
     * 经营目标（万元）
     */
    @ColumnWidth(20)
    @ExcelProperty("经营目标（万元）")
    private BigDecimal targetAmount;
    /**
     * 经营周期
     */
    @ColumnWidth(10)
    @ExcelProperty("经营周期")
    private String taxType;

    /**
     * 突击顺序
     */
    @ColumnWidth(10)
    @ExcelProperty("突击顺序")
    private Integer statisticGrade;

    /**
     * 突击金额
     */
    @ColumnWidth(20)
    @ExcelProperty("突击金额（万元）")
    private BigDecimal statisticAmount;

    /**
     * 税率
     */
    @ColumnWidth(20)
    @ExcelProperty("增值税税率（%）")
    private BigDecimal vatRate;
}
