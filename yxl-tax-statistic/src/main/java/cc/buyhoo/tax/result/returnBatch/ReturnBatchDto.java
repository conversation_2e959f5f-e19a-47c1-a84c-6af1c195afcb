package cc.buyhoo.tax.result.returnBatch;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReturnBatchDto implements Serializable {

    private Long id;

    /**
     * 店铺编号
     */
    private Long shopUnique;
    /**
     * 批次单号
     */
    private String batchNo;
    /**
     * 总金额
     */
    private BigDecimal totalMoney;
    /**
     * 在线支付金额
     */
    private BigDecimal onlineMoney;
    /**
     * 现金支付金额
     */
    private BigDecimal cashMoney;
    /**
     * 退货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 供货商名称
     */
    private String shopName;


}
