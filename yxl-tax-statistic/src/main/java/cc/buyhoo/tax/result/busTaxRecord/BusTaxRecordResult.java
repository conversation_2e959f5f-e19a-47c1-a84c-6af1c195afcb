package cc.buyhoo.tax.result.busTaxRecord;

import cc.buyhoo.common.standard.FullPageResult;
import lombok.Data;

import java.io.Serializable;

/**
 * 申报记录分页
 * @ClassName BusTaxRecordResult
 * <AUTHOR>
 * @Date 2023/8/11 11:33
 **/
@Data
public class BusTaxRecordResult extends FullPageResult<BusTaxRecordDto> implements Serializable {
    private static final long serialVersionUID = -8198392202301690113L;
}
