package cc.buyhoo.tax.result.saleList;

import cc.buyhoo.common.annotation.Sensitive;
import cc.buyhoo.common.enums.SensitiveStrategyEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class SaleListExcel implements Serializable {
    private static final long serialVersionUID = 4148234659399145807L;

    @ColumnWidth(50)
    @ExcelProperty("供货商名称")
    private String shopName;

    @ColumnWidth(30)
    @ExcelProperty("订单编号")
    private String saleListUnique;

    @ColumnWidth(50)
    @ExcelProperty("省市区县")
    private String areaName;

    @ColumnWidth(50)
    @ExcelProperty("乡镇")
    private String townName;

    @ColumnWidth(50)
    @ExcelProperty("供货商性质")
    private String shopNatureDesc;

    @ColumnWidth(20)
    @ExcelProperty("收款金额")
    private String saleListActuallyReceived;

    @ColumnWidth(20)
    @ExcelProperty("服务费")
    private BigDecimal saleListServiceFee;

    @ColumnWidth(30)
    @ExcelProperty("下单时间")
    private String saleListDatetime;

    @ColumnWidth(30)
    @ExcelProperty("收款方式")
    private String payTypeName;


    @ColumnWidth(30)
    @ExcelProperty("成本金额")
    private BigDecimal profitTotal;
}
