package cc.buyhoo.tax.result.busTradeRecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName TradeRecordListQueryDto
 * <AUTHOR>
 * @Date 2024/8/31 16:36
 */
@Data
public class TradeRecordListQueryDto implements Serializable {
    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tradeTime;
    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;
    /**
     * 账户余额
     */
    private BigDecimal companyBalance;
    /**
     * 交易方向
     */
    private Integer tradeWay;
    /**
     * 收（付）方名称
     */
    private String tradeOtherName;
    /**
     * 收（付）方账号
     */
    private String tradeOtherAccount;
    /**
     * 流水号
     */
    private String serialNumber;
    /**
     * 摘要
     */
    private String tradeAbstract;
}
