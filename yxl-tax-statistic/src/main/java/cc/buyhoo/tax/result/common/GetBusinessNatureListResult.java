package cc.buyhoo.tax.result.common;

import cc.buyhoo.tax.entity.common.EnumEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class GetBusinessNatureListResult implements Serializable {
    public static final Long serialVersionUID = 1L;

    /**
     * 企业性质
     */
    private List<EnumEntity> natureList;
}
