package cc.buyhoo.tax.result.busShopInvoice;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ShopInvoiceListDto {

    private Long id;
    /**
     * 商户编码
     */
    private Long shopUnique;
    /**
     * 订单编号
     */
    private String saleListUnique;

    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 机器编码
     */
    private String machineCode;
    /**
     * 发票号码
     */
    private String invoiceNumber;
    /**
     * 发票种类:1普通发票2增值税发票3专用发票
     */
    private Integer invoiceKind;
    /**
     * 发票种类中文
     */
    private String invoiceKindName;
    /**
     * 开票日期
     */
    private String invoiceDate;
    /**
     * 校验码
     */
    private String checkCode;
    /**
     * 购买方名称
     */
    private String purchaseName;
    /**
     * 购买方纳税人识别号
     */
    private String purchaseIdentity;
    /**
     * 购买方地址
     */
    private String purchaseAddress;
    /**
     * 购买方电话
     */
    private String purchasePhone;
    /**
     * 购买方开户行
     */
    private String purchaseBank;
    /**
     * 购买方开户行账号
     */
    private String purchaseBankNo;
    /**
     * 销售方名称
     */
    private String saleName;
    /**
     * 销售方纳税人识别号
     */
    private String saleIdentity;
    /**
     * 销售方地址
     */
    private String saleAddress;
    /**
     * 销售方电话
     */
    private String salePhone;
    /**
     * 销售方开户行
     */
    private String saleBank;
    /**
     * 销售方开户行账号
     */
    private String saleBankNo;
    /**
     * 合计订单金额
     */
    private BigDecimal orderMoney;
    /**
     * 合计税额
     */
    private BigDecimal taxMoney;
    /**
     * 价税合计
     */
    private BigDecimal orderTaxMoney;
    /**
     * 收款人
     */
    private String payee;
    /**
     * 复核人
     */
    private String checker;
    /**
     * 开票人
     */
    private String invoiceMan;
    /**
     * 修改人
     */
    private String modifyUser;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 修改时间
     */
    private String modifyTime;
    /**
     * 采购方类型：1、个人；2、企业
     */
    private Integer purchaseType;
    /**
     * 发票介质：1、电子发票；2、纸质发票
     */
    private Integer mediumType;

    /**
     * 是否开票：1已开票2未开票；3、开票中；4、开票失败
     */
    private Integer status;

    /**
     * 备注
     */
    private String notes;

    /**
     * 是否展示购方地址、电话信息：1-是；2-否
     */
    private Integer purchasePersonFlag;

    /**
     * 是否展示销方地址、电话信息：1-是；2-否
     */
    private Integer salePersonFlag;
    /**
     * 是否展示购方开户银行、银行账号信息：1-是；2-否
     */
    private Integer purchaseBankFlag;

    /**
     * 是否展示销方开户银行、银行账号信息：1-是；2-否
     */
    private Integer saleBankFlag;

    /**
     * 发票附件
     */
    private String imageUrl;
    
    /**
     * 未申请订单是否可以开票：0-否1-是
     */
    private Integer invoiceFlag;
    /**
     * 是否申请开票：0-否1-是
     */
    private Integer applyFlag;

}
