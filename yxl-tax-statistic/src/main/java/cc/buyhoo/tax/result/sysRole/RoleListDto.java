package cc.buyhoo.tax.result.sysRole;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色列表返回值
 */
@Data
public class RoleListDto implements Serializable {

    private static final long serialVersionUID = 8337067774274904000L;
    /**
     * 角色ID
     */
    private Long id;
    /**
     * 企业ID
     */
    private Long companyId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色类型:1超级管理员2普通管理员
     */
    private Integer roleType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 最后操作人
     */
    private String modifyUser;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

}
