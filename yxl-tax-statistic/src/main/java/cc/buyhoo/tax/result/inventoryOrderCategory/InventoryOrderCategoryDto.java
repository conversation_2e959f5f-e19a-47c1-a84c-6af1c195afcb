package cc.buyhoo.tax.result.inventoryOrderCategory;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存分类返回值
 * @ClassName InventoryOrderCategoryDto
 * <AUTHOR>
 * @Date 2023/7/30 16:45
 **/
@Data
public class InventoryOrderCategoryDto implements Serializable {
    private static final long serialVersionUID = -4804782024440588340L;

    private Long id;
    /**
     * 一级分类
     */
    private Long categoryId;
    /**
     * 一级分类名称
     */
    private String categoryName;
    /**
     * 二级分类
     */
    private Long categoryTwoId;
    /**
     * 二级分类名称
     */
    private String categoryTwoName;
    /**
     * 批次数量总和
     */
    private BigDecimal totalGoodsCount;
    /**
     * 商品金额
     */
    private BigDecimal totalMoney;
}
