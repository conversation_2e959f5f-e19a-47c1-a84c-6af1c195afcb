package cc.buyhoo.tax.result.busTradeRecord;

import cc.buyhoo.common.standard.FullPageResult;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName TradeRecordListQueryResult
 * <AUTHOR>
 * @Date 2024/8/31 16:42
 */
@Data
public class TradeRecordListQueryResult extends FullPageResult<TradeRecordListQueryDto> implements Serializable {
    /**
     * 入账总笔数
     */
    private Long inTradeCount;
    /**
     * 入账总金额
     */
    private BigDecimal inTradeAmount;
    /**
     * 出账总笔数
     */
    private Long outTradeCount;
    /**
     * 出账总金额
     */
    private BigDecimal outTradeAmount;
}
