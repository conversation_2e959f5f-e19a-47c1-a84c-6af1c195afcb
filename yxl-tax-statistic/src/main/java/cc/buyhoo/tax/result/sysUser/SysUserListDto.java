package cc.buyhoo.tax.result.sysUser;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户列表返回值
 */
@Data
public class SysUserListDto implements Serializable {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long companyId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 用户类型：1超级管理员2普通管理员
     */
    private Integer userType;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 修改人
     */
    private String modifyUser;
    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 创建人ID
     */
    private Long createUser;

}
