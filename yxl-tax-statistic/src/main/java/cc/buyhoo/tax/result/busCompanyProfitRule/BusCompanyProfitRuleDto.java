package cc.buyhoo.tax.result.busCompanyProfitRule;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 企业利润规则返回值
 * @ClassName BusCompanyProfitRuleDto
 * <AUTHOR>
 * @Date 2024/6/25 10:11
 **/
@Data
public class BusCompanyProfitRuleDto {

    /**
     * ID
     */
    private Long id;

    /**
     * 规则类型：1-按订单支付比例，2-按金额数量
     */
    private Integer ruleType;

    /**
     * 按订单比例方式，金额
     */
    private BigDecimal amount;

    /**
     * 按订单：小于等于金额，利润百分比
     */
    private BigDecimal firstProfitRate;

    /**
     * 按订单：大于金额，利润百分比
     */
    private BigDecimal secondProfitRate;

    /**
     * 按订单：每月最大利润百分比
     */
    private BigDecimal monthProfitRate;

    /**
     * 按金额数量：固定利润百分比
     */
    private BigDecimal profitRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

}
