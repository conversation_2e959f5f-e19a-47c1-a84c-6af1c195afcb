package cc.buyhoo.tax.result.invoice;

import java.util.Date;

public class BusInvoiceStaffDto {
    /**
     * id
     */
    private Long id;

    /**
     * sys_company.id
     */
    private Long companyId;

    /**
     * 员工名称
     */
    private String staffName;

    /**
     * 全电4期登录账户
     */
    private String staffAccount;

    /**
     * 全电4期密码
     */
    private String staffPwd;

    /**
     * 责任人类型：01、法人；02、财务负责人；03、办税员；05、管理员；08、社保经办人；09、开票员；10、销售人员
     */
    private String dutyType;

    /**
     * 1、已认证成功；0、未认证；
     */
    private Integer authStatus;

    /**
     * 创建时间
     */
    private Date createDatetime;

    /**
     * 1、可用；0、不可用
     */
    private Integer validType;

    /**
     * 最后一次更新时间
     */
    private Date updateDatetime;
    /**
     * 1、已登录；0、未登录
     */
    private Integer isLogin;
    /**
     * 最后一次登录时间
     */
    private String lastLoginTime;
}
