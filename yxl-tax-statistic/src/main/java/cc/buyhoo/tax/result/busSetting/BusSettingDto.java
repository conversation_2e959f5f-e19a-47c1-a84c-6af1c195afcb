package cc.buyhoo.tax.result.busSetting;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业设置返回
 * @ClassName BusSettingDto
 * <AUTHOR>
 * @Date 2023/7/31 8:47
 **/
@Data
public class BusSettingDto implements Serializable {
    private static final long serialVersionUID = -3883287976691693340L;

    /**
     * 邀请地址
     */
    private String invitationUrl;

    /**
     * 纳统金额
     */
    private BigDecimal targetAmount;

    /**
     * 纳统类型
     */
    private String taxType;
}
