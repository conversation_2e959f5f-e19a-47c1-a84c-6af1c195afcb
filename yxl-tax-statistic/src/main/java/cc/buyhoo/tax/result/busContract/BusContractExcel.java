package cc.buyhoo.tax.result.busContract;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同导出数据
 * @ClassName BusContractDto
 * <AUTHOR>
 * @Date 2024/7/13 11:14
 **/
@Data
public class BusContractExcel implements Serializable {
    private static final long serialVersionUID = 4148234659399145807L;

    /**
     * 合同名称
     */
    @ColumnWidth(50)
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 合同编码
     */
    @ColumnWidth(50)
    @ExcelProperty("合同编码")
    private String contractNo;

    /**
     * 关联单号
     */
    @ColumnWidth(50)
    @ExcelProperty("关联单号")
    private String orderNo;

    /**
     * 合同金额（元）
     */
    @ColumnWidth(20)
    @ExcelProperty("合同金额(元)")
    private BigDecimal totalAmount;
    /**
     * 商户编码
     */
    @ColumnWidth(20)
    @ExcelProperty("合作方")
    private String shopName;

    /**
     * 合同状态：0-草拟，1-待审，2-已签署，3-执行中，4-已完成，5-终止
     */
    @ColumnWidth(12)
    @ExcelProperty("合同状态")
    private String contractStatusName;

    /**
     * 合同类型：0-默认，1-销售合同，2-采购合同，3-联营合同，4-其它
     */
    @ColumnWidth(12)
    @ExcelProperty("合同类型")
    private String contractTypeName;

    /**
     * 合同开始时间
     */
    @ColumnWidth(20)
    @ExcelProperty("合同起始日期")
    private String startTime;

    /**
     * 合同结束时间
     */
    @ColumnWidth(20)
    @ExcelProperty("合同结束日期")
    private String endTime;

    /**
     * 合同签订日期
     */
    @ColumnWidth(20)
    @ExcelProperty("合同签订日期")
    private String signTime;
}
