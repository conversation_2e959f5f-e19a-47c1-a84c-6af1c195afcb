package cc.buyhoo.tax.result.inventoryBatchDetail;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 入库批次明细
 * @ClassName InventoryBatchDetailDto
 * <AUTHOR>
 * @Date 2023/8/3 8:26
 **/
@Data
public class InventoryBatchDetailDto implements Serializable {

    private Long id;
    /**
     * bus_inventory_batch.id
     */
    private Long batchId;
    /**
     * 一级分类
     */
    private Long categoryId;
    /**
     * 二级分类
     */
    private Long categoryTwoId;
    /**
     * bus_goods.id
     */
    private Long goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 批次数量
     */
    private BigDecimal goodsCount;
    /**
     * 批次金额
     */
    private BigDecimal totalMoney;
    /**
     * 企业ID
     */
    private Long companyId;
}
