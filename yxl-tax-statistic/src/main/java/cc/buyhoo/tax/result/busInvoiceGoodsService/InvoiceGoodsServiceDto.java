package cc.buyhoo.tax.result.busInvoiceGoodsService;

import lombok.Data;

import java.io.Serializable;

/**
 * 开票服务返回值
 * @ClassName ShopGoodsServiceResult
 * <AUTHOR>
 * @Date 2023/9/6 14:19
 **/
@Data
public class InvoiceGoodsServiceDto implements Serializable {

    private static final long serialVersionUID = -6234845989753504562L;
    /**
     * 开票服务ID
     */
    private Long id;
    /**
     * 税收分类编码
     */
    private String categoryNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品税目
     */
    private String taxItem;
    /**
     * 商品规格
     */
    private String goodsSpec;
    /**
     * 商品单位
     */
    private String goodsUnit;
    /**
     * 税率
     */
    private String taxRate;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

}
