package cc.buyhoo.tax.result.sysRole;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色详情返回值
 */
@Data
public class RoleDetailResult implements Serializable {

    private static final long serialVersionUID = 3367662518003125161L;
    /**
     * 角色ID
     */
    private Long id;
    /**
     * 企业ID
     */
    private Long companyId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    private Integer dataScope;
    /**
     * 数据范围(指定部门数组)
     */
    private String dataScopeDeptIds;
    /**
     * 备注
     */
    private String remark;
    /**
     * 权限
     */
    private List<Long> menuIds;

}
