package cc.buyhoo.tax.result.disassembleList;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class DisassembleListDto implements Serializable {
    //记录ID
    private Long id;
    //企业ID
    private Long companyId;
    //创建时间
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;
    //创建人
    private String createUser;
    //原订单号
    private String saleListUnique;
    //拆单状态，明文
    private String disassembleStatus;
    //拆单状态，数值
    private Integer disassembleStatusValue;
    //拆单后订单数量
    private Integer disassembleCount;
    //设定的最小拆单金额
    private BigDecimal minAmountSet;
    //设定的最大拆单金额
    private BigDecimal maxAmountSet;
    //实际得最小拆单金额
    private BigDecimal minAmountActual;
    //实际的最大拆单金额
    private BigDecimal maxAmountActual;
    //拆单备注信息
    private String disassmebleRemarks;
    //拆解订单的金额
    private BigDecimal orderAmount;
}
