package cc.buyhoo.tax.result.disassembleList;

import cc.buyhoo.common.standard.FullPageResult;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QueryDisassembleDetailListResult extends FullPageResult<DisassembleDetailListDto> implements Serializable {
    /**
     * 来源单号
     */
    private String saleListUnique;

    /**
     * 拆单数量
     */
    private Integer disassembleCount;

    /**
     * 实际拆单的最小金额
     */
    private BigDecimal minAmountActual;
    /**
     * 实际拆单的最大金额
     */
    private BigDecimal maxAmountActual;
}

