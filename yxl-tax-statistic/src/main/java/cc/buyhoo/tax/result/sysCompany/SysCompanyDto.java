package cc.buyhoo.tax.result.sysCompany;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 企业列表
 */
@Data
public class SysCompanyDto {
    /**
     * id
     */
    private Long id;
    /**
     * 企业类型
     */
    private Integer companyType;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 统一社会信用代码
     */
    private String licenseNumber;
    /**
     * 公司地址
     */
    private String address;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系电话
     */
    private String contactMobile;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
    /**
     * 邀请码
     */
    private String invitationCode;
    /**
     * 开户行名称
     */
    private String bankName;
    /**
     * 卡号
     */
    private String bankCard;
    /**
     * 预计纳税目标金额
     */
    private BigDecimal targetAmount;
    /**
     * 纳统类型,1-按月,2-按季度,3-按年
     */
    private String taxType;
    /**
     * 转账是否要审批:1无需审批2需审批
     */
    private Integer transferAccountAudit;
    /**
     * 操作人
     */
    private String modifyUser;
    /**
     * 所属行业ID
     */
    private Long industryId;
    /**
     * 所属市场ID
     */
    private Long marketId;
}
