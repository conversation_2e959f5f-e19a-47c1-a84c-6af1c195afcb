package cc.buyhoo.tax.result.saleList;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName SelectBuyhooSaleListInfoDto
 * <AUTHOR>
 * @Date 2024/10/31 14:00
 */
@Data
public class SaleListSyncDto implements Serializable {
    /**
     * 商户
     */
    private Long shopUnique;
    /**
     * 商户名
     */
    private String shopName;
    /**
     * 订单编号
     */
    private String saleListUnique;
    /**
     * 销售金额
     */
    private BigDecimal saleListActuallyReceived;
}
