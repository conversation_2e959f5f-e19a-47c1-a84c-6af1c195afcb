package cc.buyhoo.tax.result.indexData.v2;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 首页返回值
 */
@Data
public class IndexDataV2Result {

    /**
     * 纳统类型,1-按月,2-按季度,3-按年
     */
    private String taxType;

    /**
     * 总营业额
     */
    private BigDecimal totalTurnover;

    /**
     * 总营业额格式化
     */
    private BigDecimal totalTurnoverFormat;

    /**
     * 线上支付金额
     */
    private BigDecimal totalOnlineMoney;

    /**
     * 现金支付金额
     */
    private BigDecimal totalCashMoney;

    /**
     * 在线支付占比
     */
    private Integer onlineRatio;

    /**
     * 现金支付占比
     */
    private Integer cashRatio;

    /**
     * 企业盈利
     */
    private BigDecimal totalProfit;

    /**
     * 企业盈利格式化
     */
    private BigDecimal totalProfitFormat;

    /**
     * 纳税总额
     */
    private BigDecimal totalTaxableAmount;

    /**
     * 总进项税
     */
    private BigDecimal totalInputTax;

    /**
     * 总进项税格式化
     */
    private BigDecimal totalInputTaxFormat;

    /**
     * 总销项税
     */
    private BigDecimal totalOutputTax;

    /**
     * 总销项税格式化
     */
    private BigDecimal totalOutputTaxFormat;

    /**
     * 已开进项税
     */
    private BigDecimal finishInputTax;

    /**
     * 已开进项税格式化
     */
    private BigDecimal finishInputTaxFormat;

    /**
     * 应纳税总额
     */
    private BigDecimal needInputTax;

    /**
     * 应纳税总额格式化
     */
    private BigDecimal needInputTaxFormat;

    /**
     * 已开销项税
     */
    private BigDecimal finishOutputTax;

    /**
     * 需开销项税
     */
    private BigDecimal needOutputTax;

    /**
     * 目标金额
     */
    private BigDecimal targetAmount;

    /**
     * 目标金额格式化
     */
    private BigDecimal targetAmountFormat;

    /**
     * 完成金额
     */
    private BigDecimal finishAmount;

    /**
     * 完成金额格式化
     */
    private BigDecimal finishAmountFormat;

    /**
     * 完成比例
     */
    private Integer finishAmountRatio;

    /**
     * 日目标
     */
    private BigDecimal dayTarget;

    /**
     * 日目标格式化
     */
    private BigDecimal dayTargetFormat;

    /**
     * 月目标
     */
    private BigDecimal monthTarget;

    /**
     * 月目标格式化
     */
    private BigDecimal monthTargetFormat;

    /**
     * 季度目标
     */
    private BigDecimal quarterTarget;

    /**
     * 季度目标
     */
    private BigDecimal quarterTargetFormat;

    /**
     * 日完成
     */
    private BigDecimal dayFinish;

    /**
     * 日完成格式化
     */
    private BigDecimal dayFinishFormat;

    /**
     * 月完成
     */
    private BigDecimal monthFinish;

    /**
     * 月完成格式化
     */
    private BigDecimal monthFinishFormat;

    /**
     * 季度完成
     */
    private BigDecimal quarterFinish;

    /**
     * 季度完成格式化
     */
    private BigDecimal quarterFinishFormat;

    /**
     * 日完成占比
     */
    private Integer dayFinishRatio;

    /**
     * 月完成占比
     */
    private Integer monthFinishRatio;

    /**
     * 季度完成占比
     */
    private Integer quarterFinishRatio;

    /**
     * 统计柱状图x轴
     */
    private List<String> countBarXData;

    /**
     * 统计柱状图y轴
     */
    private List<BigDecimal> countBarYData;

    /**
     * 预计完成时间
     */
    private String finishDate;

    /**
     * 纳统开始时间
     */
    private String dateStart;

    /**
     * 纳统结束时间
     */
    private String dateEnd;

    /**
     * 商户数量
     */
    private Long shopCount;

    /**
     * 总订单数
     */
    private Long totalOrderCount;

    /**
     * 已开票订单数
     */
    private Long finishInOrderCount;

    /**
     * 未开票订单数
     */
    private Long needInOrderCount;

}
