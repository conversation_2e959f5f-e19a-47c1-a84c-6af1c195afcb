package cc.buyhoo.tax.result.returnOrderCategory;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ReturnOrderCategoryDto implements Serializable {
    private Long id;
    /**
     * 一级分类
     */
    private Long categoryId;
    /**
     * 一级分类名称
     */
    private String categoryName;
    /**
     * 二级分类
     */
    private Long categoryTwoId;
    /**
     * 二级分类名称
     */
    private String categoryTwoName;
    /**
     * 批次数量总和
     */
    private BigDecimal totalGoodsCount;
    /**
     * 商品金额
     */
    private BigDecimal totalMoney;
}
