package cc.buyhoo.tax.result.disassembleList;

import cc.buyhoo.tax.entity.BusSaleListPayDetailEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class CreateOrderSigleResult implements Serializable {
    public final static Long serialVersionUID = 1L;
    private boolean flag;
    private BigDecimal saleListTotal;
    private String msg;
    private List<BusSaleListPayDetailEntity> saleListPayDetailEntities;

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public BigDecimal getSaleListTotal() {
        return saleListTotal;
    }

    public void setSaleListTotal(BigDecimal saleListTotal) {
        this.saleListTotal = saleListTotal;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<BusSaleListPayDetailEntity> getSaleListPayDetailEntities() {
        return saleListPayDetailEntities;
    }

    public void setSaleListPayDetailEntities(List<BusSaleListPayDetailEntity> saleListPayDetailEntities) {
        this.saleListPayDetailEntities = saleListPayDetailEntities;
    }

    public CreateOrderSigleResult(boolean flag, BigDecimal saleListTotal, String msg) {
        this.flag = flag;
        this.saleListTotal = saleListTotal;
        this.msg = msg;
    }
    public CreateOrderSigleResult() {
    }
}
