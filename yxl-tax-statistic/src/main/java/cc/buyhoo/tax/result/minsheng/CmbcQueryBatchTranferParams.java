package cc.buyhoo.tax.result.minsheng;

import lombok.Data;
import lombok.EqualsAndHashCode;


import java.io.Serializable;
import java.util.List;

/**
 * @ClassName CmbcTranferParams
 * @Description 民生银行单笔转账返回结果
 * <AUTHOR>
 * @Date 2025/4/2 19:27
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CmbcQueryBatchTranferParams extends CmbcTranferBaseParams implements Serializable {
    private static final long serialVersionUID = -4804782024440588340L;


    /**
     *   客户端交易的唯一标志
     */
    private String trnId;
    /**
     * 原值返回
     */
    private String insId;

    /**
     * 订单查询结果
     */
    private List<BillInfo> billInfos;

    /**
     * 订单信息
     */
    @Data
    public static class BillInfo implements Serializable {

        private static final long serialVersionUID = 3179963189514571874L;
        /**
         * 交易参考号
         */
        private String transactionReferenceNumber;
        /**
         * 交易状态 状态码说明：10:成功;20:失败
         */
        private String transactionStatus;
        /**
         * 收款账号
         */
        private String receivingAccountNumber;
        /**
         * 收款账户名
         */
        private String receivingAccountName;
        /**
         * 用途
         */
        private String purpose;
        /**
         * 备注
         */
        private String remarks;
        /**
         * 本他行标志
         */
        private String bankFlag="0";
        /**
         * 汇路
         */
        private String remittanceRoute;
        /**
         * 收款行行名
         */
        private String receivingBankName;
        /**
         * 收款行行号
         */
        private String receivingBankCode;
        /**
         * 企业自制凭证号
         */
        private String enterpriseSelfMadeVoucherNumber;
        /**
         * 付款人账号
         */
        private String payerNo;

        /**
         * 付款人名称
         */
        private String payerName;

        /**
         * 付款人行名
         */
        private String payerBankName;
        /**
         * 备用字段 1
         */
        private String spareField1;
        /**
         * 备用字段 2
         */
        private String spareField2;
        /**
         * 备用字段 3
         */
        private String spareField3;
        /**
         * 金额
         */
        private String amount;
        /**
         * 错误信息
         */
        private String errorMsg;


    }
}