package cc.buyhoo.tax.result.busTradeRecord;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 交易流水导出数据
 * @ClassName TradeRecordExcel
 * <AUTHOR>
 * @Date 2024-9-2 08:58:33
 **/
@Data
public class TradeRecordExcel implements Serializable {
    private static final long serialVersionUID = 4148234659399145807L;

    /**
     * 交易时间
     */
    @ColumnWidth(20)
    @ExcelProperty("交易时间")
    private String tradeTime;

    /**
     * 交易金额
     */
    @ColumnWidth(10)
    @ExcelProperty("交易金额（元）")
    private BigDecimal tradeAmount;

    /**
     * 账户余额
     */
    @ColumnWidth(10)
    @ExcelProperty("账户余额（元）")
    private BigDecimal companyBalance;

    /**
     * 交易方向
     */
    @ColumnWidth(10)
    @ExcelProperty("交易方向")
    private String tradeWay;
    /**
     * 收（付）方名称
     */
    @ColumnWidth(50)
    @ExcelProperty("收（付）方名称")
    private String tradeOtherName;

    /**
     * 收（付）方账号
     */
    @ColumnWidth(50)
    @ExcelProperty("收（付）方账号")
    private String tradeOtherAccount;

    /**
     * 银行交易流水号
     */
    @ColumnWidth(30)
    @ExcelProperty("银行交易流水号")
    private String serialNumber;

    /**
     * 摘要
     */
    @ColumnWidth(20)
    @ExcelProperty("摘要")
    private String tradeAbstract;
}
