package cc.buyhoo.tax.result.busShopBank;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 供货商银卡
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 08:52
 **/
@Data
public class BusShopBankDto implements Serializable {
    private static final long serialVersionUID = -5924315062024414209L;

    /**
     *  主键ID
     */
    private Long id;
    /**
     * 商家唯一标识
     */
    private Long shopUnique;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 开户行行号
     */
    private String bankCode;
    /**
     * 开户行所在地（非银联卡必传）
     */
    private String bankCity;
    /**
     * 银行卡号
     */
    private String bankCard;
    /**
     * 所属银行ID，见表bank_list
     */
    private Long bankId;

    /**
     *  总行名称
     */
    private String headBankName;

    /**
     * 法人姓名
     */
    private String legalPerson;
    /**
     * 法人手机号
     */
    private String legalPhone;
    /**
     * 账户类型：1:对公；2:对私；
     */
    private Integer rcvCustType;

}
