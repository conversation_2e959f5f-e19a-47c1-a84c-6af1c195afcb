package cc.buyhoo.tax.result.userArea;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName AreaDictQueryDto
 * <AUTHOR>
 * @Date 2024/11/18 14:51
 */
@Data
public class AreaDictQueryDto implements Serializable {
    /**
     * 行政区代码
     */
    private Long areaCode;
    /**
     * 行政区名称
     */
    private String areaName;
    /**
     * 子行政区
     */
    private List<AreaDictQueryDto> childList;
    /**
     * 是否选中:0-未选中，1-部分选中,2-全选
     */
    private Integer isChecked = 0;
}
