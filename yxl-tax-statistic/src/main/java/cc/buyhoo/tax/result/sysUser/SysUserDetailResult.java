package cc.buyhoo.tax.result.sysUser;

import cc.buyhoo.tax.result.userArea.AreaDictQueryDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户详情
 */
@Data
public class SysUserDetailResult implements Serializable {
    /**
     * 用户ID
     */
    private Long id;
    /**
     * 企业ID
     */
    private Long companyId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 用户类型：1超级管理员2普通管理员
     */
    private Integer userType;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
    /**
     * 备注
     */
    private String remark;

    /**
     * 角色Id
     */
    private Long roleId;
    /**
     * 数据权限树
     */
    private List<AreaDictQueryDto> dataScopeList;
    /**
     * 数据维度
     */
    private Integer level;
}
