package cc.buyhoo.tax.result.indexData;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 营收目标
 * @ClassName RevenueTargetResult
 * <AUTHOR>
 * @Date 2024/8/5 15:38
 **/
@Data
public class RevenueTargetResult implements Serializable {
    private static final long serialVersionUID = -7658072603313453625L;

    /**
     * 申报方式, 1-月，2-季度，3-年
     */
    private String taxType;

    /**
     * 预计完成时间
     */
    private String finishDate;

    /**
     * 纳统开始时间
     */
    private String dateStart;

    /**
     * 纳统结束时间
     */
    private String dateEnd;

    /**
     * 目标金额
     */
    private BigDecimal targetAmount;

    /**
     * 目标金额格式化
     */
    private BigDecimal targetAmountFormat;

    /**
     * 完成金额
     */
    private BigDecimal finishAmount;

    /**
     * 完成金额格式化
     */
    private BigDecimal finishAmountFormat;

    /**
     * 完成比例
     */
    private Integer finishAmountRatio;

    /**
     * 日目标
     */
    private BigDecimal dayTarget;

    /**
     * 日目标格式化
     */
    private BigDecimal dayTargetFormat;

    /**
     * 日完成
     */
    private BigDecimal dayFinish;

    /**
     * 日完成格式化
     */
    private BigDecimal dayFinishFormat;

    /**
     * 日完成占比
     */
    private Integer dayFinishRatio;

    /**
     * 月目标
     */
    private BigDecimal monthTarget;

    /**
     * 月目标格式化
     */
    private BigDecimal monthTargetFormat;

    /**
     * 季度目标
     */
    private BigDecimal quarterTarget;

    /**
     * 季度目标
     */
    private BigDecimal quarterTargetFormat;

    /**
     * 月完成
     */
    private BigDecimal monthFinish;

    /**
     * 月完成格式化
     */
    private BigDecimal monthFinishFormat;
    /**
     * 月完成占比
     */
    private Integer monthFinishRatio;

    /**
     * 季度完成
     */
    private BigDecimal quarterFinish;

    /**
     * 季度完成格式化
     */
    private BigDecimal quarterFinishFormat;

    /**
     * 季度完成占比
     */
    private Integer quarterFinishRatio;
}
