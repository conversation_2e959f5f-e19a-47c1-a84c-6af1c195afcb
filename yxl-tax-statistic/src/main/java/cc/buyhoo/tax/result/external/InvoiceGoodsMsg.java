package cc.buyhoo.tax.result.external;

import lombok.Data;

import java.util.List;

@Data
public class InvoiceGoodsMsg {
    /**
     * 税收分类编码
     */
    private String categoryNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品税目
     */
    private String taxItem;

    /**
     * 商品规格
     */
    private String goodsSpec;

    /**
     * 商品单位
     */
    private String goodsUnit;

    /**
     * 税率
     */
    private String taxRate;

    private List<String> goodsBarcodeList;
}
