package cc.buyhoo.tax.result.busTaxRecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 申报记录
 * @ClassName BusTaxRecordDto
 * <AUTHOR>
 * @Date 2023/8/11 11:33
 **/
@Data
public class BusTaxRecordDto implements Serializable {
    private static final long serialVersionUID = 3350765373569016235L;

    private Long id;
    /**
     * 企业ID
     */
    private Long companyId;
    /**
     * 纳统类型,1-按月,2-按季度,2-按年
     */
    private String taxType;
    /**
     * 申报金额
     */
    private BigDecimal targetAmount;
    /**
     * 营业金额
     */
    private BigDecimal taxAmount;
    /**
     * 申报日期
     */
    private String taxDate;
    /**
     * 申报日期
     */
    private String taxDateView;
    /**
     * 申报状态：0-未完成，1-已完成
     */
    private String taxStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改人
     */
    private String modifyUserName;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

}
