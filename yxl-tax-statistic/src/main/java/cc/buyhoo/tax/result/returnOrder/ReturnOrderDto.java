package cc.buyhoo.tax.result.returnOrder;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReturnOrderDto implements Serializable {
    private Long id;
    /**
     * 入库单号
     */
    private String orderNo;
    /**
     * 入库金额
     */
    private BigDecimal totalMoney;
    /**
     * 在线支付金额
     */
    private BigDecimal onlineMoney;
    /**
     * 现金支付金额
     */
    private BigDecimal cashMoney;

    /**
     * 入库时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;
}
