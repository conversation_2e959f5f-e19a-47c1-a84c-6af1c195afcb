package cc.buyhoo.tax.result.busShopBill;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * @ClassName BusShopBillDto
 * <AUTHOR>
 * @Date 2023/7/27 18:21
 **/
@Data
public class BusShopBillExcel implements Serializable {
    private static final long serialVersionUID = 7223097441750157775L;

    @ColumnWidth(15)
    @ExcelProperty("供应商编码")
    private String shopUnique;

    @ColumnWidth(30)
    @ExcelProperty("供货商名称")
    private String shopName;

    @ColumnWidth(13)
    @ExcelProperty("手机号")
    private String shopPhone;

    @ColumnWidth(30)
    @ExcelProperty("开户行名称")
    private String bankName;

    @ColumnWidth(18)
    @ExcelProperty("银行卡号")
    private String bankCard;

    @ColumnWidth(20)
    @ExcelProperty("待结算金额(元)")
    private BigDecimal unsettledAmount;

    @ColumnWidth(30)
    @ExcelProperty("企业名称")
    private String companyName;

    @ColumnWidth(30)
    @ExcelProperty("企业开户行")
    private String companyBankName;

    @ColumnWidth(18)
    @ExcelProperty("企业对公账号")
    private String companyBankCard;

    @ColumnWidth(12)
    @ExcelProperty(value = "结算状态", converter = SettledStatusConverter.class)
    private String settledStatus = "0";
}
