package cc.buyhoo.tax.result.goodsCateogry;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
*  商品分类返回值
* @ClassName BusGoodsCategory
* <AUTHOR> 
* @Date 2023-07-26
**/
@Data
public class BusGoodsCategoryDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 所属父类ID
	 */
	private Long parentId;

	/**
	 * 祖籍IDs
	 */
	private String ancestors;

	/**
	 * 分类名称
	 */
	private String categoryName;

	/**
	 * 启用状态，0-停用，1-启用
	 */
	private Integer enableStatus;

	/**
	 * 所属企业ID
	 */
	private Long companyId;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建人ID
	 */
	private String createUser;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
	private Date createTime;

	/**
	 * 修改人ID
	 */
	private String modifyUser;

	/**
	 * 分类类型:1默认分类2普通分类
	 */
	private Integer categoryType;

	/**
	 * 税目
	 */
	private String categoryNo;

	/**
	 * 开票商品名称
	 */
	private String goodsName;

	/**
	 * 开票税率
	 */
	private String taxRate;

	/**
	 * 单位
	 */
	private String unit;

	/**
	 * 修改时间
	 */
	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
	private Date modifyTime;

	@JsonProperty(value = "isDisabled")
	private boolean isDisabled = false;

	/**
	 * 子类列表
	 */
	private List<BusGoodsCategoryDto> children = Collections.emptyList();
}