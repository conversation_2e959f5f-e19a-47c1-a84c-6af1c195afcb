package cc.buyhoo.tax.result.busContract;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同返回值
 * @ClassName BusContractDto
 * <AUTHOR>
 * @Date 2024/7/13 11:14
 **/
@Data
public class BusContractDto implements Serializable {
    private static final long serialVersionUID = 4148234659399145807L;

    /**
     * 合同ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 合同编码
     */
    private String contractNo;

    /**
     * 商户编码
     */
    private Long shopUnique;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同类型：0-默认，1-销售合同，2-采购合同，3-联营合同，4-其它
     */
    private Integer contractType;

    /**
     * 合同金额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 合同状态：0-草拟，1-待审，2-已签署，3-执行中，4-已完成，5-终止
     */
    private Integer contractStatus;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date startTime;

    /**
     * 合同结束时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date endTime;

    /**
     * 合同签订日期
     */
    private String signTime;

    /**
     * 关联单号,多个逗号(,)分割
     */
    private String orderNo;

    /**
     * 附件地址
     */
    private String fileUrl;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 修改人
     */
    private Long modifyUser;
}
