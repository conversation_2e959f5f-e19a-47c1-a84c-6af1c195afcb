package cc.buyhoo.tax.result.busGoods;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品返回值
 * @ClassName BusGoodsDto
 * <AUTHOR>
 * @Date 2023/7/29 18:04
 **/
@Data
public class BusGoodsDto implements Serializable {
    private static final long serialVersionUID = 8286973066210749641L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 百货端商品ID
     */
    private Long goodsId;

    /**
     * 供应商编码
     */
    private Long shopUnique;

    /**
     * 供应商名称
     */
    private String shopUniqueName;

    /**
     * 商品编码
     */
    private String goodsBarcode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品别名
     */
    private String goodsAlias;

    /**
     * 商品进价
     */
    private BigDecimal goodsInPrice;

    /**
     * 商品售价
     */
    private BigDecimal goodsSalePrice;

    /**
     * 商品线上售价
     */
    private BigDecimal goodsWebSalePrice;

    /**
     * 商品保质天数
     */
    private Integer goodsLife;

    /**
     * 商品规格
     */
    private String goodsStandard;

    /**
     * 商品计价单位
     */
    private String goodsUnit;

    /**
     * 一级分类ID
     */
    private Long categoryId;
    /**
     * 一级分类ID
     */
    private String categoryName;

    /**
     * 二级分类ID
     */
    private Long categoryTwoId;

    /**
     * 二级分类ID
     */
    private String categoryTwoName;

    /**
     * 备注
     */
    private String remark;

}
