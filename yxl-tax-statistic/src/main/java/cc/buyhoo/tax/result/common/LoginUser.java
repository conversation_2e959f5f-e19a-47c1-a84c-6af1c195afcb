package cc.buyhoo.tax.result.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 登录用户信息
 * @ClassName LoginUser
 * <AUTHOR>
 * @Date 2024/8/27 10:40
 **/
@Data
public class LoginUser implements Serializable {
    private static final long serialVersionUID = -2540585622726806800L;

    /**
     * 登录用户ID
     */
    private Long id;
    /**
     * 所属企业ID
     */
    private Long companyId;
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 角色类型
     */
    private Integer roleType;
    /**
     * 用户名
     */
    private String username;
    /**
     * 用户类型：1超级管理员2普通管理员
     */
    private Integer userType;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司类型:1系统平台2总公司3分公司
     */
    private Integer companyType;

    /**
     * 用户权限
     */
    private List<String> permissionList;
}
