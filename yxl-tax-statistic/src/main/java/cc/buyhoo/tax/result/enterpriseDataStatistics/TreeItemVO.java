package cc.buyhoo.tax.result.enterpriseDataStatistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName TreeItemVo
 * @Description 地方
 * <AUTHOR>
 * @Date 2025/6/16 下午2:10
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TreeItemVO implements Serializable {

   /**
 * 节点ID
 */
@JsonFormat(shape = JsonFormat.Shape.STRING)
public String id;

/**
 * 节点父ID
 */
@JsonFormat(shape = JsonFormat.Shape.STRING)
public String parentId;

/**
 * 节点显示内容
 */
public String label;
/**
 * 子节点列表
 */
public List<TreeItemVO> children;

/**
 * 统计信息
 */
public Statistics statistics;

}