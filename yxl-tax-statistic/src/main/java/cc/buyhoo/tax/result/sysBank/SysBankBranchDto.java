package cc.buyhoo.tax.result.sysBank;

import lombok.Data;

import java.io.Serializable;

/**
 * 支行信息
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 13:58
 **/
@Data
public class SysBankBranchDto implements Serializable {

    private static final long serialVersionUID = -2762465702154243745L;
    /**
     * 总行行号
     */
    private String bankCode;
    /**
     * 总行名称
     */
    private String bankName;
    /**
     * 分行行号
     */
    private String branchCode;
    /**
     * 分行名称
     */
    private String branchName;
    /**
     * 地区代码
     */
    private String areaCode;
}
