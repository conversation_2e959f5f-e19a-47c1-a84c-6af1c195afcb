package cc.buyhoo.tax.result.busShop;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * @ClassName BusShopDto
 * <AUTHOR>
 * @Date 2023/7/26 15:57
 **/
@Data
public class BusShopDto implements Serializable {

    private static final long serialVersionUID = -8337359762726006791L;
    private Long id;

    /**
     * 商家唯一标识
     */
    private Long shopUnique;

    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 店铺类型:1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农标准站、简易站、专业站；6宁宇总店、7宁宇分店、8五金店 9学校、10、机关食堂0：其他 11:加油站；12、餐饮店；13、拼团店统一社会信用代码
     */
    private Integer shopType;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String shopPhone;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 区县编码
     */
    private String countyCode;

    /**
     * 乡镇名称
     */
    private String townName;
    /**
     * 村编码
     */
    private String townCode;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * 法人姓名
     */
    private String legalPerson;

    /**
     * 法人手机号
     */
    private String legalPhone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 结算费率
     */
    private BigDecimal serviceFeeRate;

    /**
     * 是否有服务费：0-无，1-有
     */
    private Integer hasServiceFee;

    /**
     * 合同附件地址
     */
    private String contractUrl;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date modifyTime;
    /**
     * 市场ID
     */
    private Long marketId;
    /**
     * 市场名称
     */
    private String marketName;

    /**
     * 已完成金额
     */
    private BigDecimal completedAmount;
    /**
     * 期末预计完成金额
     */
    private BigDecimal expectedCompleteAmount;
    /**
     * 合作方式
     */
    private Integer cooperateType;
    /**
     * 合作方式
     */
    private String cooperateTypeDesc;
    /**
     * 本月累计流水
     */
    private BigDecimal monthAccumulatedRevenue;
    /**
     * 本月联营订单总额
     */
    private BigDecimal jointVentureMonthAmount;

    /**
     * 子账簿账号
     */
    private String subAccNo;


    /**
     * 合作商成本占比
     */
    private BigDecimal cooperatorCostProportion;

    /**
     * 供货商编号
     */
    private Long supplierNo;

    /**
     * 店铺性质（0-一般纳税人，1-个体工商户，2-小规模纳税人，3-）
     */
    private Integer shopNature;

    /**
     * 店铺性质（0-一般纳税人，1-个体工商户，2-小规模纳税人，3-）
     */
    private String shopNatureDesc;
}
