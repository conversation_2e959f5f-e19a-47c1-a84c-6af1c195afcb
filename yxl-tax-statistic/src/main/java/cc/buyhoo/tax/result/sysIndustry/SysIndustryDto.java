package cc.buyhoo.tax.result.sysIndustry;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 行业列表返回值
 * @ClassName SysIndustryDto
 * <AUTHOR>
 * @Date 2024/8/23 16:09
 **/
@Data
public class SysIndustryDto implements Serializable {
    private static final long serialVersionUID = -4714247011801511540L;
    /**
     * 行业ID
     */
    private Long id;

    /**
     * 行业名称
     */
    private String industryName;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
