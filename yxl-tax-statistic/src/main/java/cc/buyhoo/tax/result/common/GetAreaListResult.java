package cc.buyhoo.tax.result.common;

import cc.buyhoo.tax.entity.common.AreaEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetAreaListResult implements Serializable {
    public static final long serialVersionUID = 1L;
    private List<AreaEntity> areaList;
}
