package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusInvoiceStaffEntity;
import cc.buyhoo.tax.entity.invoice.EleUserLoginData;
import cc.buyhoo.tax.entity.invoice.EleUserLoginResult;
import cc.buyhoo.tax.entity.invoice.RpaAuthStatusData;
import cc.buyhoo.tax.entity.invoice.RpaQrCodeData;
import cc.buyhoo.tax.params.invoice.*;
import cc.buyhoo.tax.result.invoice.QueryInvoiceStaffListResult;

public interface BusInvoiceStaffService{

    /**
     * 获取员工实名认证状态
     * @param params
     * @return
     */
    public Result<RpaAuthStatusData> rpaAuthStatus(RpaAuthStatusParams params);
    /**
     * 获取实名认证二维码
     * @param params
     * @return
     */
    public Result<RpaQrCodeData> rpaQrCode(RpaQrCodeParams params);
    /**
     * 登录全电账号
     * 所有登录步骤都走一个接口
     * @param params
     * @return
     */
    public Result<EleUserLoginData> eleUserLogin(EleUserLoginParams params);
    /**
     * 更新或删除员工信息
     * @param params
     * @return
     */
    public Result<Void> updateInvoiceStaff(UpdateInvoiceStaffParams params);
    /**
     * 添加新的员工信息
     * @param params
     * @return
     */
    public Result<BusInvoiceStaffEntity> saveInvoiceStaff(SaveInvoiceStaffParams params);
    /**
     * 获取指定员工的信息
     * @param params
     * @return
     */
    public Result<BusInvoiceStaffEntity> queryInvoiceStaffById(QueryInvoiceStaffByIdParams params);
    /**
     * 获取指定店铺的员工列表信息
     * @param params
     * @return
     */
    public Result<QueryInvoiceStaffListResult> queryInvoiceStaffList(QueryInvoiceStaffListParams params);

}
