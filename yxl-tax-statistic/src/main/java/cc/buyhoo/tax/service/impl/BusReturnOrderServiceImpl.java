package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusReturnOrderMapper;
import cc.buyhoo.tax.entity.BusReturnOrderEntity;
import cc.buyhoo.tax.params.returnOrder.ReturnOrderParams;
import cc.buyhoo.tax.result.returnOrder.ReturnOrderDto;
import cc.buyhoo.tax.result.returnOrder.ReturnOrderListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusReturnOrderService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BusReturnOrderServiceImpl extends BaseService implements BusReturnOrderService {

    private final BusReturnOrderMapper busReturnOrderMapper;

    /**
     * 退货单列表
     * @param params
     * @return
     */
    @Override
    public Result<ReturnOrderListResult> pageList(ReturnOrderParams params) {
        LambdaQueryWrapper<BusReturnOrderEntity> returnWrapper = new LambdaQueryWrapper<>();
        returnWrapper.eq(BusReturnOrderEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getOrderNo())) {
            returnWrapper.like(BusReturnOrderEntity::getOrderNo,params.getOrderNo());
        }
        if (ObjectUtil.isNotEmpty(params.getCreateTime())) {
            returnWrapper.between(BusReturnOrderEntity::getCreateTime,params.getCreateTime()[0],params.getCreateTime()[1]);
        }
        returnWrapper.orderByDesc(BusReturnOrderEntity::getId);
        PageUtils.startPage(params);
        List<BusReturnOrderEntity> returnList = busReturnOrderMapper.selectList(returnWrapper);
        List<ReturnOrderDto> dtoList = BeanUtils.copyList(returnList, ReturnOrderDto.class);

        return convertPageData(returnList,dtoList,ReturnOrderListResult.class,params);
    }
}
