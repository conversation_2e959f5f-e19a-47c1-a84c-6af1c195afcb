package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.StringUtils;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.DisassembleStatusEnum;
import cc.buyhoo.tax.params.disassembleList.QueryDisassembleDetailListParams;
import cc.buyhoo.tax.params.disassembleList.QueryDisassembleListParams;
import cc.buyhoo.tax.params.disassembleList.RetryDisassembleParams;
import cc.buyhoo.tax.result.disassembleList.DisassembleDetailListDto;
import cc.buyhoo.tax.result.disassembleList.DisassembleListDto;
import cc.buyhoo.tax.result.disassembleList.QueryDisassembleDetailListResult;
import cc.buyhoo.tax.result.disassembleList.QueryDisassembleListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusSaleListService;
import cc.buyhoo.tax.service.DisassembleListService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DisassembleListServiceImpl extends BaseService implements DisassembleListService {

    @Resource
    private DisassembleListMapper disassembleListMapper;

    @Resource
    private BusSaleListMapper busSaleListMapper;

    @Resource
    private BusSaleListService busSaleListService;

    @Resource
    private BusSaleListDetailMapper busSaleListDetailMapper;

    @Resource
    private BusSaleListPayDetailMapper busSaleListPayDetailMapper;


    /**
     * 查询拆单任务列表
     * @param params 查询参数
     * @return
     */
    public Result<QueryDisassembleListResult> queryDisassembleList(QueryDisassembleListParams params) {
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        PageUtils.startPage(params);
        LambdaQueryWrapper<DisassembleListEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisassembleListEntity::getCompanyId, companyId);
        queryWrapper.between(ObjectUtil.isNotEmpty(params.getStartDate()), DisassembleListEntity::getCreateTime, params.getStartDate(), params.getEndDate());
        if (ObjectUtil.isNotEmpty(params.getDisassembleStatus())) {
            queryWrapper.eq(DisassembleListEntity::getDisassembleStatus, params.getDisassembleStatus());
        }
        if (ObjectUtil.isNotEmpty(params.getSaleListUnique())) {
            queryWrapper.like(DisassembleListEntity::getSaleListUnique, StringUtils.join("%",params.getSaleListUnique(), "%"));
        }
        queryWrapper.between(ObjectUtil.isNotEmpty(params.getStartDate()), DisassembleListEntity::getCreateTime, params.getStartDate(), params.getEndDate());
        queryWrapper.orderByDesc(DisassembleListEntity::getCreateTime);

        List<DisassembleListEntity> list = disassembleListMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(list)) {
            List<DisassembleListDto> dtoList = list.stream().map(v -> {
                DisassembleListDto dto = new DisassembleListDto();
                BeanUtils.copyProperties(v, dto);
                dto.setDisassembleStatusValue(v.getDisassembleStatus());
                dto.setDisassembleStatus(DisassembleStatusEnum.getName(v.getDisassembleStatus()));
                return dto;
            }).collect(Collectors.toList());
            return convertPageData(list, dtoList, QueryDisassembleListResult.class, params);
        }
        QueryDisassembleListResult result = new QueryDisassembleListResult();
        result.setTotal(0L);
        result.setRows(CollectionUtil.newArrayList());
        result.setPageIndex(params.getPageIndex());
        result.setPageIndex(params.getPageSize());
        return Result.ok(result);
    }

    /**
     * 查询拆单任务详情列表
     * @param params
     * @return
     */
    public Result<QueryDisassembleDetailListResult> queryDisassembleDetailList(QueryDisassembleDetailListParams params) {
        Long companyId = SatokenUtil.getLoginUserCompanyId();

        LambdaQueryWrapper<DisassembleListEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisassembleListEntity::getCompanyId, companyId);
        queryWrapper.eq(DisassembleListEntity::getSaleListUnique, params.getSaleListUnique());

        DisassembleListEntity entity = disassembleListMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_NOT_EXIST);
        }

        PageUtils.startPage(params);

        LambdaQueryWrapper<BusSaleListEntity> saleListQueryWrapper = new LambdaQueryWrapper<>();
        saleListQueryWrapper.eq(BusSaleListEntity::getCompanyId, companyId);
        saleListQueryWrapper.eq(BusSaleListEntity::getParentListUnique, params.getSaleListUnique());

        List<BusSaleListEntity> saleLists = busSaleListMapper.selectList(saleListQueryWrapper);

        Map<String, BigDecimal> goodsCountMap = new HashMap<>();
        Map<String, String> payTypeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(saleLists)) {
            Set<String> shopUniques = saleLists.stream().map(BusSaleListEntity::getSaleListUnique).collect(Collectors.toSet());
            QueryWrapper<BusSaleListDetailEntity> detailQuery = new QueryWrapper<>();
            detailQuery.select("sale_list_unique, sum(sale_list_detail_count) as count");
            detailQuery.lambda().eq(BusSaleListDetailEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId())
                    .in(BusSaleListDetailEntity::getSaleListUnique, shopUniques)
                    .groupBy(BusSaleListDetailEntity::getSaleListUnique);
            List<Map<String, Object>> countMap = busSaleListDetailMapper.selectMaps(detailQuery);
            for (Map<String, Object> m : countMap) {
                String shopUnique = (String) m.get("sale_list_unique");
                BigDecimal count = (BigDecimal) m.get("count");
                goodsCountMap.put(shopUnique, count);
            }

            QueryWrapper<BusSaleListPayDetailEntity> payDetailQuery = new QueryWrapper<>();
            payDetailQuery.select("DISTINCT sale_list_unique, server_type, pay_method");
            payDetailQuery.lambda().eq(BusSaleListPayDetailEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId())
                    .in(BusSaleListPayDetailEntity::getSaleListUnique, shopUniques);
            List<BusSaleListPayDetailEntity> payDetailList = busSaleListPayDetailMapper.selectList(payDetailQuery);
            for (BusSaleListPayDetailEntity payDetailEntity : payDetailList) {
                String payType = convertPayType(payDetailEntity.getPayMethod());
                if (StrUtil.isNotBlank(payType)) {
                    String v = payTypeMap.get(payDetailEntity.getSaleListUnique());
                    if (StrUtil.isNotBlank(v)) {
                        payTypeMap.put(payDetailEntity.getSaleListUnique(), v + "," + payType);
                    } else {
                        payTypeMap.put(payDetailEntity.getSaleListUnique(), payType);
                    }
                }
            }
        }

        List<DisassembleDetailListDto> dtoList = saleLists.stream().map(v -> {

            DisassembleDetailListDto dto = new DisassembleDetailListDto();
            BeanUtils.copyProperties(v, dto);
            dto.setGoodsCount(goodsCountMap.get(v.getSaleListUnique()));
            dto.setPayTypeName(payTypeMap.get(v.getSaleListUnique()));
            return dto;
        }).collect(Collectors.toList());


        Result<QueryDisassembleDetailListResult> res = convertPageData(saleLists, dtoList, QueryDisassembleDetailListResult.class, params);

        //数据存在，转换成系统所需的
        QueryDisassembleDetailListResult result = res.getData();
        result.setSaleListUnique(entity.getSaleListUnique());
        result.setDisassembleCount(entity.getDisassembleCount());
        result.setMinAmountActual(entity.getMinAmountActual());
        result.setMaxAmountActual(entity.getMaxAmountActual());

        return Result.ok(result);
    }

    /**
     * 支付方式转换
     * @param payMethod
     * @return
     */
    private String convertPayType(Integer payMethod) {
        String resp = "在线支付";
        switch (payMethod) {
            case 1:
                resp = "现金";
                break;
            case 2:
                resp = "支付宝";
                break;
            case 3:
                resp = "微信";
                break;
            case 4:
                resp = "银行卡";
                break;
            case 5:
                resp = "储值卡";
                break;
            case 6:
                resp = "美团外卖";
                break;
            case 7:
                resp = "饿了么外卖";
                break;
            case 9:
                resp = "免密支付";
                break;
            case 10:
                resp = "积分兑换";
                break;
            case 11:
                resp = "百货豆";
                break;
            case 12:
                resp = "拉卡拉";
                break;
            case 13:
                resp = "易通支付";
                break;
            case 15:
                resp = "银联支付";
                break;
            case 16:
                resp = "合利宝";
                break;
            case 17:
                resp = "银行转账";
                break;
        }
        return resp;
    }

    /**
     * 重新拆单
     * @param params
     * @return
     */
    public Result<Void> retryDisassemble(RetryDisassembleParams params) {
        //获取当前拆单的记录，如果不是拆单失败，则不允许重新拆单
        LambdaQueryWrapper<DisassembleListEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisassembleListEntity::getSaleListUnique, params.getSaleListUnique());
        DisassembleListEntity entity = disassembleListMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_NOT_EXIST);
        }

        if (entity.getDisassembleStatus() != DisassembleStatusEnum.DISASSEMBLE_FAIL.getValue()) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "当前拆单任务不是失败状态，不允许重新拆单");
        }

        LambdaQueryWrapper<BusSaleListEntity> saleListQueryWrapper = new LambdaQueryWrapper<>();
        saleListQueryWrapper.eq(BusSaleListEntity::getSaleListUnique, params.getSaleListUnique());
        BusSaleListEntity saleListEntity = busSaleListMapper.selectOne(saleListQueryWrapper);

        if (ObjectUtil.isEmpty(saleListEntity)) {
            entity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_FAIL.getValue());
            disassembleListMapper.updateById(entity);
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "订单不存在");
        }

        //将拆单信息修改为进行中
        entity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_ING.getValue());
        disassembleListMapper.updateById(entity);

        Long userId = SatokenUtil.getLoginUserId();
        //此处异步执行，直接返回结果即可
        ThreadUtil.execAsync( () -> {
            try {
                busSaleListService.disassembleOrderSub(entity.getMinAmountSet(), entity.getMaxAmountSet(), saleListEntity.getSaleListActuallyReceived(), saleListEntity.getProfitTotal(), saleListEntity.getShopUnique(), saleListEntity.getSaleListUnique());
            } catch (Exception e) {
                log.error("-----[订单拆解异常]-------------处理订单异常：{}-------------------", e);
                try {
                    //将数据修改为失败状态
                    LambdaUpdateWrapper<DisassembleListEntity> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(DisassembleListEntity::getSaleListUnique, params.getSaleListUnique());
                    DisassembleListEntity updateEntity = new DisassembleListEntity();
                    updateEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_FAIL.getValue());
                    updateEntity.setDisassmebleRemarks(e.getMessage());
                    updateEntity.setModifyUser(userId);
                    updateEntity.setModifyTime(DateUtil.date());
                    updateEntity.setSaleListUnique(params.getSaleListUnique());
                    disassembleListMapper.update(updateEntity, updateWrapper);
                } catch (Exception e1) {
                    log.error("-----[订单拆解异常后，处理订单状态异常]-------------处理订单异常：{}-------------------", e1);
                }
            }
        });

        return Result.ok();
    }
}
