package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.common.AreaEntity;
import cc.buyhoo.tax.params.common.GetAreaListSubParam;
import cc.buyhoo.tax.result.common.GetAreaListResult;
import cc.buyhoo.tax.result.common.GetAreaListSubResult;

import java.util.List;

public interface CommonService {
    /**
     * 获取省市区县信息列表
     * @return
     */
    public Result<List<AreaEntity>> getAreaListResult();

    public Result<List<AreaEntity>> getAreaListSub(GetAreaListSubParam getAreaListSubParam);
}
