package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusShopBillDetailMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusShopBillDetailEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.params.busShopBill.BusShopBillDetailParams;
import cc.buyhoo.tax.result.busShopBill.BusShopBillDetailDto;
import cc.buyhoo.tax.result.busShopBill.BusShopBillDetailPageResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopBillDetailService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 供应商账单明细管理
 * @ClassName BusShopBillDetailServiceImpl
 * <AUTHOR>
 * @Date 2023/7/27 18:14
 **/
@Slf4j
@Service
public class BusShopBillDetailServiceImpl extends BaseService implements BusShopBillDetailService {
    @Resource
    private BusShopBillDetailMapper busShopBillDetailMapper;
    @Resource
    private BusShopMapper busShopMapper;

    @Override
    public Result<BusShopBillDetailPageResult> pageList(BusShopBillDetailParams params) {
        //账单详情
        LambdaQueryWrapper<BusShopBillDetailEntity> detailWrapper = handleQueryWrapepr(params);
        PageUtils.startPage(params);
        List<BusShopBillDetailEntity> detailList = busShopBillDetailMapper.selectList(detailWrapper);

        List<BusShopBillDetailDto> dtoList = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(detailList)) {
            Set<Long> userIds = detailList.stream().map(BusShopBillDetailEntity::getModifyUser).collect(Collectors.toSet());
            //员工信息
            Map<Long, String> userIdAndNameMap = handleSysUserIdName(userIds);
            //店铺信息
            Set<Long> shopUniques = detailList.stream().map(BusShopBillDetailEntity::getShopUnique).collect(Collectors.toSet());
            LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
            shopWrapper.in(BusShopEntity::getShopUnique,shopUniques);
            List<BusShopEntity> shopList = busShopMapper.selectList(shopWrapper);
            Map<Long, String> shopUniqueNameMap = shopList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, BusShopEntity::getShopName));

            for (BusShopBillDetailEntity d : detailList) {
                BusShopBillDetailDto dto = new BusShopBillDetailDto();
                BeanUtil.copyProperties(d,dto);
//                BeanUtils.copy(d,dto);
                dto.setShopName(shopUniqueNameMap.get(d.getShopUnique()));
                Map<String, Object> convertMap = convertSettledStatus(d.getSettledStatus());
                dto.setSettledStatus(MapUtils.getString(convertMap,"settledStatus"));
                dto.setTransferStatus(MapUtils.getInteger(convertMap,"transferStatus"));
                dto.setModifyUser(userIdAndNameMap.get(d.getModifyUser()));

                dtoList.add(dto);
            }
        }

        return convertPageData(detailList,dtoList,BusShopBillDetailPageResult.class,params);
    }

    /**
     * 构建查询参数
     * @param params
     * @return
     */
    private LambdaQueryWrapper<BusShopBillDetailEntity> handleQueryWrapepr(BusShopBillDetailParams params) {
        LambdaQueryWrapper<BusShopBillDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(BusShopBillDetailEntity::getBillId,params.getBillId());
        if (ObjectUtil.isNotEmpty(params.getSettledType())) {
            detailWrapper.eq(BusShopBillDetailEntity::getSettledType,params.getSettledType());
        }
        if (ObjectUtil.isNotEmpty(params.getCreateTime())) {
            String[] createTime = params.getCreateTime();
            detailWrapper.between(BusShopBillDetailEntity::getCreateTime,createTime[0],createTime[1]);
        }
        if (ObjectUtil.isNotEmpty(params.getTransferStatus())) {
            List<String> statusList = new ArrayList<>();
            if (params.getTransferStatus() == 1) { //转账成功
                statusList.add("FINS");
            } else if (params.getTransferStatus() == 2) { //转账中
                statusList.add("YTJ");
                statusList.add("AUT");
                statusList.add("NTE");
                statusList.add("BNK");
                statusList.add("OPR");
                statusList.add("APW");
                statusList.add("WRF");
            } else if (params.getTransferStatus() == 3) { //转账失败
                statusList.add("FINF");
                statusList.add("FINB");
                statusList.add("FINR");
                statusList.add("FIND");
                statusList.add("FINC");
                statusList.add("FINU");
            }
            detailWrapper.in(BusShopBillDetailEntity::getSettledStatus,statusList);
        }
        detailWrapper.orderByDesc(BusShopBillDetailEntity::getId);
        return detailWrapper;
    }

    /**
     * 结算状态转换
     * @param settledStatus
     * @return
     */
    private Map<String,Object> convertSettledStatus(String settledStatus) {
        String resp = "";
        // 1转账成功、2转账中、3转账失败
        Integer transferStatus = -1;
        switch (settledStatus) {
            case "YTJ":
                resp = "已提交";
                transferStatus = 2;
                break;
            case "AUT":
                resp = "等待审批";
                transferStatus = 2;
                break;
            case "NTE":
                resp = "终审完毕";
                transferStatus = 2;
                break;
            case "BNK":
                resp = "银行处理中";
                transferStatus = 2;
                break;
            case "FINS":
                resp = "银行支付成功";
                transferStatus = 1;
                break;
            case "FINF":
                resp = "银行支付失败";
                transferStatus = 3;
                break;
            case "FINB":
                resp = "银行支付被退票";
                transferStatus = 3;
                break;
            case "FINR":
                resp = "企业审批否决";
                transferStatus = 3;
                break;
            case "FIND":
                resp = "企业过期不审批";
                transferStatus = 3;
                break;
            case "FINC":
                resp = "企业撤销";
                transferStatus = 3;
                break;
            case "FINU":
                resp = "银行挂账";
                transferStatus = 3;
                break;
            case "OPR":
                resp = "数据接收中";
                transferStatus = 2;
                break;
            case "APW":
                resp = "银行人工审批";
                transferStatus = 2;
                break;
            case "WRF":
                resp = "需要人工介入处理";
                transferStatus = 2;
                break;
        }

        Map<String,Object> map = new HashMap<>();
        map.put("settledStatus",resp);
        map.put("transferStatus",transferStatus);
        return map;
    }
}
