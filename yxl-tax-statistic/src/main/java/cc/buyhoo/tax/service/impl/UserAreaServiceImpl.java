package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.Cnarea2023Mapper;
import cc.buyhoo.tax.dao.StbusDataLevelMapper;
import cc.buyhoo.tax.dao.SysMarketMapper;
import cc.buyhoo.tax.entity.Cnarea2023Entity;
import cc.buyhoo.tax.entity.StbusDataLevelEntity;
import cc.buyhoo.tax.entity.SysMarketEntity;
import cc.buyhoo.tax.enums.Cnarea2023LevelEnum;
import cc.buyhoo.tax.enums.LevelEnum;
import cc.buyhoo.tax.params.userArea.AreaDictQueryParams;
import cc.buyhoo.tax.result.userArea.AreaDictQueryDto;
import cc.buyhoo.tax.result.userArea.AreaDictQueryList;
import cc.buyhoo.tax.result.userArea.DataLevelQueryDto;
import cc.buyhoo.tax.result.userArea.DataLevelQueryResult;
import cc.buyhoo.tax.service.UserAreaService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName UserAreaServiceImpl
 * <AUTHOR>
 * @Date 2024/11/18 16:03
 */
@Service
@AllArgsConstructor
public class UserAreaServiceImpl implements UserAreaService {
    @Resource
    private Cnarea2023Mapper cnarea2023Mapper;
    @Resource
    private StbusDataLevelMapper stbusDataLevelMapper;
    @Resource
    private SysMarketMapper sysMarketMapper;

    @Override
    public Result<DataLevelQueryResult> queryDataLevel() {
        DataLevelQueryResult result = new DataLevelQueryResult();
        result.setList(Collections.EMPTY_LIST);
        LambdaQueryWrapper<StbusDataLevelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.notIn(StbusDataLevelEntity::getLevel, Arrays.asList(LevelEnum.COMPANY.getCode()));
        List<StbusDataLevelEntity> list = stbusDataLevelMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            List<DataLevelQueryDto> dtoList = list.stream().map(v -> {
                DataLevelQueryDto dto = new DataLevelQueryDto();
                BeanUtil.copyProperties(v, dto);
                return dto;
            }).collect(Collectors.toList());
            result.setList(dtoList);
        }
        return Result.ok(result);
    }

    /**
     * 查询地区字典
     * @return
     */
    @Override
    public Result<AreaDictQueryList> queryAreaDict() {
        AreaDictQueryList result = new AreaDictQueryList();
        LambdaQueryWrapper<Cnarea2023Entity> cnarea2023LambdaQueryWrapper = new LambdaQueryWrapper<>();
        cnarea2023LambdaQueryWrapper.in(Cnarea2023Entity::getLevel, new ArrayList<>(Arrays.asList(Cnarea2023LevelEnum.COUNTRY.getValue()
                , Cnarea2023LevelEnum.PROVINCE.getValue(), Cnarea2023LevelEnum.CITY.getValue(), Cnarea2023LevelEnum.COUNTY.getValue())));
        cnarea2023LambdaQueryWrapper.select(Cnarea2023Entity::getShortAreaCode, Cnarea2023Entity::getShortParentCode, Cnarea2023Entity::getName,
                Cnarea2023Entity::getLevel);
        cnarea2023LambdaQueryWrapper.notLikeRight(Cnarea2023Entity::getAreaCode, "71");
        cnarea2023LambdaQueryWrapper.notLikeRight(Cnarea2023Entity::getAreaCode, "81");
        cnarea2023LambdaQueryWrapper.notLikeRight(Cnarea2023Entity::getAreaCode, "82");
        List<Cnarea2023Entity> cnarea2023EntityList = cnarea2023Mapper.selectList(cnarea2023LambdaQueryWrapper);
        List<SysMarketEntity> sysMarketList = sysMarketMapper.selectList(new LambdaQueryWrapper<>());

        List<Cnarea2023Entity> countryEntityList = ObjectUtil.isNotEmpty(cnarea2023EntityList) ?
                cnarea2023EntityList.stream().filter(v -> ObjectUtil.equals(Cnarea2023LevelEnum.COUNTRY.getValue(), v.getLevel())).collect(Collectors.toList()) : new ArrayList<>();
        List<Cnarea2023Entity> provinceEntityList = ObjectUtil.isNotEmpty(cnarea2023EntityList) ?
                cnarea2023EntityList.stream().filter(v -> ObjectUtil.equals(Cnarea2023LevelEnum.PROVINCE.getValue(), v.getLevel())).collect(Collectors.toList()) : new ArrayList<>();
        List<Cnarea2023Entity> cityEntityList = ObjectUtil.isNotEmpty(cnarea2023EntityList) ?
                cnarea2023EntityList.stream().filter(v -> ObjectUtil.equals(Cnarea2023LevelEnum.CITY.getValue(), v.getLevel())).collect(Collectors.toList()) : new ArrayList<>();
        List<Cnarea2023Entity> countyEntityList = ObjectUtil.isNotEmpty(cnarea2023EntityList) ?
                cnarea2023EntityList.stream().filter(v -> ObjectUtil.equals(Cnarea2023LevelEnum.COUNTY.getValue(), v.getLevel())).collect(Collectors.toList()) : new ArrayList<>();
        List<AreaDictQueryDto> list = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(countryEntityList)) {
            list = countryEntityList.stream().map(v -> {
                AreaDictQueryDto countrtAreaDictQueryDto = new AreaDictQueryDto();
                countrtAreaDictQueryDto.setAreaCode(v.getShortAreaCode());
                countrtAreaDictQueryDto.setAreaName(v.getName());
                countrtAreaDictQueryDto.setChildList(new ArrayList<>());
                if (ObjectUtil.isNotEmpty(provinceEntityList)) {
                    List<AreaDictQueryDto> provinceList = provinceEntityList.stream().map(province -> {
                        AreaDictQueryDto provinceAreaDictQueryDto = new AreaDictQueryDto();
                        provinceAreaDictQueryDto.setAreaCode(province.getShortAreaCode());
                        provinceAreaDictQueryDto.setAreaName(province.getName());
                        provinceAreaDictQueryDto.setChildList(new ArrayList<>());
                        if (ObjectUtil.isNotEmpty(cityEntityList)) {
                            List<AreaDictQueryDto> cityList = cityEntityList.stream().filter(city -> ObjectUtil.equals(province.getShortAreaCode(), city.getShortParentCode())).map(city -> {
                                AreaDictQueryDto cityAreaDictQueryDto = new AreaDictQueryDto();
                                cityAreaDictQueryDto.setAreaCode(city.getShortAreaCode());
                                cityAreaDictQueryDto.setAreaName(city.getName());
                                cityAreaDictQueryDto.setChildList(new ArrayList<>());
                                if (ObjectUtil.isNotEmpty(countyEntityList)) {
                                    List<AreaDictQueryDto> countyList = countyEntityList.stream().filter(county -> ObjectUtil.equals(city.getShortAreaCode(), county.getShortParentCode())).map(county -> {
                                        AreaDictQueryDto countyAreaDictQueryDto = new AreaDictQueryDto();
                                        countyAreaDictQueryDto.setAreaCode(county.getShortAreaCode());
                                        countyAreaDictQueryDto.setAreaName(county.getName());
                                        countyAreaDictQueryDto.setChildList(Collections.EMPTY_LIST);
                                        if (ObjectUtil.isNotEmpty(sysMarketList)) {
                                            List<AreaDictQueryDto> marketList = sysMarketList.stream().filter(market -> ObjectUtil.equals(market.getDistrictId(), county.getShortAreaCode())).map(market -> {
                                                AreaDictQueryDto marketAreaDictQueryDto = new AreaDictQueryDto();
                                                marketAreaDictQueryDto.setAreaCode(market.getId());
                                                marketAreaDictQueryDto.setAreaName(market.getMarketName());
                                                return marketAreaDictQueryDto;
                                            }).collect(Collectors.toList());
                                            countyAreaDictQueryDto.setChildList(marketList);
                                        }
                                        return countyAreaDictQueryDto;
                                    }).collect(Collectors.toList());
                                    cityAreaDictQueryDto.setChildList(countyList);
                                }
                                return cityAreaDictQueryDto;
                            }).collect(Collectors.toList());
                            provinceAreaDictQueryDto.setChildList(cityList);
                        }
                        return provinceAreaDictQueryDto;
                    }).collect(Collectors.toList());
                    countrtAreaDictQueryDto.setChildList(provinceList);
                }
                return countrtAreaDictQueryDto;
            }).collect(Collectors.toList());
        }
        result.setList(list);
        return Result.ok(result);
    }
}
