package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysUser.*;
import cc.buyhoo.tax.params.sysUserRole.SysUserRoleQueryParams;
import cc.buyhoo.tax.result.sysUser.SysUserPageResult;
import cc.buyhoo.tax.result.sysUser.SysUserDetailResult;
import cc.buyhoo.tax.result.sysUser.UserRoleListDto;

import java.util.List;

public interface SysUserService {

    /**
     * 用户列表
     * @param params
     * @return
     */
    public Result<SysUserPageResult> pageList(UserPageParams params);

    /**
     * 新增用户
     * @param params
     * @return
     */
    public Result<Void> addUser(SysUserAddParams params);


    /**
     * 修改用户
     * @param params
     * @return
     */
    public Result<Void> updateUser(SysUserEditParams params);

    /**
     * 用户详情
     * @param id
     * @return
     */
    public Result<SysUserDetailResult> selectById(Long id);

    /**
     * 用户角色列表
     * @return
     */
    public Result<List<UserRoleListDto>> userRoleList(SysUserRoleQueryParams queryParams);

    /**
     * 删除用户
     * @param params
     * @return
     */
    public Result<Void> deleteUser(DeleteUserParams params);

    /**
     * 重置密码
     * @param params
     * @return
     */
    public Result<Void> resetPwd(ResetPwdParams params);

    /**
     * 修改密码
     * @param params
     * @return
     */
    public Result<Void> updatePwd(UpdatePwdParams params);

}
