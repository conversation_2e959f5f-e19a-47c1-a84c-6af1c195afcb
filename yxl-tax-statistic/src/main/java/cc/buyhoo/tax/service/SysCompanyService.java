package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.params.busSetting.SaveBusSettingParams;
import cc.buyhoo.tax.params.busShop.ShopListExportParams;
import cc.buyhoo.tax.params.sysCompany.*;
import cc.buyhoo.tax.result.busSetting.BusSettingDto;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysCompany.SysCompanyDetailResult;
import cc.buyhoo.tax.result.sysCompany.SysCompanyPageResult;
import cc.buyhoo.tax.result.sysCompany.GetShopSettleSettingResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface SysCompanyService {

    /**
     * 企业分页列表
     * @param params
     * @return
     */
    Result<SysCompanyPageResult> pageList(SysCompanyPageParams params);

    /**
     * 新增企业
     * @param params
     * @return
     */
    Result<Void> addSysCompany(SysCompanyAddParams params);

    /**
     * 修改企业
     * @param params
     * @return
     */
    Result<Void> updateSysCompany(SysCompanySaveParams params);

    /**
     * 根据公司ID查询
     * @param id
     * @return
     */
    Result<SysCompanyDetailResult> getCompanyById(Long id);

    /**
     * 企业下拉框选项数据
     *
     * @return
     */
    Result<List<SelectDataDto>> companySelectData(SysCompanyQueryParams queryParams);

    /**
     * 获取企业邀请配置
     * @return
     */
    Result<BusSettingDto> getBusSettings();

    /**
     * 保存纳统设置
     * @param params
     * @return
     */
    Result<Void> saveBusSettings(SaveBusSettingParams params);

    /**
     * 查询银行信息
     * @return
     */
    Result<GetShopSettleSettingResult> getShopSettleSetting();

    /**
     * 保存银行信息
     * @param params
     * @return
     */
    Result<Void> saveShopSettleSetting(SaveShopSettleSettingParams params);

    /**
     * 删除-纳管开户
     */
    Result<Void> deleteById(SysCompanyIdParams params);

    /**
     * 企业下拉框选项数据
     *
     * @return
     */
    Result<List<SelectDataDto>> queryCompanyByMarket(SysCompanyQueryByMarketIdParams params);

    void export(SysCompanyExportParams params, HttpServletResponse response);
}
