package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.SysCompanyErrorEnum;
import cc.buyhoo.tax.enums.SysMenuErrorEnum;
import cc.buyhoo.tax.enums.sys.BuiltInSystemEnum;
import cc.buyhoo.tax.enums.sys.MenuLevelEnum;
import cc.buyhoo.tax.enums.sys.MenuTypeEnum;
import cc.buyhoo.tax.enums.sys.UserTypeEnum;
import cc.buyhoo.tax.params.sysMenu.SysMenuAddParams;
import cc.buyhoo.tax.params.sysMenu.DeleteMenuParams;
import cc.buyhoo.tax.params.sysMenu.MenuListParams;
import cc.buyhoo.tax.params.sysMenu.UpdateMenuAddParams;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.sysMenu.*;
import cc.buyhoo.tax.service.SysMenuService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl implements SysMenuService {

    private final SysMenuMapper sysMenuMapper;
    private final SysUserMapper sysUserMapper;
    private final SysRoleMapper sysRoleMapper;
    private final SysRoleMenuMapper sysRoleMenuMapper;

    /**
     * 菜单列表
     *
     * @return
     */
    @Override
    public Result<RouterMenuListResult> routerList() {
        RouterMenuListResult result = new RouterMenuListResult();

        //查询所有的菜单
        List<SysMenuEntity> menuList = getMenuByUserId();

        if (ObjectUtil.isEmpty(menuList)) {
            result.setPermissionList(new ArrayList<>());
            result.setMenuList(new ArrayList<>());
            return Result.ok(result);
        }
        //参数构建
        //过滤：1目录2菜单
        result.setMenuList(buildMenu(menuList.stream().filter(m -> m.getType().equals(1) || m.getType().equals(2)).collect(Collectors.toList())));
        result.setPermissionList(new ArrayList<>(menuList.stream().filter(m -> ObjectUtil.isNotEmpty(m.getPermission())).map(SysMenuEntity::getPermission).collect(Collectors.toSet())));
        return Result.ok(result);
    }

    /**
     * 菜单管理
     *
     * @return
     */
    @Override
    public Result<MenuListResult> menuList(MenuListParams params) {
        //查询全部的菜单，如果当前用户不是平台就不让查
        if (!SatokenUtil.isPlatform(SatokenUtil.getLoginUser().getCompanyType()))
            throw new BusinessException(SysCompanyErrorEnum.PERMISSION_ERROR);

        LambdaQueryWrapper<SysMenuEntity> menuWrapper = new LambdaQueryWrapper<>();
        menuWrapper.eq(SysMenuEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        menuWrapper.like(StrUtil.isNotBlank(params.getTitle()), SysMenuEntity::getMenuName, params.getTitle());
        menuWrapper.in(ArrayUtil.isNotEmpty(params.getMenuLevel()), SysMenuEntity::getMenuLevel, params.getMenuLevel());
        menuWrapper.orderByAsc(SysMenuEntity::getSort);
        List<SysMenuEntity> menuList = sysMenuMapper.selectList(menuWrapper);
        List<RouterListDto> routerListDtos = buildMenu(menuList);
        MenuListResult result = new MenuListResult();
        result.setMenuList(routerListDtos);
        return Result.ok(result);
    }

    /**
     * 角色管理选择菜单
     *
     * @return
     */
    @Override
    public Result<RoleMenuListResult> roleMenuList() {
        RoleMenuListResult result = new RoleMenuListResult();
        LoginUser loginUser = SatokenUtil.getLoginUser();
        LambdaQueryWrapper<SysMenuEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMenuEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysMenuEntity> menuList = new ArrayList<>();
        if (SatokenUtil.isPlatform(loginUser.getCompanyType()) && SatokenUtil.isSuperAdmin(loginUser.getUserType())) {
            // 超级管理员查询所有
        } else {
            List<SysRoleMenuEntity> list = sysRoleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenuEntity>().eq(SysRoleMenuEntity::getRoleId, loginUser.getRoleId()));
            if (CollectionUtil.isEmpty(list)) {
                result.setMenuList(Collections.EMPTY_LIST);
                return Result.ok(result);
            } else {
                Set<Long> menuIds = list.stream().map(SysRoleMenuEntity::getMenuId).collect(Collectors.toSet());
                queryWrapper.in(SysMenuEntity::getId, menuIds);
            }
        }
        queryWrapper.orderByAsc(SysMenuEntity::getSort, SysMenuEntity::getId);
        menuList = sysMenuMapper.selectList(queryWrapper);
        List<Long> checkedList = menuList.stream().filter(v -> ObjectUtil.equal(BuiltInSystemEnum.YES.getValue(), v.getBuiltInSystem())).map(SysMenuEntity::getId).collect(Collectors.toList());
        result.setMenuList(buildRoleMenu(menuList, true));
        result.setCheckedList(checkedList);
        return Result.ok(result);
    }

    /**
     * 新增菜单
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addMenu(SysMenuAddParams params) {
        //这个与前端的isHide有冲突，需要修改下，现在统一设置为显示
        params.setHidden(2);
        recheckParams(params);
        //保存数据
        SysMenuEntity menu = new SysMenuEntity();
        BeanUtil.copyProperties(params, menu);
        if (ObjectUtil.equal(3, menu.getType())) {
            // 按钮类型  设置menulevel为父级menulevel值
            SysMenuEntity entity = sysMenuMapper.selectById(menu.getParentId());
            if (null != entity) {
                menu.setMenuLevel(entity.getMenuLevel());
            }
        }
        menu.setPath(menu.getComponent());
        menu.setCreateUser(SatokenUtil.getLoginUserId());
        menu.setModifyUser(menu.getCreateUser());
        sysMenuMapper.insert(menu);

        return Result.ok();
    }

    /**
     * 修改菜单
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateMenu(UpdateMenuAddParams params) {

        SysMenuEntity oldMenu = sysMenuMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(oldMenu)) {
            throw new BusinessException(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_EMPTY);
        }
        //这个与前端的isHide有冲突，需要修改下，现在统一设置为显示
        params.setHidden(2);
        recheckParams(params);
        SysMenuEntity upd = new SysMenuEntity();
        BeanUtil.copyProperties(params, upd);
        if (ObjectUtil.notEqual(MenuTypeEnum.BUTTON.getCode(), upd.getType()) && ObjectUtil.notEqual(params.getType(), oldMenu.getType())) {
            List<SysMenuEntity> children = sysMenuMapper.selectList(new LambdaQueryWrapper<SysMenuEntity>().eq(SysMenuEntity::getParentId, params.getId()).eq(SysMenuEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
            if (CollectionUtil.isNotEmpty(children)) {
                children.stream().forEach(v -> {
                    v.setType(params.getType());
                });
                sysMenuMapper.updateBatchById(children);
            }
        } else {
            // 按钮类型  设置menulevel为父级menulevel值
            SysMenuEntity entity = sysMenuMapper.selectById(upd.getParentId());
            if (null != entity) {
                upd.setMenuLevel(entity.getMenuLevel());
            }
        }
        upd.setPath(upd.getComponent());
        upd.setCreateUser(oldMenu.getCreateUser());
        upd.setCreateTime(oldMenu.getCreateTime());
        upd.setModifyUser(SatokenUtil.getLoginUserId());
        sysMenuMapper.updateById(upd);
        return Result.ok();
    }

    /**
     * 菜单新增/修改-查询上级菜单
     *
     * @return
     */
    @Override
    public Result<RoleMenuListResult> queryPreMenu() {
        List<Integer> typelist = Arrays.asList(1, 2);
        List<SysMenuEntity> menuList = handlePlatformRoleMenu(typelist);

        RoleMenuListResult result = new RoleMenuListResult();
        if (ObjectUtil.isEmpty(menuList)) {
            result.setMenuList(new ArrayList<>());
        } else {
            //增加一级分类返回
            List<RoleMenuDto> roleMenuDtos = buildRoleMenu(menuList, false);
            RoleMenuDto ancestor = new RoleMenuDto();
            ancestor.setLabel("一级菜单");
            ancestor.setId(0L);
            ancestor.setChildren(roleMenuDtos);
            List<RoleMenuDto> resp = new ArrayList<>();
            resp.add(ancestor);
            result.setMenuList(resp);
        }
        return Result.ok(result);
    }

    /**
     * 菜单删除
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteMenu(DeleteMenuParams params) {
        SysMenuEntity entity = sysMenuMapper.selectById(params.getId());
        if (ObjectUtil.isNull(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST, "菜单不存在");
        }
        //存在子菜单不允许删除
        LambdaQueryWrapper<SysMenuEntity> menuWrapper = new LambdaQueryWrapper<>();
        menuWrapper.eq(SysMenuEntity::getParentId, params.getId());
        boolean existsChild = sysMenuMapper.exists(menuWrapper);
        if (existsChild) {
            throw new BusinessException(SysMenuErrorEnum.EXISTS_CHILD_ERROR);
        }

        //删除菜单
        entity.setDelFlag(DelFlagEnum.DELETED.getCode());
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        int n = sysMenuMapper.updateById(entity);
        if (n > 0) {
            //删除角色菜单
            sysRoleMapper.deleteRoleMenuByMenuId(params.getId());
            return Result.ok();
        }
        return Result.fail();
    }

    /**
     * 根据用户编号查询菜单
     */
    private List<SysMenuEntity> getMenuByUserId() {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        LambdaQueryWrapper<SysMenuEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMenuEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        if (SatokenUtil.isPlatform(loginUser.getCompanyType()) && SatokenUtil.isSuperAdmin(loginUser.getUserType())) {
            //平台超级管理员
            queryWrapper.and(wrapper -> wrapper.eq(SysMenuEntity::getMenuLevel, MenuLevelEnum.PLATFORM.getValue()).or().eq(SysMenuEntity::getMenuLevel, MenuLevelEnum.SYSTEM.getValue()));
            queryWrapper.orderByAsc(SysMenuEntity::getSort);
            return sysMenuMapper.selectList(queryWrapper);
        } else {
            Long loginUserId = SatokenUtil.getLoginUserId();
            //角色
            Long roleId = sysUserMapper.getRoleIdByUserId(loginUserId);
            //查询菜单
            List<Long> menuIdList = sysMenuMapper.getMenuIdByRoleId(roleId);
            LambdaQueryWrapper<SysMenuEntity> menuWrapper = new LambdaQueryWrapper<>();
            if (SatokenUtil.isPlatform(loginUser.getCompanyType())) {
                queryWrapper.and(wrapper -> wrapper.eq(SysMenuEntity::getMenuLevel, MenuLevelEnum.PLATFORM.getValue()).or().eq(SysMenuEntity::getMenuLevel, MenuLevelEnum.SYSTEM.getValue()));
            }
            menuWrapper.in(SysMenuEntity::getId, menuIdList);
            menuWrapper.orderByAsc(SysMenuEntity::getSort);
            return sysMenuMapper.selectList(menuWrapper);
        }
    }

    /**
     * 构建路由菜单
     *
     * @param menuList
     */
    private List<RouterListDto> buildMenu(List<SysMenuEntity> menuList) {
        List<RouterListDto> result = new ArrayList<>();
        //格式转换
        List<RouterListDto> dtoList = handleChildrenDto(menuList);
        for (RouterListDto d : dtoList) {
            if (d.getParentId() == 0) { //遍历父节点下的所有数据
                recursionMenu(dtoList, d); //递归菜单
                result.add(d);
            }
        }
        return result;
    }

    /**
     * 递归菜单
     */
    private void recursionMenu(List<RouterListDto> dtoList, RouterListDto d) {
        List<RouterListDto> childList = getChildList(dtoList, d);
        d.setChildren(childList);
        for (RouterListDto ml : childList) {
            if (hasChild(dtoList, ml)) {
                recursionMenu(dtoList, ml);
            }
        }
    }

    /**
     * 获取子菜单
     *
     * @return
     */
    private List<RouterListDto> getChildList(List<RouterListDto> dtoList, RouterListDto d) {
        return dtoList.stream().filter(o -> o.getParentId().equals(d.getId())).collect(Collectors.toList());
    }

    /**
     * 子菜单转换
     *
     * @param childList
     * @return
     */
    private List<RouterListDto> handleChildrenDto(List<SysMenuEntity> childList) {
        List<RouterListDto> dtoList = new ArrayList<>();
        for (SysMenuEntity m : childList) {
            dtoList.add(handleMenuDto(m));
        }
        return dtoList;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<RouterListDto> dtoList, RouterListDto d) {
        return getChildList(dtoList, d).size() > 0;
    }

    /**
     * 菜单转换
     */
    private RouterListDto handleMenuDto(SysMenuEntity m) {
        RouterListDto dto = new RouterListDto();
        dto.setPath(m.getPath());
        dto.setName(m.getComponentName());
        dto.setComponent(m.getComponent());
        dto.setId(m.getId());
        dto.setParentId(m.getParentId());
        dto.setType(m.getType());
        dto.setSort(m.getSort());
        dto.setPermission(m.getPermission());
        dto.setMenuLevel(m.getMenuLevel());

        RouterMenuMeta meta = new RouterMenuMeta();
        meta.setIcon(m.getIcon());
        meta.setTitle(m.getMenuName());
        meta.setIsLink(false);
        meta.setIsHide(Integer.valueOf(1).equals(m.getHidden()));
        meta.setIsKeepAlive(true);

        dto.setMeta(meta);
        return dto;
    }

    /**
     * 平台角色菜单
     *
     * @return
     */
    private List<SysMenuEntity> handlePlatformRoleMenu(List<Integer> menuTypeList) {
        LambdaQueryWrapper<SysMenuEntity> menuWrapper = new LambdaQueryWrapper<>();
        menuWrapper.eq(SysMenuEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        menuWrapper.eq(SysMenuEntity::getHidden, 2); //显示
        if (ObjectUtil.isNotEmpty(menuTypeList)) {
            menuWrapper.in(SysMenuEntity::getType, menuTypeList);
        }
        menuWrapper.orderByAsc(SysMenuEntity::getSort);
        return sysMenuMapper.selectList(menuWrapper);
    }

    /**
     * 非平台角色菜单
     *
     * @return
     */
    private List<SysMenuEntity> handleCompanyRoleMenu(Long companyId) {
        //查询该公司的超级管理员账号
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getRoleType, 1); //超级管理员
        roleWrapper.eq(SysRoleEntity::getCompanyId, companyId);
        SysRoleEntity sysRole = sysRoleMapper.selectOne(roleWrapper);

        List<Long> menuIds = sysMenuMapper.getMenuIdByRoleId(sysRole.getId());
        LambdaQueryWrapper<SysMenuEntity> menuWrapper = new LambdaQueryWrapper<>();
        menuWrapper.eq(SysMenuEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        menuWrapper.in(SysMenuEntity::getId, menuIds); //显示
        menuWrapper.orderByAsc(SysMenuEntity::getSort);
        return sysMenuMapper.selectList(menuWrapper);
    }

    /**
     * 构建角色菜单
     *
     * @param menuList
     */
    private List<RoleMenuDto> buildRoleMenu(List<SysMenuEntity> menuList, boolean showDisabled) {
        List<RoleMenuDto> result = new ArrayList<>();
        //格式转换
        List<RoleMenuDto> dtoList = handleRoleMenuChildrenDto(menuList, showDisabled);
        for (RoleMenuDto d : dtoList) {
            if (d.getParentId() == 0) { //遍历父节点下的所有数据
                recursionRoleMenu(dtoList, d); //递归菜单
                result.add(d);
            }
        }

        return result;
    }

    /**
     * 子菜单转换
     *
     * @param childList
     * @return
     */
    private List<RoleMenuDto> handleRoleMenuChildrenDto(List<SysMenuEntity> childList, boolean shopDisabled) {
        List<RoleMenuDto> dtoList = new ArrayList<>();
        for (SysMenuEntity m : childList) {
            dtoList.add(handleRoleMenuDto(m, shopDisabled));
        }
        return dtoList;
    }

    /**
     * 菜单转换
     */
    private RoleMenuDto handleRoleMenuDto(SysMenuEntity m, boolean showDisabled) {
        RoleMenuDto dto = new RoleMenuDto();
        dto.setId(m.getId());
        dto.setParentId(m.getParentId());
        dto.setLabel(m.getMenuName());
        if (showDisabled) {
            dto.setDisabled(ObjectUtil.equal(BuiltInSystemEnum.YES.getValue(), m.getBuiltInSystem()));
        }
        return dto;
    }

    /**
     * 递归菜单
     */
    private void recursionRoleMenu(List<RoleMenuDto> dtoList, RoleMenuDto d) {
        List<RoleMenuDto> childList = getRoleMenuChildList(dtoList, d);
        d.setChildren(childList);
        for (RoleMenuDto ml : childList) {
            if (hasRoleMenuChild(dtoList, ml)) {
                recursionRoleMenu(dtoList, ml);
            }
        }
    }

    /**
     * 获取子菜单
     *
     * @return
     */
    private List<RoleMenuDto> getRoleMenuChildList(List<RoleMenuDto> dtoList, RoleMenuDto d) {
        return dtoList.stream().filter(o -> o.getParentId().equals(d.getId())).collect(Collectors.toList());
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasRoleMenuChild(List<RoleMenuDto> dtoList, RoleMenuDto d) {
        return getRoleMenuChildList(dtoList, d).size() > 0;
    }

    /**
     * 参数重新校验下，防止有刁民
     *
     * @param params
     */
    private void recheckParams(SysMenuAddParams params) {
        //菜单类型:1目录2菜单3按钮
        //parentId  menuName  type sort
        Integer type = params.getType();
        if (type == 1) {
            if (ObjectUtil.isEmpty(params.getIcon()) || ObjectUtil.isEmpty(params.getComponent()) || ObjectUtil.isEmpty(params.getMenuLevel())) {
                throw new BusinessException(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_EMPTY);
            }
        } else if (type == 2) {
            if (ObjectUtil.isEmpty(params.getIcon()) || ObjectUtil.isEmpty(params.getPermission()) || ObjectUtil.isEmpty(params.getComponentName()) || ObjectUtil.isEmpty(params.getMenuLevel())) {
                throw new BusinessException(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_EMPTY);
            }
        } else if (type == 3) {
            if (ObjectUtil.isEmpty(params.getPermission()))
                throw new BusinessException(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_EMPTY);
        }
    }
}
