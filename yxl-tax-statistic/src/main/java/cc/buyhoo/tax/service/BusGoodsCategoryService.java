package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusGoodsCategoryEntity;
import cc.buyhoo.tax.params.goodsCategory.BusGoodsCategoryParams;
import cc.buyhoo.tax.params.goodsCategory.EdutGoodsCategoryListParams;
import cc.buyhoo.tax.result.goodsCateogry.BusGoodsCategoryDto;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
* @Description 商品分类
* @ClassName BusGoodsCategory
* <AUTHOR> 
* @Date 2023-07-26
**/
public interface BusGoodsCategoryService {

    /**
     * 批量修改商品分类税务信息
     * @param params
     * @return
     */
    Result<Void> edutGoodsCategoryList(EdutGoodsCategoryListParams params);
    Result<List<BusGoodsCategoryDto>> list(BusGoodsCategoryParams busGoodsCategoryParams);

    Result<Void> save(BusGoodsCategoryParams busGoodsCategoryParams);

    Result<Void> deleteByIds(List<Long> ids);

    Result<List<BusGoodsCategoryEntity>> categoryList(String level, BusGoodsCategoryParams busGoodsCategoryParams);

    Result<List<BusGoodsCategoryEntity>> categoryListByParentId(BusGoodsCategoryParams busGoodsCategoryParams);
}