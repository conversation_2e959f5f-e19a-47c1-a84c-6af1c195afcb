package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.SysCityInfoMapper;
import cc.buyhoo.tax.entity.SysCityInfoEntity;
import cc.buyhoo.tax.result.sysCityInfo.SysCityInoDto;
import cc.buyhoo.tax.service.SysCityInfoService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 城市表
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-08-23
 */
@Service
@RequiredArgsConstructor
public class SysCityInfoServiceImpl implements SysCityInfoService {

    private final SysCityInfoMapper sysCityInfoMapper;

    @Override
    public Result<List<SysCityInoDto>> selectList(Long pid, Integer level) {
        LambdaQueryWrapper<SysCityInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SysCityInfoEntity::getId, SysCityInfoEntity::getName, SysCityInfoEntity::getGrade);
        queryWrapper.eq(ObjectUtil.isNotNull(pid), SysCityInfoEntity::getPid, pid);
        List<SysCityInfoEntity> list = sysCityInfoMapper.selectList(queryWrapper);
        List<SysCityInoDto> dtoList = Collections.EMPTY_LIST;
        if (ObjectUtil.isNotNull(list)){
            dtoList = list.stream().map(v -> {
                SysCityInoDto dto = new SysCityInoDto();
                dto.setLabel(v.getName());
                dto.setValue(v.getId());
                dto.setLeaf(v.getGrade() >= level);
                return dto;
            }).collect(Collectors.toList());
        }
        return Result.ok(dtoList);
    }

    @Override
    public List<SysCityInfoEntity> selectByIds(Long[] cityInfoIds) {
        LambdaQueryWrapper<SysCityInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SysCityInfoEntity::getId, SysCityInfoEntity::getName);
        queryWrapper.in(SysCityInfoEntity::getId, cityInfoIds);
        List<SysCityInfoEntity> list = sysCityInfoMapper.selectList(queryWrapper);
        return list;
    }
}