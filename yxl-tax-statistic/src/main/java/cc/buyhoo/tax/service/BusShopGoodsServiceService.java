package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busInvoiceGoodsService.InvoiceGoodsServiceParams;
import cc.buyhoo.tax.params.busInvoiceGoodsService.InvoiceGoodsServiceSaveParams;
import cc.buyhoo.tax.result.busInvoiceGoodsService.InvoiceGoodsServiceResult;

import java.util.List;

/**
 * 开票服务
 * @ClassName BusShopGoodsServiceService
 * <AUTHOR>
 * @Date 2023/9/6 14:18
 **/
public interface BusShopGoodsServiceService {
    Result<InvoiceGoodsServiceResult> pageList(InvoiceGoodsServiceParams params);

    Result<Void> save(InvoiceGoodsServiceSaveParams params);

    Result<Void> delete(List<Long> ids);

    Result<Void> enable(InvoiceGoodsServiceSaveParams params);
}
