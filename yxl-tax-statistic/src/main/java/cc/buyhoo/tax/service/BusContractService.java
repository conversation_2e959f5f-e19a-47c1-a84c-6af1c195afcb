package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busContract.*;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busContract.BusContractDto;
import cc.buyhoo.tax.result.busContract.BusContractListResult;
import cc.buyhoo.tax.result.busContract.BusContractNoDto;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

/**
 * 合同管理
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-07-13
 */
public interface BusContractService {

    Result<BusContractListResult> pageList(BusContractListParams params);

    Result<Void> addContract(BusContractAddParams addParams);

    Result<Void> updateContract(BusContractUpdateParams updateParams);

    Result<Void> deleteContract(DeleteIdsParams idsParams);

    Result<BusContractNoDto> createContractNoAndName(BusContractNoParams params);

    Result<BigDecimal> checkOrderNo(BusContractCheckParams checkParams);

    void export(BusContractListParams params, HttpServletResponse response);

    Result<BusContractDto> selectByContractNo(String contractNo);
}