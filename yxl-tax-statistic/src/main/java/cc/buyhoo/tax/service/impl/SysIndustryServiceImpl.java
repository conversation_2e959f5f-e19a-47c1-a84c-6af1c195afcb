package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.SysIndustryMapper;
import cc.buyhoo.tax.entity.SysIndustryEntity;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryAddParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryEditParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryPageParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryQueryParams;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysIndustry.SysIndustryDto;
import cc.buyhoo.tax.result.sysIndustry.SysIndustryPageResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.SysIndustryService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 行业管理表
 * @ClassName SysIndustryServiceImpl
 * <AUTHOR> 
 * @since 1.0.0 2024-08-23
 */
@Service
@AllArgsConstructor
public class SysIndustryServiceImpl extends BaseService implements SysIndustryService {

    private final SysIndustryMapper sysIndustryMapper;

    @Override
    public Result<SysIndustryPageResult> pageList(SysIndustryPageParams pageParams) {
        PageUtils.startPage(pageParams);
        LambdaQueryWrapper<SysIndustryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(pageParams.getIndustryName()), SysIndustryEntity::getIndustryName, pageParams.getIndustryName());
        queryWrapper.eq(ObjectUtil.isNotNull(pageParams.getEnableStatus()), SysIndustryEntity::getEnableStatus, pageParams.getEnableStatus());
        queryWrapper.eq(SysIndustryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysIndustryEntity> list = sysIndustryMapper.selectList(queryWrapper);
        List<SysIndustryDto> dtoList = Collections.EMPTY_LIST;
        if (CollectionUtil.isNotEmpty(list)) {
            dtoList = BeanUtil.copyToList(list, SysIndustryDto.class);
        }
        return convertPageData(list, dtoList, SysIndustryPageResult.class, pageParams);
    }

    @Override
    public Result<Void> addIndustry(SysIndustryAddParams addParams) {
        SysIndustryEntity entity = new SysIndustryEntity();
        BeanUtil.copyProperties(addParams, entity);
        checkIndustry(entity);
        entity.setCreateUser(SatokenUtil.getLoginUserId());
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        int n = sysIndustryMapper.insert(entity);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<Void> editIndustry(SysIndustryEditParams updateParams) {
        SysIndustryEntity entity = sysIndustryMapper.selectById(updateParams.getId());
        if (ObjectUtil.isNull(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
        }
        BeanUtil.copyProperties(updateParams, entity);
        checkIndustry(entity);
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        int n = sysIndustryMapper.updateById(entity);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    private void checkIndustry(SysIndustryEntity entity) {
        LambdaQueryWrapper<SysIndustryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysIndustryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(SysIndustryEntity::getIndustryName, entity.getIndustryName());
        queryWrapper.ne(ObjectUtil.isNotNull(entity.getId()), SysIndustryEntity::getId, entity.getId());
        Long count = sysIndustryMapper.selectCount(queryWrapper);
        if (null != count && count > 0) {
            throw new BusinessException(SystemErrorEnum.SYSTEM_VERIFY_ERROR.getCode(), "行业名称已存在");
        }
    }

    @Override
    public Result<Void> deleteByIds(DeleteIdsParams idsParams) {
        int n = sysIndustryMapper.deleteBatchIds(idsParams.getIds());
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<SysIndustryDto> selectById(Long id) {
        SysIndustryEntity entity = sysIndustryMapper.selectById(id);
        if (ObjectUtil.isNotNull(entity)) {
            SysIndustryDto dto = new SysIndustryDto();
            BeanUtil.copyProperties(entity, dto);
            return Result.ok(dto);
        }
        return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
    }

    @Override
    public Result<List<SelectDataDto>> industrySelectData(SysIndustryQueryParams queryParams) {
        LambdaQueryWrapper<SysIndustryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysIndustryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(ObjectUtil.isNotNull(queryParams.getEnableStatus()), SysIndustryEntity::getEnableStatus, queryParams.getEnableStatus());
        List<SysIndustryEntity> list = sysIndustryMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            List<SelectDataDto> dtoList = list.stream().map(v -> {
                SelectDataDto dto = new SelectDataDto();
                dto.setLabel(v.getIndustryName());
                dto.setValue(v.getId());
                return dto;
            }).collect(Collectors.toList());
            return Result.ok(dtoList);
        }
        return Result.ok(Collections.EMPTY_LIST);
    }
}