package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busGoods.GoodsCategoryBindParams;
import cc.buyhoo.tax.params.busGoods.GoodsListParams;
import cc.buyhoo.tax.result.busGoods.BusGoodsListResult;

/**
* @Description 商品管理表
* @ClassName BusGoods
* <AUTHOR> 
* @Date 2023-07-29
**/
public interface BusGoodsService {

    Result<BusGoodsListResult> pageList(GoodsListParams goodsListParams);

    Result<Void> bindCategory(GoodsCategoryBindParams goodsCategoryBindParams);
}