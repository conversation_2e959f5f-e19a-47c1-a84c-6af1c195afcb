package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busCompanyProfitRule.BusCompanyProfitRuleParams;
import cc.buyhoo.tax.result.busCompanyProfitRule.BusCompanyProfitRuleDto;

/**
* @Description 企业利润规则
* @ClassName BusCompanyProfitRuleEntity
* <AUTHOR> 
* @Date 2024-06-25
**/
public interface BusCompanyProfitRuleEntityService {

    Result<BusCompanyProfitRuleDto> queryCompanyProfitRule();

    Result saveCompanyProfitRule(BusCompanyProfitRuleParams params);
}