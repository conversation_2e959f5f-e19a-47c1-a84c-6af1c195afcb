package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusCompanyProfitRuleMapper;
import cc.buyhoo.tax.entity.BusCompanyProfitRuleEntity;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.enums.CompanyProfitRuleTypeEnum;
import cc.buyhoo.tax.params.busCompanyProfitRule.BusCompanyProfitRuleParams;
import cc.buyhoo.tax.result.busCompanyProfitRule.BusCompanyProfitRuleDto;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.BusCompanyProfitRuleEntityService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
* @Description 企业利润规则
* @ClassName BusCompanyProfitRuleEntity
* <AUTHOR> 
* @Date 2024-06-25
**/
@Service
@AllArgsConstructor
public class BusCompanyProfitRuleEntityServiceImpl implements BusCompanyProfitRuleEntityService {

    private final BusCompanyProfitRuleMapper busCompanyProfitRuleMapper;

    @Override
    public Result<BusCompanyProfitRuleDto> queryCompanyProfitRule() {
        LambdaQueryWrapper<BusCompanyProfitRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusCompanyProfitRuleEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.eq(BusCompanyProfitRuleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.orderByDesc(BusCompanyProfitRuleEntity::getId);
        queryWrapper.last("limit 1");
        BusCompanyProfitRuleEntity entity = busCompanyProfitRuleMapper.selectOne(queryWrapper);
        BusCompanyProfitRuleDto dto = null;
        if (ObjectUtil.isNotNull(entity)) {
            dto = BeanUtil.toBean(entity, BusCompanyProfitRuleDto.class);
        }
        return Result.ok(dto);
    }

    @Override
    public Result saveCompanyProfitRule(BusCompanyProfitRuleParams params) {
        if (ObjectUtil.equals(EnableStatusEnum.AVAILABLE.getCode(), params.getEnableStatus())) {
            if (ObjectUtil.equals(CompanyProfitRuleTypeEnum.BY_ORDER.getValue(), params.getRuleType())) {
                if (null != params.getAmount() && BigDecimal.ZERO.compareTo(params.getAmount()) == 1) {
                    return Result.fail(CommonErrorEnum.PARAM_ERROR, "订单金额不能小于0");
                }
                if (null != params.getFirstProfitRate() && BigDecimal.ZERO.compareTo(params.getFirstProfitRate()) == 1) {
                    return Result.fail(CommonErrorEnum.PARAM_ERROR, "利润率不能小于0");
                }
                if (null != params.getSecondProfitRate() && BigDecimal.ZERO.compareTo(params.getSecondProfitRate()) == 1) {
                    return Result.fail(CommonErrorEnum.PARAM_ERROR, "利润率不能小于0");
                }
                if (null != params.getMonthProfitRate() && BigDecimal.ZERO.compareTo(params.getMonthProfitRate()) == 1) {
                    return Result.fail(CommonErrorEnum.PARAM_ERROR, "每月整体利润率不能小于0");
                }
            } else if (ObjectUtil.equals(CompanyProfitRuleTypeEnum.BY_COUNT.getValue(), params.getRuleType())) {
                if (null != params.getProfitRate() && BigDecimal.ZERO.compareTo(params.getProfitRate()) == 1) {
                    return Result.fail(CommonErrorEnum.PARAM_ERROR, "每月整体利润不能小于0");
                }
            } else {
                return Result.fail(CommonErrorEnum.PARAM_ERROR, "利润校验规则类型错误");
            }
        }
        BusCompanyProfitRuleEntity entity;
        if (ObjectUtil.isNotNull(params.getId())) {
            entity = busCompanyProfitRuleMapper.selectById(params.getId());
        } else {
            LambdaQueryWrapper<BusCompanyProfitRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusCompanyProfitRuleEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
            queryWrapper.eq(BusCompanyProfitRuleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
            queryWrapper.orderByDesc(BusCompanyProfitRuleEntity::getId);
            queryWrapper.last("limit 1");
            entity = busCompanyProfitRuleMapper.selectOne(queryWrapper);
        }
        if (ObjectUtil.isNull(entity)) {
            entity = new BusCompanyProfitRuleEntity();
            BeanUtil.copyProperties(params, entity);
            entity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
            entity.setCreateUser(SatokenUtil.getLoginUserId());
            int n = busCompanyProfitRuleMapper.insert(entity);
            return n > 0 ? Result.ok() : Result.fail();
        } else {
            entity.setRuleType(params.getRuleType());
            entity.setAmount(params.getAmount());
            entity.setFirstProfitRate(params.getFirstProfitRate());
            entity.setSecondProfitRate(params.getSecondProfitRate());
            entity.setMonthProfitRate(params.getMonthProfitRate());
            entity.setProfitRate(params.getProfitRate());
            entity.setModifyUser(SatokenUtil.getLoginUserId());
            entity.setModifyTime(DateUtil.date());
            entity.setEnableStatus(params.getEnableStatus());
            int n = busCompanyProfitRuleMapper.updateById(entity);
            return n > 0 ? Result.ok() : Result.fail();
        }
    }
}