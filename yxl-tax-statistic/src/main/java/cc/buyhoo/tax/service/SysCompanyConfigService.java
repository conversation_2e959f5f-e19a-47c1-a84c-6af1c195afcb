package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.SysCompanyConfigEntity;
import cc.buyhoo.tax.params.sysCompanyConfig.UpdateSysCompanyConfigParams;

public interface SysCompanyConfigService {

    /**
     * 查询本店关于拆单的设置
     * @return
     */
    public Result<SysCompanyConfigEntity> getCompanyConfig();

    /**
     * 更新本店关于拆单的设置
     * @param updateSysCompanyConfigParams
     * @return
     */
    public Result<Void> updateCompanyConfig(UpdateSysCompanyConfigParams updateSysCompanyConfigParams);
}
