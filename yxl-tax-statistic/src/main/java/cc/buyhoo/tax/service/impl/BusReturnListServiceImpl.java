package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusReturnListDetailMapper;
import cc.buyhoo.tax.dao.BusReturnListMapper;
import cc.buyhoo.tax.dao.BusReturnListPaydetailMapper;
import cc.buyhoo.tax.entity.BusReturnListDetailEntity;
import cc.buyhoo.tax.entity.BusReturnListEntity;
import cc.buyhoo.tax.entity.BusReturnListPaydetailEntity;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.params.returnList.ReturnListDetailParams;
import cc.buyhoo.tax.params.returnList.ReturnListParams;
import cc.buyhoo.tax.result.returnList.*;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusReturnListService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class BusReturnListServiceImpl extends BaseService implements BusReturnListService {

    @Resource
    private BusReturnListMapper busReturnListMapper;

    @Resource
    private BusReturnListDetailMapper busReturnListDetailMapper;

    @Resource
    private BusReturnListPaydetailMapper busReturnListPaydetailMapper;

    /**
     * 退单列表
     * @param params
     * @return
     */
    @Override
    public Result<ReturnListResult> returnList(ReturnListParams params) {
        //查询参数
        QueryWrapper<BusReturnListEntity> returnListWrapper = handleListWrapper(false,params);
        PageUtils.startPage(params);
        //查询数据
        List<BusReturnListEntity> returnList = busReturnListMapper.selectList(returnListWrapper);
        List<ReturnListDto> dtoList = new ArrayList<>();
        for (BusReturnListEntity entity : returnList) {
            ReturnListDto dto = new ReturnListDto();
            BeanUtils.copy(entity,dto);
            dto.setRetListDatetime(DateUtil.format(entity.getRetListDatetime(), DatePattern.NORM_DATETIME_PATTERN));

            dtoList.add(dto);
        }

        Result<ReturnListResult> result = convertPageData(returnList, dtoList, ReturnListResult.class, params);
        ReturnListResult data = result.getData();
        data.setTotalRetListTotalMoney(BigDecimal.ZERO);

        //统计数据
        QueryWrapper<BusReturnListEntity> countWrapper = handleListWrapper(true, params);
        List<Map<String, Object>> list = busReturnListMapper.selectMaps(countWrapper);
        if (ObjectUtil.isNotEmpty(list) && ObjectUtil.isNotEmpty(list.get(0))) {
            Map<String, Object> countMap = list.get(0);
            data.setTotalRetListTotalMoney(new BigDecimal(countMap.getOrDefault("totalRetListTotalMoney","0").toString()));
        }

        return result;
    }

    /**
     * 退单详情
     * @param params
     * @return
     */
    @Override
    public Result<ReturnListDetailResult> returnListDetail(ReturnListDetailParams params) {

        //查询退单
        BusReturnListEntity returnList = busReturnListMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(returnList) || !SatokenUtil.getLoginUserCompanyId().equals(returnList.getCompanyId())) throw new BusinessException(CommonErrorEnum.PARAM_ERROR);

        //查询退单详情
        LambdaQueryWrapper<BusReturnListDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(BusReturnListDetailEntity::getRetListUnique,returnList.getRetListUnique());
        List<BusReturnListDetailEntity> detailList = busReturnListDetailMapper.selectList(detailWrapper);

        //查询退单支付
        LambdaQueryWrapper<BusReturnListPaydetailEntity> payWrapper = new LambdaQueryWrapper<>();
        payWrapper.eq(BusReturnListPaydetailEntity::getRetListUnique,returnList.getRetListUnique());
        List<BusReturnListPaydetailEntity> payList = busReturnListPaydetailMapper.selectList(payWrapper);

        ReturnListDetailResult result = new ReturnListDetailResult();
        result.setDetailList(BeanUtils.copyList(detailList, ReturnListDetailDto.class));

        //数据转换
        List<ReturnListDetailPayDto> dtoPayList = new ArrayList<>();
        for (BusReturnListPaydetailEntity pay : payList) {
            ReturnListDetailPayDto dto = new ReturnListDetailPayDto();
            dto.setPayMoney(pay.getPayMoney());
            dto.setPayType(convertPayType(pay.getPayType()));

            dtoPayList.add(dto);
        }
        result.setPayList(dtoPayList);

        return Result.ok(result);
    }

    /**
     * 支付方式转换
     * @param payType
     * @return
     */
    private String convertPayType(Integer payType) {
        String resp = "在线支付";
        switch (payType) {
            case 1:
                resp = "现金";
                break;
            case 2:
                resp = "支付宝";
                break;
            case 3:
                resp = "微信";
                break;
            case 4:
                resp = "银行卡";
                break;
            case 5:
                resp = "储值卡";
                break;
            case 6:
                resp = "其他";
                break;
            case 7:
                resp = "优惠券";
                break;
            case 8:
                resp = "百货豆";
                break;
        }
        return resp;
    }

    /**
     * 请求参数
     * @return
     */
    private QueryWrapper<BusReturnListEntity> handleListWrapper(boolean isCount, ReturnListParams params) {
        QueryWrapper<BusReturnListEntity> returnListWrapper = new QueryWrapper<>();
        returnListWrapper.eq("company_id", SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getSaleListUnique())) {
            returnListWrapper.eq("sale_list_unique",params.getSaleListUnique());
        }
        if (ObjectUtil.isNotEmpty(params.getDateStart())) {
            returnListWrapper.between("ret_list_datetime",params.getDateStart(),params.getDateEnd());
        }
        if (ObjectUtil.isNotEmpty(params.getShopUnique())) {
            returnListWrapper.eq("shop_unique", params.getShopUnique());
        }
        if (ObjectUtil.isNotEmpty(params.getRetListUnique())) {
            returnListWrapper.eq("ret_list_unique",params.getRetListUnique());
        }
        if (!isCount) {
            returnListWrapper.orderByDesc("id");
        }
        if (isCount) {
            returnListWrapper.select("sum(ret_list_total_money) as totalRetListTotalMoney");
        }

        return returnListWrapper;
    }
}
