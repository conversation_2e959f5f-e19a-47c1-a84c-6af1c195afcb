package cc.buyhoo.tax.service.impl;

import cc.buyhoo.tax.dao.SysCompanyConfigMapper;
import cc.buyhoo.tax.entity.SysCompanyConfigEntity;
import cc.buyhoo.tax.params.sysCompanyConfig.UpdateSysCompanyConfigParams;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.SysCompanyConfigService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import cc.buyhoo.common.standard.Result;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Service
@Slf4j
public class SysCompanyConfigServiceImpl extends BaseService implements SysCompanyConfigService {
    @Resource
    private SysCompanyConfigMapper sysCompanyConfigMapper;

    /**
     * 查询本店关于拆单的设置
     * @return
     */
    public Result<SysCompanyConfigEntity> getCompanyConfig() {
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        LambdaQueryWrapper<SysCompanyConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCompanyConfigEntity::getCompanyId, companyId);
        SysCompanyConfigEntity sysCompanyConfigEntity = sysCompanyConfigMapper.selectOne(wrapper);

        if (null == sysCompanyConfigEntity) {
            sysCompanyConfigEntity = new SysCompanyConfigEntity();
            sysCompanyConfigEntity.setCompanyId(companyId);
            sysCompanyConfigEntity.setDisassembleStatus(0);
            sysCompanyConfigEntity.setDisassembleMinMoney(BigDecimal.ZERO);
            sysCompanyConfigEntity.setDisassembleMaxMoney(BigDecimal.ZERO);
            sysCompanyConfigEntity.setDisassembleStartMoney(BigDecimal.ZERO);

            //保存新的记录
            sysCompanyConfigEntity.setCreateUser(SatokenUtil.getLoginUserId());
            sysCompanyConfigEntity.setCreateTime(DateUtil.date());
            sysCompanyConfigMapper.insert(sysCompanyConfigEntity);
        }
        return Result.ok(sysCompanyConfigEntity);
    }

    /**
     * 更新本店关于拆单的设置
     * @param updateSysCompanyConfigParams
     * @return
     */
    public Result<Void> updateCompanyConfig(UpdateSysCompanyConfigParams updateSysCompanyConfigParams) {
        SysCompanyConfigEntity sysCompanyConfigEntity = new SysCompanyConfigEntity();
        BeanUtils.copyProperties(updateSysCompanyConfigParams, sysCompanyConfigEntity);
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        sysCompanyConfigEntity.setCompanyId(companyId);
        sysCompanyConfigEntity.setModifyUser(SatokenUtil.getLoginUserId());
        sysCompanyConfigEntity.setModifyTime(DateUtil.date());

        LambdaQueryWrapper<SysCompanyConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCompanyConfigEntity::getCompanyId, companyId);

        sysCompanyConfigMapper.update(sysCompanyConfigEntity, wrapper);
        return Result.ok();
    }
}
