package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysBank.SysBankBranchPageParam;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysBank.SysBankBranchPageResult;

import java.util.List;

/**
 * @description: 银行类型
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 11:45
 **/
public interface SysBankListService {
    Result<List<SelectDataDto>> bankSelectData();

    Result<SysBankBranchPageResult> bankBranchPageList(SysBankBranchPageParam pageParam);
}
