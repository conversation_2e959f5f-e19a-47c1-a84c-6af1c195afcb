package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnBatch.ReturnBatchListParams;
import cc.buyhoo.tax.result.returnBatch.ReturnBatchListResult;
import org.springframework.validation.annotation.Validated;

public interface BusReturnBatchService {

    /**
     * 退货单批次列表
     * @param params
     * @return
     */
    public Result<ReturnBatchListResult> pageList(@Validated ReturnBatchListParams params);

}
