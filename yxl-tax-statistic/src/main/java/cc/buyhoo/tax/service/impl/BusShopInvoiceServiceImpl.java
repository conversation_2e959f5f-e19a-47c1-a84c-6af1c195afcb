package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.minio.MinioUploadHelper;
import cc.buyhoo.common.minio.result.MinioUploadResult;
import cc.buyhoo.common.smsg.config.properties.SmsgProperties;
import cc.buyhoo.common.smsg.core.AliyunSmsTemplate;
import cc.buyhoo.common.smsg.entity.SmsResult;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.config.InvoiceNotifyConfig;
import cc.buyhoo.tax.config.properties.InvoiceProperties;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.entity.invoice.*;
import cc.buyhoo.tax.enums.*;
import cc.buyhoo.tax.params.busShopInvoice.*;
import cc.buyhoo.tax.params.invoice.*;
import cc.buyhoo.tax.result.busShopInvoice.*;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopInvoiceService;
import cc.buyhoo.tax.task.GetQdInvoiceBatchTask;
import cc.buyhoo.tax.task.GetQdInvoiceTask;
import cc.buyhoo.tax.task.QuerySdInvoiceFileBatchTask;
import cc.buyhoo.tax.task.QuerySdInvoiceFileTask;
import cc.buyhoo.tax.util.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BusShopInvoiceServiceImpl extends BaseService implements BusShopInvoiceService {

    private final MinioUploadHelper minioUploadHelper;
    private final SysCompanyMapper sysCompanyMapper;
    private final BusShopInvoiceMapper busShopInvoiceMapper;
    private final BusShopMapper busShopMapper;
    private final BusShopInvoiceDetailMapper busShopInvoiceDetailMapper;
    private final BusShopInvoiceSettingMapper busShopInvoiceSettingMapper;
    private final BusInvoiceStaffMapper busInvoiceStaffMapper;
    private final BusSaleListDetailMapper busSaleListDetailMapper;
    private final BusGoodsMapper busGoodsMapper;
    private final BusGoodsCategoryMapper busGoodsCategoryMapper;
    private final BusSaleListMapper busSaleListMapper;
    @Resource
    private InvoiceNotifyConfig invoiceNotifyConfig;
    @Resource
    private InvoiceProperties invoiceProperties;
    /**
     * 短信配置
     */
    @Resource
    private SmsgProperties smsgProperties;

    @Resource
    private InvoiceUrlUtil invoiceUrlUtil;


    /**
     * 测试minio上传文件（仅用作测试）
     *
     * @return
     */
    public Result<Void> testMinio(String path) {
        File f = new File(path);
        if (f.exists() && f.isFile()) {
            MinioUploadResult result = minioUploadHelper.uploadImg(f);
            System.out.println(result);
        }
        return Result.ok();
    }

    /**
     * 获取当前发票的下载地址
     *
     * @param params
     * @return
     */
    public Result<String> getInvoiceUrl(InvoiceApplyParams params) {
        //校验发票信息是否存在
        BusShopInvoiceEntity invoiceEntity = busShopInvoiceMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(invoiceEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "申请记录不存在");
        }
        if (invoiceEntity.getStatus() != 1) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该记录尚未开票成功");
        }
        return Result.ok(invoiceProperties.getFileHost() + "/" + invoiceEntity.getImageUrl());
    }

    /**
     * 重新发送发票信息
     *
     * @param params
     * @return
     */
    public Result<Void> resend(InvoiceApplyParams params) {
        //校验发票信息是否存在
        BusShopInvoiceEntity invoiceEntity = busShopInvoiceMapper.selectById(params.getId());

        if (ObjectUtil.isEmpty(invoiceEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "申请记录不存在");
        }
        if (invoiceEntity.getStatus() != 1) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该记录尚未开票成功");
        }

        //已开票成功，重新发送
        String receiveMsg = invoiceEntity.getReceiveMsg();
        if (ObjectUtil.isNotEmpty(receiveMsg)) {
            //校验地址格式是手机号
            if (SystemUtil.isValidPhoneNumber(receiveMsg)) {
                //发送短信
                Map<String, String> map = new HashMap<>();
                //注意,imgurl不包含域名信息
                map.put("recordId", invoiceEntity.getId().toString());

                AliyunSmsTemplate smsTemplate = new AliyunSmsTemplate(smsgProperties);

                SmsResult smsResult = smsTemplate.send(receiveMsg, smsgProperties.getTemplatedIdForInvoice(), map);
                System.out.println("短信发送结果" + smsResult);

            } else if (SystemUtil.isValidEmail(receiveMsg)) {
                //发送邮件
                MailUtil.send(receiveMsg, "电子发票", "谢谢您的光临，您的发票下载地址为：<a download='发票.jpg'  href='" + invoiceProperties.getFileHost() + File.separator + invoiceEntity.getImageUrl() + "'>下载地址</a>", true);
            } else {
                //未上传数据，不发送
            }
        }

        return Result.ok();
    }

    /**
     * 发票申请
     *
     * @param params
     * @return
     */
    public Result<Void> invoiceApply(InvoiceApplyParams params) {
        //校验发票信息是否存在
        BusShopInvoiceEntity invoiceEntity = busShopInvoiceMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(invoiceEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "申请记录不存在");
        }

        if (invoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_1.getValue()) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该记录已开票");
        }

        if (invoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_3.getValue()) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "开票中，请勿重复申请");
        }

        //根据店铺发票期数，选择开票方式
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> settingWrapper = new LambdaQueryWrapper<>();
        settingWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, companyId);
        //查询是否配置了开票信息
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(settingWrapper);
        if (ObjectUtil.isEmpty(settingEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该企业未配置发票信息");
        }

        //三期税务
        if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel()) {
            invoiceEntity.setPeriodsLevel(PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel());

            //三期开票功能，三期开票和发送一起，不需要单独处理
            //三期需要验证税盘是否在线


        } else if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel()) {
            //获取当前已登录的账号信息
            LambdaQueryWrapper<BusInvoiceStaffEntity> staffWrapper = new LambdaQueryWrapper<>();
            staffWrapper.eq(BusInvoiceStaffEntity::getCompanyId, companyId);
            staffWrapper.eq(BusInvoiceStaffEntity::getIsLogin, StaffIsLogin.IS_LOGIN.getValue());
            staffWrapper.orderByDesc(BusInvoiceStaffEntity::getLastLoginTime);
            BusInvoiceStaffEntity staffEntity = busInvoiceStaffMapper.selectOne(staffWrapper);
            if (null == staffEntity) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "请先登录全电账户");
            }
            //四期税务
            GenerateQdInvoiceEntity generateQdInvoiceEntity = new GenerateQdInvoiceEntity();

            BeanUtil.copyProperties(invoiceEntity, settingEntity);
            generateQdInvoiceEntity.setElectricAccount(staffEntity.getStaffAccount());
            generateQdInvoiceEntity.setInvoiceType(InvoiceTypeEnum.NORMAT.getValue());
            generateQdInvoiceEntity.setClientName(invoiceEntity.getPurchaseName());
            generateQdInvoiceEntity.setClientTaxCode(invoiceEntity.getPurchaseIdentity());
            generateQdInvoiceEntity.setClientBankName(invoiceEntity.getPurchaseBank());
            generateQdInvoiceEntity.setClientBankAccountNumber(invoiceEntity.getPurchaseBankNo());
            generateQdInvoiceEntity.setClientAddress(invoiceEntity.getPurchaseAddress());
            generateQdInvoiceEntity.setClientPhone(invoiceEntity.getPurchasePhone());
            generateQdInvoiceEntity.setSellerName(settingEntity.getCompanyName());
            generateQdInvoiceEntity.setSellerAddress(settingEntity.getCompanyAddress());
            generateQdInvoiceEntity.setSellerTaxCode(settingEntity.getCompanyTaxNo());
            generateQdInvoiceEntity.setSellerBankName(settingEntity.getInvoiceBankName());
            generateQdInvoiceEntity.setSellerBankAccountNumber(settingEntity.getInvoiceBankCard());
            generateQdInvoiceEntity.setSellerPhone(settingEntity.getCompanyPhone());

            generateQdInvoiceEntity.setInvoicer(settingEntity.getInvoiceMan());
            generateQdInvoiceEntity.setChecker(settingEntity.getChecker());
            generateQdInvoiceEntity.setCashier(settingEntity.getPayee());
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setBillNumber("" + System.currentTimeMillis());
            generateQdInvoiceEntity.setInvoiceKind(InvoiceKindEnum.NormalTicket.getValue());
            generateQdInvoiceEntity.setTotalAmount(invoiceEntity.getOrderTaxMoney() + "");
            generateQdInvoiceEntity.setAmount(invoiceEntity.getOrderMoney() + "");
            generateQdInvoiceEntity.setTaxAmount(invoiceEntity.getTaxMoney() + "");

            System.out.println("当前申请的开票信息为" + generateQdInvoiceEntity);

            //查询商品明细
            String saleListUnique = invoiceEntity.getSaleListUnique();
            List<InvoiceGoodsExternalEntity> details = busSaleListDetailMapper.querySaleListDetailForInvoice(saleListUnique);
            //根据商品详情计算税额信息
            if (ObjectUtil.isEmpty(details)) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "未配置开票信息设置");
            }

            //合并同类项，计算税额
            List<GenerateQdInvoiceGoodsEntity> goodsList = new ArrayList<>();
            BigDecimal goodsTotal = BigDecimal.ZERO;

            for (GenerateQdInvoiceGoodsEntity detail : details) {
                if (detail.getGoodsTaxNo() == null) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "未配置开票信息设置");
                }
                detail.setTaxPre(TaxPreEnum.DOTUSE.getValue());
                goodsTotal = goodsTotal.add(new BigDecimal(detail.getAmount()));
                boolean haveSaveGoods = false;
                for (GenerateQdInvoiceGoodsEntity goods : goodsList) {
                    if (goods.getGoodsTaxNo().equals(detail.getGoodsTaxNo()) && goods.getGoodsName().equals(detail.getGoodsName())) {
                        haveSaveGoods = true;
                        System.out.println("当前已有数据信息" + detail);
                        System.out.println("匹配到的数据" + goods);
                        detail.setAmount((new BigDecimal(goods.getAmount()).add(new BigDecimal(detail.getAmount())).setScale(2, RoundingMode.HALF_UP)).toString());
                        System.out.println("匹配后的数据" + detail);
                        break;
                    }
                }
                if (!haveSaveGoods) {
                    goodsList.add(detail);
                }
            }

            //应开商品总额（含税）
            BigDecimal balanceTotal = new BigDecimal(invoiceEntity.getOrderTaxMoney().toString());
            BigDecimal balance = balanceTotal;

            //根据订单优惠后的总金额，计算各商品应开票金额
            for (Integer i = 0; i < goodsList.size(); i++) {
                if (i == goodsList.size() - 1) {
                    goodsList.get(i).setAmount(balance.toString());
                    break;
                }
                //不是最后一项
                GenerateQdInvoiceGoodsEntity goods = goodsList.get(i);
                BigDecimal amount = new BigDecimal(goods.getAmount());
                amount = goodsTotal.multiply(amount).divide(balanceTotal, 2, RoundingMode.HALF_UP);
                goodsList.get(i).setAmount(amount.toString());
            }


            for (GenerateQdInvoiceGoodsEntity goods : goodsList) {
                //根据金额，计算税额
                BigDecimal amount = new BigDecimal(goods.getAmount());
                //税率
                BigDecimal taxRate = new BigDecimal(goods.getTaxRate());
                //不含税单价
                BigDecimal price = amount.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP);

                goods.setPrice(price.toString());
            }

            System.out.println("计算后的商品数据列表" + goodsList);

            //申请开票信息
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setInvoiceDetails(goodsList);

            //此处需要单独处理，开票需要时间，不能立即查询
            GenerateQdInvoiceResult resultData = InvoiceUtil.generateQdInvoice(generateQdInvoiceEntity);
            //将申请号billNumber存储到发票列表，用于后续查询
            if (resultData.getCode().equals("200")) {
                //如果成功，下载发票信息，并保存
                GetQdInvoiceEntity getQdInvoiceEntity = new GetQdInvoiceEntity();
                getQdInvoiceEntity.setBillNumber(generateQdInvoiceEntity.getBillNumber());
                getQdInvoiceEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                GenerateQdInvoiceResultData data = resultData.getData();
                if ("1101".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"数电认证失效");
                }
                if ("031999".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR,resultData.getData().getZTXX());
                }

                //如果开票成功，需要将数据同步到开票记录中，防止开票过程中，开票信息发生变更无法开票
                invoiceEntity.setSaleName(settingEntity.getCompanyName());
                invoiceEntity.setSaleAddress(settingEntity.getCompanyAddress());
                invoiceEntity.setSalePhone(settingEntity.getCompanyPhone());

                if ("0320000".equals(data.getSecCode())) {
                    //开票中
                    //将开票记录ID存储，方便后期查询
                    GetQdInvoiceTask getQdInvoiceTask = new GetQdInvoiceTask(getQdInvoiceEntity,invoiceEntity,invoiceProperties,minioUploadHelper,smsgProperties,busShopInvoiceMapper,invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    getQdInvoiceTask.start();
                } else if ("0000".equals(data.getSecCode())) {
                    //开票成功
                    QuerySdInvoiceFileEntity querySdInvoiceFileEntity = new QuerySdInvoiceFileEntity();
                    querySdInvoiceFileEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                    querySdInvoiceFileEntity.setFphm(data.getFphm());
                    querySdInvoiceFileEntity.setWjlx("PDF");
                    querySdInvoiceFileEntity.setElectricAccount(staffEntity.getStaffAccount());
                    QuerySdInvoiceFileTask querySdInvoiceFileTask = new QuerySdInvoiceFileTask(generateQdInvoiceEntity.getBillNumber(), querySdInvoiceFileEntity,invoiceEntity,invoiceProperties,minioUploadHelper,smsgProperties,busShopInvoiceMapper,invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    querySdInvoiceFileTask.start();
                }

            } else {
                //开票失败,返回失败原因
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, resultData.getMsg());
            }
        }

        return Result.ok();
    }

    /**
     * 税务发票列表
     *
     * @param params
     * @return
     */
    @Override
    public Result<ShopInvoiceListResult> list(ShopInvoiceListParams params) {
        SysCompanyEntity sysCompanyEntity = sysCompanyMapper.selectById(SatokenUtil.getLoginUserCompanyId());
        QueryWrapper<BusShopInvoiceEntity> invoiceWrapper = handleListWrapper(false, params);
        PageUtils.startPage(params);
        //查询
        List<BusShopInvoiceEntity> invoiceList = busShopInvoiceMapper.selectList(invoiceWrapper);

        //查询店铺信息
        Map<Long, String> shopUniqueShopNameMap = new HashMap<>();
        //用户id-姓名
        Map<Long, String> userIdNameMap = new HashMap<>();
        if (params.getInvoiceType() == 2) { //销项票
            Set<Long> shopUniqueSet = invoiceList.stream().map(BusShopInvoiceEntity::getShopUnique).collect(Collectors.toSet());
            if (ObjectUtil.isNotEmpty(shopUniqueSet)) {
                LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
                shopWrapper.in(BusShopEntity::getShopUnique, shopUniqueSet);
                List<BusShopEntity> shopList = busShopMapper.selectList(shopWrapper);
                shopUniqueShopNameMap = shopList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, BusShopEntity::getShopName));
            }
        } else if (params.getInvoiceType() == 1) { //进项票
            Set<Long> userIds = invoiceList.stream().filter(i -> ObjectUtil.isNotEmpty(i.getModifyUser()) && i.getModifyUser() > 0).map(BusShopInvoiceEntity::getModifyUser).collect(Collectors.toSet());
            userIdNameMap = handleSysUserIdName(userIds);
        }

        //数据转换
        List<ShopInvoiceListDto> dtoList = new ArrayList<>();
        for (BusShopInvoiceEntity entity : invoiceList) {
            ShopInvoiceListDto dto = new ShopInvoiceListDto();
            BeanUtil.copyProperties(entity, dto);
            dto.setInvoiceDate(DateUtil.format(entity.getInvoiceDate(), DatePattern.NORM_DATE_PATTERN));
            dto.setInvoiceKindName(convertInvoiceKind(entity.getInvoiceKind()));
            dto.setShopName(params.getInvoiceType() == 2 ? shopUniqueShopNameMap.get(entity.getShopUnique()) : "");
            dto.setModifyTime(DateUtil.format(entity.getModifyTime(), DatePattern.NORM_DATETIME_PATTERN));
            String username = userIdNameMap.get(entity.getModifyUser());
            dto.setModifyUser(ObjectUtil.isEmpty(username) ? "" : username);
            dto.setInvoiceFlag(sysCompanyEntity.getInvoiceFlag());
            dtoList.add(dto);
        }

        Result<ShopInvoiceListResult> invoiceResult = convertPageData(invoiceList, dtoList, ShopInvoiceListResult.class, params);
        ShopInvoiceListResult result = invoiceResult.getData();
        result.setTotalOrderMoney(BigDecimal.ZERO);
        result.setTotalTaxMoney(BigDecimal.ZERO);
        result.setTotalOrderTaxMoney(BigDecimal.ZERO);

        //查询统计数据
        QueryWrapper<BusShopInvoiceEntity> countListWrapper = handleListWrapper(true, params);
        List<Map<String, Object>> list = busShopInvoiceMapper.selectMaps(countListWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            Map<String, Object> countMap = list.get(0);
            if (ObjectUtil.isNotEmpty(countMap)) {
                result.setTotalOrderMoney(new BigDecimal(countMap.getOrDefault("totalOrderMoney", "0").toString()));
                result.setTotalTaxMoney(new BigDecimal(countMap.getOrDefault("totalTaxMoney", "0").toString()));
                result.setTotalOrderTaxMoney(new BigDecimal(countMap.getOrDefault("orderTaxMoney", "0").toString()));
            }
        }

        return invoiceResult;
    }

    /**
     * 新增进项票
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> add(ShopInvoiceAddParams params) {
        //发票号码是否重复
        LambdaQueryWrapper<BusShopInvoiceEntity> invoiceWrapper = new LambdaQueryWrapper<>();
        invoiceWrapper.eq(BusShopInvoiceEntity::getInvoiceNumber, params.getInvoiceNumber());
        invoiceWrapper.eq(BusShopInvoiceEntity::getInvoiceType, 1);
        List<BusShopInvoiceEntity> invoiceList = busShopInvoiceMapper.selectList(invoiceWrapper);
        if (ObjectUtil.isNotEmpty(invoiceList)) {
            throw new BusinessException(BusShopInvoicerErrorEnum.INVOICE_NUMBER_REPEAT_ERROR);
        }

        LoginUser loginUser = SatokenUtil.getLoginUser();

        //新增进项票
        BusShopInvoiceEntity entity = new BusShopInvoiceEntity();
        BeanUtils.copy(params, entity);
        entity.setCompanyId(loginUser.getCompanyId());
        entity.setInvoiceType(1);
        entity.setCreateUser(loginUser.getId());
        entity.setModifyUser(loginUser.getId());
        entity.setInvoiceDate(DateUtil.parse(params.getInvoiceDate(), DatePattern.NORM_DATE_PATTERN));
        entity.setStatus(1);

        busShopInvoiceMapper.insert(entity);

        return Result.ok();
    }

    /**
     * 发票服务明细
     *
     * @param params
     * @return
     */
    @Override
    public Result<ShopInvoiceDetailResult> invoiceDetail(ShopInvoiceDetailParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();

        //数据校验
        BusShopInvoiceEntity invoice = busShopInvoiceMapper.selectById(params.getInvoiceId());
        if (ObjectUtil.isEmpty(invoice) || !loginUser.getCompanyId().equals(invoice.getCompanyId()))
            throw new BusinessException(CommonErrorEnum.PARAM_ERROR);

        //查询服务明细
        LambdaQueryWrapper<BusShopInvoiceDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(BusShopInvoiceDetailEntity::getInvoiceId, params.getInvoiceId());
        List<BusShopInvoiceDetailEntity> detailList = busShopInvoiceDetailMapper.selectList(detailWrapper);

        List<InvoiceDetailDto> dtoList = new ArrayList<>();
        for (BusShopInvoiceDetailEntity d : detailList) {
            InvoiceDetailDto dto = new InvoiceDetailDto();
            BeanUtil.copyProperties(d, dto);
            dto.setTaxRatePer(d.getTaxRate());
            dto.setTaxRate(StringUtils.join(NumberUtil.mul(d.getTaxRate(), new BigDecimal("100")).stripTrailingZeros().toPlainString(), "%"));
            dtoList.add(dto);
        }

        ShopInvoiceDetailResult result = new ShopInvoiceDetailResult();
        result.setDetailList(dtoList);
        return Result.ok(result);
    }

    @Override
    public Result<ShopInvoiceDetailResult> invoiceDetailByInvoiceIds(ShopInvoiceDetailsParams params) {
        List<BusShopInvoiceEntity> invoiceEntityList = busShopInvoiceMapper.selectBatchIds(params.getInvoiceIds());
        if (ObjectUtil.isNull(invoiceEntityList) || invoiceEntityList.size() != params.getInvoiceIds().size()) {
            return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "部分待开票记录不存在，请刷新后重新选择");
        }
        List<BusShopInvoiceEntity> validateList = invoiceEntityList.stream().filter(v -> ObjectUtil.notEqual(SatokenUtil.getLoginUserCompanyId(), v.getCompanyId())).collect(Collectors.toList());
        if (null != validateList && validateList.size() > 0) {
            return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR);
        }
        //查询服务明细
        LambdaQueryWrapper<BusShopInvoiceDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(BusShopInvoiceDetailEntity::getInvoiceId, invoiceEntityList.stream().map(BusShopInvoiceEntity::getId).collect(Collectors.toSet()));
        List<BusShopInvoiceDetailEntity> detailList = busShopInvoiceDetailMapper.selectList(detailWrapper);

        List<InvoiceDetailDto> dtoList = new ArrayList<>();
        for (BusShopInvoiceDetailEntity d : detailList) {
            InvoiceDetailDto dto = new InvoiceDetailDto();
            BeanUtil.copyProperties(d, dto);
            dto.setTaxRatePer(d.getTaxRate());
            dto.setTaxRate(StringUtils.join(NumberUtil.mul(d.getTaxRate(), new BigDecimal("100")).stripTrailingZeros().toPlainString(), "%"));
            dtoList.add(dto);
        }

        ShopInvoiceDetailResult result = new ShopInvoiceDetailResult();
        result.setDetailList(dtoList);
        return Result.ok(result);
    }

    @Override
    public Result<Void> saveShopInvoice(ShopInvoiceSaveParams params) {
        //校验发票信息是否存在
        BusShopInvoiceEntity invoiceEntity = busShopInvoiceMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(invoiceEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "申请记录不存在");
        }

        if (invoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_1.getValue()) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该记录已开票");
        }

        if (invoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_3.getValue()) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "开票中");
        }
        if (ObjectUtil.isNotEmpty(params.getGoodList())) {
            BigDecimal orderTaxMoney = BigDecimal.ZERO;
            for (ShopInvoiceGoodListAddParams good : params.getGoodList()) {
                orderTaxMoney = orderTaxMoney.add(good.getAmount());
            }
            if (orderTaxMoney.compareTo(invoiceEntity.getOrderTaxMoney()) != 0) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "税价合计与订单总金额不一致！");
            }
        }
        if (ObjectUtil.isNotEmpty(params.getPurchasePersonFlag())) {
            if (ObjectUtil.equals(params.getPurchasePersonFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getPurchaseAddress())
                        || ObjectUtil.isEmpty(params.getPurchasePhone())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "购方地址、电话必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getPurchaseBankFlag())) {
            if (ObjectUtil.equals(params.getPurchaseBankFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getPurchaseBankNo())
                        || ObjectUtil.isEmpty(params.getPurchaseBank())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "购方开户银行、银行账号必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSalePersonFlag())) {
            if (ObjectUtil.equals(params.getSalePersonFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getSaleAddress())
                        || ObjectUtil.isEmpty(params.getSalePhone())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "销售方地址、电话必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSaleBankFlag())) {
            if (ObjectUtil.equals(params.getSaleBankFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getSaleBank())
                        || ObjectUtil.isEmpty(params.getSaleBankNo())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "销售方开户银行、银行账号必填！");
                }
            }
        }
        BeanUtils.copy(params, invoiceEntity);
        invoiceEntity.setModifyUser(SatokenUtil.getLoginUser().getId());
        invoiceEntity.setModifyTime(DateUtil.date());
        busShopInvoiceMapper.updateById(invoiceEntity);

        if (ObjectUtil.isNotEmpty(params.getGoodList())) {
            LambdaQueryWrapper<BusShopInvoiceDetailEntity> shopInvoiceDetailWrapper = new LambdaQueryWrapper<>();
            shopInvoiceDetailWrapper.eq(BusShopInvoiceDetailEntity::getInvoiceId, params.getId());
            List<BusShopInvoiceDetailEntity> shopInvoiceDetailList = busShopInvoiceDetailMapper.selectList(shopInvoiceDetailWrapper);
            if (ObjectUtil.isNotEmpty(shopInvoiceDetailList)) {
                List<Long> shopInvoiceDetailIdList = shopInvoiceDetailList.stream().map(BusShopInvoiceDetailEntity::getId).collect(Collectors.toList());
                busShopInvoiceDetailMapper.deleteBatchIds(shopInvoiceDetailIdList);
            }
            shopInvoiceDetailList = new ArrayList<>();
            for (ShopInvoiceGoodListAddParams good : params.getGoodList()) {
                BusShopInvoiceDetailEntity shopInvoiceDetailEntity = new BusShopInvoiceDetailEntity();
                shopInvoiceDetailEntity.setInvoiceId(params.getId());
                BeanUtils.copy(good, shopInvoiceDetailEntity);
                shopInvoiceDetailEntity.setTaxRate(good.getTaxRatePer());
                shopInvoiceDetailList.add(shopInvoiceDetailEntity);
            }
            busShopInvoiceDetailMapper.insertBatch(shopInvoiceDetailList);
        }
        return Result.ok();
    }

    @Override
    public Result<Void> manualInvoiceApply(ManualInvoiceApplyParams params) {
        //校验发票信息是否存在
        BusShopInvoiceEntity invoiceEntity = busShopInvoiceMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(invoiceEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "申请记录不存在");
        }

        if (invoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_1.getValue()) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该记录已开票");
        }

        if (invoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_3.getValue()) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "开票中");
        }
        if (ObjectUtil.isEmpty(params.getGoodList())) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "未配置开票信息");
        }
        if (ObjectUtil.isNotEmpty(params.getGoodList())) {
            BigDecimal orderTaxMoney = BigDecimal.ZERO;
            for (ManualInvoiceApplyGoodListParams good : params.getGoodList()) {
                orderTaxMoney = orderTaxMoney.add(good.getAmount());
            }
            if (orderTaxMoney.compareTo(invoiceEntity.getOrderTaxMoney()) != 0) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "税价合计与订单总金额不一致！");
            }
        }
        if (ObjectUtil.isNotEmpty(params.getPurchasePersonFlag())) {
            if (ObjectUtil.equals(params.getPurchasePersonFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getPurchaseAddress())
                        || ObjectUtil.isEmpty(params.getPurchasePhone())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "购方地址、电话必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getPurchaseBankFlag())) {
            if (ObjectUtil.equals(params.getPurchaseBankFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getPurchaseBankNo())
                        || ObjectUtil.isEmpty(params.getPurchaseBank())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "购方开户银行、银行账号必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSalePersonFlag())) {
            if (ObjectUtil.equals(params.getSalePersonFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getSaleAddress())
                        || ObjectUtil.isEmpty(params.getSalePhone())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "销售方地址、电话必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSaleBankFlag())) {
            if (ObjectUtil.equals(params.getSaleBankFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getSaleBank())
                        || ObjectUtil.isEmpty(params.getSaleBankNo())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "销售方开户银行、银行账号必填！");
                }
            }
        }
        BeanUtils.copy(params, invoiceEntity);
        if (ObjectUtil.isNotEmpty(params.getPurchasePersonFlag())) {
            if (ObjectUtil.equals(params.getPurchasePersonFlag(), 1)) {
                if (!ObjectUtil.contains(params.getNotes(), params.getPurchaseAddress())
                        && !ObjectUtil.contains(params.getNotes(), params.getPurchasePhone())) {
                    invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 购买方企业电话：" + params.getPurchasePhone() + " 购买方企业地址：" + params.getPurchaseAddress());
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getPurchaseBankFlag())) {
            if (ObjectUtil.equals(params.getPurchaseBankFlag(), 1)) {
                if (!ObjectUtil.contains(params.getNotes(), params.getPurchaseBankNo())
                        && !ObjectUtil.contains(params.getNotes(), params.getPurchaseBank())) {
                    invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 购买方银行账号：" + params.getPurchaseBankNo() + " 购买方开户银行：" + params.getPurchaseBank());
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSalePersonFlag())) {
            if (ObjectUtil.equals(params.getSalePersonFlag(), 1)) {
                if (!ObjectUtil.contains(params.getNotes(), params.getSaleAddress())
                        && !ObjectUtil.contains(params.getNotes(), params.getSalePhone())) {
                    invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 销售方企业电话：" + params.getSalePhone() + " 销售方企业地址：" + params.getSaleAddress());
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSaleBankFlag())) {
            if (ObjectUtil.equals(params.getSaleBankFlag(), 1)) {
                if (!ObjectUtil.contains(params.getNotes(), params.getSaleBank())
                        && !ObjectUtil.contains(params.getNotes(), params.getSaleBankNo())) {
                    invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 销售方银行账号：" + params.getSaleBankNo() + " 销售方开户银行：" + params.getSaleBank());
                }
            }
        }
        invoiceEntity.setModifyUser(SatokenUtil.getLoginUser().getId());
        invoiceEntity.setModifyTime(DateUtil.date());
        invoiceEntity.setInvoiceDate(new Date());
        busShopInvoiceMapper.updateById(invoiceEntity);

        if (ObjectUtil.isNotEmpty(params.getGoodList())) {
            LambdaQueryWrapper<BusShopInvoiceDetailEntity> shopInvoiceDetailWrapper = new LambdaQueryWrapper<>();
            shopInvoiceDetailWrapper.eq(BusShopInvoiceDetailEntity::getInvoiceId, params.getId());
            List<BusShopInvoiceDetailEntity> shopInvoiceDetailList = busShopInvoiceDetailMapper.selectList(shopInvoiceDetailWrapper);
            if (ObjectUtil.isNotEmpty(shopInvoiceDetailList)) {
                List<Long> shopInvoiceDetailIdList = shopInvoiceDetailList.stream().map(BusShopInvoiceDetailEntity::getId).collect(Collectors.toList());
                busShopInvoiceDetailMapper.deleteBatchIds(shopInvoiceDetailIdList);
            }
            shopInvoiceDetailList = new ArrayList<>();
            for (ManualInvoiceApplyGoodListParams good : params.getGoodList()) {
                BusShopInvoiceDetailEntity shopInvoiceDetailEntity = new BusShopInvoiceDetailEntity();
                shopInvoiceDetailEntity.setInvoiceId(params.getId());
                BeanUtils.copy(good, shopInvoiceDetailEntity);
                shopInvoiceDetailEntity.setTaxRate(good.getTaxRatePer());
                shopInvoiceDetailList.add(shopInvoiceDetailEntity);
            }
            busShopInvoiceDetailMapper.insertBatch(shopInvoiceDetailList);
        }
        //根据店铺发票期数，选择开票方式
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> settingWrapper = new LambdaQueryWrapper<>();
        settingWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, companyId);
        //查询是否配置了开票信息
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(settingWrapper);
        if (ObjectUtil.isEmpty(settingEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该企业未配置发票信息");
        }

        //三期税务
        if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel()) {
            invoiceEntity.setPeriodsLevel(PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel());

            //三期开票功能，三期开票和发送一起，不需要单独处理
            //三期需要验证税盘是否在线


        } else if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel()) {
            //获取当前已登录的账号信息
            LambdaQueryWrapper<BusInvoiceStaffEntity> staffWrapper = new LambdaQueryWrapper<>();
            staffWrapper.eq(BusInvoiceStaffEntity::getCompanyId, companyId);
            staffWrapper.eq(BusInvoiceStaffEntity::getIsLogin, StaffIsLogin.IS_LOGIN.getValue());
            staffWrapper.orderByDesc(BusInvoiceStaffEntity::getLastLoginTime);
            BusInvoiceStaffEntity staffEntity = busInvoiceStaffMapper.selectOne(staffWrapper);
            if (null == staffEntity) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "请先登录全电账户");
            }
            //四期税务
            GenerateQdInvoiceEntity generateQdInvoiceEntity = new GenerateQdInvoiceEntity();

            BeanUtil.copyProperties(invoiceEntity, settingEntity);
            generateQdInvoiceEntity.setElectricAccount(staffEntity.getStaffAccount());
            generateQdInvoiceEntity.setInvoiceType(InvoiceTypeEnum.NORMAT.getValue());
            generateQdInvoiceEntity.setClientName(invoiceEntity.getPurchaseName());
            generateQdInvoiceEntity.setClientTaxCode(invoiceEntity.getPurchaseIdentity());
            generateQdInvoiceEntity.setClientBankName(invoiceEntity.getPurchaseBank());
            generateQdInvoiceEntity.setClientBankAccountNumber(invoiceEntity.getPurchaseBankNo());
            generateQdInvoiceEntity.setClientAddress(invoiceEntity.getPurchaseAddress());
            generateQdInvoiceEntity.setClientPhone(invoiceEntity.getPurchasePhone());
            generateQdInvoiceEntity.setSellerName(settingEntity.getCompanyName());
            generateQdInvoiceEntity.setSelesAddress(settingEntity.getCompanyAddress());
            generateQdInvoiceEntity.setSellerTaxCode(settingEntity.getCompanyTaxNo());
            generateQdInvoiceEntity.setSellerBankName(settingEntity.getInvoiceBankName());
            generateQdInvoiceEntity.setSellerBankAccountNumber(settingEntity.getInvoiceBankCard());
            generateQdInvoiceEntity.setSellerPhone(settingEntity.getCompanyPhone());

            generateQdInvoiceEntity.setInvoicer(settingEntity.getInvoiceMan());
            generateQdInvoiceEntity.setChecker(settingEntity.getChecker());
            generateQdInvoiceEntity.setCashier(settingEntity.getPayee());
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setBillNumber("" + System.currentTimeMillis());
            generateQdInvoiceEntity.setInvoiceKind(InvoiceKindEnum.NormalTicket.getValue());
            generateQdInvoiceEntity.setTotalAmount(invoiceEntity.getOrderTaxMoney() + "");
            generateQdInvoiceEntity.setAmount(invoiceEntity.getOrderMoney() + "");
            generateQdInvoiceEntity.setTaxAmount(invoiceEntity.getTaxMoney() + "");
            generateQdInvoiceEntity.setNotes(invoiceEntity.getNotes());

            System.out.println("当前申请的开票信息为" + generateQdInvoiceEntity);
            //合并同类项，计算税额
            List<GenerateQdInvoiceGoodsEntity> goodsList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(params.getGoodList())) {
                int i = 1;
                for (ManualInvoiceApplyGoodListParams good : params.getGoodList()) {
                    GenerateQdInvoiceGoodsEntity detail = new GenerateQdInvoiceGoodsEntity();
                    detail.setGoodsTaxNo(good.getTaxClassificationCode());
                    detail.setGoodsName(good.getGoodsName());
                    detail.setAmount(NumberUtil.round(good.getAmount(), 2) + "");
                    detail.setPrice(good.getPrice() + "");
                    detail.setNumber(good.getQuantity() + "");
                    detail.setTaxRate(good.getTaxRatePer() + "");
                    detail.setTaxAmount(good.getTaxAmount() + "");
                    detail.setRowNumbe(i + "");
                    detail.setRowKind("0");
                    detail.setPriceKind("1");
                    detail.setStandard(good.getSpec());
                    detail.setUnit(good.getUnit());
                    detail.setOriginalRowNumber("1");
                    detail.setTaxPre(TaxPreEnum.DOTUSE.getValue());
                    goodsList.add(detail);
                    i++;
                }
            }

            /*//查询商品明细
            String saleListUnique = invoiceEntity.getSaleListUnique();
            List<InvoiceGoodsExternalEntity> details = busSaleListDetailMapper.querySaleListDetailForInvoice(saleListUnique);
            //根据商品详情计算税额信息
            if (ObjectUtil.isEmpty(details)){
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"未配置开票信息设置");
            }

            //合并同类项，计算税额
            List<GenerateQdInvoiceGoodsEntity> goodsList = new ArrayList<>();
            BigDecimal goodsTotal = BigDecimal.ZERO;

            for (GenerateQdInvoiceGoodsEntity detail : details) {
                if (detail.getGoodsTaxNo() == null) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"未配置开票信息设置");
                }
                detail.setTaxPre(TaxPreEnum.DOTUSE.getValue());
                goodsTotal = goodsTotal.add(new BigDecimal(detail.getAmount()));
                boolean haveSaveGoods = false;
                for (GenerateQdInvoiceGoodsEntity goods : goodsList) {
                    if (goods.getGoodsTaxNo().equals(detail.getGoodsTaxNo()) && goods.getGoodsName().equals(detail.getGoodsName())) {
                        haveSaveGoods = true;
                        System.out.println("当前已有数据信息" + detail);
                        System.out.println("匹配到的数据" + goods);
                        detail.setAmount((new BigDecimal(goods.getAmount()).add(new BigDecimal(detail.getAmount())).setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
                        System.out.println("匹配后的数据" + detail);
                        break;
                    }
                }
                if (!haveSaveGoods) {
                    goodsList.add(detail);
                }
            }

            //应开商品总额（含税）
            BigDecimal balanceTotal = new BigDecimal(invoiceEntity.getOrderTaxMoney().toString());
            BigDecimal balance = balanceTotal;

            //根据订单优惠后的总金额，计算各商品应开票金额
            for (Integer i = 0; i < goodsList.size(); i++) {
                if (i == goodsList.size() - 1) {
                    goodsList.get(i).setAmount(balance.toString());
                    break;
                }
                //不是最后一项
                GenerateQdInvoiceGoodsEntity goods = goodsList.get(i);
                BigDecimal amount = new BigDecimal(goods.getAmount());
                amount = goodsTotal.multiply(amount).divide(balanceTotal, 2, BigDecimal.ROUND_HALF_UP);
                goodsList.get(i).setAmount(amount.toString());
            }


            for (GenerateQdInvoiceGoodsEntity goods : goodsList ) {
                //根据金额，计算税额
                BigDecimal amount = new BigDecimal(goods.getAmount());
                //税率
                BigDecimal taxRate = new BigDecimal(goods.getTaxRate());
                //不含税单价
                BigDecimal price = amount.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP)), 2, BigDecimal.ROUND_HALF_UP);

                goods.setPrice(price.toString());
            }

            System.out.println("计算后的商品数据列表" + goodsList);*/

            //申请开票信息
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setInvoiceDetails(goodsList);

            //此处需要单独处理，开票需要时间，不能立即查询
            GenerateQdInvoiceResult resultData = InvoiceUtil.generateQdInvoice(generateQdInvoiceEntity);
            //将申请号billNumber存储到发票列表，用于后续查询
            if (resultData.getCode().equals("200")) {
                //如果成功，下载发票信息，并保存
                GetQdInvoiceEntity getQdInvoiceEntity = new GetQdInvoiceEntity();
                getQdInvoiceEntity.setBillNumber(generateQdInvoiceEntity.getBillNumber());
                getQdInvoiceEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                GenerateQdInvoiceResultData data = resultData.getData();
                if ("1101".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"数电认证失效");
                }
                if ("031999".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR,resultData.getData().getZTXX());
                }
                invoiceEntity.setBillNumber(generateQdInvoiceEntity.getBillNumber());
                busShopInvoiceMapper.updateById(invoiceEntity);

                //如果开票成功，需要将数据同步到开票记录中，防止开票过程中，开票信息发生变更无法开票
                invoiceEntity.setSaleName(settingEntity.getCompanyName());
                invoiceEntity.setSaleAddress(settingEntity.getCompanyAddress());
                invoiceEntity.setSalePhone(settingEntity.getCompanyPhone());
                if ("0320000".equals(data.getSecCode())) {
                    //开票中
                    //将开票记录ID存储，方便后期查询
                    GetQdInvoiceTask getQdInvoiceTask = new GetQdInvoiceTask(getQdInvoiceEntity,invoiceEntity,invoiceProperties,minioUploadHelper,smsgProperties,busShopInvoiceMapper,invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    getQdInvoiceTask.start();
                } else if ("0000".equals(data.getSecCode())) {
                    //开票成功
                    QuerySdInvoiceFileEntity querySdInvoiceFileEntity = new QuerySdInvoiceFileEntity();
                    querySdInvoiceFileEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                    querySdInvoiceFileEntity.setFphm(data.getFphm());
                    querySdInvoiceFileEntity.setWjlx("PDF");
                    querySdInvoiceFileEntity.setElectricAccount(staffEntity.getStaffAccount());
                    QuerySdInvoiceFileTask querySdInvoiceFileTask = new QuerySdInvoiceFileTask(generateQdInvoiceEntity.getBillNumber(), querySdInvoiceFileEntity,invoiceEntity,invoiceProperties,minioUploadHelper,smsgProperties,busShopInvoiceMapper,invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    querySdInvoiceFileTask.start();
                }

            } else {
                //开票失败,返回失败原因
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, resultData.getMsg());
            }
        }

        return Result.ok();
    }

    @Override
    public Result<Void> manualBatchInvoiceApply(ManualBatchInvoiceApplyParams params) {
        //校验发票信息是否存在
        List<BusShopInvoiceEntity> invoiceEntityList = busShopInvoiceMapper.selectBatchIds(params.getIdList());
        if (CollectionUtil.isEmpty(invoiceEntityList)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "申请记录不存在");
        }

        List<BusShopInvoiceEntity> validateList = invoiceEntityList.stream().filter(v -> ObjectUtil.notEqual(InvoiceStatusEnum.INVOICE_STATUS_2.getValue(), v.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(validateList)) {
            List<Long> shopUniques = validateList.stream().map(BusShopInvoiceEntity::getShopUnique).collect(Collectors.toList());
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "订单" + StrUtil.join(",", shopUniques.iterator()) + "记录非未开票状态");
        }
        if (ObjectUtil.isEmpty(params.getGoodList())) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "未配置开票信息");
        }
        if (ObjectUtil.isNotEmpty(params.getPurchasePersonFlag())) {
            if (ObjectUtil.equals(params.getPurchasePersonFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getPurchaseAddress())
                        || ObjectUtil.isEmpty(params.getPurchasePhone())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "购方地址、电话必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getPurchaseBankFlag())) {
            if (ObjectUtil.equals(params.getPurchaseBankFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getPurchaseBankNo())
                        || ObjectUtil.isEmpty(params.getPurchaseBank())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "购方开户银行、银行账号必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSalePersonFlag())) {
            if (ObjectUtil.equals(params.getSalePersonFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getSaleAddress())
                        || ObjectUtil.isEmpty(params.getSalePhone())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "销售方地址、电话必填！");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(params.getSaleBankFlag())) {
            if (ObjectUtil.equals(params.getSaleBankFlag(), 1)) {
                if (ObjectUtil.isEmpty(params.getSaleBank())
                        || ObjectUtil.isEmpty(params.getSaleBankNo())) {
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "销售方开户银行、银行账号必填！");
                }
            }
        }
        for (BusShopInvoiceEntity invoiceEntity : invoiceEntityList) {
            BeanUtils.copy(params, invoiceEntity);
            if (ObjectUtil.isNotEmpty(params.getPurchasePersonFlag())) {
                if (ObjectUtil.equals(params.getPurchasePersonFlag(), 1)) {
                    if (!ObjectUtil.contains(params.getNotes(), params.getPurchaseAddress())
                            && !ObjectUtil.contains(params.getNotes(), params.getPurchasePhone())) {
                        invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 购买方企业电话：" + params.getPurchasePhone() + " 购买方企业地址：" + params.getPurchaseAddress());
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(params.getPurchaseBankFlag())) {
                if (ObjectUtil.equals(params.getPurchaseBankFlag(), 1)) {
                    if (!ObjectUtil.contains(params.getNotes(), params.getPurchaseBankNo())
                            && !ObjectUtil.contains(params.getNotes(), params.getPurchaseBank())) {
                        invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 购买方银行账号：" + params.getPurchaseBankNo() + " 购买方开户银行：" + params.getPurchaseBank());
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(params.getSalePersonFlag())) {
                if (ObjectUtil.equals(params.getSalePersonFlag(), 1)) {
                    if (!ObjectUtil.contains(params.getNotes(), params.getSaleAddress())
                            && !ObjectUtil.contains(params.getNotes(), params.getSalePhone())) {
                        invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 销售方企业电话：" + params.getSalePhone() + " 销售方企业地址：" + params.getSaleAddress());
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(params.getSaleBankFlag())) {
                if (ObjectUtil.equals(params.getSaleBankFlag(), 1)) {
                    if (!ObjectUtil.contains(params.getNotes(), params.getSaleBank())
                            && !ObjectUtil.contains(params.getNotes(), params.getSaleBankNo())) {
                        invoiceEntity.setNotes((ObjectUtil.isNotEmpty(invoiceEntity.getNotes()) ? invoiceEntity.getNotes() : "") + " 销售方银行账号：" + params.getSaleBankNo() + " 销售方开户银行：" + params.getSaleBank());
                    }
                }
            }
            invoiceEntity.setModifyUser(SatokenUtil.getLoginUser().getId());
            invoiceEntity.setModifyTime(DateUtil.date());
            invoiceEntity.setInvoiceDate(new Date());
        }

        busShopInvoiceMapper.updateBatchById(invoiceEntityList);
        //根据店铺发票期数，选择开票方式
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> settingWrapper = new LambdaQueryWrapper<>();
        settingWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, companyId);
        //查询是否配置了开票信息
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(settingWrapper);
        if (ObjectUtil.isEmpty(settingEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该企业未配置发票信息");
        }
        BusShopInvoiceEntity invoiceEntity = invoiceEntityList.get(0);
        //三期税务
        if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel()) {
            invoiceEntity.setPeriodsLevel(PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel());

            //三期开票功能，三期开票和发送一起，不需要单独处理
            //三期需要验证税盘是否在线

        } else if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel()) {
            //获取当前已登录的账号信息
            LambdaQueryWrapper<BusInvoiceStaffEntity> staffWrapper = new LambdaQueryWrapper<>();
            staffWrapper.eq(BusInvoiceStaffEntity::getCompanyId, companyId);
            staffWrapper.eq(BusInvoiceStaffEntity::getIsLogin, StaffIsLogin.IS_LOGIN.getValue());
            staffWrapper.orderByDesc(BusInvoiceStaffEntity::getLastLoginTime);
            BusInvoiceStaffEntity staffEntity = busInvoiceStaffMapper.selectOne(staffWrapper);
            if (null == staffEntity) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "请先登录全电账户");
            }
            //四期税务
            GenerateQdInvoiceEntity generateQdInvoiceEntity = new GenerateQdInvoiceEntity();

            BeanUtil.copyProperties(invoiceEntity, settingEntity);
            generateQdInvoiceEntity.setElectricAccount(staffEntity.getStaffAccount());
            generateQdInvoiceEntity.setInvoiceType(InvoiceTypeEnum.NORMAT.getValue());
            generateQdInvoiceEntity.setClientName(invoiceEntity.getPurchaseName());
            generateQdInvoiceEntity.setClientTaxCode(invoiceEntity.getPurchaseIdentity());
            generateQdInvoiceEntity.setClientBankName(invoiceEntity.getPurchaseBank());
            generateQdInvoiceEntity.setClientBankAccountNumber(invoiceEntity.getPurchaseBankNo());
            generateQdInvoiceEntity.setClientAddress(invoiceEntity.getPurchaseAddress());
            generateQdInvoiceEntity.setClientPhone(invoiceEntity.getPurchasePhone());
            generateQdInvoiceEntity.setSellerName(settingEntity.getCompanyName());
            generateQdInvoiceEntity.setSelesAddress(settingEntity.getCompanyAddress());
            generateQdInvoiceEntity.setSellerTaxCode(settingEntity.getCompanyTaxNo());
            generateQdInvoiceEntity.setSellerBankName(settingEntity.getInvoiceBankName());
            generateQdInvoiceEntity.setSellerBankAccountNumber(settingEntity.getInvoiceBankCard());
            generateQdInvoiceEntity.setSellerPhone(settingEntity.getCompanyPhone());

            generateQdInvoiceEntity.setInvoicer(settingEntity.getInvoiceMan());
            generateQdInvoiceEntity.setChecker(settingEntity.getChecker());
            generateQdInvoiceEntity.setCashier(settingEntity.getPayee());
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setBillNumber("" + System.currentTimeMillis());
            generateQdInvoiceEntity.setInvoiceKind(InvoiceKindEnum.NormalTicket.getValue());
            BigDecimal totalOrderTaxMoney = invoiceEntityList.stream().map(BusShopInvoiceEntity::getOrderTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            generateQdInvoiceEntity.setTotalAmount(totalOrderTaxMoney + "");
            BigDecimal totalOrderMoney = invoiceEntityList.stream().map(BusShopInvoiceEntity::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            generateQdInvoiceEntity.setAmount(totalOrderMoney + "");
            BigDecimal totalTaxAmount = invoiceEntityList.stream().map(BusShopInvoiceEntity::getTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            generateQdInvoiceEntity.setTaxAmount(totalTaxAmount + "");
            generateQdInvoiceEntity.setNotes(invoiceEntity.getNotes());

            System.out.println("当前申请的开票信息为" + generateQdInvoiceEntity);
            //合并同类项，计算税额
            List<GenerateQdInvoiceGoodsEntity> goodsList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(params.getGoodList())) {
                int i = 1;
                for (ManualInvoiceApplyGoodListParams good : params.getGoodList()) {
                    GenerateQdInvoiceGoodsEntity detail = new GenerateQdInvoiceGoodsEntity();
                    detail.setGoodsTaxNo(good.getTaxClassificationCode());
                    detail.setGoodsName(good.getGoodsName());
                    detail.setAmount(NumberUtil.round(good.getAmount(), 2) + "");
                    detail.setPrice(good.getPrice() + "");
                    detail.setNumber(good.getQuantity() + "");
                    detail.setTaxRate(good.getTaxRatePer() + "");
                    detail.setTaxAmount(good.getTaxAmount() + "");
                    detail.setRowNumbe(i + "");
                    detail.setRowKind("0");
                    detail.setPriceKind("1");
                    detail.setStandard(good.getSpec());
                    detail.setUnit(good.getUnit());
                    detail.setOriginalRowNumber("1");
                    detail.setTaxPre(TaxPreEnum.DOTUSE.getValue());
                    goodsList.add(detail);
                    i++;
                }
            }

            //申请开票信息
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setInvoiceDetails(goodsList);

            //此处需要单独处理，开票需要时间，不能立即查询
            GenerateQdInvoiceResult resultData = InvoiceUtil.generateQdInvoice(generateQdInvoiceEntity);
            //将申请号billNumber存储到发票列表，用于后续查询
            if (resultData.getCode().equals("200")) {
                //如果成功，下载发票信息，并保存
                GetQdInvoiceEntity getQdInvoiceEntity = new GetQdInvoiceEntity();
                getQdInvoiceEntity.setBillNumber(generateQdInvoiceEntity.getBillNumber());
                getQdInvoiceEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                GenerateQdInvoiceResultData data = resultData.getData();
                if ("1101".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"数电认证失效");
                }
                if ("031999".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR,resultData.getData().getZTXX());
                }
                invoiceEntityList.stream().forEach(v -> {
                    v.setBillNumber(generateQdInvoiceEntity.getBillNumber());
                    //如果开票成功，需要将数据同步到开票记录中，防止开票过程中，开票信息发生变更无法开票
                    v.setSaleName(settingEntity.getCompanyName());
                    v.setSaleAddress(settingEntity.getCompanyAddress());
                    v.setSalePhone(settingEntity.getCompanyPhone());
                });
                busShopInvoiceMapper.updateBatchById(invoiceEntityList);

                if ("0320000".equals(data.getSecCode())) {
                    //开票中
                    //将开票记录ID存储，方便后期查询
                    GetQdInvoiceBatchTask getQdInvoiceTask = new GetQdInvoiceBatchTask(getQdInvoiceEntity, invoiceEntityList, invoiceProperties, minioUploadHelper, smsgProperties, busShopInvoiceMapper, invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    getQdInvoiceTask.start();
                } else if ("0000".equals(data.getSecCode())) {
                    //开票成功
                    QuerySdInvoiceFileEntity querySdInvoiceFileEntity = new QuerySdInvoiceFileEntity();
                    querySdInvoiceFileEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                    querySdInvoiceFileEntity.setFphm(data.getFphm());
                    querySdInvoiceFileEntity.setWjlx("PDF");
                    querySdInvoiceFileEntity.setElectricAccount(staffEntity.getStaffAccount());
                    QuerySdInvoiceFileBatchTask querySdInvoiceFileBatchTask = new QuerySdInvoiceFileBatchTask(generateQdInvoiceEntity.getBillNumber(), querySdInvoiceFileEntity,invoiceEntityList,invoiceProperties,minioUploadHelper,smsgProperties,busShopInvoiceMapper,invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    querySdInvoiceFileBatchTask.start();
                }
            } else {
                //开票失败,返回失败原因
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, resultData.getMsg());
            }
        }

        return Result.ok();
    }

    @Override
    public Result<ShopGoodQueryList> queryGoodList(ShopGoodQueryParams params) {
        ShopGoodQueryList result = new ShopGoodQueryList();
        result.setList(new ArrayList<>());
        LoginUser loginUser = SatokenUtil.getLoginUser();

        //数据校验
        BusShopInvoiceEntity invoice = busShopInvoiceMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(invoice) || !loginUser.getCompanyId().equals(invoice.getCompanyId())) {
            throw new BusinessException(CommonErrorEnum.PARAM_ERROR);
        }
        Long shopUnique = invoice.getShopUnique();
        LambdaQueryWrapper<BusGoodsEntity> goodListWrapper = new LambdaQueryWrapper<>();
        goodListWrapper.eq(BusGoodsEntity::getCompanyId, loginUser.getCompanyId());
        goodListWrapper.eq(BusGoodsEntity::getShopUnique, shopUnique);
        goodListWrapper.eq(BusGoodsEntity::getDelFlag, 0);
        goodListWrapper.select(BusGoodsEntity::getGoodsBarcode, BusGoodsEntity::getGoodsName, BusGoodsEntity::getCategoryId);
        List<BusGoodsEntity> goodList = busGoodsMapper.selectList(goodListWrapper);
        if (ObjectUtil.isNotEmpty(goodList)) {
            Set<Long> categoryIdSet = goodList.stream().filter(Objects::nonNull).map(BusGoodsEntity::getCategoryId).collect(Collectors.toSet());
            LambdaQueryWrapper<BusGoodsCategoryEntity> goodCategoryWrapper = new LambdaQueryWrapper<>();
            goodCategoryWrapper.in(BusGoodsCategoryEntity::getId, categoryIdSet);
            goodCategoryWrapper.eq(BusGoodsCategoryEntity::getDelFlag, 0);
            goodCategoryWrapper.eq(BusGoodsCategoryEntity::getEnableStatus, 1);
            goodCategoryWrapper.eq(BusGoodsCategoryEntity::getCompanyId, loginUser.getCompanyId());
            goodCategoryWrapper.eq(BusGoodsCategoryEntity::getParentId, 0);
            goodCategoryWrapper.select(BusGoodsCategoryEntity::getId, BusGoodsCategoryEntity::getTaxRate, BusGoodsCategoryEntity::getCategoryNo);
            List<BusGoodsCategoryEntity> goodCategoryList = busGoodsCategoryMapper.selectList(goodCategoryWrapper);
            List<ShopGoodQueryDto> dtoList = goodList.stream().map(goods -> {
                ShopGoodQueryDto dto = new ShopGoodQueryDto();
                dto.setGoodsBarcode(goods.getGoodsBarcode());
                dto.setGoodsName(goods.getGoodsName());
                if (ObjectUtil.isNotEmpty(goods.getCategoryId())) {
                    if (ObjectUtil.isNotEmpty(goodCategoryList) && goodCategoryList.stream().anyMatch(category -> category.getId().equals(goods.getCategoryId()))) {
                        if (ObjectUtil.isNotEmpty(goodCategoryList.stream().filter(category -> category.getId().equals(goods.getCategoryId())).findFirst().get().getCategoryNo())) {
                            dto.setTaxClassificationCode(goodCategoryList.stream().filter(category -> category.getId().equals(goods.getCategoryId())).findFirst().get().getCategoryNo());
                        }
                        if (ObjectUtil.isNotEmpty(goodCategoryList.stream().filter(category -> category.getId().equals(goods.getCategoryId())).findFirst().get().getTaxRate())) {
                            dto.setTaxRatePer(NumberUtil.div(goodCategoryList.stream().filter(category -> category.getId().equals(goods.getCategoryId())).findFirst().get().getTaxRate(), 100, 2));
                            dto.setTaxRate(StringUtils.join(goodCategoryList.stream().filter(category -> category.getId().equals(goods.getCategoryId())).findFirst().get().getTaxRate().stripTrailingZeros().toPlainString(), "%"));
                        }
                    }
                }
                return dto;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(dtoList)) {
                result.setList(dtoList);
            } else {
                result.setList(Collections.emptyList());
            }
        }
        return Result.ok(result);
    }

    @Override
    public Result<Void> queryInvoiceResult(InvoiceResultQueryParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        GetQdInvoiceEntity getQdInvoiceEntity = new GetQdInvoiceEntity();
        BeanUtil.copyProperties(params, getQdInvoiceEntity);
        String faPiaoPath = invoiceProperties.getFilePath();
        BusShopInvoiceEntity invoiceEntity = busShopInvoiceMapper.selectById(params.getInvoiceId());
        //查询店铺 取店铺类型
        BusShopEntity busShopEntity = busShopMapper.selectOne(Wrappers.<BusShopEntity>lambdaQuery().eq(BusShopEntity::getShopUnique, invoiceEntity.getShopUnique()));
        String url = invoiceUrlUtil.buildUrl(busShopEntity.getShopType());
        if (ObjectUtil.isEmpty(invoiceEntity) || !loginUser.getCompanyId().equals(invoiceEntity.getCompanyId()))
            throw new BusinessException(CommonErrorEnum.PARAM_ERROR);
        try {
            ImageMergeUtil.checkFilePath(faPiaoPath);
            GetQdInvoiceResult getQdInvoiceResult = InvoiceUtil.getQdInvoice(getQdInvoiceEntity);
            if (getQdInvoiceResult.getCode().equals("200")) {
                //获取开票信息成功
                List<GetQdInvoiceResultData> dataList = getQdInvoiceResult.getData();
                //正常会有多张发票吗？
                for (GetQdInvoiceResultData d : dataList) {
                    //验证开票状态
                    String statusCode = d.getStatusCode();
                    if (!statusCode.equals("030000")) {
                        continue;
                    }
                    invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_1.getValue());
                    String fileContent = d.getFileContent();
                    File file = InvoiceUtil.base64ToFile(fileContent, faPiaoPath + invoiceEntity.getSaleListUnique() + "." + d.getFileType());
                    System.out.println("本地存储的绝对路径为=====" + file.getAbsolutePath());

                    //转换图片格式
                    InvoiceUtil.pdfToImage(file.getAbsolutePath(), faPiaoPath);
                    MinioUploadResult minioUploadResult = minioUploadHelper.uploadImg(file);
                    invoiceEntity.setImageUrl(minioUploadResult.getUrl());
                    invoiceEntity.setPeriodsLevel(PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel());
                    //将发票发送给客户
                    String receiveMsg = invoiceEntity.getReceiveMsg();
                    if (ObjectUtil.isNotEmpty(receiveMsg)) {
                        //校验地址格式是手机号
                        if (SystemUtil.isValidPhoneNumber(receiveMsg)) {
                            //发送短信
                            Map<String, String> map = new HashMap<>();
                            //注意,imgurl不包含域名信息
                            map.put("recordId", invoiceEntity.getId().toString());
                            AliyunSmsTemplate smsTemplate = new AliyunSmsTemplate(smsgProperties);
                            smsTemplate.send(receiveMsg, smsgProperties.getTemplatedIdForInvoice(), map);
                        } else if (SystemUtil.isValidEmail(receiveMsg)) {
                            //发送邮件
                            MailUtil.send(receiveMsg, "电子发票", "谢谢您的光临", false, file);
                        }
                    }
                    try {
                        NotifyInvoiceResultParams notifyInvoiceResultParams = new NotifyInvoiceResultParams();
                        notifyInvoiceResultParams.setSaleListUnique(invoiceEntity.getSaleListUnique());
                        notifyInvoiceResultParams.setBillNumber(getQdInvoiceEntity.getBillNumber());
                        notifyInvoiceResultParams.setStatus(3);
                        notifyInvoiceResultParams.setImageUrl(minioUploadResult.getUrl());
                        System.out.println("通知开票状态:" + url + "[" + JSONUtil.toJsonStr(notifyInvoiceResultParams) + "]");
                        HttpUtil.post(url, JSONUtil.toJsonStr(notifyInvoiceResultParams));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            invoiceEntity.setBillNumber(getQdInvoiceEntity.getBillNumber());
            busShopInvoiceMapper.updateById(invoiceEntity);
        } catch (Exception e) {
            System.out.println("开票失败zz");
            e.printStackTrace();
            //更新发票状态为开票失败
            invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_4.getValue());
            busShopInvoiceMapper.updateById(invoiceEntity);
            try {
                NotifyInvoiceResultParams notifyInvoiceResultParams = new NotifyInvoiceResultParams();
                notifyInvoiceResultParams.setSaleListUnique(invoiceEntity.getSaleListUnique());
                notifyInvoiceResultParams.setBillNumber(invoiceEntity.getBillNumber());
                notifyInvoiceResultParams.setStatus(4);
                notifyInvoiceResultParams.setReviewComments(e.getMessage());
                System.out.println("通知开票状态:" + url + "[" + JSONUtil.toJsonStr(notifyInvoiceResultParams) + "]");
                HttpUtil.post(url, JSONUtil.toJsonStr(notifyInvoiceResultParams));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
        return Result.ok();
    }


    /**
     * 处理查询参数
     *
     * @param params
     * @return
     */
    private QueryWrapper<BusShopInvoiceEntity> handleListWrapper(boolean isCount, ShopInvoiceListParams params) {
        QueryWrapper<BusShopInvoiceEntity> invoiceWrapper = new QueryWrapper<>();
        if (!isCount) {
            invoiceWrapper.orderByDesc("id");
        }
        invoiceWrapper.eq("company_id", SatokenUtil.getLoginUserCompanyId());
        invoiceWrapper.eq("invoice_type", params.getInvoiceType());
        if (ObjectUtil.isNotEmpty(params.getInvoiceKind())) {
            invoiceWrapper.eq("invoice_kind", params.getInvoiceKind());
        }
        if (ObjectUtil.isNotEmpty(params.getInvoiceNumber())) {
            invoiceWrapper.eq("invoice_number", params.getInvoiceNumber());
        }
        if (ObjectUtil.isNotEmpty(params.getShopUnique())) {
            invoiceWrapper.eq("shop_unique", params.getShopUnique());
        }
        if (ObjectUtil.isNotEmpty(params.getInvoiceDate())) {
            invoiceWrapper.between("invoice_date", params.getInvoiceDate().get(0), params.getInvoiceDate().get(1));
        }
        if (isCount) { //统计查询
            invoiceWrapper.select("sum(order_money) as totalOrderMoney,sum(tax_money) as totalTaxMoney,sum(order_tax_money) as orderTaxMoney");
        }
        if (ObjectUtil.isNotNull(params.getStatus())) {
            invoiceWrapper.eq("status", params.getStatus());
        }
        if (ObjectUtil.isNotEmpty(params.getApplyFlag())) {
            invoiceWrapper.eq("apply_flag", params.getApplyFlag());
        }
        if (StrUtil.isNotBlank(params.getSaleListUnique())) {
            List<String> saleListUniqueList = StrUtil.split(params.getSaleListUnique(), ",");
            if (CollectionUtil.isNotEmpty(saleListUniqueList)) {
                invoiceWrapper.lambda().in(BusShopInvoiceEntity::getSaleListUnique, saleListUniqueList);
            }
        }
        return invoiceWrapper;
    }

    /**
     * 发票种类转换
     *
     * @param invoiceKind
     * @return
     */
    private String convertInvoiceKind(Integer invoiceKind) {
        if (invoiceKind == 1) {
            return "增值税电子普通发票";
        } else if (invoiceKind == 2) {
            return "增值税电子专用发票 ";
        }
        return "";
    }


}
