package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusCompanyProfitRuleEntity;
import cc.buyhoo.tax.entity.BusSaleListEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.params.inventoryOrder.InventoryOrderParams;
import cc.buyhoo.tax.result.inventoryOrder.InventoryOrderListResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 入库
 * @ClassName BusInventoryOrderService
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
public interface BusInventoryOrderService {
    Result<InventoryOrderListResult> pageList(InventoryOrderParams params);

    /**
     * 未配置启用成本或为配置处理进货订单
     * @param saleListMap
     * @param shopMap
     * @param companyEntity
     */
    void createInventoryOrder(Map<Long, List<BusSaleListEntity>> saleListMap, Map<Long, BusShopEntity> shopMap, SysCompanyEntity companyEntity);

    /**
     * 根据成本规则生成进货订单
     * @param monthSaleListMoney
     * @param monthTotalCostMoney
     * @param profitRule
     * @param saleListMap
     * @param shopMap
     * @param companyEntity
     */
    void createInventoryOrderProfit(BigDecimal monthSaleListMoney, BigDecimal monthTotalCostMoney, BusCompanyProfitRuleEntity profitRule, Map<Long, List<BusSaleListEntity>> saleListMap, Map<Long, BusShopEntity> shopMap, SysCompanyEntity companyEntity);
}
