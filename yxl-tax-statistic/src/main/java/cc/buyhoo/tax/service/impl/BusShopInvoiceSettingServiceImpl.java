package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusShopInvoiceSettingMapper;
import cc.buyhoo.tax.entity.BusShopInvoiceSettingEntity;
import cc.buyhoo.tax.result.busShopInvoiceSetting.GetInvoiceSettingResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopInvoiceSettingService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class BusShopInvoiceSettingServiceImpl extends BaseService implements BusShopInvoiceSettingService {

    @Resource
    private BusShopInvoiceSettingMapper busShopInvoiceSettingMapper;

    /**
     * 开票设置
     * @return
     */
    @Override
    public Result<GetInvoiceSettingResult> getInvoiceSetting() {
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> invoiceWrapper = new LambdaQueryWrapper<>();
        invoiceWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        BusShopInvoiceSettingEntity setting = busShopInvoiceSettingMapper.selectOne(invoiceWrapper);
        GetInvoiceSettingResult result = new GetInvoiceSettingResult();
        if (ObjectUtil.isNotEmpty(setting)) {
            BeanUtils.copyProperties(setting, result);
        }
        return Result.ok(result);
    }

    @Override
    public Result<Void> saveInvoiceSetting(BusShopInvoiceSettingEntity entity) {
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> invoiceWrapper = new LambdaQueryWrapper<>();
        invoiceWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        BusShopInvoiceSettingEntity setting = busShopInvoiceSettingMapper.selectOne(invoiceWrapper);
        if (ObjectUtil.isNotEmpty(setting)) {
            BusShopInvoiceSettingEntity updateSetting = new BusShopInvoiceSettingEntity();
            BeanUtils.copyProperties(entity, updateSetting);
            updateSetting.setId(setting.getId());
            updateSetting.setCreateUser(setting.getCreateUser());
            updateSetting.setCreateTime(setting.getCreateTime());
            updateSetting.setModifyUser(SatokenUtil.getLoginUserId());
            updateSetting.setModifyTime(DateUtil.date());
            busShopInvoiceSettingMapper.updateById(updateSetting);
        } else {
            entity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
            entity.setCreateTime(DateUtil.date());
            entity.setCreateUser(SatokenUtil.getLoginUserId());
            busShopInvoiceSettingMapper.insert(entity);
        }
        return Result.ok();
    }
}
