package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.result.indexData.RevenueTargetResult;
import cc.buyhoo.tax.result.indexData.SaleListDataResult;

/**
 * @Description
 * @ClassName IndexService
 * <AUTHOR>
 * @Date 2023/7/31 17:26
 **/
public interface IndexService {

    /**
     * 首页-营收目标
     * @return
     */
    Result<RevenueTargetResult> revenueTarget();

    /**
     * 首页-订单统计
     * @return
     */
    Result<SaleListDataResult> saleListInfo();
}
