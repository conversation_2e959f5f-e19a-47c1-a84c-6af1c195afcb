package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusSaleListEntity;
import cc.buyhoo.tax.params.saleList.AutoDisassembleSaleListParams;
import cc.buyhoo.tax.params.saleList.DisassembleSaleListParams;
import cc.buyhoo.tax.params.saleList.SaleListDetailParams;
import cc.buyhoo.tax.params.saleList.SaleListParams;
import cc.buyhoo.tax.result.saleList.SaleListDetailResult;
import cc.buyhoo.tax.result.saleList.SaleListResult;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

public interface BusSaleListService {


    void exportSaleList(@Validated @RequestBody SaleListParams params, HttpServletResponse response);
    /**
     *
     * @param disassembleParams
     * @return
     */
    public Result<Void> disassembleSaleList(DisassembleSaleListParams disassembleParams);

    /**
     * 根据条件，自动拆单
     * @param params
     * @return
     */
    public Result<Void> autoDisassembleSaleList(AutoDisassembleSaleListParams params);

    /**
     * 拆解订单
     * @param minValue
     * @param maxValue
     * @param totalMoney
     * @param shopUnique
     * @param saleListUnique
     */
    @Transactional
    public void disassembleOrderSub(BigDecimal minValue, BigDecimal maxValue, BigDecimal totalMoney, BigDecimal proTotal, Long shopUnique, String saleListUnique);

    /**
     * 订单列表
     * @param params
     * @return
     */
    public Result<SaleListResult> saleList(SaleListParams params);

    /**
     * 订单详情
     * @param params
     * @return
     */
    public Result<SaleListDetailResult> saleListDetail(SaleListDetailParams params);

    /**
     * 批量更新订单成本
     * @param updateSaleList
     * @return
     */
    int updateProfitRecords(List<BusSaleListEntity> updateSaleList);
}
