package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.enums.SysCompanyErrorEnum;
import cc.buyhoo.tax.enums.SysRoleErrorEnum;
import cc.buyhoo.tax.enums.sys.RoleTypeEnum;
import cc.buyhoo.tax.params.sysRole.*;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.sysRole.RoleDetailResult;
import cc.buyhoo.tax.result.sysRole.RoleListDto;
import cc.buyhoo.tax.result.sysRole.SysRolePageResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.SysRoleService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SysRoleServiceImpl extends BaseService implements SysRoleService {
    private final SysRoleMapper sysRoleMapper;
    private final SysMenuMapper sysMenuMapper;
    private final SysCompanyMapper sysCompanyMapper;
    private final SysRoleMenuMapper sysRoleMenuMapper;
    private final SysUserRoleMapper sysUserRoleMapper;

    /**
     * 角色管理
     *
     * @param params
     * @return
     */
    @Override
    public Result<SysRolePageResult> pageList(SysRolePageParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //查询数据
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        if (SatokenUtil.isPlatform(loginUser.getCompanyType()) && SatokenUtil.isSuperAdmin(loginUser.getUserType())) {
            // 平台超管
            roleWrapper.eq(ObjectUtil.isNotNull(params.getCompanyId()), SysRoleEntity::getCompanyId, params.getCompanyId());
            roleWrapper.and(wrapper -> wrapper.eq(SysRoleEntity::getCompanyId, loginUser.getCompanyId()).or().eq(SysRoleEntity::getRoleType, RoleTypeEnum.SUPER_ROLE.getValue()));
        } else if (SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            // 平台用户
            roleWrapper.eq(ObjectUtil.isNotNull(params.getCompanyId()), SysRoleEntity::getCompanyId, params.getCompanyId());
            List<SysUserRoleEntity> list = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, loginUser.getId()));
            if (CollectionUtil.isNotEmpty(list)) {
                roleWrapper.and(wrapper -> wrapper.eq(SysRoleEntity::getId, list.get(0).getRoleId()).or(wr -> wr.eq(SysRoleEntity::getRoleType, RoleTypeEnum.SUPER_ROLE.getValue()).ne(SysRoleEntity::getCompanyId, loginUser.getCompanyId())));
            } else {
                roleWrapper.and(wrapper -> wrapper.eq(SysRoleEntity::getRoleType, RoleTypeEnum.SUPER_ROLE.getValue()).ne(SysRoleEntity::getCompanyId, loginUser.getCompanyId()));
            }
        } else if (SatokenUtil.isSuperAdmin(loginUser.getUserType())){
            roleWrapper.eq(SysRoleEntity::getCompanyId, loginUser.getCompanyId());
        } else {
            List<SysUserRoleEntity> list = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, loginUser.getId()));
            if (CollectionUtil.isEmpty(list)) {
                return convertPageData(Collections.EMPTY_LIST, SysRolePageResult.class, params);
            }
            roleWrapper.eq(SysRoleEntity::getId, list.get(0).getRoleId());
        }
        roleWrapper.like(StrUtil.isNotBlank(params.getRoleName()), SysRoleEntity::getRoleName, params.getRoleName());
        roleWrapper.orderByDesc(SysRoleEntity::getId);
        PageUtils.startPage(params);
        List<SysRoleEntity> roleList = sysRoleMapper.selectList(roleWrapper);

        //转换数据
        Set<Long> userIdList = roleList.stream().map(SysRoleEntity::getModifyUser).collect(Collectors.toSet());
        Map<Long, String> userIdNameMap = handleSysUserIdName(userIdList);

        List<RoleListDto> dtoList = new ArrayList<>();
        for (SysRoleEntity role : roleList) {
            RoleListDto dto = new RoleListDto();
            BeanUtil.copyProperties(role, dto);
            dto.setModifyUser(userIdNameMap.get(role.getModifyUser()));
            dtoList.add(dto);
        }
        return convertPageData(roleList, dtoList, SysRolePageResult.class, params);
    }

    /**
     * 新增角色
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addRole(SysRoleAddParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        SysRoleEntity entity = new SysRoleEntity();
        BeanUtil.copyProperties(params, entity);
        //校验角色名称是否重复
        checkRoleName(entity);
        //查询公司信息
        SysCompanyEntity company = sysCompanyMapper.selectById(params.getCompanyId());
        if (ObjectUtil.isNull(company)) {
            return Result.fail(SysCompanyErrorEnum.ID_NULL_ERROR);
        }
        if (SatokenUtil.isPlatform(loginUser.getCompanyType()) && !SatokenUtil.isPlatform(company.getCompanyType())) {
            entity.setRoleType(RoleTypeEnum.SUPER_ROLE.getValue());
        } else {
            entity.setRoleType(RoleTypeEnum.MANAGER.getValue());
        }
        entity.setCreateUser(SatokenUtil.getLoginUserId());
        entity.setModifyUser(entity.getCreateUser());
        int n = sysRoleMapper.insert(entity);
        if (n > 0) {
            List<SysRoleMenuEntity> list = params.getMenuIds().stream().map(v -> {
                SysRoleMenuEntity e = new SysRoleMenuEntity();
                e.setRoleId(entity.getId());
                e.setMenuId(v);
                return e;
            }).collect(Collectors.toList());
            sysRoleMenuMapper.insertBatch(list);
            return Result.ok();
        }
        return Result.fail();
    }

    /**
     * 修改角色
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateRole(SysRoleEditParams params) {
        SysRoleEntity entity = new SysRoleEntity();
        BeanUtil.copyProperties(params, entity);
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //数据校验
        SysRoleEntity oldRole = sysRoleMapper.selectById(params.getId());
        if (null == oldRole) {
            return Result.fail(SysRoleErrorEnum.NOT_EXIST);
        }
        if (ObjectUtil.notEqual(oldRole.getCompanyId(), params.getCompanyId())) {
            return Result.fail(SysRoleErrorEnum.NOT_CONSISTENT);
        }
        if (ObjectUtil.equal(RoleTypeEnum.SUPER_ROLE.getValue(), oldRole.getRoleType()) && !SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            throw new BusinessException(SysRoleErrorEnum.NOT_ALLOW_MODIFY);
        }
        if (!oldRole.getRoleName().equals(params.getRoleName())) { //修改了角色名称
            checkRoleName(entity);
        }
        //更新数据
        oldRole.setRoleName(params.getRoleName());
        oldRole.setRemark(params.getRemark());
        oldRole.setModifyUser(loginUser.getId());
        int n = sysRoleMapper.updateById(oldRole);
        if (n > 0) {
            //删除旧的角色权限
            sysRoleMapper.deleteRoleMenuByRoleId(params.getId());
            List<SysRoleMenuEntity> list = params.getMenuIds().stream().map(v -> {
                SysRoleMenuEntity e = new SysRoleMenuEntity();
                e.setRoleId(entity.getId());
                e.setMenuId(v);
                return e;
            }).collect(Collectors.toList());
            sysRoleMenuMapper.insertBatch(list);
            return Result.ok();
        }
        return Result.fail();
    }

    /**
     * 角色详情
     *
     * @param id
     * @return
     */
    @Override
    public Result<RoleDetailResult> selectById(Long id) {
        //角色校验
        SysRoleEntity entity = sysRoleMapper.selectById(id);
        if (ObjectUtil.isNull(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
        }
        //返回参数构建
        RoleDetailResult result = new RoleDetailResult();
        BeanUtil.copyProperties(entity, result);
        result.setMenuIds(sysMenuMapper.getMenuIdByRoleId(entity.getId()));
        return Result.ok(result);
    }

    /**
     * 删除角色
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteRole(DeleteRoleParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //角色校验
        checkRoleIdList(params.getIds(), loginUser);
        //校验是否有绑定的用户
        List<SysUserRoleEntity> userRoleList = sysRoleMapper.findUserByRoleId(params.getIds());
        if (ObjectUtil.isNotEmpty(userRoleList)) {
            throw new BusinessException(SysRoleErrorEnum.EXISTS_USER_ROLE_ERROR);
        }

        //删除角色
        List<SysRoleEntity> updList = new ArrayList<>();
        for (Long roleId : params.getIds()) {
            SysRoleEntity updRole = new SysRoleEntity();
            updRole.setId(roleId);
            updRole.setModifyUser(loginUser.getId());
            updRole.setDelFlag(DelFlagEnum.DELETED.getCode());
            updList.add(updRole);
        }
        sysRoleMapper.updateBatchById(updList);

        return Result.ok();
    }

    /**
     * 查询公司权限
     *
     * @param companyId
     * @return
     */
    private List<Long> getCompanyMenuId(Long companyId) {
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getCompanyId, companyId);
        roleWrapper.eq(SysRoleEntity::getRoleType, 1);
        roleWrapper.eq(SysRoleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());

        SysRoleEntity role = sysRoleMapper.selectOne(roleWrapper);
        if (ObjectUtil.isEmpty(role)) return new ArrayList<>();

        return sysMenuMapper.getMenuIdByRoleId(role.getId());
    }

    /**
     * 校验角色名称
     *
     * @param entity
     */
    private void checkRoleName(SysRoleEntity entity) {
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getRoleName, entity.getRoleName());
        roleWrapper.eq(SysRoleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        roleWrapper.eq(SysRoleEntity::getCompanyId, entity.getCompanyId());
        roleWrapper.ne(ObjectUtil.isNotNull(entity.getId()), SysRoleEntity::getId, entity.getId());
        Long count = sysRoleMapper.selectCount(roleWrapper);
        if (null != count && count > 0) {
            throw new BusinessException(SysRoleErrorEnum.ROLENAME_REPEAT_ERROR);
        }
    }

    /**
     * 校验角色id
     *
     * @param id
     * @param companyId
     */
    private SysRoleEntity checkRoleId(Long id, Long companyId) {
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        roleWrapper.eq(SysRoleEntity::getId, id);
        roleWrapper.eq(SysRoleEntity::getCompanyId, companyId);
        SysRoleEntity oldRole = sysRoleMapper.selectOne(roleWrapper);
        if (ObjectUtil.isEmpty(oldRole)) {
            throw new BusinessException(CommonErrorEnum.PARAM_ERROR);
        }
        return oldRole;
    }

    /**
     * 校验角色id
     *
     * @param ids
     * @param loginUser
     */
    private void checkRoleIdList(List<Long> ids, LoginUser loginUser) {
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        roleWrapper.in(SysRoleEntity::getId, ids);
        if (SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            roleWrapper.eq(SysRoleEntity::getRoleType, RoleTypeEnum.SUPER_ROLE.getValue());
            roleWrapper.eq(SysRoleEntity::getCompanyId, loginUser.getCompanyId());
        } else {
            roleWrapper.eq(SysRoleEntity::getRoleType, RoleTypeEnum.SUPER_ROLE.getValue());
        }
        Long count = sysRoleMapper.selectCount(roleWrapper);
        if (null != count && count > 0) {
            throw new BusinessException(SysRoleErrorEnum.NOT_ALL_DELETE);
        };
    }
}
