package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.minio.MinioUploadHelper;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.smsg.config.properties.SmsgProperties;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.config.InvoiceNotifyConfig;
import cc.buyhoo.tax.config.properties.InvoiceProperties;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.entity.invoice.*;
import cc.buyhoo.tax.enums.*;
import cc.buyhoo.tax.params.busShopInvoice.ShopInvoiceGoodListAddParams;
import cc.buyhoo.tax.params.external.*;
import cc.buyhoo.tax.params.invoice.NotifyInvoiceResultParams;
import cc.buyhoo.tax.result.external.GetInvoiceMsgResult;
import cc.buyhoo.tax.result.external.InvoiceGoodsMsg;
import cc.buyhoo.tax.service.BusGoodsCategoryService;
import cc.buyhoo.tax.service.ExternalService;
import cc.buyhoo.tax.task.GetQdInvoiceTask;
import cc.buyhoo.tax.util.InvoiceUrlUtil;
import cc.buyhoo.tax.task.QuerySdInvoiceFileTask;
import cc.buyhoo.tax.util.InvoiceUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ExternalServiceImpl implements ExternalService {
    @Resource
    private MinioUploadHelper minioUploadHelper;
    @Resource
    private InvoiceNotifyConfig invoiceNotifyConfig;
    @Resource
    private BusShopInvoiceMapper busShopInvoiceMapper;
    @Resource
    private BusShopMapper busShopMapper;
    @Resource
    private BusShopInvoiceSettingMapper busShopInvoiceSettingMapper;
    @Resource
    private BusShopInvoiceGoodsServiceMapper busShopInvoiceGoodsServiceMapper;
    @Resource
    private BusShopInvoiceDetailMapper busShopInvoiceDetailMapper;
    @Resource
    private BusSaleListMapper busSaleListMapper;
    @Resource
    private BusSaleListDetailMapper busSaleListDetailMapper;
    @Resource
    private BusGoodsMapper busGoodsMapper;
    @Resource
    private BusInvoiceStaffMapper busInvoiceStaffMapper;
    @Autowired
    private InvoiceProperties invoiceProperties;
    @Autowired
    private BusGoodsCategoryMapper busGoodsCategoryMapper;
    /**
     * 短信配置
     */
    @Autowired
    private SmsgProperties smsgProperties;
    @Autowired
    private RedisCache redisCache;
    @Resource
    private InvoiceUrlUtil invoiceUrlUtil;

    /**
     * 申请开票
     * 将开票功能存放到纳统平台，开票完成后，回调到餐饮或其他相关业务系统
     * 客户需要上传回调地址，开票的购买方信息及订单信息
     *
     * @param saveInvoiceMsgPublicParams
     * @return
     */
    public Result<Void> saveInvoiceMsgPub(SaveInvoiceMsgPublicParams saveInvoiceMsgPublicParams) {
        //需要防止客户重复提交
        if (ObjectUtil.isNotNull(redisCache.getCacheObject(saveInvoiceMsgPublicParams.getSaleListUnique()))) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该订单已提交开票申请，请勿重复提交");
        }

        //校验订单信息是否存在,如果订单信息不存在,支付返回
        BusSaleListEntity saleList = busSaleListMapper.selectOne(new LambdaQueryWrapper<BusSaleListEntity>().eq(BusSaleListEntity::getSaleListUnique, saveInvoiceMsgPublicParams.getSaleListUnique()));
        if (ObjectUtil.isNull(saleList)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该订单不存在，请确认订单号");
        }


        //获取订单开票记录，如果有，根据状态判断下一步操作
        BusShopInvoiceEntity busShopInvoiceEntity = busShopInvoiceMapper.selectOne(new LambdaQueryWrapper<BusShopInvoiceEntity>().eq(BusShopInvoiceEntity::getSaleListUnique, saveInvoiceMsgPublicParams.getSaleListUnique()));

        if (ObjectUtil.isNotNull(busShopInvoiceEntity)) {
            //判断状态
            if (busShopInvoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_1.getValue() || busShopInvoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_2.getValue() || busShopInvoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_3.getValue()) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该订单已提交开票申请，请勿重复提交");
            } else if (busShopInvoiceEntity.getStatus() == InvoiceStatusEnum.INVOICE_STATUS_4.getValue()) {
                //如果开票失败了，重新申请开票，并返回开票申请成功
                return Result.ok();
            }
        }

        /**
         * 如果没有记录，说明第一次开票，添加开票申请记录，并发送开票申请
         * 1、获取订单信息
         * 2、根据店铺编号获取对应的纳统企业信息
         * 3、根据纳统企业信息获取对应的开票信息
         * 4、根据订单信息，获取对应商品的开票信息
         * 5、将开票信息保存到开票记录表中
         * 6、发送开票申请
         * 7、返回发票申请状态
         * 特别注意，开票详情需要等开票成功后添加，如果企业信息发生了变化，需要重新计算税率等问题
         */
        //购物方信息
        busShopInvoiceEntity = new BusShopInvoiceEntity();
        busShopInvoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_2.getValue());
        busShopInvoiceEntity.setPurchaseType(saveInvoiceMsgPublicParams.getPurchaseType());
        busShopInvoiceEntity.setPurchaseName(saveInvoiceMsgPublicParams.getPurchaseName());
        busShopInvoiceEntity.setPurchaseBank(saveInvoiceMsgPublicParams.getPurchaseBank());
        busShopInvoiceEntity.setPurchaseBankNo(saveInvoiceMsgPublicParams.getPurchaseBankNo());
        busShopInvoiceEntity.setPurchaseAddress(saveInvoiceMsgPublicParams.getPurchaseAddress());
        busShopInvoiceEntity.setPurchasePhone(saveInvoiceMsgPublicParams.getPurchasePhone());
        busShopInvoiceEntity.setPurchaseIdentity(saveInvoiceMsgPublicParams.getPurchaseIdentity());
        busShopInvoiceEntity.setReceiveMsg(saveInvoiceMsgPublicParams.getReceiveMsg());

        busShopInvoiceEntity.setSaleListUnique(saveInvoiceMsgPublicParams.getSaleListUnique());
        busShopInvoiceEntity.setInvoiceType(2);
        busShopInvoiceEntity.setMediumType(1);
        busShopInvoiceEntity.setInvoiceKind(1);

        //销售方信息
        GetInvoiceMsgParmas getInvoiceMsgParmas = new GetInvoiceMsgParmas();
        getInvoiceMsgParmas.setShopUnique(saveInvoiceMsgPublicParams.getShopUnique());

        Result<GetInvoiceMsgResult> getInvoiceMsgResult = getInvoiceMsg(getInvoiceMsgParmas);
        GetInvoiceMsgResult invoiceMsgResult = getInvoiceMsgResult.getData();

        if (getInvoiceMsgResult.getStatus() == 0) {
            //如果未配置开票信息，先存储开票申请，后期补开
        } else {
            busShopInvoiceEntity.setSaleAddress(invoiceMsgResult.getCompanyAddress());
            busShopInvoiceEntity.setSaleName(invoiceMsgResult.getCompanyName());
            busShopInvoiceEntity.setSaleBank(invoiceMsgResult.getInvoiceBankName());
            busShopInvoiceEntity.setSaleIdentity(invoiceMsgResult.getCompanyTaxNo());
            busShopInvoiceEntity.setSaleBankNo(invoiceMsgResult.getInvoiceBankCard());
            busShopInvoiceEntity.setSalePhone(invoiceMsgResult.getCompanyPhone());
            busShopInvoiceEntity.setPayee(invoiceMsgResult.getPayee());
            busShopInvoiceEntity.setChecker(invoiceMsgResult.getChecker());
            busShopInvoiceEntity.setInvoiceMan(invoiceMsgResult.getInvoiceMan());
            busShopInvoiceEntity.setPeriodsLevel(invoiceMsgResult.getPeriodsLevel());
        }
        LambdaQueryWrapper<BusSaleListEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusSaleListEntity::getSaleListUnique, saveInvoiceMsgPublicParams.getSaleListUnique());

        //合计订单金额，不含税金额，需要给据订单详情计算，不同的商品税率不一样
        busShopInvoiceEntity.setOrderMoney(null);
        //税额
        busShopInvoiceEntity.setTaxMoney(null);
        //价税合计金额
        busShopInvoiceEntity.setOrderTaxMoney(saleList.getSaleListActuallyReceived());

        busShopInvoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_2.getValue());
        busShopInvoiceEntity.setCreateTime(null);
        List<InvoiceGoodsExternalEntity> details = busSaleListDetailMapper.querySaleListDetailForInvoice(saveInvoiceMsgPublicParams.getSaleListUnique());
        if (ObjectUtil.isEmpty(details)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该订单不存在商品信息");
        }

        //以下信息，等申请开票时计算，现在查询了没用，等开时，开票信息可能发生变化
//        //合并同类项，计算税额
//        List<GenerateQdInvoiceGoodsEntity> goodsList = new ArrayList<>();
//        //商品总金额，和订单总金额校验
//        BigDecimal goodsTotal = BigDecimal.ZERO;
//        //税额总额
//        BigDecimal taxMoney = BigDecimal.ZERO;
//        //商品总额，不含税价格。税额总额+商品总额=商品总金额（含税）
//        BigDecimal orderMoney = BigDecimal.ZERO;
//        for (InvoiceGoodsExternalEntity detail : details) {
//            if (detail.getGoodsTaxNo() == null) {
//                return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"商品“" + detail.getDetailName() + "”未配置开票信息设置");
//            }
//            detail.setTaxPre(TaxPreEnum.DOTUSE.getValue());
//            goodsTotal = goodsTotal.add(new BigDecimal(detail.getAmount()));
//            boolean haveSaveGoods = false;
//            for (GenerateQdInvoiceGoodsEntity goods : goodsList) {
//                if (goods.getGoodsTaxNo().equals(detail.getGoodsTaxNo()) && goods.getGoodsName().equals(detail.getGoodsName())) {
//                    haveSaveGoods = true;
//                    //增加总额和税额
//                    TaxCountEntity taxObject = TaxUtil.getTaxAmount(new BigDecimal(detail.getGoodsTaxNo()), new BigDecimal(detail.getTaxRate()));
//                    taxMoney = taxMoney.add(taxObject.getTaxAmount());
//                    orderMoney = orderMoney.add(taxObject.getPrice());
//                    detail.setAmount((new BigDecimal(goods.getAmount()).add(new BigDecimal(detail.getAmount())).setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
//                    break;
//                }
//            }
//
//            if (!haveSaveGoods) {
//                TaxCountEntity taxObject = TaxUtil.getTaxAmount(new BigDecimal(detail.getGoodsTaxNo()), new BigDecimal(detail.getTaxRate()));
//                taxMoney = taxMoney.add(taxObject.getTaxAmount());
//                orderMoney = orderMoney.add(taxObject.getPrice());
//                goodsList.add(detail);
//            }
//        }
//
//        busShopInvoiceEntity.setOrderMoney(orderMoney);
//        busShopInvoiceEntity.setTaxMoney(taxMoney);
//
//        //校验订单总金额和商品总金额是否一致，如果不一致返回错误
//        if (!goodsTotal.setScale(2,BigDecimal.ROUND_HALF_UP).equals(saleList.getSaleListActuallyReceived())) {
//            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"订单金额和商品金额不一致");
//        }
        busShopInvoiceMapper.insert(busShopInvoiceEntity);

        return Result.ok();
    }

    /**
     * 根据开票的店铺信息，获取对应的纳统企业的开票信息及对应的开票内容
     *
     * @param getInvoiceMsgParmas
     * @return
     */
    @Override
    public Result<GetInvoiceMsgResult> getInvoiceMsg(GetInvoiceMsgParmas getInvoiceMsgParmas) {

        GetInvoiceMsgResult result = new GetInvoiceMsgResult();
        /**
         * 1、获取企业对应的纳统企业信息
         * 2、获取纳统企业的开票信息
         * 3、获取纳统企业的可用开票信息
         */
        BusShopEntity busShopEntity = busShopMapper.selectOne(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getShopUnique, getInvoiceMsgParmas.getShopUnique()));
        //查询的信息不存在
        if (ObjectUtil.isNull(busShopEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该店铺未配置对应的企业信息");
        }

        //企业ID
        Long companyId = busShopEntity.getCompanyId();
        //获取企业开票信息
        BusShopInvoiceSettingEntity busShopInvoiceSettingEntity = busShopInvoiceSettingMapper.selectOne(new LambdaQueryWrapper<BusShopInvoiceSettingEntity>().eq(BusShopInvoiceSettingEntity::getCompanyId, companyId));
        //企业未配置开票信息
        if (ObjectUtil.isNull(busShopInvoiceSettingEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该企业暂未开通开票功能");
        }

        BeanUtil.copyProperties(busShopInvoiceSettingEntity, result);
        //获取
        //获取开票详情
        LambdaQueryWrapper<BusGoodsCategoryEntity> lambdaQueryWrapper = new LambdaQueryWrapper<BusGoodsCategoryEntity>();
        lambdaQueryWrapper.eq(BusGoodsCategoryEntity::getCompanyId, companyId);
        lambdaQueryWrapper.eq(BusGoodsCategoryEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        lambdaQueryWrapper.eq(BusGoodsCategoryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<BusGoodsCategoryEntity> goodsServiceEntityList = busGoodsCategoryMapper.selectList(lambdaQueryWrapper);

        //未配置开票内容
        if (CollectionUtil.isEmpty(goodsServiceEntityList)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该企业暂未开通开票功能");
        }

        //取出所有商品分类id
        List<Long> goodsIdList = goodsServiceEntityList.stream().map(BusGoodsCategoryEntity::getId).collect(Collectors.toList());
        //按照id分组
        Map<Long, List<BusGoodsCategoryEntity>> goosCategoryEntityMap = goodsServiceEntityList.stream().collect(Collectors.groupingBy(BusGoodsCategoryEntity::getId));
        //查询符合商品分类的商品
        LambdaQueryWrapper<BusGoodsEntity> goodsQueryWrapper = new LambdaQueryWrapper<>();
        goodsQueryWrapper.in(BusGoodsEntity::getCategoryId, goodsIdList);
        goodsQueryWrapper.eq(BusGoodsEntity::getCompanyId, companyId);
        goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, getInvoiceMsgParmas.getShopUnique());
        List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
        //按照分类分组
        Map<Long, List<BusGoodsEntity>> busGoodsEntity = goodsList.stream().collect(Collectors.groupingBy(BusGoodsEntity::getCategoryId));
        List<InvoiceGoodsMsg> invoiceGoodsMsgs = buildInvoiceGoodsMsgList(goosCategoryEntityMap, busGoodsEntity);
        result.setGoodsMsgList(invoiceGoodsMsgs);
        return Result.ok(result);
    }



    /**
     * 存储开票结果
     *
     * @param saveInvoiceMsgParams
     * @return
     */
    public Result<Void> saveInvoiceMsg(SaveInvoiceMsgParams saveInvoiceMsgParams) {

        //校验是否已存在相同订单号的发票信息，如果存在，跳过
        LambdaQueryWrapper<BusShopInvoiceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopInvoiceEntity::getSaleListUnique, saveInvoiceMsgParams.getSaleListUnique());

        List<BusShopInvoiceEntity> invoices = busShopInvoiceMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(invoices)) {
            //已经开过票了，不再接受新开票，除非红票
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该订单已开票");
        }

        //存储当前开票信息
        BusShopInvoiceEntity busShopInvoiceEntity = new BusShopInvoiceEntity();
        BeanUtil.copyProperties(saveInvoiceMsgParams, busShopInvoiceEntity);

        busShopInvoiceMapper.insert(busShopInvoiceEntity);

        //获取店铺关联的企业ID
        List<SaveInvoiceDetailMsg> detailMsgs = saveInvoiceMsgParams.getDetailEntityList();
        List<BusShopInvoiceDetailEntity> detailEntityList = new ArrayList<>();
        for (SaveInvoiceDetailMsg s : detailMsgs) {
            BusShopInvoiceDetailEntity d = new BusShopInvoiceDetailEntity();
            BeanUtil.copyProperties(s, d);
            d.setInvoiceId(busShopInvoiceEntity.getId());
            detailEntityList.add(d);
        }

        busShopInvoiceDetailMapper.insertBatch(detailEntityList);

        return Result.ok();
    }

    @Override
    @Transactional
    public Result<NotifyInvoiceResultParams> saveShopInvoice(ShopInvoiceAddParams params) {
        LambdaQueryWrapper<BusShopInvoiceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopInvoiceEntity::getSaleListUnique, params.getSaleListUnique());
        List<BusShopInvoiceEntity> invoices = busShopInvoiceMapper.selectList(queryWrapper);
        BusShopInvoiceEntity invoiceEntity = new BusShopInvoiceEntity();
        if (ObjectUtil.isNotEmpty(invoices)) {
            invoiceEntity = invoices.get(0);
            //已经开过票了，不再接受新开票，除非红票
            if (ObjectUtil.isNotEmpty(invoiceEntity) && ObjectUtil.isNotEmpty(invoiceEntity.getStatus())) {
                Integer status = invoiceEntity.getStatus();
                BeanUtils.copy(params, invoiceEntity);
                invoiceEntity.setStatus(status);
                invoiceEntity.setMediumType(1);
                invoiceEntity.setReceiveMsg(params.getEmailAddress());
                invoiceEntity.setModifyTime(DateUtil.date());
                invoiceEntity.setApplyFlag(InvoiceApplyFlagEnum.YES.getValue());
                busShopInvoiceMapper.updateById(invoiceEntity);
                if (ObjectUtil.equals(InvoiceStatusEnum.INVOICE_STATUS_1.getValue(), status)) {
                    NotifyInvoiceResultParams notifyInvoiceResultParams = new NotifyInvoiceResultParams();
                    notifyInvoiceResultParams.setSaleListUnique(invoiceEntity.getSaleListUnique());
                    notifyInvoiceResultParams.setBillNumber(invoiceEntity.getBillNumber());
                    notifyInvoiceResultParams.setStatus(3);
                    notifyInvoiceResultParams.setImageUrl(invoiceEntity.getImageUrl());
                    return Result.ok(notifyInvoiceResultParams);
                } else if (ObjectUtil.equals(InvoiceStatusEnum.INVOICE_STATUS_4.getValue(), status)) {
                    NotifyInvoiceResultParams notifyInvoiceResultParams = new NotifyInvoiceResultParams();
                    notifyInvoiceResultParams.setSaleListUnique(invoiceEntity.getSaleListUnique());
                    notifyInvoiceResultParams.setBillNumber(invoiceEntity.getBillNumber());
                    notifyInvoiceResultParams.setStatus(4);
                    return Result.ok(notifyInvoiceResultParams);
                }
            }
        } else {
            BeanUtils.copy(params, invoiceEntity);
            invoiceEntity.setMediumType(1);
            invoiceEntity.setApplyFlag(InvoiceApplyFlagEnum.YES.getValue());
            invoiceEntity.setReceiveMsg(params.getEmailAddress());
            invoiceEntity.setModifyTime(DateUtil.date());
            busShopInvoiceMapper.insert(invoiceEntity);
        }

        if (ObjectUtil.isNotEmpty(params.getGoodList())) {
            LambdaQueryWrapper<BusShopInvoiceDetailEntity> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper.eq(BusShopInvoiceDetailEntity::getInvoiceId, invoiceEntity.getId());
            List<BusShopInvoiceDetailEntity> shopInvoiceDetailEntities = busShopInvoiceDetailMapper.selectList(detailQueryWrapper);
            if (ObjectUtil.isEmpty(shopInvoiceDetailEntities)) {
                List<BusShopInvoiceDetailEntity> shopInvoiceDetailList = new ArrayList<>();
                for (ShopInvoiceGoodListAddParams good : params.getGoodList()) {
                    BusShopInvoiceDetailEntity shopInvoiceDetailEntity = new BusShopInvoiceDetailEntity();
                    shopInvoiceDetailEntity.setInvoiceId(invoiceEntity.getId());
                    BeanUtils.copy(good, shopInvoiceDetailEntity);
                    shopInvoiceDetailEntity.setTaxRate(good.getTaxRatePer());
                    shopInvoiceDetailList.add(shopInvoiceDetailEntity);
                }
                busShopInvoiceDetailMapper.insertBatch(shopInvoiceDetailList);
            }
        }
        //根据店铺发票期数，选择开票方式
        Long companyId = params.getCompanyId();
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> settingWrapper = new LambdaQueryWrapper<>();
        settingWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, companyId);
        //查询是否配置了开票信息
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(settingWrapper);
        if (ObjectUtil.isEmpty(settingEntity)) {
            return Result.ok();
        }

        //三期税务
        if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel()) {
            invoiceEntity.setPeriodsLevel(PeriodNumberEnum.PERIOD_NUMBER_THIRD.getPeriodsLevel());

            //三期开票功能，三期开票和发送一起，不需要单独处理
            //三期需要验证税盘是否在线


        } else if (settingEntity.getPeriodsLevel() == PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel()) {
            //获取当前已登录的账号信息
            LambdaQueryWrapper<BusInvoiceStaffEntity> staffWrapper = new LambdaQueryWrapper<>();
            staffWrapper.eq(BusInvoiceStaffEntity::getCompanyId, companyId);
            staffWrapper.eq(BusInvoiceStaffEntity::getIsLogin, StaffIsLogin.IS_LOGIN.getValue());
            staffWrapper.orderByDesc(BusInvoiceStaffEntity::getLastLoginTime);
            BusInvoiceStaffEntity staffEntity = busInvoiceStaffMapper.selectOne(staffWrapper);
            if (null == staffEntity) {
                return Result.ok();
            }
            //四期税务
            GenerateQdInvoiceEntity generateQdInvoiceEntity = new GenerateQdInvoiceEntity();

            BeanUtil.copyProperties(invoiceEntity, settingEntity);
            generateQdInvoiceEntity.setElectricAccount(staffEntity.getStaffAccount());
            generateQdInvoiceEntity.setInvoiceType(InvoiceTypeEnum.NORMAT.getValue());
            generateQdInvoiceEntity.setClientName(invoiceEntity.getPurchaseName());
            generateQdInvoiceEntity.setClientTaxCode(invoiceEntity.getPurchaseIdentity());
            generateQdInvoiceEntity.setClientBankName(invoiceEntity.getPurchaseBank());
            generateQdInvoiceEntity.setClientBankAccountNumber(invoiceEntity.getPurchaseBankNo());
            generateQdInvoiceEntity.setClientAddress(invoiceEntity.getPurchaseAddress());
            generateQdInvoiceEntity.setClientPhone(invoiceEntity.getPurchasePhone());
            generateQdInvoiceEntity.setSellerName(settingEntity.getCompanyName());
            generateQdInvoiceEntity.setSelesAddress(settingEntity.getCompanyAddress());
            generateQdInvoiceEntity.setSellerTaxCode(settingEntity.getCompanyTaxNo());
            generateQdInvoiceEntity.setSellerBankName(settingEntity.getInvoiceBankName());
            generateQdInvoiceEntity.setSellerBankAccountNumber(settingEntity.getInvoiceBankCard());
            generateQdInvoiceEntity.setSellerPhone(settingEntity.getCompanyPhone());

            generateQdInvoiceEntity.setInvoicer(settingEntity.getInvoiceMan());
            generateQdInvoiceEntity.setChecker(settingEntity.getChecker());
            generateQdInvoiceEntity.setCashier(settingEntity.getPayee());
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setBillNumber("" + System.currentTimeMillis());
            generateQdInvoiceEntity.setInvoiceKind(InvoiceKindEnum.NormalTicket.getValue());
            generateQdInvoiceEntity.setTotalAmount(invoiceEntity.getOrderTaxMoney().setScale(2, RoundingMode.HALF_UP) + "");
            generateQdInvoiceEntity.setAmount(invoiceEntity.getOrderMoney().setScale(2, RoundingMode.HALF_UP) + "");
            generateQdInvoiceEntity.setTaxAmount(invoiceEntity.getTaxMoney().setScale(2, RoundingMode.HALF_UP) + "");
            generateQdInvoiceEntity.setNotes(invoiceEntity.getNotes());

            System.out.println("当前申请的开票信息为" + generateQdInvoiceEntity);
            //合并同类项，计算税额
            List<GenerateQdInvoiceGoodsEntity> goodsList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(params.getGoodList())) {
                int i = 1;
                for (ShopInvoiceGoodListAddParams good : params.getGoodList()) {
                    GenerateQdInvoiceGoodsEntity detail = new GenerateQdInvoiceGoodsEntity();
                    detail.setGoodsTaxNo(good.getTaxClassificationCode());
                    detail.setGoodsName(good.getGoodsName());
                    detail.setAmount(NumberUtil.round(good.getAmount(), 2) + "");
                    detail.setPrice(good.getPrice() + "");
                    detail.setNumber(good.getQuantity() + "");
                    detail.setTaxRate(good.getTaxRatePer() + "");
                    detail.setTaxAmount(good.getTaxAmount() + "");
                    detail.setRowNumbe(i + "");
                    detail.setRowKind("0");
                    detail.setPriceKind("1");
                    detail.setStandard(good.getSpec());
                    detail.setUnit(good.getUnit());
                    detail.setOriginalRowNumber("1");
                    detail.setTaxPre(TaxPreEnum.DOTUSE.getValue());
                    goodsList.add(detail);
                    i++;
                }
            }

            //申请开票信息
            generateQdInvoiceEntity.setGoodsListFlag("true");
            generateQdInvoiceEntity.setInvoiceDetails(goodsList);

            //此处需要单独处理，开票需要时间，不能立即查询
            GenerateQdInvoiceResult resultData = InvoiceUtil.generateQdInvoice(generateQdInvoiceEntity);
            //将申请号billNumber存储到发票列表，用于后续查询
            if (resultData.getCode().equals("200")) {
                //如果成功，下载发票信息，并保存
                GetQdInvoiceEntity getQdInvoiceEntity = new GetQdInvoiceEntity();
                getQdInvoiceEntity.setBillNumber(generateQdInvoiceEntity.getBillNumber());
                getQdInvoiceEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                GenerateQdInvoiceResultData data = resultData.getData();
                if ("1101".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "数电认证失效");
                }
                if ("031999".equals(data.getSecCode())) {
                    //开票失败，返回失败原因
                    return Result.fail(SystemErrorEnum.SYSTEM_ERROR, resultData.getData().getZTXX());
                }
                invoiceEntity.setBillNumber(generateQdInvoiceEntity.getBillNumber());
                busShopInvoiceMapper.updateById(invoiceEntity);

                //如果开票成功，需要将数据同步到开票记录中，防止开票过程中，开票信息发生变更无法开票
                invoiceEntity.setSaleName(settingEntity.getCompanyName());
                invoiceEntity.setSaleAddress(settingEntity.getCompanyAddress());
                invoiceEntity.setSalePhone(settingEntity.getCompanyPhone());
                if ("0320000".equals(data.getSecCode())) {
                    //开票中
                    //将开票记录ID存储，方便后期查询
                    GetQdInvoiceTask getQdInvoiceTask = new GetQdInvoiceTask(getQdInvoiceEntity, invoiceEntity, invoiceProperties, minioUploadHelper, smsgProperties, busShopInvoiceMapper, invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    getQdInvoiceTask.start();
                } else if ("0000".equals(data.getSecCode())) {
                    //开票成功
                    QuerySdInvoiceFileEntity querySdInvoiceFileEntity = new QuerySdInvoiceFileEntity();
                    querySdInvoiceFileEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
                    querySdInvoiceFileEntity.setFphm(data.getFphm());
                    querySdInvoiceFileEntity.setWjlx("PDF");
                    querySdInvoiceFileEntity.setElectricAccount(staffEntity.getStaffAccount());
                    QuerySdInvoiceFileTask querySdInvoiceFileTask = new QuerySdInvoiceFileTask(generateQdInvoiceEntity.getBillNumber(), querySdInvoiceFileEntity, invoiceEntity, invoiceProperties, minioUploadHelper, smsgProperties, busShopInvoiceMapper, invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    querySdInvoiceFileTask.start();
                }

            } else {
                //开票失败,返回失败原因
                return Result.ok();
            }
        }
        return Result.ok();
    }

    /**
     * 构造发票数据
     *
     * @param goosCategoryEntityMap
     * @param busGoodsEntity
     * @return
     */
    List<InvoiceGoodsMsg> buildInvoiceGoodsMsgList(Map<Long, List<BusGoodsCategoryEntity>> goosCategoryEntityMap, Map<Long, List<BusGoodsEntity>> busGoodsEntity) {
        List<InvoiceGoodsMsg> invoiceGoodsMsgs = new ArrayList<>();
        for (Long categoryId : busGoodsEntity.keySet()) {
            List<BusGoodsCategoryEntity> busGoodsCategoryEntities = goosCategoryEntityMap.get(categoryId);
            BusGoodsCategoryEntity busGoodsCategoryEntity = busGoodsCategoryEntities.get(0);
            InvoiceGoodsMsg i = new InvoiceGoodsMsg();
            BeanUtil.copyProperties(busGoodsCategoryEntity,i);
            i.setGoodsUnit(busGoodsCategoryEntity.getUnit());
            List<BusGoodsEntity> busGoodsEntities = busGoodsEntity.get(categoryId);
            i.setGoodsBarcodeList(busGoodsEntities.stream().map(BusGoodsEntity::getGoodsBarcode).collect(Collectors.toList()));
            invoiceGoodsMsgs.add(i);
        }
        return invoiceGoodsMsgs;
    }
}
