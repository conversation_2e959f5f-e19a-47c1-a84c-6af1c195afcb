package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.baidu.result.VatInvoiceFormalResult;
import cc.buyhoo.common.baidu.util.VatInvoiceUtil;
import cc.buyhoo.common.baidu.result.VatInvoiceFormalWordResult;
import cc.buyhoo.common.baidu.util.AuthUtil;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.minio.MinioUploadHelper;
import cc.buyhoo.common.minio.result.MinioUploadResult;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.util.FileUtils;
import cc.buyhoo.tax.config.properties.BaiduProperties;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.result.busShopInvoice.AnalysisInvoiceDto;
import cc.buyhoo.tax.result.common.UploadFileDto;
import cc.buyhoo.tax.service.UploadService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class UploadServiceImpl implements UploadService {

    @Autowired
    private MinioUploadHelper minioUploadHelper;

    @Resource
    private BaiduProperties baiduProperties;
    @Resource
    private RedisCache redisCache;

    /**
     * 文件上传
     *
     * @param request
     * @return
     */
    @Override
    public Result<UploadFileDto> uploadFile(HttpServletRequest request) {
        //获取图片
        MultipartFile mfile = FileUtils.getFile(request, "file");

        //图片上传
        File file = FileUtils.transferToFile(mfile);
        MinioUploadResult minioUploadResult = minioUploadHelper.uploadImg(file);
        UploadFileDto dto = new UploadFileDto();
        dto.setName(mfile.getName());
        dto.setUrl(minioUploadResult.getUrl());
        //返回参数
        return Result.ok(dto);
    }

    @Override
    public Result<List<UploadFileDto>> uploadFileList(HttpServletRequest request) {
        //获取图片
        Map<String, MultipartFile> allFiles = FileUtils.getAllFiles(request);
        Iterator<Map.Entry<String, MultipartFile>> iter = allFiles.entrySet().iterator();
        List<UploadFileDto> dtoList = new ArrayList<>();

        //图片上传
        while (iter.hasNext()) {
            Map.Entry<String, MultipartFile> entry = iter.next();
            String name = entry.getKey();
            MultipartFile mfile = entry.getValue();
            File file = FileUtils.transferToFile(mfile);

            //以下为测试代码，请勿删除
            try {
                VatInvoiceFormalResult vatInvoiceFormalResult = VatInvoiceUtil.vatInvoiceByFile(file, "24.1d510a82b94333ebfe64a0f4c232dab4.2592000.1724291747.282335-96707964");
                System.out.println(vatInvoiceFormalResult);
            } catch (Exception e) {
                e.printStackTrace();
            }

            MinioUploadResult minioUploadResult = minioUploadHelper.uploadImg(file);
            UploadFileDto dto = new UploadFileDto();
            dto.setName(name);
            dto.setUrl(minioUploadResult.getUrl());

            dtoList.add(dto);
        }
        //返回参数
        return Result.ok(dtoList);
    }


    @Override
    public Result<AnalysisInvoiceDto> analysisInvoice(HttpServletRequest request) {
        AnalysisInvoiceDto dto = null;
        //获取图片
        MultipartFile mfile = FileUtils.getFile(request, "file");
        String rediskey = baiduProperties.getAk() + "_" + baiduProperties.getSk();
        String token = (String) redisCache.getCacheObject(rediskey);
        if (StrUtil.isBlank(token)) {
            token = AuthUtil.getAuth(baiduProperties.getAk(), baiduProperties.getSk());
            redisCache.setCacheObject(rediskey, token, 10, TimeUnit.DAYS);
        }
        String s = AuthUtil.getAuth(baiduProperties.getAk(), baiduProperties.getSk());
        try {
            VatInvoiceFormalResult vfr = VatInvoiceUtil.vatInvoiceByFile(convert(mfile), token);
            VatInvoiceFormalResult vfr1 = VatInvoiceUtil.vatInvoiceByFile(convert(mfile), s);
            if (null != vfr && null != vfr.getWords_result()) {
                VatInvoiceFormalWordResult fw = vfr.getWords_result();
                dto = new AnalysisInvoiceDto();
                dto.setInvoiceNumber(fw.getInvoiceNumConfirm());
                if (StrUtil.contains(fw.getInvoiceType(), "专用发票")) {
                    dto.setInvoiceKind(2);
                } else {
                    dto.setInvoiceKind(1);
                }
                Date date = DateUtil.parse(fw.getInvoiceDate(), "yyyy年MM月dd日");
                if (null != date) {
                    dto.setInvoiceDate(DateUtil.formatDate(date));
                }
                dto.setPurchaseName(fw.getPurchaserName());
                dto.setPurchaseIdentity(fw.getPurchaserRegisterNum());
                dto.setPurchaseAddress(fw.getPurchaserAddress());
                dto.setSaleName(fw.getSellerName());
                dto.setSaleIdentity(fw.getSellerRegisterNum());
                dto.setSaleAddress(fw.getSellerAddress());
                dto.setOrderMoney(fw.getTotalAmount());
                dto.setTaxMoney(fw.getTotalTax());
                dto.setOrderTaxMoney(fw.getAmountInFiguers());
                dto.setInvoiceMan(fw.getNoteDrawer());
            } else {
                redisCache.deleteObject(rediskey);
            }
            return Result.ok(dto);
        } catch (IOException e) {
            throw new BusinessException(CommonErrorEnum.PARAM_ERROR);
        }
    }

    public static File convert(MultipartFile file) throws IOException {
        File convFile = new File(file.getOriginalFilename());
        convFile.createNewFile();
        try (InputStream in = file.getInputStream();
             OutputStream out = new FileOutputStream(convFile)) {
            byte[] bytes = new byte[1024];
            int read;
            while ((read = in.read(bytes)) != -1) {
                out.write(bytes, 0, read);
            }
            return convFile;
        }
    }
}
