package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.PageParams;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusShopCoverChargeMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusShopCoverChargeEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.params.busShopCoverCharge.ShopCoverChargeListParams;
import cc.buyhoo.tax.result.busShopCoverCharge.ShopCoverChargeListDto;
import cc.buyhoo.tax.result.busShopCoverCharge.ShopCoverChargeListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopCoverChargeService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName BusShopCoverChargeServiceImpl
 * <AUTHOR>
 * @Date 2024/7/25 9:45
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusShopCoverChargeServiceImpl extends BaseService implements BusShopCoverChargeService {
    private final BusShopCoverChargeMapper busShopCoverChargeMapper;
    private final BusShopMapper busShopMapper;
    @Override
    public Result<ShopCoverChargeListResult> list(ShopCoverChargeListParams params) {
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        //查询参数
        LambdaQueryWrapper<BusShopCoverChargeEntity> coverChargeWrapper = new LambdaQueryWrapper<>();
        coverChargeWrapper.eq(BusShopCoverChargeEntity::getCompanyId, companyId);
        if (ObjectUtil.isNotEmpty(params.getShopUnique())) {
            coverChargeWrapper.eq(BusShopCoverChargeEntity::getShopUnique, params.getShopUnique());
        }
        //查询
        List<BusShopCoverChargeEntity> coverChargeList = busShopCoverChargeMapper.selectList(coverChargeWrapper);
        //查询店铺信息
        Set<Long> shopUniqueSet = coverChargeList.stream().map(BusShopCoverChargeEntity::getShopUnique).collect(Collectors.toSet());
        Map<Long, String> shopUniqueShopNameMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(shopUniqueSet)) {
            LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
            shopWrapper.in(BusShopEntity::getShopUnique, shopUniqueSet);
            List<BusShopEntity> shopList = busShopMapper.selectList(shopWrapper);
            shopUniqueShopNameMap = shopList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, BusShopEntity::getShopName));
        }
        Map<Long, String> finalShopUniqueShopNameMap = shopUniqueShopNameMap;
        List<ShopCoverChargeListDto> allDtoList = coverChargeList.stream().collect(Collectors.groupingBy(
                        BusShopCoverChargeEntity::getShopUnique,
                        Collectors.reducing(BigDecimal.ZERO,
                                BusShopCoverChargeEntity::getCoverCharge,
                                BigDecimal::add)
                )).entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    ShopCoverChargeListDto dto = new ShopCoverChargeListDto();
                    dto.setShopName(finalShopUniqueShopNameMap.get(entry.getKey()));
                    dto.setCoverCharge(entry.getValue());
                    return dto;
                }).collect(Collectors.toList());
        List<ShopCoverChargeListDto> dtoList = listSplit(allDtoList, params);
        return convertPageData(allDtoList, dtoList, ShopCoverChargeListResult.class, params);
    }

    private static <V> List<V> listSplit(List<V> list, PageParams params) {
        List<V> newList;
        int total = list.size();
        if (params.getPageSize() * (params.getPageIndex() - 1) < total) {
            newList = list.subList(params.getPageSize() * (params.getPageIndex() - 1), (Math.min((params.getPageSize() * params.getPageIndex()), total)));
        } else {
            newList = new ArrayList<>();
        }
        return newList;
    }
}
