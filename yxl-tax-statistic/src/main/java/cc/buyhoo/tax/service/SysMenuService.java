package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysMenu.SysMenuAddParams;
import cc.buyhoo.tax.params.sysMenu.DeleteMenuParams;
import cc.buyhoo.tax.params.sysMenu.MenuListParams;
import cc.buyhoo.tax.params.sysMenu.UpdateMenuAddParams;
import cc.buyhoo.tax.result.sysMenu.MenuListResult;
import cc.buyhoo.tax.result.sysMenu.RoleMenuListResult;
import cc.buyhoo.tax.result.sysMenu.RouterMenuListResult;

public interface SysMenuService {

    /**
     * 路由菜单列表
     * @return
     */
    public Result<RouterMenuListResult> routerList();

    /**
     * 菜单管理
     * @return
     */
    public Result<MenuListResult> menuList(MenuListParams params);

    /**
     * 角色管理选择菜单
     */
    public Result<RoleMenuListResult> roleMenuList();

    /**
     * 新增菜单
     * @param params
     * @return
     */
    public Result<Void> addMenu(SysMenuAddParams params);

    /**
     * 修改菜单
     * @param params
     * @return
     */
    public Result<Void> updateMenu(UpdateMenuAddParams params);

    /**
     * 菜单新增/修改-查询上级菜单
     * @return
     */
    public Result<RoleMenuListResult> queryPreMenu();

    /**
     * 删除菜单
     * @param params
     * @return
     */
    public Result<Void> deleteMenu(DeleteMenuParams params);


}
