package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.service.CashUpgradeService;
import cc.buyhoo.upgrade.UpgradeFacade;
import cc.buyhoo.upgrade.params.cashUpgrade.*;
import cc.buyhoo.upgrade.result.cashUpgrade.GetMaxVersionResult;
import cc.buyhoo.upgrade.result.cashUpgrade.QueryShopsResult;
import cc.buyhoo.upgrade.result.cashUpgrade.QueryUpgradeShopResult;
import cc.buyhoo.upgrade.result.cashUpgrade.UpgradeListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CashUpgradeServiceImpl implements CashUpgradeService {

    @DubboReference
    private UpgradeFacade upgradeFacade;

    /**
     * 版本列表
     * @param params
     * @return
     */
    @Override
    public Result<UpgradeListResult> upgradeList(UpgradeListParams params) {
        return upgradeFacade.upgradeList(params);
    }

    /**
     * 查询最高版本
     * @param params
     * @return
     */
    @Override
    public Result<GetMaxVersionResult> getMaxVersion(GetMaxVersionParams params) {
        return upgradeFacade.getMaxVersion(params);
    }

    /**
     * 新增版本
     * @param params
     * @return
     */
    @Override
    public Result<Void> addUpgrade(AddUpgradeParams params) {
        return upgradeFacade.addUpgrade(params);
    }

    /**
     * 修改版本
     * @param params
     * @return
     */
    @Override
    public Result<Void> updateUpgrade(UpdateUpgradeParams params) {
        return upgradeFacade.updateUpgrade(params);
    }

    /**
     * 删除版本
     * @param params
     * @return
     */
    @Override
    public Result<Void> deleteUpgrade(DeleteUpgradeParams params) {
        return upgradeFacade.deleteUpgrade(params);
    }

    /**
     * 刷新版本
     * @param params
     * @return
     */
    @Override
    public Result<Void> refreshUpgrade(RefreshUpgradeParams params) {
        return upgradeFacade.refreshUpgrade(params);
    }

    /**
     * 版本绑定店铺
     * @param params
     * @return
     */
    @Override
    public Result<Void> bindUpgradeShop(BindUpgradeShopParams params) {
        return upgradeFacade.bindUpgradeShop(params);
    }

    /**
     * 店铺查询
     * @param params
     * @return
     */
    @Override
    public Result<QueryShopsResult> queryShops(QueryShopsParams params) {
        return upgradeFacade.queryShops(params);
    }

    /**
     * 升级绑定店铺查询
     * @param params
     * @return
     */
    @Override
    public Result<QueryUpgradeShopResult> queryUpgradeShop(QueryUpgradeShopParams params) {
        return upgradeFacade.queryUpgradeShop(params);
    }
}
