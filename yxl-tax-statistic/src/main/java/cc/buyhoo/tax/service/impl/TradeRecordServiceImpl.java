package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.bankpay.enums.CmbLoanMark;
import cc.buyhoo.common.bankpay.enums.MsgtypEnums;
import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.constant.Constants;
import cc.buyhoo.tax.dao.SysCompanyMapper;
import cc.buyhoo.tax.dao.TradeRecordMapper;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.entity.TradeRecordEntity;
import cc.buyhoo.tax.entity.cmb.CMBNoteData;
import cc.buyhoo.tax.entity.cmb.CMBNoteDataDetail;
import cc.buyhoo.tax.enums.BusTradeWayEnum;
import cc.buyhoo.tax.enums.DateFormatEnums;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListExportParams;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListQueryParams;
import cc.buyhoo.tax.result.busTradeRecord.TradeRecordExcel;
import cc.buyhoo.tax.result.busTradeRecord.TradeRecordListQueryDto;
import cc.buyhoo.tax.result.busTradeRecord.TradeRecordListQueryResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.TradeRecordService;
import cc.buyhoo.tax.util.SatokenUtil;
import cc.buyhoo.tax.util.SystemUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
@Slf4j
@Service
public class TradeRecordServiceImpl extends BaseService implements TradeRecordService {
    @Resource
    private TradeRecordMapper tradeRecordMapper;
    @Resource
    private SysCompanyMapper sysCompanyMapper;
    @Resource
    private RedisCache redisCache;

    /**
     * 保存交易流水
     * @param cmbNoteData
     * @return
     */
    public Result<Void> saveTradeRecord(CMBNoteData cmbNoteData) {
        TradeRecordEntity tradeRecordEntity = new TradeRecordEntity();
        String msgdat = cmbNoteData.getMsgdat();
        CMBNoteDataDetail cmbNoteDataDetail = JSONObject.parseObject(msgdat, CMBNoteDataDetail.class);

        String redisId = Constants.CREATE_NEW_TRADE_RECORD_KEY + cmbNoteDataDetail.getRefnbr();

        if (redisCache.hasKey(redisId) && redisCache.getCacheObject(redisId) != null) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"该流水号已存在");
        }
        redisCache.setCacheObject(redisId,true);

        //获取其对应的企业信息
        LambdaQueryWrapper<SysCompanyEntity> companyQueryWrapper = new LambdaQueryWrapper<>();
        companyQueryWrapper.eq(SysCompanyEntity::getBankCard,cmbNoteDataDetail.getAccnbr());

        SysCompanyEntity sysCompanyEntity = sysCompanyMapper.selectOne(companyQueryWrapper);

        if (ObjectUtil.isNull(sysCompanyEntity)) {
            return Result.ok();
        }

        tradeRecordEntity.setCompanyId(sysCompanyEntity.getId());
        tradeRecordEntity.setCompanyBankName(cmbNoteDataDetail.getAccnam());
        tradeRecordEntity.setCompanyBankAccount(cmbNoteDataDetail.getAccnbr());
        tradeRecordEntity.setTradeDate(SystemUtil.convertTimeFormat(DateFormatEnums.yyyyMMdd.getFormat(), DateFormatEnums.yyyy_MM_dd.getFormat(), cmbNoteDataDetail.getTrsdat()));
        tradeRecordEntity.setTradeTime(SystemUtil.convertTimeFormat(DateFormatEnums.HHmmss.getFormat(), DateFormatEnums.HH_mm_ss.getFormat(), cmbNoteDataDetail.getTrstim()));
        tradeRecordEntity.setTradeAmount(cmbNoteDataDetail.getC_trsamt());
        Integer tradeWay = cmbNoteData.getMsgtyp().equals(MsgtypEnums.NCCRTTRS.getMode())? 1 : 2;
        tradeRecordEntity.setTradeWay(tradeWay);
        tradeRecordEntity.setTradeAbstract(cmbNoteDataDetail.getNaryur());
        tradeRecordEntity.setTradeOtherAccount(cmbNoteDataDetail.getRpyacc());
        tradeRecordEntity.setTradeOtherName(cmbNoteDataDetail.getRpynam());
        tradeRecordEntity.setCompanyBalance(cmbNoteDataDetail.getBlvamt());
        tradeRecordEntity.setSerialNumber(cmbNoteDataDetail.getRefnbr());
        tradeRecordEntity.setTradeType(cmbNoteDataDetail.getTrscod());
        tradeRecordEntity.setCreditMark(CmbLoanMark.getLoadMarkValue(cmbNoteDataDetail.getAmtcdr()));
        tradeRecordEntity.setBusinessNum(cmbNoteDataDetail.getRefnbr());
        tradeRecordEntity.setBankBranchId(cmbNoteDataDetail.getRpybbk());
        tradeRecordEntity.setFromerBankAddress(cmbNoteDataDetail.getRpyadr());

        LambdaQueryWrapper<TradeRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TradeRecordEntity::getSerialNumber, tradeRecordEntity.getSerialNumber());
        TradeRecordEntity tradeRecord = tradeRecordMapper.selectOne(wrapper);
        if (ObjectUtil.isNotNull(tradeRecord)) {
            //该记录信息已存在
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"该记录信息已存在");
        }

        tradeRecordMapper.insert(tradeRecordEntity);
        return Result.ok();
    }

    @Override
    public Result<TradeRecordListQueryResult> queryTradeRecordList(TradeRecordListQueryParams params) {
        Result<TradeRecordListQueryResult> result = new Result<>();
        QueryWrapper<TradeRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("company_id", SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getTradeTime()) && params.getTradeTime().size() == 2) {
            queryWrapper.between("CONCAT(trade_date, ' ', trade_time)", params.getTradeTime().get(0), params.getTradeTime().get(1));
        } else {
            queryWrapper.between("CONCAT(trade_date, ' ', trade_time)", DateUtil.beginOfDay(DateUtil.date()), DateUtil.endOfDay(DateUtil.date()));
        }
        if (ObjectUtil.isNotEmpty(params.getTradeWay())) {
            queryWrapper.eq("trade_way", params.getTradeWay());
        }
        if (ObjectUtil.isNotEmpty(params.getTradeAbstract())) {
            queryWrapper.like("trade_abstract", params.getTradeAbstract());
        }
        if (ObjectUtil.isNotEmpty(params.getSerialNumber())) {
            queryWrapper.eq("serial_number", params.getSerialNumber());
        }
        if (ObjectUtil.isNotEmpty(params.getTradeOtherAccount())) {
            queryWrapper.eq("trade_other_account", params.getTradeOtherAccount());
        }
        if (ObjectUtil.isNotEmpty(params.getTradeOtherName())) {
            queryWrapper.like("trade_other_name", params.getTradeOtherName());
        }
        queryWrapper.orderByDesc("trade_date").orderByDesc("trade_time");
        List<TradeRecordEntity> tradeRecordStatList = tradeRecordMapper.selectList(queryWrapper);
        PageUtils.startPage(params);
        List<TradeRecordEntity> tradeRecordList = tradeRecordMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(tradeRecordList)) {
            List<TradeRecordListQueryDto> dtoList = tradeRecordList.stream().map(item -> {
                TradeRecordListQueryDto dto = new TradeRecordListQueryDto();
                BeanUtils.copy(item, dto);
                dto.setTradeTime(DateUtil.parse(item.getTradeDate() + " " + item.getTradeTime(), "yyyy-MM-dd HH:mm:ss"));
                return dto;
            }).collect(Collectors.toList());
            result = convertPageData(tradeRecordList, dtoList, TradeRecordListQueryResult.class, params);
            if (ObjectUtil.isNotEmpty(tradeRecordStatList)) {
                AtomicReference<Long> inTradeCount = new AtomicReference<>(0L);
                AtomicReference<Long> outTradeCount = new AtomicReference<>(0L);
                AtomicReference<BigDecimal> inTradeAmount = new AtomicReference<>(BigDecimal.ZERO);
                AtomicReference<BigDecimal> outTradeAmount = new AtomicReference<>(BigDecimal.ZERO);
                tradeRecordStatList.forEach(item -> {
                    if (ObjectUtil.equals(BusTradeWayEnum.IN.getValue(), item.getTradeWay())) {
                        inTradeCount.updateAndGet(v -> v + 1L);
                        inTradeAmount.updateAndGet(v -> v.add(item.getTradeAmount()));
                    } else {
                        outTradeCount.updateAndGet(v -> v + 1L);
                        outTradeAmount.updateAndGet(v -> v.add(item.getTradeAmount()));
                    }
                });
                result.getData().setInTradeCount(inTradeCount.get());
                result.getData().setOutTradeCount(outTradeCount.get());
                result.getData().setInTradeAmount(inTradeAmount.get());
                result.getData().setOutTradeAmount(outTradeAmount.get());
            }
            return result;
        }

        TradeRecordListQueryResult tradeRecordListQueryResult = new TradeRecordListQueryResult();
        tradeRecordListQueryResult.setRows(Collections.emptyList());
        return Result.ok(tradeRecordListQueryResult);
    }

    @Override
    public void exportTradeRecord(TradeRecordListExportParams params, HttpServletResponse response) {
        QueryWrapper<TradeRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("company_id", SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getTradeTime()) && params.getTradeTime().size() == 2) {
            queryWrapper.between("CONCAT(trade_date, ' ', trade_time)", params.getTradeTime().get(0), params.getTradeTime().get(1));
        } else {
            queryWrapper.between("CONCAT(trade_date, ' ', trade_time)", DateUtil.beginOfDay(DateUtil.date()),DateUtil.endOfDay(DateUtil.date()));
        }
        if (ObjectUtil.isNotEmpty(params.getTradeWay())) {
            queryWrapper.eq("trade_way", params.getTradeWay());
        }
        if (ObjectUtil.isNotEmpty(params.getTradeAbstract())) {
            queryWrapper.like("trade_abstract", params.getTradeAbstract());
        }
        if (ObjectUtil.isNotEmpty(params.getSerialNumber())) {
            queryWrapper.eq("serial_number", params.getSerialNumber());
        }
        if (ObjectUtil.isNotEmpty(params.getTradeOtherAccount())) {
            queryWrapper.eq("trade_other_account", params.getTradeOtherAccount());
        }
        if (ObjectUtil.isNotEmpty(params.getTradeOtherName())) {
            queryWrapper.like("trade_other_name", params.getTradeOtherName());
        }
        queryWrapper.orderByDesc("trade_date").orderByDesc("trade_time");
        List<TradeRecordEntity> tradeRecordList = tradeRecordMapper.selectList(queryWrapper);
        List<TradeRecordExcel> excelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(tradeRecordList)) {
            excelList = tradeRecordList.stream().map(v -> {
                TradeRecordExcel e = new TradeRecordExcel();
                e.setTradeWay(BusTradeWayEnum.getName(v.getTradeWay()));
                e.setTradeTime(ObjectUtil.isNotNull(v.getTradeDate() + " " + v.getTradeTime()) ? v.getTradeDate() + " " + v.getTradeTime() : StrUtil.EMPTY);
                e.setTradeAbstract(v.getTradeAbstract());
                e.setSerialNumber(v.getSerialNumber());
                e.setTradeAmount(ObjectUtil.defaultIfNull(v.getTradeAmount(), BigDecimal.ZERO));
                e.setCompanyBalance(ObjectUtil.defaultIfNull(v.getCompanyBalance(), BigDecimal.ZERO));
                e.setTradeOtherName(v.getTradeOtherName());
                e.setTradeOtherAccount(v.getTradeOtherAccount());
                return e;
            }).collect(Collectors.toList());
        }
        String excelName = "交易流水_" + DateUtil.current() + ".xlsx";
        try {
            // 设置响应头，指定文件类型及编码
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName));
            // 清除响应
            response.reset();
            // 使用 EasyExcel 写入数据到 response 输出流
            EasyExcel.write(response.getOutputStream(), TradeRecordExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("Sheet1")
                    .doWrite(excelList); // dataList 是你要导出的数据列表
        } catch (IOException e) {
            log.error("导出交易流水Excel异常");
        }
    }

}
