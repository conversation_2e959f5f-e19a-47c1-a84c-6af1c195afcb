package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusTaxRecordMapper;
import cc.buyhoo.tax.entity.BusTaxRecordEntity;
import cc.buyhoo.tax.enums.SeasonEnum;
import cc.buyhoo.tax.enums.TaxStatusEnum;
import cc.buyhoo.tax.enums.TaxTypeEnum;
import cc.buyhoo.tax.params.busTaxRecord.BusTaxRecordParams;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busTaxRecord.BusTaxRecordDto;
import cc.buyhoo.tax.result.busTaxRecord.BusTaxRecordResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusTaxRecordService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 申报记录
 * @ClassName BusTaxRecordServiceImpl
 * <AUTHOR>
 * @Date 2023/8/11 11:29
 **/
@Slf4j
@Service
public class BusTaxRecordServiceImpl extends BaseService implements BusTaxRecordService {

    @Resource
    private BusTaxRecordMapper busTaxRecordMapper;

    @Override
    public Result<BusTaxRecordResult> pageList(BusTaxRecordParams params) {
        PageUtils.startPage(params);
        LambdaQueryWrapper<BusTaxRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusTaxRecordEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (StrUtil.isNotBlank(params.getTaxStatus())) {
            queryWrapper.eq(BusTaxRecordEntity::getTaxStatus, params.getTaxStatus());
        }
        if (StrUtil.isNotBlank(params.getTaxType())) {
            queryWrapper.eq(BusTaxRecordEntity::getTaxType, params.getTaxType());
        }
        queryWrapper.orderByDesc(BusTaxRecordEntity::getId);
        List<BusTaxRecordEntity> list = busTaxRecordMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            Set<Long> createIds = list.stream().map(BusTaxRecordEntity::getCreateUser).collect(Collectors.toSet());
            Set<Long> modifyIds = list.stream().map(BusTaxRecordEntity::getModifyUser).collect(Collectors.toSet());
            createIds.addAll(modifyIds);
            Map<Long, String> userIdNameMap = handleSysUserIdName(createIds);
            List<BusTaxRecordDto> resultList = list.stream().map(v -> {
                BusTaxRecordDto dto = new BusTaxRecordDto();
                BeanUtil.copyProperties(v, dto);
                dto.setCreateUserName(userIdNameMap.get(v.getCreateUser()));
                dto.setModifyUserName(userIdNameMap.getOrDefault(v.getModifyUser(), dto.getCreateUserName()));
                dto.setTaxDateView(v.getTaxDate());
                if (StrUtil.equals(TaxTypeEnum.SEASON.getValue(), dto.getTaxType())) {
                    dto.setTaxDateView(SeasonEnum.getLabel(dto.getTaxDate()));
                }
                return dto;
            }).collect(Collectors.toList());
            return convertPageData(list, resultList, BusTaxRecordResult.class, params);
        }
        return convertPageData(list, BusTaxRecordResult.class, params);
    }

    @Override
    public Result<Void> save(BusTaxRecordParams params) {
        BusTaxRecordEntity entity;
        if (null != params.getId()) {
            entity = busTaxRecordMapper.selectById(params.getId());
            entity.setTaxType(params.getTaxType());
            entity.setTargetAmount(params.getTargetAmount());
            entity.setTaxAmount(params.getTaxAmount());
            entity.setTaxDate(params.getTaxDate());
            entity.setRemark(params.getRemark());
            entity.setModifyTime(DateUtil.date());
            entity.setModifyUser(SatokenUtil.getLoginUserId());
        } else {
            entity = new BusTaxRecordEntity();
            BeanUtil.copyProperties(params, entity);
            entity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
            entity.setCreateUser(SatokenUtil.getLoginUserId());
            entity.setCreateTime(DateUtil.date());
        }
        boolean flag = busTaxRecordMapper.insertOrUpdate(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Void> delete(DeleteIdsParams idsParams) {
        int num = busTaxRecordMapper.deleteBatchIds(idsParams.getIds());
        return num > 0 ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Void> updateSuccess(BusTaxRecordParams params) {
        if (null == params.getId()) {
            return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "ID不能为空");
        }
        BusTaxRecordEntity entity = busTaxRecordMapper.selectById(params.getId());
        if (null != entity) {
            entity.setTaxStatus(TaxStatusEnum.FINISHED.getValue());
            entity.setModifyTime(DateUtil.date());
            entity.setModifyUser(SatokenUtil.getLoginUserId());
            busTaxRecordMapper.updateById(entity);
            return Result.ok();
        }
        return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST, "申报数据不存在");
    }
}
