package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShopBank.BusShopBankAddParams;
import cc.buyhoo.tax.params.busShopBank.BusShopBankUpdateParams;
import cc.buyhoo.tax.params.busShopBank.ShopBankListParam;
import cc.buyhoo.tax.result.busShopBank.BusShopBankDto;

import java.util.List;

/**
 * @description: 供货商银行卡管理
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 08:49
 **/
public interface BusShopBankService {
    Result<List<BusShopBankDto>> list(ShopBankListParam param);

    Result<String> addShopBank(BusShopBankAddParams param);

    Result<String> updateShopBank(BusShopBankUpdateParams param);

    Result<String> deleteShopBank(Long id);

    Result<BusShopBankDto> selectById(Long id);
}
