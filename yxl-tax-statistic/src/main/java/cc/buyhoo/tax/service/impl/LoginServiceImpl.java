package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.captcha.CaptchaHelper;
import cc.buyhoo.common.captcha.properties.CaptchaProperties;
import cc.buyhoo.common.captcha.result.GenCaptchaResult;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.config.properties.LoginPasswordProperties;
import cc.buyhoo.tax.constant.Constants;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.LoginErrorEnum;
import cc.buyhoo.tax.enums.sys.MenuLevelEnum;
import cc.buyhoo.tax.params.login.LoginParams;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.login.CaptchaResult;
import cc.buyhoo.tax.result.login.LoginResult;
import cc.buyhoo.tax.service.LoginService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class LoginServiceImpl implements LoginService {

    private final CaptchaHelper captchaHelper;
    private final RedisCache redisCache;
    private final CaptchaProperties captchaProperties;
    private final SysUserMapper sysUserMapper;
    private final LoginPasswordProperties loginPasswordProperties;
    private final SysCompanyMapper sysCompanyMapper;
    private final SysRoleMapper sysRoleMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysMenuMapper sysMenuMapper;

    @Override
    public Result<CaptchaResult> captcha() {
        Result<GenCaptchaResult> capResult = captchaHelper.genCaptcha();
        if (capResult.hasFail()) {
            throw new BusinessException(LoginErrorEnum.CAPTCHA_ERROR);
        }
        GenCaptchaResult genResult = capResult.getData();

        //返回参数构建
        CaptchaResult result = new CaptchaResult();
        result.setImg(genResult.getImg());
        result.setUuid(genResult.getUuid());

        //验证码存redis
        String key = StringUtils.join(captchaProperties.getRedisPrefixKey(), genResult.getUuid());
        redisCache.setCacheObject(key, genResult.getCode(), captchaProperties.getExpireMinute(), TimeUnit.MINUTES);

        return Result.ok(result);
    }

    /**
     * 用户登录
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<LoginResult> login(LoginParams params) {
        //校验验证码
        //checkCode(params);

        //查询用户
        LambdaQueryWrapper<SysUserEntity> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(SysUserEntity::getUsername, params.getUsername());
        userWrapper.eq(SysUserEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        SysUserEntity sysUserEntity = sysUserMapper.selectOne(userWrapper);

        if (ObjectUtil.isEmpty(sysUserEntity)) { //用户不存在
            throw new BusinessException(LoginErrorEnum.USERNAME_PWD_ERROR);
        }
        if (ObjectUtil.notEqual(sysUserEntity.getEnableStatus(), EnableStatusEnum.AVAILABLE.getCode())) { //用户已被禁用
            throw new BusinessException(LoginErrorEnum.ENABLE_ERROR);
        }

        //登录校验
        checkLogin(sysUserEntity, params);

        //查询公司信息
        SysCompanyEntity company = sysCompanyMapper.selectById(sysUserEntity.getCompanyId());

        SaLoginModel saLoginModel = new SaLoginModel();
        //加载用户权限
        List<String> permissionList = new ArrayList<>();
        if (SatokenUtil.isPlatform(company.getCompanyType()) && SatokenUtil.isSuperAdmin(sysUserEntity.getUserType())) {
            //平台超级管理员
//            queryWrapper.or(wrapper -> wrapper.eq(SysMenuEntity::getMenuLevel, MenuLevelEnum.PLATFORM.getValue()).or().eq(SysMenuEntity::getMenuLevel, MenuLevelEnum.SYSTEM.getValue()));
//            queryWrapper.orderByAsc(SysMenuEntity::getSort);
//            List<SysMenuEntity> menuList = sysMenuMapper.selectList(queryWrapper);
//            permissionList = new ArrayList<>(menuList.stream().filter(m -> ObjectUtil.isNotEmpty(m.getPermission())).map(SysMenuEntity::getPermission).collect(Collectors.toSet()));
            permissionList.add("*");
        } else {
            //角色
            Long roleId = sysUserMapper.getRoleIdByUserId(sysUserEntity.getId());
            if (ObjectUtil.isNotNull(roleId)){
                //权限
                List<Long> menuIdList = sysMenuMapper.getMenuIdByRoleId(roleId);
                if (ObjectUtil.isNotEmpty(menuIdList)) {
                    List<SysMenuEntity> menuList = sysMenuMapper.selectBatchIds(menuIdList);
                    permissionList = new ArrayList<>(menuList.stream().filter(m -> ObjectUtil.isNotEmpty(m.getPermission())).map(SysMenuEntity::getPermission).collect(Collectors.toSet()));
                }
            }
        }
        saLoginModel.setExtra("permissions", permissionList);
        //系统登录
        StpUtil.login(sysUserEntity.getId(), saLoginModel);

        LoginResult result = new LoginResult();
        result.setUserId(sysUserEntity.getId());
        result.setToken(StpUtil.getTokenValue());
        result.setUsername(sysUserEntity.getUsername());
        result.setCompanyType(company.getCompanyType());
        result.setCompanyName(company.getCompanyName());
        result.setUserType(sysUserEntity.getUserType());
        LoginUser loginUser = new LoginUser();
        loginUser.setId(sysUserEntity.getId());
        loginUser.setUsername(sysUserEntity.getUsername());
        loginUser.setUserType(sysUserEntity.getUserType());
        loginUser.setCompanyId(sysUserEntity.getCompanyId());
        loginUser.setCompanyType(company.getCompanyType());
        loginUser.setCompanyName(company.getCompanyName());
        loginUser.setUserType(sysUserEntity.getUserType());
        //查询角色
        SysUserRoleEntity userRole = sysUserRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, sysUserEntity.getId()));
        if (null != userRole && null != userRole.getRoleId()) {
            SysRoleEntity role = sysRoleMapper.selectById(userRole.getRoleId());
            if (null != role) {
                loginUser.setRoleId(role.getId());
                loginUser.setRoleType(role.getRoleType());
            }
        }
        StpUtil.getTokenSession().set(Constants.LOGIN_USER, loginUser);
        return Result.ok(result);
    }

    /**
     * 退出登录
     *
     * @return
     */
    @Override
    public Result<Void> logout() {
        StpUtil.logout();
        return Result.ok();
    }

    /**
     * 验证码校验
     *
     * @param params
     */
    private void checkCode(LoginParams params) {
        String code = redisCache.getCacheObject(StringUtils.join(captchaProperties.getRedisPrefixKey(), params.getUuid()));
        if (ObjectUtil.isEmpty(code)) {
            throw new BusinessException(LoginErrorEnum.CAPTCHA_EXPIRE_ERROR);
        }
        if (ObjectUtil.notEqual(code, params.getCode())) {
            throw new BusinessException(LoginErrorEnum.CAPTCHA_CODE_ERROR);
        }
    }

    /**
     * 登陆校验
     *
     * @param sysUserEntity
     * @param params
     */
    private void checkLogin(SysUserEntity sysUserEntity, LoginParams params) {
        //密码登录错误次数
        String errorKey = Constants.PWD_ERR_CNT_KEY + params.getUsername();
        //锁定时间
        Integer lockMinute = loginPasswordProperties.getLockMinute();
        //失败次数
        Integer maxRetryCount = loginPasswordProperties.getMaxRetryCount();

        //失败次数
        Integer errCnt = redisCache.getCacheObject(errorKey);

        //失败次数到了
        if (ObjectUtil.isNotNull(errCnt) && errCnt.equals(maxRetryCount)) {
            throw new BusinessException(LoginErrorEnum.LOGIN_LIMIT_EXCEED_ERROR.getCode(), StrUtil.format("失败已达上线，需锁定{}分钟", lockMinute));
        }
        //验证码存redis
        String key = StringUtils.join(captchaProperties.getRedisPrefixKey(), params.getUuid());
        String code = redisCache.getCacheObject(key);
        if (StrUtil.isBlank(code)) {
            throw new BusinessException(LoginErrorEnum.CAPTCHA_EXPIRE_ERROR);
        }
        if (!StrUtil.equals(code, params.getCode())) {
            throw new BusinessException(LoginErrorEnum.CAPTCHA_CODE_ERROR);
        }
        //失败次数没到
        boolean checkpw = BCrypt.checkpw(params.getPassword(), sysUserEntity.getPassword());
        if (!checkpw) {
            errCnt = ObjectUtil.isNull(errCnt) ? 1 : errCnt + 1;
            if (errCnt.equals(maxRetryCount)) { //达到了最大失败次数
                redisCache.setCacheObject(errorKey, errCnt, lockMinute, TimeUnit.MINUTES);
                throw new BusinessException(LoginErrorEnum.LOGIN_LIMIT_EXCEED_ERROR.getCode(), StrUtil.format("失败已达上线，需锁定{}分钟", lockMinute));
            } else {
                redisCache.setCacheObject(errorKey, errCnt);
                throw new BusinessException(LoginErrorEnum.USERNAME_PWD_ERROR.getCode(), StrUtil.format("密码错误，还剩{}次机会", maxRetryCount - errCnt));
            }
        }

        //清空登陆失败次数
        redisCache.deleteObject(errorKey);
    }
}
