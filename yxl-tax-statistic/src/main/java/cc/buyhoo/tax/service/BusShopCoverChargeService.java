package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShopCoverCharge.ShopCoverChargeListParams;
import cc.buyhoo.tax.result.busShopCoverCharge.ShopCoverChargeListResult;

public interface BusShopCoverChargeService {

    /**
     * 服务费列表
     * @param params
     * @return
     */
    Result<ShopCoverChargeListResult> list(ShopCoverChargeListParams params);

}
