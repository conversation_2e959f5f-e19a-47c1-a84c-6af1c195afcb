package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusReturnBatchMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusReturnBatchEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.params.returnBatch.ReturnBatchListParams;
import cc.buyhoo.tax.result.returnBatch.ReturnBatchDto;
import cc.buyhoo.tax.result.returnBatch.ReturnBatchListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusReturnBatchService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BusReturnBatchServiceImpl extends BaseService implements BusReturnBatchService {

    private final BusReturnBatchMapper busReturnBatchMapper;

    private final BusShopMapper busShopMapper;

    /**
     * 退货单批次列表
     * @param params
     * @return
     */
    @Override
    public Result<ReturnBatchListResult> pageList(ReturnBatchListParams params) {
        LambdaQueryWrapper<BusReturnBatchEntity> batchWrapper = new LambdaQueryWrapper<>();
        batchWrapper.eq(BusReturnBatchEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getShopUnique())) {
            batchWrapper.eq(BusReturnBatchEntity::getShopUnique,params.getShopUnique());
        }
        if (ObjectUtil.isNotEmpty(params.getBatchNo())){
            batchWrapper.like(BusReturnBatchEntity::getBatchNo, StringUtils.join("%",params.getBatchNo(),"%"));
        }
        if (ObjectUtil.isNotEmpty(params.getCreateTime()) && params.getCreateTime().length == 2) {
            batchWrapper.between(BusReturnBatchEntity::getCreateTime,params.getCreateTime()[0], params.getCreateTime()[1]);
        }
        batchWrapper.orderByDesc(BusReturnBatchEntity::getId);
        PageUtils.startPage(params);
        List<BusReturnBatchEntity> batchList = busReturnBatchMapper.selectList(batchWrapper);

        List<BusShopEntity> shopEntityList = busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId()));
        Map<Long, String> shopMap = shopEntityList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, BusShopEntity::getShopName));
        List<ReturnBatchDto> dtoList = BeanUtils.copyList(batchList, ReturnBatchDto.class);
        for (ReturnBatchDto d :dtoList) {
            d.setShopName(shopMap.get(d.getShopUnique()));
        }

        return convertPageData(batchList,dtoList,ReturnBatchListResult.class,params);
    }
}
