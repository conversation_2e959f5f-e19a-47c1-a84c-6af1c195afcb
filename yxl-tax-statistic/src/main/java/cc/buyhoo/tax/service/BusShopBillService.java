package cc.buyhoo.tax.service;

import cc.buyhoo.common.bankpay.params.CmbPayConfigParams;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusShopBillEntity;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.params.busShopBill.ManualTransferAccountsParams;
import cc.buyhoo.tax.params.busShopBill.UpdateShopUnsettledAmountParams;
import cc.buyhoo.tax.result.busShopBill.BusShopBillPageResult;
import cc.buyhoo.tax.params.busShopBill.BusShopBillParams;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 供应商账单管理
 * @ClassName BusShopBillService
 * <AUTHOR>
 * @Date 2023/7/27 18:13
 **/
public interface BusShopBillService {
    Result<BusShopBillPageResult> pageList(BusShopBillParams busShopBillParams);

    Result<Void> export(BusShopBillParams busShopBillParams, HttpServletResponse response);

    Result<Void> importExcel(MultipartFile file);

    /**
     * 更新商铺待结算金额
     * @return
     */
    Result<Void> updateShopUnsettledAmount(UpdateShopUnsettledAmountParams params);

    /**
     * 一键转账
     * @return
     */
    Result<Void> transferAccounts();

    /**
     * 手动转账
     * @param params
     * @return
     */
    Result<Void> manualTransferAccounts(ManualTransferAccountsParams params);

    public void cmbcTransfer(BigDecimal transferMoney, String remark, Long userId, List<BusShopBillEntity> cmbcBill, SysCompanyEntity company);

    /**
     * 获取企业打款配置
     * @param companyId
     * @return
     */
    public CmbPayConfigParams getCompanyPayConfigById(Long companyId);
}
