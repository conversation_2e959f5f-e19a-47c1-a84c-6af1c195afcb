package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.inventoryOrderCategory.InventoryOrderCategoryParams;
import cc.buyhoo.tax.result.inventoryOrderCategory.InventoryOrderCategoryListResult;

/**
 * 分类库存
 * @ClassName BusInventoryOrderCatetoryService
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
public interface BusInventoryOrderCatetoryService {
    Result<InventoryOrderCategoryListResult> pageList(InventoryOrderCategoryParams params);
}
