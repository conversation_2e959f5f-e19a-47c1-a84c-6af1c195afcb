package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShopInvoice.*;
import cc.buyhoo.tax.params.invoice.InvoiceApplyParams;
import cc.buyhoo.tax.params.invoice.InvoiceResultQueryParams;
import cc.buyhoo.tax.params.invoice.ManualBatchInvoiceApplyParams;
import cc.buyhoo.tax.params.invoice.ManualInvoiceApplyParams;
import cc.buyhoo.tax.result.busShopInvoice.ShopGoodQueryList;
import cc.buyhoo.tax.result.busShopInvoice.ShopInvoiceDetailResult;
import cc.buyhoo.tax.result.busShopInvoice.ShopInvoiceListResult;

public interface BusShopInvoiceService {

    public Result<Void> testMinio(String path);
    /**
     * 获取当前发票的下载地址
     * @param params
     * @return
     */
    public Result<String> getInvoiceUrl(InvoiceApplyParams params);
    /**
     * 重新发送发票信息
     * @param params
     * @return
     */
    public Result<Void> resend(InvoiceApplyParams params);
    /**
     * 发票申请
     * @param params
     * @return
     */
    public Result<Void> invoiceApply(InvoiceApplyParams params);
    /**
     * 税务发票列表
     * @param params
     * @return
     */
    public Result<ShopInvoiceListResult> list(ShopInvoiceListParams params);

    /**
     * 新增进项票
     * @param params
     * @return
     */
    public Result<Void> add(ShopInvoiceAddParams params);

    /**
     * 发票明细
     * @param params
     * @return
     */
    public Result<ShopInvoiceDetailResult> invoiceDetail(ShopInvoiceDetailParams params);

    Result<ShopInvoiceDetailResult> invoiceDetailByInvoiceIds(ShopInvoiceDetailsParams params);

    Result<Void> saveShopInvoice(ShopInvoiceSaveParams params);

    Result<Void> manualInvoiceApply(ManualInvoiceApplyParams params);

    Result<Void> manualBatchInvoiceApply(ManualBatchInvoiceApplyParams params);

    Result<ShopGoodQueryList> queryGoodList(ShopGoodQueryParams params);

    Result<Void> queryInvoiceResult(InvoiceResultQueryParams params);
}
