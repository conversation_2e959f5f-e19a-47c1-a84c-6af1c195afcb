package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShop.ShopListExportParams;
import cc.buyhoo.tax.result.enterpriseDataStatistics.TreeVo;

import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName EnterpriseDataStatisticsServer
 * @Description 企业数据统计功能
 * <AUTHOR>
 * @Date 2025/6/16 上午11:35
 * @Version 1.0
 */
public interface EnterpriseDataStatisticsServer {

    /**
     * 统计数据
     * @param params
     * @return
     */
    Result<TreeVo> dataStatistics(ShopListExportParams params);

    /**
     * 按地区统计
     * @param params
     * @return
     */
    Result<TreeVo> getStatisticsByArea(ShopListExportParams params);
}