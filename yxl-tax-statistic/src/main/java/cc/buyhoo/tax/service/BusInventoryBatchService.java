package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusInventoryBatchEntity;
import cc.buyhoo.tax.params.inventoryBatch.InventoryBatchAddParams;
import cc.buyhoo.tax.params.inventoryBatch.InventoryBatchParams;
import cc.buyhoo.tax.result.inventoryBatch.InventoryBatchListResult;

import java.util.List;

/**
 * 入库
 * @ClassName BusInventoryBatchService
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
public interface BusInventoryBatchService {
    Result<InventoryBatchListResult> pageList(InventoryBatchParams params);

    Result<Void> saveInventoryBatch(InventoryBatchAddParams addParams);

    Result<List<BusInventoryBatchEntity>> getShopUniqueAndBatchIdByOrderId(Long orderId);

    Result<Void> deleteByIds(List<Long> ids);
}
