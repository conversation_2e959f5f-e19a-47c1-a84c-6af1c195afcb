package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.SysCompanyMapper;
import cc.buyhoo.tax.dao.SysMigrateMapper;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.entity.SysIndustryEntity;
import cc.buyhoo.tax.entity.SysMarketEntity;
import cc.buyhoo.tax.entity.SysMigrateEntity;
import cc.buyhoo.tax.enums.SysMigrateAuditStatusEnm;
import cc.buyhoo.tax.enums.TaxTypeEnum;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateAddParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateAuditParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateExportParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigratePageParams;
import cc.buyhoo.tax.result.sysCompany.CompanyExcel;
import cc.buyhoo.tax.result.sysMigrate.*;
import cc.buyhoo.tax.service.BaseService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.SysMigrateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 迁入迁出管理
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-08-29
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysMigrateServiceImpl extends BaseService implements SysMigrateService {

    private final SysMigrateMapper sysMigrateMapper;
    private final SysCompanyMapper sysCompanyMapper;

    @Override
    public Result<SysMigratePageResult> selectPageList(SysMigratePageParams pageParams) {
        PageUtils.startPage(pageParams);
        List<SysMigrateDto> pageList = sysMigrateMapper.selectPageList(pageParams);
        return convertPageData(pageList, SysMigratePageResult.class, pageParams);
    }

    @Override
    public Result<SysMigrateDetailResult> selectById(Long id) {
        SysMigrateDetailResult result = sysMigrateMapper.selectDetailById(id);
        if (ObjectUtil.isNotEmpty(result)) {
            result.setAuditStatusDesc(SysMigrateAuditStatusEnm.getLabel(result.getAuditStatus()));
            result.setTaxType(TaxTypeEnum.getLabel(result.getTaxType()));
            return Result.ok(result);
        }
        return Result.ok();
    }

    @Override
    public Result<String> addSysMigrate(SysMigrateAddParams addParams) {
        return null;
    }

    @Override
    public Result<String> auditSysMigrate(SysMigrateAuditParams auditParams) {
        return null;
    }

    @Override
    public Result<String> deleteByIds(DeleteIdsParams idsParams) {
        return null;
    }

    @Override
    public void export(SysMigrateExportParams params, HttpServletResponse response) {
        List<SysMigrateDto> list = sysMigrateMapper.selectList(params);
        List<SysMigrateExcel> excelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            excelList = list.stream().map(v -> {
                SysMigrateExcel e = new SysMigrateExcel();
                BeanUtil.copyProperties(v, e);
                e.setTargetAmount(NumberUtil.div(v.getTargetAmount(), BigDecimal.valueOf(10000)));
                e.setTaxTypeDesc(TaxTypeEnum.getLabel(String.valueOf(v.getTaxType())));
                e.setAuditStatusDesc(SysMigrateAuditStatusEnm.getLabel(v.getAuditStatus()));
                return e;
            }).collect(Collectors.toList());
        }
        String excelName = "市场_" + DateUtil.current() + ".xlsx";
        try {
            // 设置响应头，指定文件类型及编码
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName));
            // 清除响应
            response.reset();
            // 使用 EasyExcel 写入数据到 response 输出流
            EasyExcel.write(response.getOutputStream(), SysMigrateExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("Sheet1")
                    .doWrite(excelList); // dataList 是你要导出的数据列表
        } catch (IOException e) {
            log.error("导出市场Excel异常");
        }
    }

    @Override
    public Result<List<SysCompanyQueryDto>> queryCompanyList() {
        LambdaQueryWrapper<SysCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        List<SysCompanyEntity> list = sysCompanyMapper.selectList(queryWrapper);
        if(ObjectUtil.isNotEmpty(list)){
            return Result.ok(list.stream().map(v -> {
                SysCompanyQueryDto dto = new SysCompanyQueryDto();
                dto.setCompanyId(v.getId());
                dto.setCompanyName(v.getCompanyName());
                return dto;
            }).collect(Collectors.toList()));
        }
        return null;
    }
}