package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.ErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.config.properties.PayCenterProperties;
import cc.buyhoo.tax.dao.SysBankListMapper;
import cc.buyhoo.tax.dao.SysCompanyMapper;
import cc.buyhoo.tax.entity.SysBankListEntity;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.sysBank.SysBankBranchDto;
import cc.buyhoo.tax.temporary.PayCenterPayHelper;
import cc.buyhoo.tax.temporary.params.BankBranchParams;
import cc.buyhoo.tax.params.sysBank.SysBankBranchPageParam;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysBank.SysBankBranchPageResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.SysBankListService;
import cc.buyhoo.tax.temporary.result.BankBranchResp;
import cc.buyhoo.tax.temporary.result.PayCenterBaseResp;
import cc.buyhoo.tax.temporary.result.TableDataInfoResp;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.util.IpUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 银行类型
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 11:46
 **/
@Service
@RequiredArgsConstructor
public class SysBankListServiceImpl extends BaseService implements SysBankListService {

    private final SysBankListMapper sysBankListMapper;
    private final SysCompanyMapper sysCompanyMapper;
    private final PayCenterPayHelper payCenterPayHelper;
    private final PayCenterProperties payCenterProperties;

    @Override
    public Result<List<SelectDataDto>> bankSelectData() {
        List<SysBankListEntity> list = sysBankListMapper.selectList(Wrappers.lambdaQuery(SysBankListEntity.class).select(SysBankListEntity::getId, SysBankListEntity::getBankName).orderByAsc(SysBankListEntity::getBankName));
        List<SelectDataDto> dataDtos = list.stream().map(v ->{
            SelectDataDto selectDataDto = new SelectDataDto();
            selectDataDto.setLabel(v.getBankName());
            selectDataDto.setValue(v.getId());
            return selectDataDto;
        }).collect(Collectors.toList());
        return Result.ok(dataDtos);
    }

    @Override
    public Result<SysBankBranchPageResult> bankBranchPageList(SysBankBranchPageParam pageParam) {
        if (ObjUtil.hasEmpty(pageParam.getPageIndex(), pageParam.getPageSize())) {
            return Result.fail(CommonErrorEnum.PARAM_ERROR, "分页参数不能为空");
        }
        BankBranchParams bo = new BankBranchParams();
        BeanUtil.copyProperties(pageParam, bo);
        bo.setPageNum(pageParam.getPageIndex().longValue());
        bo.setPageSize(pageParam.getPageSize().longValue());
        LoginUser loginUser = SatokenUtil.getLoginUser();
        SysCompanyEntity company = sysCompanyMapper.selectById(loginUser.getCompanyId());
        bo.setMchId(company.getMchId());
        bo.setClientIp(IpUtil.getIp());
        PayCenterBaseResp payCenterBaseResp = payCenterPayHelper.queryBankBranch(payCenterProperties.getUrl(), bo, company.getPayCenterSecretKey());
        if (StrUtil.isNotBlank(payCenterBaseResp.getData())) {
            TableDataInfoResp<SysBankBranchDto> tableDataInfoResp = JSONUtil.toBean(payCenterBaseResp.getData(), TableDataInfoResp.class, true);
            SysBankBranchPageResult result = new SysBankBranchPageResult();
            result.setPageSize(pageParam.getPageSize());
            result.setPageIndex(pageParam.getPageIndex());
            result.setRows(tableDataInfoResp.getRows());
            result.setTotal(tableDataInfoResp.getTotal());
            return Result.ok(result);
        } else if (payCenterBaseResp.getCode() != HttpStatus.HTTP_OK) {
            return Result.fail(CommonErrorEnum.PARAM_ERROR, payCenterBaseResp.getMsg());
        }
        return convertPageData(Collections.EMPTY_LIST, SysBankBranchPageResult.class, pageParam);
    }
}
