package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.SysCityInfoEntity;
import cc.buyhoo.tax.result.sysCityInfo.SysCityInoDto;

import java.util.List;

/**
 * 城市表
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-08-23
 */
public interface SysCityInfoService {

    /**
     * 查询城市列表
     * @param pid 上级ID
     * @param level 查询层级
     * @return
     */
    Result<List<SysCityInoDto>> selectList(Long pid, Integer level);

    /**
     * 批量查询城市
     * @param cityInfoIds
     * @return
     */
    List<SysCityInfoEntity> selectByIds(Long[] cityInfoIds);
}