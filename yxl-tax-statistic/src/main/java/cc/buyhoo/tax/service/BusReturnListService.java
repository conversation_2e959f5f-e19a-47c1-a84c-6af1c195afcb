package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnList.ReturnListDetailParams;
import cc.buyhoo.tax.params.returnList.ReturnListParams;
import cc.buyhoo.tax.result.returnList.ReturnListDetailResult;
import cc.buyhoo.tax.result.returnList.ReturnListResult;

public interface BusReturnListService {

    /**
     * 退单列表
     * @param params
     * @return
     */
    public Result<ReturnListResult> returnList(ReturnListParams params);

    /**
     * 退单详情
     * @param params
     * @return
     */
    public Result<ReturnListDetailResult> returnListDetail(ReturnListDetailParams params);

}
