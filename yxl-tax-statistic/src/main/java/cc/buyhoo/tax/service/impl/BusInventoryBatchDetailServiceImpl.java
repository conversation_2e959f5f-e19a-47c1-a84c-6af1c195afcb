package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusInventoryBatchDetailMapper;
import cc.buyhoo.tax.entity.BusInventoryBatchDetailEntity;
import cc.buyhoo.tax.params.inventoryBatchDetail.InventoryBatchDetailParams;
import cc.buyhoo.tax.result.inventoryBatchDetail.InventoryBatchDetailDto;
import cc.buyhoo.tax.result.inventoryBatchDetail.InventoryBatchDetailListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusInventoryBatchDetailService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName BusInventoryBatchDetailServiceImpl
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
@Slf4j
@Service
public class BusInventoryBatchDetailServiceImpl extends BaseService implements BusInventoryBatchDetailService {
    @Resource
    private BusInventoryBatchDetailMapper busInventoryBatchDetailMapper;


    @Override
    public Result<InventoryBatchDetailListResult> pageList(InventoryBatchDetailParams params) {
        PageUtils.startPage(params);
        LambdaQueryWrapper<BusInventoryBatchDetailEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusInventoryBatchDetailEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getBatchId())) {
            queryWrapper.eq(BusInventoryBatchDetailEntity::getBatchId, params.getBatchId());
        }
        queryWrapper.orderByAsc(BusInventoryBatchDetailEntity::getId);
        List<BusInventoryBatchDetailEntity> list = busInventoryBatchDetailMapper.selectList(queryWrapper);
        List<InventoryBatchDetailDto> dtoList = list.stream().map(v -> {
            InventoryBatchDetailDto dto = new InventoryBatchDetailDto();
            BeanUtils.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());
        return convertPageData(list, dtoList, InventoryBatchDetailListResult.class, params);
    }

    @Override
    public Result<List<InventoryBatchDetailDto>> listAll(InventoryBatchDetailParams params) {
        LambdaQueryWrapper<BusInventoryBatchDetailEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusInventoryBatchDetailEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getBatchId())) {
            queryWrapper.eq(BusInventoryBatchDetailEntity::getBatchId, params.getBatchId());
        }
        queryWrapper.orderByAsc(BusInventoryBatchDetailEntity::getId);
        List<BusInventoryBatchDetailEntity> list = busInventoryBatchDetailMapper.selectList(queryWrapper);
        List<InventoryBatchDetailDto> dtoList = list.stream().map(v -> {
            InventoryBatchDetailDto dto = new InventoryBatchDetailDto();
            BeanUtils.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());
        return Result.ok(dtoList);
    }
}
