package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.login.LoginParams;
import cc.buyhoo.tax.result.login.CaptchaResult;
import cc.buyhoo.tax.result.login.LoginResult;

public interface LoginService {

    /**
     * 获取验证码
     * @return
     */
    public Result<CaptchaResult> captcha();

    /**
     * 用户登录
     * @param params
     * @return
     */
    public Result<LoginResult> login(LoginParams params);

    /**
     * 退出登录
     * @return
     */
    public Result<Void> logout();

}
