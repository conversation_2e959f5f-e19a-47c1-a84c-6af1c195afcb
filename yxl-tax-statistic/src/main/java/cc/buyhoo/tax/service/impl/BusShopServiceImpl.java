package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.config.properties.AllScmProperties;
import cc.buyhoo.tax.config.properties.PayCenterProperties;
import cc.buyhoo.tax.constant.AllScmConstants;
import cc.buyhoo.tax.constant.PayCenterConstants;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.entity.invoice.EleUserLoginResult;
import cc.buyhoo.tax.enums.*;
import cc.buyhoo.tax.enums.sys.UserTypeEnum;
import cc.buyhoo.tax.facade.BusShopApi;
import cc.buyhoo.tax.facade.params.busShop.ShopPayChangeParams;
import cc.buyhoo.tax.params.busShop.*;
import cc.buyhoo.tax.result.busShop.*;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.subAccount.CreateSubAccountResult;
import cc.buyhoo.tax.result.subAccount.PayCenterBaseResp;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopService;
import cc.buyhoo.tax.util.*;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description 供应商管理
 * @ClassName BusShopServiceImpl
 * <AUTHOR>
 * @Date 2023/7/26 13:54
 **/
@Slf4j
@Service
public class BusShopServiceImpl extends BaseService implements BusShopService {
    private final static String BUS_SHOP_AMOUNT_CACHE_KEY = "busShopIoAmount";
    @Resource
    private BusShopMapper busShopMapper;
    @Resource
    private BusShopBankMapper busShopBankMapper;
    @Resource
    private Cnarea2023Mapper cnarea2023Mapper;
    @Resource
    private SysCompanyMapper sysCompanyMapper;
    @Resource
    private SysMarketMapper sysMarketMapper;
    @Resource
    private BusSaleListMapper busSaleListMapper;
    @Resource
    private SysMigrateMapper sysMigrateMapper;
    @Autowired
    private RedisCache redisCache;
    @DubboReference
    private BusShopApi busShopApi;
    @Autowired
    private PayCenterProperties payCenterProperties;
    @Autowired
    private AllScmProperties allScmProperties;

    @Override
    public Result<Void> syncShopData(BusShopEntity shopEntity) {
        if (StrUtil.isNotBlank(shopEntity.getInvitationCode())) {
            SysCompanyEntity companyEntity = sysCompanyMapper.selectOne(new LambdaQueryWrapper<SysCompanyEntity>().eq(SysCompanyEntity::getInvitationCode, shopEntity.getInvitationCode()));
            if (null == companyEntity) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "邀请码：" + shopEntity.getInvitationCode() + " 对应纳统企业不存在");
            }
            shopEntity.setCompanyId(companyEntity.getId());
            shopEntity.setCreateTime(DateUtil.date());
            if (ObjectUtil.equals(shopEntity.getShopType(), Integer.valueOf(12))) {
                // 如果是餐饮店  默认只同步餐饮订单
                shopEntity.setSyncBuyhooData(Integer.valueOf(0));
                shopEntity.setSyncCanyinData(Integer.valueOf(1));
            } else {
                shopEntity.setSyncBuyhooData(Integer.valueOf(1));
                shopEntity.setSyncCanyinData(Integer.valueOf(0));
            }
            busShopMapper.insert(shopEntity);

            BusShopBankEntity bankEntity = new BusShopBankEntity();
            bankEntity.setShopUnique(shopEntity.getShopUnique());
            bankEntity.setBankId(shopEntity.getBankId());
            bankEntity.setBankName(shopEntity.getSubBank());
            bankEntity.setBankCard(shopEntity.getBankCard());
            bankEntity.setCompanyId(companyEntity.getId());
            bankEntity.setLegalPerson(shopEntity.getLegalPerson());
            bankEntity.setLegalPhone(shopEntity.getLegalPhone());
            busShopBankMapper.insert(bankEntity);
            return Result.ok();
        }
        return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "邀请码为空");
    }

    @Override
    public Result<BusShopListResult> pageList(ShopListParams shopListParams) {
        LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(shopListParams.getShopPhone()), BusShopEntity::getShopPhone, shopListParams.getShopPhone());
        queryWrapper.like(StrUtil.isNotBlank(shopListParams.getShopName()), BusShopEntity::getShopName, shopListParams.getShopName());
        queryWrapper.eq(ObjectUtil.isNotNull(shopListParams.getCompanyId()), BusShopEntity::getCompanyId, shopListParams.getCompanyId());
        if (ObjectUtil.isNotNull(shopListParams.getCountyCode())) {
            queryWrapper.eq(BusShopEntity::getCountyCode, shopListParams.getCountyCode().toString().substring(0,6));
        }
        if (ObjectUtil.isNotNull(shopListParams.getTownCode())) {
            queryWrapper.eq(BusShopEntity::getTownCode, shopListParams.getTownCode().toString().substring(0,9));
        }
        queryWrapper.eq(ObjectUtil.isNotNull(shopListParams.getShopNature()), BusShopEntity::getShopNature, shopListParams.getShopNature());
        LoginUser loginUser = SatokenUtil.getLoginUser();
        if (!SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            queryWrapper.eq(BusShopEntity::getCompanyId, loginUser.getCompanyId());
        }
        if (ObjectUtil.isNotNull(shopListParams.getMarketId())) {
            List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(new LambdaQueryWrapper<SysCompanyEntity>().select(SysCompanyEntity::getId).eq(SysCompanyEntity::getMarketId, shopListParams.getMarketId()).eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
            Set<Long> companyIds = companyList.stream().map(SysCompanyEntity::getId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(companyIds)) {
                queryWrapper.in(BusShopEntity::getCompanyId, companyIds);
            }

        }
        queryWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.orderByDesc(BusShopEntity::getId);
        PageUtils.startPage(shopListParams);
        List<BusShopEntity> list = busShopMapper.selectList(queryWrapper);
        Set<Long> userIds = list.stream().filter(v -> null != v.getModifyUser()).map(BusShopEntity::getModifyUser).collect(Collectors.toSet());
        Set<Long> companyIdSet = list.stream().filter(v -> null != v.getCompanyId()).map(BusShopEntity::getCompanyId).collect(Collectors.toSet());
        List<BusCompanyDto> companyList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(companyIdSet)) {
            List<SysCompanyEntity> sysCompanyList = sysCompanyMapper.selectList(new LambdaQueryWrapper<SysCompanyEntity>().in(SysCompanyEntity::getId, companyIdSet).select(SysCompanyEntity::getId, SysCompanyEntity::getCompanyName
                    , SysCompanyEntity::getMarketId));
            Set<Long> marketIdSet = sysCompanyList.stream().filter(v -> null != v.getMarketId()).map(SysCompanyEntity::getMarketId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(marketIdSet)) {
                List<SysMarketEntity> marketList = sysMarketMapper.selectList(new LambdaQueryWrapper<SysMarketEntity>().in(SysMarketEntity::getId, marketIdSet).select(SysMarketEntity::getId, SysMarketEntity::getMarketName));
                companyList = sysCompanyList.stream().map(v -> {
                    BusCompanyDto dto = new BusCompanyDto();
                    dto.setCompanyId(v.getId());
                    dto.setCompanyName(v.getCompanyName());
                    if (CollectionUtil.isNotEmpty(marketList) && marketList.stream().anyMatch(v1 -> v1.getId().equals(v.getMarketId()))) {
                        dto.setMarketId(v.getMarketId());
                        dto.setMarketName(marketList.stream().filter(v1 -> v1.getId().equals(v.getMarketId())).findFirst().get().getMarketName());
                    }
                    return dto;
                }).collect(Collectors.toList());
            }
        }

        List<String> countyCodes = list.stream().filter(v -> null != v.getCountyCode()).map(v -> v.getCountyCode() + "000000").collect(Collectors.toList());
        List<String> townCodes = list.stream().filter(v -> null != v.getTownCode()).map(v -> v.getTownCode() + "000").collect(Collectors.toList());
        List<Cnarea2023Entity> areaList = new ArrayList<>();
        List<Cnarea2023Entity> townList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(countyCodes)) {
            areaList = cnarea2023Mapper.selectList(new LambdaQueryWrapper<Cnarea2023Entity>().in(Cnarea2023Entity::getAreaCode, countyCodes));
        }
        if (ObjectUtil.isNotEmpty(townCodes)) {
            townList = cnarea2023Mapper.selectList(new LambdaQueryWrapper<Cnarea2023Entity>().in(Cnarea2023Entity::getAreaCode, townCodes));
        }
        Map<Long, String> userMap = handleSysUserIdName(userIds);
        List<BusCompanyDto> finalCompanyList = companyList;
        List<BusShopDto> listResults = list.stream().map(v -> {
            BusShopDto dto = new BusShopDto();
            BeanUtils.copyProperties(v, dto);
            dto.setCooperateTypeDesc(BusShopCooperateTypeEnum.getName(v.getCooperateType()));
            if (ObjectUtil.isNotEmpty(finalCompanyList) && finalCompanyList.stream().anyMatch(v1 -> v1.getCompanyId().equals(v.getCompanyId()))) {
                dto.setCompanyName(finalCompanyList.stream().filter(v1 -> v1.getCompanyId().equals(v.getCompanyId())).findFirst().get().getCompanyName());
                dto.setMarketName(finalCompanyList.stream().filter(v1 -> v1.getCompanyId().equals(v.getCompanyId())).findFirst().get().getMarketName());
            }
            if (ObjectUtil.isNotEmpty(redisCache.getCacheObject(BUS_SHOP_AMOUNT_CACHE_KEY + ":" + v.getShopUnique()))) {
                BusShopAmountDto busShopAmountDto = redisCache.getCacheObject(BUS_SHOP_AMOUNT_CACHE_KEY + ":" + v.getShopUnique());
                dto.setCompletedAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getCompletedAmount(), BigDecimal.ZERO));
                dto.setExpectedCompleteAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getExpectedCompleteAmount(), BigDecimal.ZERO));
                dto.setMonthAccumulatedRevenue(ObjectUtil.defaultIfNull(busShopAmountDto.getMonthAccumulatedRevenue(), BigDecimal.ZERO));
                dto.setJointVentureMonthAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getJointVentureMonthAmount(), BigDecimal.ZERO));
            } else {
                BusShopAmountDto busShopAmountDto = queryBusShopAmount(v.getShopUnique());
                if (ObjectUtil.isNotEmpty(busShopAmountDto)) {
                    dto.setCompletedAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getCompletedAmount(), BigDecimal.ZERO));
                    dto.setExpectedCompleteAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getExpectedCompleteAmount(), BigDecimal.ZERO));
                    dto.setMonthAccumulatedRevenue(ObjectUtil.defaultIfNull(busShopAmountDto.getMonthAccumulatedRevenue(), BigDecimal.ZERO));
                    dto.setJointVentureMonthAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getJointVentureMonthAmount(), BigDecimal.ZERO));
                }
            }
            dto.setModifyUser(userMap.get(v.getModifyUser()));
            dto.setShopNatureDesc(ShopNatureEnum.getName(v.getShopNature()));
            return dto;
        }).collect(Collectors.toList());

        for (BusShopDto v : listResults) {
            if (ObjectUtil.isNotEmpty(areaList)) {
                if (ObjectUtil.isNotEmpty(v.getCountyCode())) {
                    Cnarea2023Entity areaEntity = areaList.stream().filter(v1 -> v1.getAreaCode().toString().equals(v.getCountyCode() + "000000")).findFirst().orElse(null);
                    if (ObjectUtil.isNotNull(areaEntity)) {
                        v.setAreaName(areaEntity.getMergerName());
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(townList)) {
                if (ObjectUtil.isNotEmpty(v.getTownCode())) {
                    Cnarea2023Entity townEntity = townList.stream().filter(v1 -> ObjectUtil.equals(v.getTownCode().toString() + "000", v1.getAreaCode().toString())).findFirst().orElse(null);
                    if (ObjectUtil.isNotNull(townEntity)) {
                        v.setTownName(townEntity.getName());
                    }
                }
            }
        }

        return convertPageData(list, listResults, BusShopListResult.class, shopListParams);
    }

    @Override
    public Result<BusShopListResult> saveShop(ShopParams shopParams) {
        BusShopEntity entity = busShopMapper.selectById(shopParams.getId());
        if (ObjectUtil.isEmpty(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_NOT_EXIST);
        }
        entity.setBankName(shopParams.getBankName());
        entity.setBankCard(shopParams.getBankCard());
        entity.setLegalPerson(shopParams.getLegalPerson());
        entity.setLegalPhone(shopParams.getLegalPhone());
        entity.setServiceFeeRate(shopParams.getServiceFeeRate());
        entity.setHasServiceFee(shopParams.getHasServiceFee());
        entity.setContractUrl(shopParams.getContractUrl());
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        entity.setModifyTime(DateUtil.date());
        entity.setSubAccNo(shopParams.getSubAccNo());
        entity.setCooperatorCostProportion(shopParams.getCooperatorCostProportion());
        entity.setSupplierNo(shopParams.getSupplierNo());
        int num = busShopMapper.updateById(entity);
        return num > 0 ? Result.ok() : Result.fail();
    }

    @Override
    public Result<BusShopListResult> deleteByIds(List<Long> ids) {
        int num = busShopMapper.deleteBatchIds(ids);
        return num > 0 ? Result.ok() : Result.fail();
    }

    @Override
    public Result<List<BusShopSelectDto>> selectList(ShopListParams shopListParams) {
        LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BusShopEntity::getShopUnique, BusShopEntity::getShopName);
        queryWrapper.eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.orderByAsc(BusShopEntity::getShopUnique);
        List<BusShopEntity> list = busShopMapper.selectList(queryWrapper);
        List<BusShopSelectDto> dtoList = list.stream().map(v -> {
            BusShopSelectDto dto = new BusShopSelectDto();
            BeanUtils.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());
        return Result.ok(dtoList);
    }

    /**
     * 获取店铺银行信息
     *
     * @param params
     * @return
     */
    @Override
    public Result<GetBankInfoResult> getBankInfo(GetBankInfoParams params) {
        LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper.eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        shopWrapper.eq(BusShopEntity::getShopUnique, params.getShopUnique());
        shopWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        BusShopEntity shop = busShopMapper.selectOne(shopWrapper);
        if (ObjectUtil.isEmpty(shop)) throw new BusinessException(BusShopErrorEnum.SHOP_EMPTY_ERROR);

        GetBankInfoResult result = new GetBankInfoResult();
        BeanUtils.copyProperties(shop, result);
        return Result.ok(result);
    }

    @Override
    public Result<BusShopListResult> saveServiceRateBatch(ShopServiceRateParams params) {
        LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<BusShopEntity> list = busShopMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            Date currentDate = DateUtil.date();
            list.stream().forEach(v -> {
                v.setServiceFeeRate(params.getServiceFeeRate());
                v.setModifyUser(SatokenUtil.getLoginUserId());
                v.setModifyTime(currentDate);
            });
            busShopMapper.updateBatchById(list);
        }
        return Result.ok();
    }

    @Override
    public void exportShop(ShopListExportParams params, HttpServletResponse response) {
        LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(params.getShopPhone()), BusShopEntity::getShopPhone, params.getShopPhone());
        queryWrapper.like(StrUtil.isNotBlank(params.getShopName()), BusShopEntity::getShopName, params.getShopName());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getCompanyId()), BusShopEntity::getCompanyId, params.getCompanyId());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getShopNature()), BusShopEntity::getShopNature, params.getShopNature());
        if (ObjectUtil.isNotEmpty(params.getCountyCode())) {
            queryWrapper.eq(BusShopEntity::getCountyCode, params.getCountyCode().substring(0,6));
        }
        if  (ObjectUtil.isNotEmpty(params.getTownCode())) {
            queryWrapper.eq(BusShopEntity::getTownCode, params.getTownCode().substring(0,9));
        }
        //获取登录人
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //判断当前登录人不是系统平台的
        if (!SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            //就获取用户所属于哪个公司的
            queryWrapper.eq(BusShopEntity::getCompanyId, loginUser.getCompanyId());
        }

        if (ObjectUtil.isNotNull(params.getMarketId())) {
            List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(new LambdaQueryWrapper<SysCompanyEntity>().select(SysCompanyEntity::getId).eq(SysCompanyEntity::getMarketId, params.getMarketId()).eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
            Set<Long> companyIds = companyList.stream().map(SysCompanyEntity::getId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(companyIds)) {
                queryWrapper.in(BusShopEntity::getCompanyId, companyIds);
            }

        }
        queryWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.orderByDesc(BusShopEntity::getId);
        List<BusShopEntity> shopList = busShopMapper.selectList(queryWrapper);

        List<String> areaCodes = shopList.stream().filter(v -> null != v.getCountyCode()).map(v -> v.getCountyCode() + "000000").collect(Collectors.toList());
        List<String> townCodes = shopList.stream().filter(v -> null != v.getTownCode()).map(v -> v.getTownCode() + "000").collect(Collectors.toList());

        List<Cnarea2023Entity> areaList = new ArrayList<>();
        List<Cnarea2023Entity> townList = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(areaCodes)) {
            LambdaQueryWrapper<Cnarea2023Entity> areaQuery = new LambdaQueryWrapper<>();
            areaQuery.in(ObjectUtil.isNotEmpty(areaCodes), Cnarea2023Entity::getAreaCode,  areaCodes);
            areaList = cnarea2023Mapper.selectList(areaQuery);
        }
        if (ObjectUtil.isNotEmpty(townCodes)) {
            LambdaQueryWrapper<Cnarea2023Entity> areaQuery = new LambdaQueryWrapper<>();
            areaQuery.in(ObjectUtil.isNotEmpty(areaCodes), Cnarea2023Entity::getAreaCode,  townCodes);
            townList = cnarea2023Mapper.selectList(areaQuery);
        }
        //从shoplist中筛选出所有不为null的店铺,提取他们的companyid组成一个唯一的集合,创建空的集合来存放公司信息
        Set<Long> companyIdSet = shopList.stream().filter(v -> null != v.getCompanyId()).map(BusShopEntity::getCompanyId).collect(Collectors.toSet());
        List<BusCompanyDto> companyList = new ArrayList<>();


        if (ObjectUtil.isNotEmpty(companyIdSet)) {
            List<SysCompanyEntity> sysCompanyList = sysCompanyMapper.selectList(new LambdaQueryWrapper<SysCompanyEntity>().in(SysCompanyEntity::getId, companyIdSet).select(SysCompanyEntity::getId, SysCompanyEntity::getCompanyName
                    , SysCompanyEntity::getMarketId));
            Set<Long> marketIdSet = sysCompanyList.stream().filter(v -> null != v.getMarketId()).map(SysCompanyEntity::getMarketId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(marketIdSet)) {
                List<SysMarketEntity> marketList = sysMarketMapper.selectList(new LambdaQueryWrapper<SysMarketEntity>().in(SysMarketEntity::getId, marketIdSet).select(SysMarketEntity::getId, SysMarketEntity::getMarketName));
                companyList = sysCompanyList.stream().map(v -> {
                    BusCompanyDto dto = new BusCompanyDto();
                    dto.setCompanyId(v.getId());
                    dto.setCompanyName(v.getCompanyName());
                    if (CollectionUtil.isNotEmpty(marketList) && marketList.stream().anyMatch(v1 -> v1.getId().equals(v.getMarketId()))) {
                        dto.setMarketId(v.getMarketId());
                        dto.setMarketName(marketList.stream().filter(v1 -> v1.getId().equals(v.getMarketId())).findFirst().get().getMarketName());
                    }
                    return dto;
                }).collect(Collectors.toList());
            }
        }

        List<ShopExcel> excelList = new ArrayList<>();
        List<BusCompanyDto> finalCompanyList = companyList;
        if (CollectionUtil.isNotEmpty(shopList)) {
            List<Cnarea2023Entity> finalAreaList = areaList;
            List<Cnarea2023Entity> finalTownList = townList;
            excelList = shopList.stream().map(v -> {
                ShopExcel e = new ShopExcel();
                e.setShopName(v.getShopName());
                Cnarea2023Entity areaEntity = finalAreaList.stream().filter(v1 -> v1.getAreaCode().toString().equals(v.getCountyCode() + "000000")).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(areaEntity)) {
                    e.setAreaName(areaEntity.getMergerName());
                }
                Cnarea2023Entity townEntity = finalTownList.stream().filter(v1 -> v1.getAreaCode().toString().equals(v.getTownCode() + "000")).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(townEntity)) {
                    e.setTownName(townEntity.getName());
                }

                e.setShopNatureDesc(ShopNatureEnum.getName(v.getShopNature()));

                if (ObjectUtil.isNotEmpty(v.getAddress())) {
                    e.setAddress(StrUtil.hide(v.getAddress(), v.getAddress().length() - 8, v.getAddress().length()));
                } else {
                    e.setAddress(v.getAddress());
                }
                e.setShopPhone(StrUtil.isBlank(v.getShopPhone()) ? "" : StrUtil.hide(v.getShopPhone(), 3, v.getShopPhone().length() - 4));
                e.setCooperateTypeDesc(BusShopCooperateTypeEnum.getName(v.getCooperateType()));
                if (ObjectUtil.isNotEmpty(finalCompanyList) && finalCompanyList.stream().anyMatch(v1 -> v1.getCompanyId().equals(v.getCompanyId()))) {
                    e.setCompanyName(finalCompanyList.stream().filter(v1 -> v1.getCompanyId().equals(v.getCompanyId())).findFirst().get().getCompanyName());
                    e.setMarketName(finalCompanyList.stream().filter(v1 -> v1.getCompanyId().equals(v.getCompanyId())).findFirst().get().getMarketName());
                }
                if (ObjectUtil.isNotEmpty(redisCache.getCacheObject(BUS_SHOP_AMOUNT_CACHE_KEY + ":" + v.getShopUnique()))) {
                    BusShopAmountDto busShopAmountDto = redisCache.getCacheObject(BUS_SHOP_AMOUNT_CACHE_KEY + ":" + v.getShopUnique());
                    e.setCompletedAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getCompletedAmount(), BigDecimal.ZERO));
                    e.setExpectedCompleteAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getExpectedCompleteAmount(), BigDecimal.ZERO));
                    e.setMonthAccumulatedRevenue(ObjectUtil.defaultIfNull(busShopAmountDto.getMonthAccumulatedRevenue(), BigDecimal.ZERO));
                } else {
                    BusShopAmountDto busShopAmountDto = queryBusShopAmount(v.getShopUnique());
                    if (ObjectUtil.isNotEmpty(busShopAmountDto)) {
                        e.setCompletedAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getCompletedAmount(), BigDecimal.ZERO));
                        e.setExpectedCompleteAmount(ObjectUtil.defaultIfNull(busShopAmountDto.getExpectedCompleteAmount(), BigDecimal.ZERO));
                        e.setMonthAccumulatedRevenue(ObjectUtil.defaultIfNull(busShopAmountDto.getMonthAccumulatedRevenue(), BigDecimal.ZERO));
                    }
                }
                return e;
            }).collect(Collectors.toList());
        }
        String excelName = "供货商_" + DateUtil.current() + ".xlsx";
        try {
            // 设置响应头，指定文件类型及编码
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName));
            // 清除响应
            response.reset();

            if (loginUser.getUserType() == UserTypeEnum.SUPER_ADMIN.getValue()) {
                // 使用 EasyExcel 写入数据到 response 输出流
                EasyExcel.write(response.getOutputStream(), ShopExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("Sheet1")
                        .doWrite(excelList); // dataList 是你要导出的数据列表
            } else if (loginUser.getUserType() == UserTypeEnum.ADMIN.getValue()) {
                List<ShopExcelBase> baseExcelList = excelList.stream().map(v -> {
                    ShopExcelBase base = new ShopExcelBase();
                    BeanUtil.copyProperties(v, base);
                    return base;
                }).collect(Collectors.toList());

                EasyExcel.write(response.getOutputStream(), ShopExcelBase.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("Sheet1")
                        .doWrite(baseExcelList);
            }

        } catch (IOException e) {
            log.error("导出供货商Excel异常");
        }
    }

    @Override
    public Result<BusShopMigrateInCompanyListQueryResult> queryMigrateInCompanyList(BusShopMigrateInCompanyListQueryParams params) {
        BusShopMigrateInCompanyListQueryResult result = new BusShopMigrateInCompanyListQueryResult();
        result.setList(Collections.emptyList());

        BusShopEntity busShop = busShopMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(busShop)) {
            return Result.ok(result);
        }
        if (ObjectUtil.isEmpty(busShop.getCompanyId())) {
            return Result.ok(result);
        }
        SysCompanyEntity sysCompanyEntity = sysCompanyMapper.selectById(busShop.getCompanyId());
        if (ObjectUtil.isEmpty(sysCompanyEntity)) {
            return Result.ok(result);
        }
        if (ObjectUtil.isEmpty(sysCompanyEntity.getMarketId())) {
            return Result.ok(result);
        }
        LambdaQueryWrapper<SysCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysCompanyEntity::getMarketId, sysCompanyEntity.getMarketId());
        queryWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        List<SysCompanyEntity> list = sysCompanyMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            List<BusShopMigrateInCompanyListQueryDto> dtoList = list.stream().map(v -> {
                if (ObjectUtil.isNotEmpty(v.getId()) && ObjectUtil.equals(busShop.getCompanyId(), v.getId())) {
                    return null;
                }
                BusShopMigrateInCompanyListQueryDto dto = new BusShopMigrateInCompanyListQueryDto();
                dto.setCompanyId(v.getId());
                dto.setCompanyName(v.getCompanyName());
                return dto;
            }).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
            result.setList(dtoList);
        }
        return Result.ok(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> companyMigrateIo(ShopCompanyMigrateIoParams params) {
        BusShopEntity entity = busShopMapper.selectById(params.getId());
        SysMigrateEntity sysMigrateEntity = new SysMigrateEntity();
        LoginUser loginUser = SatokenUtil.getLoginUser();
        if (ObjectUtil.isEmpty(entity) || ObjectUtil.equals(DelFlagEnum.DELETED.getCode(), entity.getDelFlag())
                || ObjectUtil.equals(EnableStatusEnum.UNAVAILABLE.getCode(), entity.getEnableStatus())) {
            return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "商户不存在");
        }
        if (ObjectUtil.isNull(entity.getCompanyId())) {
            return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "企业不存在");
        }
        if (!SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            if (ObjectUtil.notEqual(entity.getCompanyId(), loginUser.getCompanyId())) {
                return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "企业信息错误");
            }
        }
        if (ObjectUtil.equals(entity.getCompanyId(), params.getMigrateInCompanyId())) {
            return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "迁入企业需与当前企业不同");
        }
        sysMigrateEntity.setOutCompanyId(entity.getCompanyId());
        entity.setModifyUser(loginUser.getId());
        entity.setModifyTime(DateUtil.date());
        entity.setCooperateType(params.getCooperateType());
        entity.setCompanyId(params.getMigrateInCompanyId());
        busShopMapper.updateById(entity);

        sysMigrateEntity.setInCompanyId(params.getMigrateInCompanyId());
        sysMigrateEntity.setShopUnique(entity.getShopUnique());
        sysMigrateEntity.setAuditStatus(SysMigrateAuditStatusEnm.AUDIT_SUCCESS.getValue());
        sysMigrateEntity.setAuditTime(DateUtil.date());
        sysMigrateMapper.insert(sysMigrateEntity);

        ShopPayChangeParams shopPayChangeParams = new ShopPayChangeParams();
        shopPayChangeParams.setShopUnique(entity.getShopUnique());
        shopPayChangeParams.setCompanyId(entity.getCompanyId());
        Result<Void> result = busShopApi.shopPayChangeList(shopPayChangeParams);
        if (result.getStatus() != 1) {
            throw new BusinessException(result.getCode(), result.getMessage());
        }
        redisCache.deleteObject(BUS_SHOP_AMOUNT_CACHE_KEY + ":" + params.getId());

        return Result.ok();
    }


    private List<BusShopAmountDto> queryBusShopAmountList(List<Long> shopUniqueList) {
        List<BusShopAmountDto> dtoList = new ArrayList<>();
        for (Long shopUnique : shopUniqueList) {
            BusShopAmountDto dto = new BusShopAmountDto();
            dto.setShopUnique(shopUnique);
            dto.setCompletedAmount(BigDecimal.ZERO);
            dto.setExpectedCompleteAmount(BigDecimal.ZERO);
            dto.setMonthAccumulatedRevenue(BigDecimal.ZERO);
            dto.setJointVentureMonthAmount(BigDecimal.ZERO);
            dtoList.add(dto);
        }
        LambdaQueryWrapper<BusShopEntity> shopQueryWrapper = new LambdaQueryWrapper<>();
        shopQueryWrapper.in(BusShopEntity::getShopUnique, shopUniqueList);
        List<BusShopEntity> shopList = busShopMapper.selectList(shopQueryWrapper);
        if (ObjectUtil.isNotEmpty(shopList)) {
            Set<Long> companyIdSet = shopList.stream().map(BusShopEntity::getCompanyId).collect(Collectors.toSet());
            LambdaQueryWrapper<SysCompanyEntity> companyQueryWrapper = new LambdaQueryWrapper<>();
            companyQueryWrapper.in(SysCompanyEntity::getId, companyIdSet);
            companyQueryWrapper.select(SysCompanyEntity::getId, SysCompanyEntity::getTaxType);
            List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyQueryWrapper);
            List<BusShopCompanyDto> busShopCompanyList = shopList.stream().map(v -> {
                BusShopCompanyDto busShopCompanyDto = new BusShopCompanyDto();
                busShopCompanyDto.setShopUnique(v.getShopUnique());
                if (ObjectUtil.isNotEmpty(v.getCompanyId()) && companyList.stream().anyMatch(c -> ObjectUtil.equals(c.getId(), v.getCompanyId()))) {
                    busShopCompanyDto.setCompanyId(v.getCompanyId());
                    busShopCompanyDto.setTaxType(companyList.stream().filter(c -> ObjectUtil.equals(c.getId(), v.getCompanyId())).findFirst().get().getTaxType());
                }
                return busShopCompanyDto;
            }).collect(Collectors.toList());
            QueryWrapper<BusSaleListEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(BusSaleListEntity::getShopUnique, shopUniqueList);
            queryWrapper.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfYear(new Date()));
            queryWrapper.lambda().select(BusSaleListEntity::getSaleListActuallyReceived, BusSaleListEntity::getCompanyId, BusSaleListEntity::getSaleListDatetime,
                    BusSaleListEntity::getOrderType, BusSaleListEntity::getShopUnique);
            List<BusSaleListEntity> saleList = busSaleListMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(saleList)) {
                for (BusShopAmountDto dto : dtoList) {
                    long allDay = 0L;
                    long day = 0L;
                    Date startDate = new Date();
                    if (ObjectUtil.isNotEmpty(busShopCompanyList) && busShopCompanyList.stream().anyMatch(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique()))
                            && ObjectUtil.isNotEmpty(busShopCompanyList.stream().filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())).findFirst().get().getTaxType())) {
                        if (ObjectUtil.equals(TaxTypeEnum.YEAR.getValue(), busShopCompanyList.stream().filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())).findFirst().get().getTaxType())) {
                            allDay = DateUtil.lengthOfYear(DateUtil.year(DateUtil.date()));
                            day = DateUtil.betweenDay(DateUtil.beginOfYear(DateUtil.date()), DateUtil.date(), true);
                            startDate = DateUtil.beginOfYear(new Date());
                        } else if (ObjectUtil.equals(TaxTypeEnum.SEASON.getValue(), busShopCompanyList.stream().filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())).findFirst().get().getTaxType())) {
                            allDay = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.endOfQuarter(DateUtil.date()), true);
                            day = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.date(), true);
                            startDate = DateUtil.beginOfQuarter(new Date());
                        } else if (ObjectUtil.equals(TaxTypeEnum.MONTH.getValue(), busShopCompanyList.stream().filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())).findFirst().get().getTaxType())) {
                            allDay = DateUtil.lengthOfMonth(DateUtil.month(DateUtil.date()), DateUtil.isLeapYear(DateUtil.year(DateUtil.date())));
                            day = DateUtil.betweenDay(DateUtil.beginOfMonth(DateUtil.date()), DateUtil.date(), true);
                            startDate = DateUtil.beginOfMonth(new Date());
                        }
                    }
                    if (ObjectUtil.isNotEmpty(busShopCompanyList.stream().filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())).findFirst().get().getCompanyId())) {
                        Date finalStartDate = startDate;
                        dto.setCompletedAmount(saleList.stream()
                                .filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())
                                        && ObjectUtil.equals(busShopCompanyList.stream().filter(w -> ObjectUtil.equals(dto.getShopUnique(), w.getShopUnique())).findFirst().get().getCompanyId(), v.getCompanyId())
                                        && v.getSaleListDatetime().compareTo(finalStartDate) >= 0)
                                .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add));
                        dto.setExpectedCompleteAmount(saleList.stream()
                                .filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())
                                        && ObjectUtil.equals(busShopCompanyList.stream().filter(w -> ObjectUtil.equals(dto.getShopUnique(), w.getShopUnique())).findFirst().get().getCompanyId(), v.getCompanyId())
                                        && v.getSaleListDatetime().compareTo(finalStartDate) >= 0)
                                .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(new BigDecimal(allDay)).divide(new BigDecimal(day), 2, BigDecimal.ROUND_HALF_UP));
                    }
                    dto.setMonthAccumulatedRevenue(saleList.stream()
                            .filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())
                                    && DateUtil.date(v.getSaleListDatetime()).compareTo(DateUtil.beginOfMonth(DateUtil.date())) >= 0)
                            .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add));
                    dto.setJointVentureMonthAmount(saleList.stream()
                            .filter(v -> ObjectUtil.equals(dto.getShopUnique(), v.getShopUnique())
                                    && ObjectUtil.equals(SaleListOrderTypeEnum.ORDER_TYPE_2.getCode(), v.getOrderType())
                                    && DateUtil.date(v.getSaleListDatetime()).compareTo(DateUtil.beginOfMonth(DateUtil.date())) >= 0)
                            .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add));
                    redisCache.setCacheObject(BUS_SHOP_AMOUNT_CACHE_KEY + ":" + dto.getShopUnique(), dto,
                            (int) DateUtil.between(DateUtil.date(), DateUtil.endOfDay(DateUtil.date()), DateUnit.MINUTE), TimeUnit.MINUTES);
                }
            }
        }
        return dtoList;
    }

    private BusShopAmountDto queryBusShopAmount(Long shopUnique) {
        BusShopAmountDto dto = new BusShopAmountDto();
        dto.setCompletedAmount(BigDecimal.ZERO);
        dto.setExpectedCompleteAmount(BigDecimal.ZERO);
        dto.setMonthAccumulatedRevenue(BigDecimal.ZERO);
        dto.setJointVentureMonthAmount(BigDecimal.ZERO);
        LambdaQueryWrapper<BusShopEntity> shopQueryWrapper = new LambdaQueryWrapper<>();
        shopQueryWrapper.eq(BusShopEntity::getShopUnique, shopUnique);
        BusShopEntity shopEntity = busShopMapper.selectOne(shopQueryWrapper);
        if (ObjectUtil.isNotEmpty(shopEntity)) {
            SysCompanyEntity sysCompanyEntity = sysCompanyMapper.selectById(shopEntity.getCompanyId());
            if (ObjectUtil.isNotEmpty(sysCompanyEntity)) {
                if (ObjectUtil.equals(DelFlagEnum.DELETED.getCode(), sysCompanyEntity.getDelFlag())
                        || ObjectUtil.equals(EnableStatusEnum.UNAVAILABLE.getCode(), sysCompanyEntity.getEnableStatus())) {
                    return dto;
                }
            }
            QueryWrapper<BusSaleListEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BusSaleListEntity::getShopUnique, shopUnique);
            long allDay = 0L;
            long day = 0L;
            if (ObjectUtil.isNotEmpty(sysCompanyEntity.getTaxType())) {
                if (ObjectUtil.equals(TaxTypeEnum.YEAR.getValue(), sysCompanyEntity.getTaxType())) {
                    allDay = DateUtil.lengthOfYear(DateUtil.year(DateUtil.date()));
                    day = DateUtil.betweenDay(DateUtil.beginOfYear(DateUtil.date()), DateUtil.date(), true);
                    queryWrapper.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfYear(new Date()));
                } else if (ObjectUtil.equals(TaxTypeEnum.SEASON.getValue(), sysCompanyEntity.getTaxType())) {
                    allDay = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.endOfQuarter(DateUtil.date()), true);
                    day = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.date(), true);
                    queryWrapper.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfQuarter(new Date()));
                } else if (ObjectUtil.equals(TaxTypeEnum.MONTH.getValue(), sysCompanyEntity.getTaxType())) {
                    allDay = DateUtil.lengthOfMonth(DateUtil.month(DateUtil.date()), DateUtil.isLeapYear(DateUtil.year(DateUtil.date())));
                    day = DateUtil.betweenDay(DateUtil.beginOfMonth(DateUtil.date()), DateUtil.date(), true);
                    queryWrapper.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfMonth(new Date()));
                }
            }
            queryWrapper.lambda().eq(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_2.getCode());
            queryWrapper.lambda().select(BusSaleListEntity::getSaleListActuallyReceived, BusSaleListEntity::getCompanyId, BusSaleListEntity::getSaleListDatetime,
                    BusSaleListEntity::getOrderType);
            List<BusSaleListEntity> saleList = busSaleListMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(saleList)) {
                dto.setCompletedAmount(saleList.stream()
                        .filter(v -> ObjectUtil.equals(sysCompanyEntity.getId(), v.getCompanyId()))
                        .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add));
                if (ObjectUtil.isNotEmpty(saleList.stream()
                        .filter(v -> ObjectUtil.equals(sysCompanyEntity.getId(), v.getCompanyId())).collect(Collectors.toList()))) {
                    BigDecimal doneAmount = (saleList.stream()
                            .filter(v -> ObjectUtil.equals(sysCompanyEntity.getId(), v.getCompanyId()))
                            .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add))
                            .divide(BigDecimal.valueOf(saleList.stream()
                                    .filter(v -> ObjectUtil.equals(sysCompanyEntity.getId(), v.getCompanyId()))
                                    .map(v -> DateUtil.format(v.getSaleListDatetime(), "yyyy-MM-dd")).distinct().count()), 2, BigDecimal.ROUND_HALF_UP);
                    dto.setExpectedCompleteAmount(saleList.stream()
                            .filter(v -> ObjectUtil.equals(sysCompanyEntity.getId(), v.getCompanyId()))
                            .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add).add(doneAmount.multiply(BigDecimal.valueOf(allDay - day))));
                }
                dto.setMonthAccumulatedRevenue(saleList.stream()
                        .filter(v -> DateUtil.date(v.getSaleListDatetime()).compareTo(DateUtil.beginOfMonth(DateUtil.date())) >= 0)
                        .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add));
                dto.setJointVentureMonthAmount(saleList.stream()
                        .filter(v -> ObjectUtil.equals(SaleListOrderTypeEnum.ORDER_TYPE_2.getCode(), v.getOrderType())
                                && DateUtil.date(v.getSaleListDatetime()).compareTo(DateUtil.beginOfMonth(DateUtil.date())) >= 0)
                        .map(v -> v.getSaleListActuallyReceived()).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            redisCache.setCacheObject(BUS_SHOP_AMOUNT_CACHE_KEY + ":" + shopUnique, dto,
                    (int) DateUtil.between(DateUtil.date(), DateUtil.endOfDay(DateUtil.date()), DateUnit.MINUTE), TimeUnit.MINUTES);
        }
        return dto;
    }

    /**
     * TODO 确认是新增还是教研
     * @param params
     * @return
     */
    @Override
    public Result<CreateSubAccountResult> addsubAccount(AddSybAccountParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        SysCompanyEntity sysCompanyEntity = sysCompanyMapper.selectById(loginUser.getCompanyId());
        if (ObjectUtil.hasEmpty(sysCompanyEntity,sysCompanyEntity.getMchId(),sysCompanyEntity.getSubAccountSecretKey(),sysCompanyEntity.getPayCenterSecretKey())) {
            return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "企业信息为空");
        }
        BusShopEntity shopEntity = busShopMapper.selectById(params.getId());
        if (ObjectUtil.isNotEmpty(shopEntity)) {
            HttpResponse response = null;
            String jsonStr = null;
//            if (ObjectUtil.isEmpty(shopEntity.getSubAccNo())) {
                //支付中心地址
                String url = payCenterProperties.getUrl() + PayCenterConstants.CREATE_SUB_ACCOUNT;
                PayCenterPayParams payParams = new PayCenterPayParams();
                payParams.setClientIp(IpUtil.getIpAddr());
                CmbcSubAccountParams subAccountParams = new CmbcSubAccountParams();
                subAccountParams.setMchBankId(sysCompanyEntity.getMchBankId());
                subAccountParams.setBankKey(params.getBankCode());
                subAccountParams.setAccNo(params.getSubAccount());
                subAccountParams.setMerSerialNo(SequenceUtils.cmbcMerSerialNo());
                subAccountParams.setPartnerName(params.getSubAccountName());
                subAccountParams.setStat(params.getSubAccountStatus());
                subAccountParams.setAccName(params.getContactUserName());
                subAccountParams.setPhoneId(params.getContactPhone());
                subAccountParams.setMail(params.getEmail());
                subAccountParams.setIsPush(params.getPubNotice());
                subAccountParams.setBankKey(params.getBankCode());
                subAccountParams.setPayClientIp(IpUtil.getIpAddr());
                subAccountParams.setMchId(sysCompanyEntity.getMchId());
                Map<String, String> headerMap = PayCenterUtil.buildHeader(subAccountParams, sysCompanyEntity.getSubAccountSecretKey());
                jsonStr = JSONUtil.toJsonStr(subAccountParams);
                log.info("创建子账号入参：{},headerMap:{},key:{}",jsonStr,JSONUtil.toJsonStr(headerMap),sysCompanyEntity.getSubAccountSecretKey());
                response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
                log.info("创建子账号返回结果：{}", response.body());
                if (response.getStatus() != HttpStatus.HTTP_OK) {
                    return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "创建子账号失败");
                }
            /*} else {
                //支付中心地址
                String url = payCenterProperties.getUrl() + PayCenterConstants.UPDATE_SUB_ACCOUNT;
                PayCenterPayParams payParams = new PayCenterPayParams();
                payParams.setClientIp(IpUtil.getIpAddr());
                CmbcSubAccountParams subAccountParams = new CmbcSubAccountParams();
                subAccountParams.setMchBankId(sysCompanyEntity.getMchBankId());
                subAccountParams.setBankKey(params.getBankCode());
                subAccountParams.setAccNo(params.getSubAccount());
                subAccountParams.setMerSerialNo(SequenceUtils.cmbcMerSerialNo());
                subAccountParams.setPartnerName(params.getSubAccountName());
                subAccountParams.setStat(params.getSubAccountStatus());
                subAccountParams.setAccName(params.getContactUserName());
                subAccountParams.setPhoneId(params.getContactPhone());
                subAccountParams.setMail(params.getEmail());
                subAccountParams.setIsPush(params.getPubNotice());
                subAccountParams.setBankKey(params.getBankCode());
                subAccountParams.setPayClientIp(IpUtil.getIpAddr());
                subAccountParams.setMchId(sysCompanyEntity.getMchId());
                Map<String, String> headerMap = PayCenterUtil.buildHeader(subAccountParams, sysCompanyEntity.getSubAccountSecretKey());
                jsonStr = JSONUtil.toJsonStr(subAccountParams);
                log.info("修改子账号入参：{},headerMap:{},key:{}",jsonStr,JSONUtil.toJsonStr(headerMap),sysCompanyEntity.getSubAccountSecretKey());
                response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
                log.info("修改子账号返回结果：{}", response.body());
                if (response.getStatus() != HttpStatus.HTTP_OK) {
                    return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "修改子账号失败");
                }
            }*/
            PayCenterBaseResp bean = JSONUtil.toBean(response.body(), PayCenterBaseResp.class);
            if (bean.getCode()!= HttpStatus.HTTP_OK) {
                log.info("创建、修改子账号失败-支付中心-错误信息：{}", bean.getMsg());
                return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, bean.getMsg());
            }
            BusShopEntity entity = new BusShopEntity();
            entity.setId(params.getId());
            CmbcSubAccountParams bean1 = JSONUtil.toBean(bean.getData(), CmbcSubAccountParams.class);
            entity.setSubAccNo(bean1.getAccNo());
            busShopMapper.updateById(entity);

            String allScmurl = allScmProperties.getUrl()+ AllScmConstants.SET_SUB_ACCOUNT;
            SetSubAccountParams param = new SetSubAccountParams();
            param.setSubAccount(bean1.getAccNo());
            param.setShopUnique(params.getShopUnique());
            String allScmJsonStr = JSONUtil.toJsonStr(param);
            log.info("创建、修改子账户-调用金圈入参：{}",allScmJsonStr);
            String msg ="";
            try {
                HttpResponse execute = HttpRequest.post(allScmurl).body(allScmJsonStr).execute();
                String body = execute.body();
                log.info("创建、修改子账户-调用金圈返回：{}",body);
                if (ObjectUtil.notEqual(body.toUpperCase(),"SUCCESS")) {
                    EleUserLoginResult bean2 = JSONUtil.toBean(body, EleUserLoginResult.class);
                    if (ObjectUtil.notEqual(bean2.getCode(),200)){
                        msg="子账号同步金圈异常:["+bean2.getMsg()+"]";
                    }
                }
            }catch (Exception e) {
                log.error("创建、修改子账户-调用金圈异常：入参{}",jsonStr,e);
                msg="子账簿创建、修改成功-同步子账户到金圈失败，请手动同步子账号";
            }
            Result<CreateSubAccountResult> ok = Result.ok(JSONUtil.toBean(bean.getData(), CreateSubAccountResult.class));
            if (StrUtil.isNotBlank(msg)){
                ok.setMsg(msg);
            }
            return ok;
        }
        return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "供货商信息为空");
    }

    @Override
    public Result<Void> allAcmGetSubAccount(AddSybAccountParams params) {
        return null;
    }

    @Override
    public Result<Void> allAcmUpdateSubAccount(AddSybAccountParams params) {
        return null;
    }

    @Override
    public List<BusShopEntity> selectBusShopByshopUniques(List<String> shopUniques) {
        return busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().in(BusShopEntity::getShopUnique, shopUniques));
    }
}
