package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShopInvoice.ShopInvoiceSaveParams;
import cc.buyhoo.tax.params.external.GetInvoiceMsgParmas;
import cc.buyhoo.tax.params.external.SaveInvoiceMsgParams;
import cc.buyhoo.tax.params.external.SaveInvoiceMsgPublicParams;
import cc.buyhoo.tax.params.external.ShopInvoiceAddParams;
import cc.buyhoo.tax.params.invoice.NotifyInvoiceResultParams;
import cc.buyhoo.tax.result.external.GetInvoiceMsgResult;
import org.springframework.web.bind.annotation.PostMapping;

public interface ExternalService {

    /**
     * 根据开票的店铺信息，获取对应的纳统企业的开票信息及对应的开票内容
     * @param getInvoiceMsgParmas
     * @return
     */
    public Result<GetInvoiceMsgResult> getInvoiceMsg(GetInvoiceMsgParmas getInvoiceMsgParmas);

    /**
     * 存储开票结果
     * @param saveInvoiceMsgParams
     * @return
     */
    public Result<Void> saveInvoiceMsg(SaveInvoiceMsgParams saveInvoiceMsgParams);

    /**
     * 申请开票
     * 将开票功能存放到纳统平台，开票完成后，回调到餐饮或其他相关业务系统
     * 客户需要上传回调地址，开票的购买方信息及订单信息
     * @param saveInvoiceMsgPublicParams
     * @return
     */
    public Result<Void> saveInvoiceMsgPub(SaveInvoiceMsgPublicParams saveInvoiceMsgPublicParams);

    Result<NotifyInvoiceResultParams> saveShopInvoice(ShopInvoiceAddParams params);
}
