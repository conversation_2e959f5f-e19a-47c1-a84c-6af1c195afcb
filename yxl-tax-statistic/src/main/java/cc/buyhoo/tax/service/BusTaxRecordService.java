package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busTaxRecord.BusTaxRecordParams;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busTaxRecord.BusTaxRecordResult;

/**
 * @Description
 * @ClassName BusTaxRecordService
 * <AUTHOR>
 * @Date 2023/8/11 11:29
 **/
public interface BusTaxRecordService {
    Result<BusTaxRecordResult> pageList(BusTaxRecordParams params);

    Result<Void> save(BusTaxRecordParams params);

    Result<Void> delete(DeleteIdsParams idsParams);

    Result<Void> updateSuccess(BusTaxRecordParams params);
}
