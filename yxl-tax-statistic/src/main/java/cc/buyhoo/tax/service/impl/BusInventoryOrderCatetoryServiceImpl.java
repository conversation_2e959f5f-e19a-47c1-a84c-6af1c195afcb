package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusInventoryOrderCategoryMapper;
import cc.buyhoo.tax.params.inventoryOrderCategory.InventoryOrderCategoryParams;
import cc.buyhoo.tax.result.inventoryOrderCategory.InventoryOrderCategoryDto;
import cc.buyhoo.tax.result.inventoryOrderCategory.InventoryOrderCategoryListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusInventoryOrderCatetoryService;
import cc.buyhoo.tax.util.SatokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * @ClassName BusInventoryOrderCatetoryServiceImpl
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
@Slf4j
@Service
public class BusInventoryOrderCatetoryServiceImpl extends BaseService implements BusInventoryOrderCatetoryService {
    @Resource
    private BusInventoryOrderCategoryMapper busInventoryOrderCategoryMapper;

    @Override
    public Result<InventoryOrderCategoryListResult> pageList(InventoryOrderCategoryParams params) {
        PageUtils.startPage(params);
        params.setCompanyId(SatokenUtil.getLoginUserCompanyId());
        params.setOrderId(params.getOrderId());
        List<InventoryOrderCategoryDto> list = busInventoryOrderCategoryMapper.selectPageList(params);
        return convertPageData(list, InventoryOrderCategoryListResult.class, params);
    }
}
