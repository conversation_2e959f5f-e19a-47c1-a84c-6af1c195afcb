package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.SysCompanyMapper;
import cc.buyhoo.tax.dao.SysMarketMapper;
import cc.buyhoo.tax.entity.SysMarketEntity;
import cc.buyhoo.tax.entity.SysCityInfoEntity;
import cc.buyhoo.tax.enums.ManagementModelEnum;
import cc.buyhoo.tax.enums.TaxTypeEnum;
import cc.buyhoo.tax.params.sysMarket.*;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysCompany.CompanyExcel;
import cc.buyhoo.tax.result.sysMarket.MarketSelectDataDto;
import cc.buyhoo.tax.result.sysMarket.SysMarketDto;
import cc.buyhoo.tax.result.sysMarket.SysMarketExcel;
import cc.buyhoo.tax.result.sysMarket.SysMarketPageResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.SysCityInfoService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.SysMarketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 市场管理表
 * @ClassName SysMarketServiceImpl
 * <AUTHOR> 
 * @since 1.0.0 2024-08-23
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysMarketServiceImpl extends BaseService implements SysMarketService {

    private final SysMarketMapper sysMarketMapper;
    private final SysCompanyMapper sysCompanyMapper;
    private final SysCityInfoService sysCityInfoService;

    @Override
    public Result<SysMarketPageResult> pageList(SysMarketPageParams pageParams) {
        PageUtils.startPage(pageParams);
        LambdaQueryWrapper<SysMarketEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(pageParams.getMarketName()), SysMarketEntity::getMarketName, pageParams.getMarketName());
        queryWrapper.eq(ObjectUtil.isNotNull(pageParams.getEnableStatus()), SysMarketEntity::getEnableStatus, pageParams.getEnableStatus());
        queryWrapper.eq(SysMarketEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysMarketEntity> list = sysMarketMapper.selectList(queryWrapper);
        List<SysMarketDto> dtoList = Collections.EMPTY_LIST;
        if (CollectionUtil.isNotEmpty(list)) {
            dtoList = BeanUtil.copyToList(list, SysMarketDto.class);
        }
        return convertPageData(list, dtoList, SysMarketPageResult.class, pageParams);
    }

    @Override
    public Result<Void> addMarket(SysMarketAddParams addParams) {
        SysMarketEntity entity = new SysMarketEntity();
        BeanUtil.copyProperties(addParams, entity);
        checkMarket(entity);
        List<SysCityInfoEntity> sysCityInfoEntityList = sysCityInfoService.selectByIds(addParams.getCityInfoIds());
        Map<Long, String> cityInfoMap = sysCityInfoEntityList.stream().collect(Collectors.toMap(SysCityInfoEntity::getId, SysCityInfoEntity::getName));
        StringBuilder cityInfo = new StringBuilder();
        if (addParams.getCityInfoIds().length > 0) {
            entity.setProvinceId(addParams.getCityInfoIds()[0]);
            cityInfo.append(MapUtil.getStr(cityInfoMap, entity.getProvinceId(), StrUtil.EMPTY));
        }
        if (addParams.getCityInfoIds().length > 1) {
            entity.setCityId(addParams.getCityInfoIds()[1]);
            cityInfo.append(MapUtil.getStr(cityInfoMap, entity.getCityId(), StrUtil.EMPTY));
        }
        if (addParams.getCityInfoIds().length > 2) {
            entity.setDistrictId(addParams.getCityInfoIds()[2]);
            cityInfo.append(MapUtil.getStr(cityInfoMap, entity.getDistrictId(), StrUtil.EMPTY));
        }
        entity.setCityInfo(cityInfo.toString());
        entity.setCreateUser(SatokenUtil.getLoginUserId());
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        int n = sysMarketMapper.insert(entity);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    private void checkMarket(SysMarketEntity entity) {
        LambdaQueryWrapper<SysMarketEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMarketEntity::getMarketName, entity.getMarketName());
        queryWrapper.ne(ObjectUtil.isNotNull(entity.getId()), SysMarketEntity::getId, entity.getId());
        queryWrapper.eq(SysMarketEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        Long count = sysMarketMapper.selectCount(queryWrapper);
        if (null != count && count > 0) {
            throw new BusinessException(SystemErrorEnum.SYSTEM_VERIFY_ERROR.getCode(), "市场名称已存在");
        }
    }

    @Override
    public Result<Void> editMarket(SysMarketEditParams updateParams) {
        SysMarketEntity entity = sysMarketMapper.selectById(updateParams.getId());
        if (ObjectUtil.isNull(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
        }
        BeanUtil.copyProperties(updateParams, entity);
        checkMarket(entity);
        List<SysCityInfoEntity> sysCityInfoEntityList = sysCityInfoService.selectByIds(updateParams.getCityInfoIds());
        Map<Long, String> cityInfoMap = sysCityInfoEntityList.stream().collect(Collectors.toMap(SysCityInfoEntity::getId, SysCityInfoEntity::getName));
        StringBuilder cityInfo = new StringBuilder();
        if (updateParams.getCityInfoIds().length > 0) {
            entity.setProvinceId(updateParams.getCityInfoIds()[0]);
            cityInfo.append(MapUtil.getStr(cityInfoMap, entity.getProvinceId(), StrUtil.EMPTY));
        }
        if (updateParams.getCityInfoIds().length > 1) {
            entity.setCityId(updateParams.getCityInfoIds()[1]);
            cityInfo.append(MapUtil.getStr(cityInfoMap, entity.getCityId(), StrUtil.EMPTY));
        }
        if (updateParams.getCityInfoIds().length > 2) {
            entity.setDistrictId(updateParams.getCityInfoIds()[2]);
            cityInfo.append(MapUtil.getStr(cityInfoMap, entity.getDistrictId(), StrUtil.EMPTY));
        }
        entity.setCityInfo(cityInfo.toString());
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        int n = sysMarketMapper.updateById(entity);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<Void> deleteByIds(DeleteIdsParams idsParams) {
        int n = sysMarketMapper.deleteBatchIds(idsParams.getIds());
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<SysMarketDto> selectById(Long id) {
        SysMarketEntity entity = sysMarketMapper.selectById(id);
        if (ObjectUtil.isNotNull(entity)) {
            SysMarketDto dto = new SysMarketDto();
            BeanUtil.copyProperties(entity, dto);
            dto.setCityInfoIds(new Long[]{entity.getProvinceId(), entity.getCityId(), entity.getDistrictId()});
            return Result.ok(dto);
        }
        return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
    }

    @Override
    public Result<List<MarketSelectDataDto>> marketSelectData(SysMarketQueryParams queryParams) {
        LambdaQueryWrapper<SysMarketEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMarketEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(ObjectUtil.isNotNull(queryParams.getEnableStatus()), SysMarketEntity::getEnableStatus, queryParams.getEnableStatus());
        List<SysMarketEntity> list = sysMarketMapper.selectList(queryWrapper);
        List<Map<String, Object>> queryMaxStatisticGradeList = sysCompanyMapper.queryMaxStatisticGrade();
        if (CollectionUtil.isNotEmpty(list)) {
            List<MarketSelectDataDto> dtoList = list.stream().map(v -> {
                MarketSelectDataDto dto = new MarketSelectDataDto();
                dto.setLabel(v.getMarketName());
                dto.setValue(v.getId());
                dto.setManagementModel(v.getManagementModel());
                if (queryMaxStatisticGradeList.stream().anyMatch(w -> Long.valueOf(String.valueOf(w.get("marketId"))).equals(v.getId()))
                        && ObjectUtil.isNotNull(queryMaxStatisticGradeList.stream().filter(w -> Long.valueOf(String.valueOf(w.get("marketId"))).equals(v.getId())).findFirst().get().get("maxStatisticGrade"))) {
                    dto.setMaxStatisticGrade(Long.valueOf(String.valueOf(queryMaxStatisticGradeList.stream().filter(w -> Long.valueOf(String.valueOf(w.get("marketId"))).equals(v.getId())).findFirst().get().get("maxStatisticGrade"))));
                }
                return dto;
            }).collect(Collectors.toList());
            return Result.ok(dtoList);
        }
        return Result.ok(Collections.EMPTY_LIST);
    }

    @Override
    public void export(SysMarketExportParams params, HttpServletResponse response) {
        LambdaQueryWrapper<SysMarketEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(params.getMarketName()), SysMarketEntity::getMarketName, params.getMarketName());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getEnableStatus()), SysMarketEntity::getEnableStatus, params.getEnableStatus());
        queryWrapper.eq(SysMarketEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysMarketEntity> list = sysMarketMapper.selectList(queryWrapper);
        List<SysMarketExcel> excelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            excelList = list.stream().map(v -> {
                SysMarketExcel e = new SysMarketExcel();
                BeanUtil.copyProperties(v, e);
                e.setManagementModelDesc(ManagementModelEnum.getLabel(v.getManagementModel()));
                e.setEnableStatusDesc(EnableStatusEnum.getLabel(v.getEnableStatus()));
                return e;
            }).collect(Collectors.toList());
        }
        String excelName = "市场_" + DateUtil.current() + ".xlsx";
        try {
            // 设置响应头，指定文件类型及编码
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName));
            // 清除响应
            response.reset();
            // 使用 EasyExcel 写入数据到 response 输出流
            EasyExcel.write(response.getOutputStream(), SysMarketExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("Sheet1")
                    .doWrite(excelList); // dataList 是你要导出的数据列表
        } catch (IOException e) {
            log.error("导出企业Excel异常");
        }
    }
}