package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.PageParams;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.SysUserMapper;
import cc.buyhoo.tax.entity.SysUserEntity;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public abstract class BaseService {

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 响应请求分页数据
     * @param list 实体分页数据
     * @param convertList 转换后的列表
     * @param type 返回Result
     * @return
     */
    public <T,V,C> Result<T> convertPageData(List<V> list, List<C> convertList, Class<T> type, PageParams params){
        //将分页请求的参数原样带回
        T t = ReflectUtil.newInstance(type);
        ReflectUtil.invoke(t,"setPageIndex", params.getPageIndex());
        ReflectUtil.invoke(t,"setPageSize", params.getPageSize());
        ReflectUtil.invoke(t,"setRows", convertList);
        ReflectUtil.invoke(t,"setTotal", new PageInfo(list).getTotal());

        return Result.ok(t);
    }

    public <T,V,C> Result<T> convertPageData(List<V> list, Class<T> type, PageParams params){
        //将分页请求的参数原样带回
        T t = ReflectUtil.newInstance(type);
        ReflectUtil.invoke(t,"setPageIndex", params.getPageIndex());
        ReflectUtil.invoke(t,"setPageSize", params.getPageSize());
        ReflectUtil.invoke(t,"setRows", list);
        ReflectUtil.invoke(t,"setTotal", new PageInfo(list).getTotal());

        return Result.ok(t);
    }

    /**
     * 用户信息处理： key：id；value：username
     * @param userIdSet
     * @return
     */
    public Map<Long,String> handleSysUserIdName(Set<Long> userIdSet) {
        if (ObjectUtil.isEmpty(userIdSet)) return new HashMap<>();
        List<SysUserEntity> userList = sysUserMapper.selectBatchIds(userIdSet);

        return userList.stream().collect(Collectors.toMap(SysUserEntity::getId,SysUserEntity::getUsername));
    }

}
