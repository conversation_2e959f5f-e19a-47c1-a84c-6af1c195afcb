package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusInventoryBatchDetailMapper;
import cc.buyhoo.tax.dao.BusInventoryBatchMapper;
import cc.buyhoo.tax.dao.BusInventoryOrderMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusInventoryBatchDetailEntity;
import cc.buyhoo.tax.entity.BusInventoryBatchEntity;
import cc.buyhoo.tax.entity.BusInventoryOrderEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.enums.InventoryTypeEnum;
import cc.buyhoo.tax.params.inventoryBatch.InventoryBatchAddParams;
import cc.buyhoo.tax.params.inventoryBatch.InventoryBatchParams;
import cc.buyhoo.tax.params.inventoryBatchDetail.InventoryBatchDetailAddParams;
import cc.buyhoo.tax.result.inventoryBatch.InventoryBatchDto;
import cc.buyhoo.tax.result.inventoryBatch.InventoryBatchListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusInventoryBatchService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName BusInventoryBatchServiceImpl
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
@Slf4j
@Service
public class BusInventoryBatchServiceImpl extends BaseService implements BusInventoryBatchService {
    @Resource
    private BusInventoryBatchMapper busInventoryBatchMapper;

    @Resource
    private BusShopMapper busShopMapper;

    @Resource
    private BusInventoryOrderMapper busInventoryOrderMapper;
    @Resource
    private BusInventoryBatchDetailMapper busInventoryBatchDetailMapper;


    @Override
    public Result<InventoryBatchListResult> pageList(InventoryBatchParams params) {
        PageUtils.startPage(params);
        LambdaQueryWrapper<BusInventoryBatchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusInventoryBatchEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(params.getShopUnique())) {
            queryWrapper.eq(BusInventoryBatchEntity::getShopUnique, params.getShopUnique());
        }
        if (ObjectUtil.isNotEmpty(params.getInventoryType())) {
            queryWrapper.eq(BusInventoryBatchEntity::getInventoryType, params.getInventoryType());
        }
        if (StrUtil.isNotBlank(params.getBatchNo())) {
            queryWrapper.like(BusInventoryBatchEntity::getBatchNo, StringUtils.join("%",params.getBatchNo(),"%"));
        }
        if (null != params.getCreateTime() && params.getCreateTime().length == 2) {
            queryWrapper.between(BusInventoryBatchEntity::getCreateTime, params.getCreateTime()[0], params.getCreateTime()[1]);
        }
        queryWrapper.orderByDesc(BusInventoryBatchEntity::getCreateTime);
        queryWrapper.orderByDesc(BusInventoryBatchEntity::getId);
        List<BusInventoryBatchEntity> list = busInventoryBatchMapper.selectList(queryWrapper);
        List<BusShopEntity> shopEntityList = busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId()));
        Map<Long, String> shopMap = shopEntityList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, BusShopEntity::getShopName));
        List<InventoryBatchDto> dtoList = list.stream().map(v -> {
            InventoryBatchDto dto = new InventoryBatchDto();
            BeanUtils.copyProperties(v, dto);
            dto.setShopName(shopMap.get(v.getShopUnique()));
            return dto;
        }).collect(Collectors.toList());
        return convertPageData(list, dtoList, InventoryBatchListResult.class, params);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<Void> saveInventoryBatch(InventoryBatchAddParams addParams) {
        BusInventoryBatchEntity batchEntity;
        BusInventoryOrderEntity orderEntity;
        // 生成一个入库单
        if (addParams.getId() != null) {
            batchEntity = busInventoryBatchMapper.selectById(addParams.getId());
            if (batchEntity == null) {
                return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST, "批次不存在");
            }
            orderEntity = busInventoryOrderMapper.selectOne(new LambdaQueryWrapper<BusInventoryOrderEntity>().eq(BusInventoryOrderEntity::getId, batchEntity.getOrderId()));
            orderEntity.setTotalMoney(addParams.getTotalMoney());
//            orderEntity.setOnlineMoney(addParams.getTotalMoney());
//            orderEntity.setCashMoney(BigDecimal.ZERO);
            orderEntity.setModifyUser(SatokenUtil.getLoginUserId());
            orderEntity.setModifyTime(DateUtil.date());
            busInventoryOrderMapper.updateById(orderEntity);

            batchEntity.setTotalMoney(addParams.getTotalMoney());
//            batchEntity.setOnlineMoney(addParams.getTotalMoney());
            batchEntity.setBatchDate(addParams.getBatchDate());
            batchEntity.setModifyTime(DateUtil.date());
            busInventoryBatchMapper.updateById(batchEntity);

            busInventoryBatchDetailMapper.delete(new LambdaQueryWrapper<BusInventoryBatchDetailEntity>().eq(BusInventoryBatchDetailEntity::getBatchId, batchEntity.getId()));
        } else {
            orderEntity = new BusInventoryOrderEntity();
            orderEntity.setTotalMoney(addParams.getTotalMoney());
//            orderEntity.setOnlineMoney(addParams.getTotalMoney());
//            orderEntity.setCashMoney(BigDecimal.ZERO);
            orderEntity.setOrderNo(StringUtils.join("CG", DateUtil.format(new Date(), "yyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
            orderEntity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
            orderEntity.setInventoryType(InventoryTypeEnum.PEOPLE_ENTER.getInventoryType());
            busInventoryOrderMapper.insert(orderEntity);
            //生成一个批次单
            batchEntity = new BusInventoryBatchEntity();
            batchEntity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
            batchEntity.setBatchNo(StringUtils.join(DateUtil.format(new Date(), "yyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
            batchEntity.setTotalMoney(addParams.getTotalMoney());
//            batchEntity.setOnlineMoney(addParams.getTotalMoney());
//            batchEntity.setCashMoney(BigDecimal.ZERO);
            batchEntity.setOrderId(orderEntity.getId());
            batchEntity.setBatchDate(addParams.getBatchDate());
            batchEntity.setInventoryType(InventoryTypeEnum.PEOPLE_ENTER.getInventoryType());
            busInventoryBatchMapper.insert(batchEntity);
        }
        // 生成入库明细
        if (CollUtil.isNotEmpty(addParams.getDetailList())) {
            List<BusInventoryBatchDetailEntity> batchDetailEntityList = new ArrayList<>();
            for (InventoryBatchDetailAddParams params : addParams.getDetailList()) {
                BusInventoryBatchDetailEntity batchDetailEntity = new BusInventoryBatchDetailEntity();
                batchDetailEntity.setBatchId(batchEntity.getId());
                batchDetailEntity.setOrderId(orderEntity.getId());
                batchDetailEntity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
                batchDetailEntity.setGoodsName(params.getGoodsName());
                batchDetailEntity.setGoodsCount(params.getGoodsCount());
                batchDetailEntity.setTotalMoney(params.getTotalMoney());
                batchDetailEntityList.add(batchDetailEntity);
            }
            if (batchDetailEntityList.size() > 0) {
                busInventoryBatchDetailMapper.insertBatch(batchDetailEntityList);
            }
        }

        return Result.ok();
    }

    @Override
    public Result<List<BusInventoryBatchEntity>> getShopUniqueAndBatchIdByOrderId(Long orderId) {
        List<BusInventoryBatchEntity> batchEntityList = busInventoryBatchMapper.selectList(new LambdaQueryWrapper<BusInventoryBatchEntity>().select(BusInventoryBatchEntity::getShopUnique, BusInventoryBatchEntity::getId).eq(BusInventoryBatchEntity::getOrderId, orderId));
        return Result.ok(batchEntityList);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<Void> deleteByIds(List<Long> ids) {
        List<BusInventoryBatchEntity> list = busInventoryBatchMapper.selectBatchIds(ids);
        for (BusInventoryBatchEntity entity : list) {
            busInventoryOrderMapper.deleteById(entity.getOrderId());
            busInventoryBatchDetailMapper.delete(new LambdaQueryWrapper<BusInventoryBatchDetailEntity>().eq(BusInventoryBatchDetailEntity::getBatchId, entity.getId()));
            busInventoryBatchMapper.deleteById(entity.getId());
        }
        return Result.ok();
    }
}
