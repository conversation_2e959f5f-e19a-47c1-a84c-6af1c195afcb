package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusGoodsCategoryMapper;
import cc.buyhoo.tax.dao.BusGoodsMapper;
import cc.buyhoo.tax.entity.BusGoodsCategoryEntity;
import cc.buyhoo.tax.entity.BusGoodsEntity;
import cc.buyhoo.tax.enums.CategoryLevelEnum;
import cc.buyhoo.tax.params.goodsCategory.BusGoodsCategoryParams;
import cc.buyhoo.tax.params.goodsCategory.EdutGoodsCategoryListParams;
import cc.buyhoo.tax.result.goodsCateogry.BusGoodsCategoryDto;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.BusGoodsCategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
* @Description 商品分类
* @ClassName BusGoodsCategory
* <AUTHOR> 
* @Date 2023-07-26
**/
@Service
@AllArgsConstructor
public class BusGoodsCategoryServiceImpl extends BaseService implements BusGoodsCategoryService {

    @Resource
    private BusGoodsCategoryMapper busGoodsCategoryMapper;
    @Resource
    private BusGoodsMapper busGoodsMapper;

    /**
     * 批量修改商品分类税务信息
     * @param params
     * @return
     */
    public Result<Void> edutGoodsCategoryList(EdutGoodsCategoryListParams params) {
        //校验数据是否正确
        List<Long> ids = params.getIds();
        List<BusGoodsCategoryEntity> list = busGoodsCategoryMapper.selectBatchIds(ids);
        if (list.size() - ids.size() < 0) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"包含不存在的分类ID");
        }

        for (Integer i = 0; i < list.size(); i++) {
            list.get(i).setTaxRate(params.getTaxRate());
            list.get(i).setCategoryNo(params.getCategoryNo());
            list.get(i).setGoodsName(params.getGoodsName());
            list.get(i).setUnit(params.getUnit());
        }

        busGoodsCategoryMapper.updateBatchById(list);

        return Result.ok();
    }
    @Override
    public Result<List<BusGoodsCategoryDto>> list(BusGoodsCategoryParams busGoodsCategoryParams) {
        LambdaQueryWrapper<BusGoodsCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusGoodsCategoryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusGoodsCategoryEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(busGoodsCategoryParams.getEnableStatus())) {
            queryWrapper.eq(BusGoodsCategoryEntity::getEnableStatus, busGoodsCategoryParams.getEnableStatus());
        }
        if (StrUtil.isNotBlank(busGoodsCategoryParams.getCategoryName())) {
            queryWrapper.like(BusGoodsCategoryEntity::getCategoryName, busGoodsCategoryParams.getCategoryName());
        }
        queryWrapper.orderByAsc(BusGoodsCategoryEntity::getId);
        List<BusGoodsCategoryEntity> list = busGoodsCategoryMapper.selectList(queryWrapper);

        Set<Long> userIds = list.stream().filter(v -> ObjectUtil.isNotEmpty(v.getCreateUser())).map(BusGoodsCategoryEntity::getCreateUser).collect(Collectors.toSet());
        Set<Long> modifyIds = list.stream().filter(v -> ObjectUtil.isNotEmpty(v.getModifyUser())).map(BusGoodsCategoryEntity::getModifyUser).collect(Collectors.toSet());
        userIds.addAll(modifyIds);
        Map<Long, String> userIdNameMap = handleSysUserIdName(userIds);
        List<BusGoodsCategoryDto> dtoList = list.stream().map(v -> {
            BusGoodsCategoryDto dto = new BusGoodsCategoryDto();
            BeanUtils.copyProperties(v, dto);
            dto.setCreateUser(userIdNameMap.get(v.getCreateUser()));
            dto.setModifyUser(userIdNameMap.get(v.getModifyUser()));
            return dto;
        }).collect(Collectors.toList());
        List<BusGoodsCategoryDto> categoryDtoList = dtoList.stream().filter(v -> Long.valueOf(0).equals(v.getParentId())).collect(Collectors.toList());
        for (BusGoodsCategoryDto dto : categoryDtoList) {
            dealChildren(dtoList, dto);
        }
        return Result.ok(categoryDtoList);
    }

    private void dealChildren(List<BusGoodsCategoryDto> list, BusGoodsCategoryDto dto) {
        Long parentId = dto.getId();
        List<BusGoodsCategoryDto> children = list.stream().filter(v -> parentId.equals(v.getParentId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(children)) {
            dto.setDisabled(true);
            dto.setChildren(children);
            for (BusGoodsCategoryDto d : children) {
                dealChildren(list, d);
            }
        }
    }

    @Override
    public Result<Void> save(BusGoodsCategoryParams busGoodsCategoryParams) {
        BusGoodsCategoryEntity entity = new BusGoodsCategoryEntity();
        BeanUtil.copyProperties(busGoodsCategoryParams, entity);
        entity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        entity.setModifyTime(DateUtil.date());
        if (ObjectUtil.isEmpty(entity.getId())) {
            entity.setCreateUser(SatokenUtil.getLoginUserId());
            entity.setCreateTime(DateUtil.date());
        }
        List<BusGoodsCategoryEntity> list = busGoodsCategoryMapper.selectList(new LambdaQueryWrapper<BusGoodsCategoryEntity>().eq(BusGoodsCategoryEntity::getCompanyId, entity.getCompanyId()).eq(BusGoodsCategoryEntity::getCategoryName, busGoodsCategoryParams.getCategoryName()));
        if (CollUtil.isNotEmpty(list)) {
            List<BusGoodsCategoryEntity> categoryEntityList = list.stream().filter(v -> !v.getId().equals(entity.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(categoryEntityList)) {
                return Result.fail(SystemErrorEnum.SYSTEM_VERIFY_ERROR, "分类名称：" + busGoodsCategoryParams.getCategoryName() + "，已存在，请勿重复添加");
            }
        }
        boolean flag = busGoodsCategoryMapper.insertOrUpdate(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Void> deleteByIds(List<Long> ids) {
        Long count = busGoodsMapper.selectCount(new LambdaQueryWrapper<BusGoodsEntity>().in(BusGoodsEntity::getCategoryId, ids).or().in(BusGoodsEntity::getCategoryTwoId, ids).eq(BusGoodsEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
        if (count > 0) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "分类使用中,禁止删除操作");
        }
        List<BusGoodsCategoryEntity> categoryEntityList = busGoodsCategoryMapper.selectBatchIds(ids);
        if (CollUtil.isNotEmpty(categoryEntityList)) {
            categoryEntityList.forEach(v -> {
                v.setDelFlag(DelFlagEnum.DELETED.getCode());
                v.setModifyTime(DateUtil.date());
            });
            boolean flag = busGoodsCategoryMapper.updateBatchById(categoryEntityList);
            return flag ? Result.ok() : Result.fail();
        }
        return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
    }

    @Override
    public Result<List<BusGoodsCategoryEntity>> categoryList(String level, BusGoodsCategoryParams busGoodsCategoryParams) {
        LambdaQueryWrapper<BusGoodsCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BusGoodsCategoryEntity::getId, BusGoodsCategoryEntity::getCategoryName, BusGoodsCategoryEntity::getParentId, BusGoodsCategoryEntity::getCategoryType,
                BusGoodsCategoryEntity::getCategoryNo,BusGoodsCategoryEntity::getGoodsName, BusGoodsCategoryEntity::getTaxRate, BusGoodsCategoryEntity::getUnit);
        queryWrapper.eq(BusGoodsCategoryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusGoodsCategoryEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotNull(busGoodsCategoryParams.getId())) {
            queryWrapper.eq(BusGoodsCategoryEntity::getId,busGoodsCategoryParams.getId());
        }
        if (ObjectUtil.isNotEmpty(busGoodsCategoryParams.getGoodsName())) {
            queryWrapper.like(BusGoodsCategoryEntity::getGoodsName, busGoodsCategoryParams.getGoodsName());
        }
        if (ObjectUtil.isNotEmpty(busGoodsCategoryParams.getCategoryNo())) {
            queryWrapper.like(BusGoodsCategoryEntity::getCategoryNo, busGoodsCategoryParams.getCategoryNo());
        }
        if (ObjectUtil.isNotEmpty(busGoodsCategoryParams.getCategoryName())) {
            queryWrapper.like(BusGoodsCategoryEntity::getCategoryName, busGoodsCategoryParams.getCategoryName());
        }
        if (null != busGoodsCategoryParams) {
            if (EnableStatusEnum.UNAVAILABLE.getCode().equals(busGoodsCategoryParams.getEnableStatus()) || EnableStatusEnum.AVAILABLE.getCode().equals(busGoodsCategoryParams.getEnableStatus())) {
                queryWrapper.eq(BusGoodsCategoryEntity::getEnableStatus, busGoodsCategoryParams.getEnableStatus());
            }
            if (null != busGoodsCategoryParams.getCategoryType()) {
                queryWrapper.eq(BusGoodsCategoryEntity::getCategoryType, busGoodsCategoryParams.getCategoryType());
            }
        }
        List<BusGoodsCategoryEntity> list = new ArrayList<>();
        if (StrUtil.equals(CategoryLevelEnum.LEVEL_1.getValue(), level)) {
            queryWrapper.eq(BusGoodsCategoryEntity::getParentId, 0);
            list = busGoodsCategoryMapper.selectList(queryWrapper);
        }
        if (StrUtil.equals(CategoryLevelEnum.LEVEL_2.getValue(), level)) {
            LambdaQueryWrapper<BusGoodsCategoryEntity> parentQueryWrapper = new LambdaQueryWrapper<>();
            parentQueryWrapper.eq(BusGoodsCategoryEntity::getParentId, 0);
            List<BusGoodsCategoryEntity> parentEntityList = busGoodsCategoryMapper.selectList(parentQueryWrapper.select(BusGoodsCategoryEntity::getId).eq(BusGoodsCategoryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
            if (CollUtil.isNotEmpty(parentEntityList)) {
                queryWrapper.in(BusGoodsCategoryEntity::getParentId, parentEntityList.stream().map(BusGoodsCategoryEntity::getId).collect(Collectors.toSet()));
                list = busGoodsCategoryMapper.selectList(queryWrapper);
            }
        }
        return Result.ok(list);
    }

    @Override
    public Result<List<BusGoodsCategoryEntity>> categoryListByParentId(BusGoodsCategoryParams busGoodsCategoryParams) {
        LambdaQueryWrapper<BusGoodsCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BusGoodsCategoryEntity::getId, BusGoodsCategoryEntity::getCategoryName);
        queryWrapper.eq(BusGoodsCategoryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusGoodsCategoryEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (null != busGoodsCategoryParams) {
            if (EnableStatusEnum.UNAVAILABLE.getCode().equals(busGoodsCategoryParams.getEnableStatus()) || EnableStatusEnum.AVAILABLE.getCode().equals(busGoodsCategoryParams.getEnableStatus())) {
                queryWrapper.eq(BusGoodsCategoryEntity::getEnableStatus, busGoodsCategoryParams.getEnableStatus());
            }
            queryWrapper.eq(BusGoodsCategoryEntity::getParentId, busGoodsCategoryParams.getParentId());
            if (null != busGoodsCategoryParams.getCategoryType()) {
                queryWrapper.eq(BusGoodsCategoryEntity::getCategoryType, busGoodsCategoryParams.getCategoryType());
            }
        }
        List<BusGoodsCategoryEntity> list = busGoodsCategoryMapper.selectList(queryWrapper);
        return Result.ok(list);
    }
}