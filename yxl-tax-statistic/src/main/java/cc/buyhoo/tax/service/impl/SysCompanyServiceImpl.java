package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.config.properties.ShopProperties;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.*;
import cc.buyhoo.tax.enums.sys.CompanyTypeEnum;
import cc.buyhoo.tax.facade.BusShopApi;
import cc.buyhoo.tax.facade.params.busShop.ShopPayChangeParams;
import cc.buyhoo.tax.params.busSetting.SaveBusSettingParams;
import cc.buyhoo.tax.params.sysCompany.*;
import cc.buyhoo.tax.result.busSetting.BusSettingDto;
import cc.buyhoo.tax.result.busShop.BusCompanyDto;
import cc.buyhoo.tax.result.busShop.BusShopAmountDto;
import cc.buyhoo.tax.result.busShop.ShopExcel;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.saleList.SaleListDailyAverageTurnoverDto;
import cc.buyhoo.tax.result.sysCompany.*;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.SysCompanyService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SysCompanyServiceImpl extends BaseService implements SysCompanyService {

    private final ShopProperties shopProperties;
    private final SysCompanyMapper sysCompanyMapper;
    private final SysMarketMapper sysMarketMapper;
    private final SysIndustryMapper sysIndustryMapper;
    private final BusSaleListMapper busSaleListMapper;
    private final BusShopMapper busShopMapper;
    private final SysMigrateMapper sysMigrateMapper;
    @Resource
    private BusShopApi busShopApi;
    /**
     * 纳管开户列表
     * @param params
     * @return
     */
    @Override
    public Result<SysCompanyPageResult> pageList(SysCompanyPageParams params) {
        LambdaQueryWrapper<SysCompanyEntity> companyWrapper = new LambdaQueryWrapper<>();
        companyWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        companyWrapper.ne(SysCompanyEntity::getCompanyType, CompanyTypeEnum.PLATFORM.getValue());
        companyWrapper.eq(ObjectUtil.isNotNull(params.getEnableStatus()), SysCompanyEntity::getEnableStatus, params.getEnableStatus());
        companyWrapper.eq(ObjectUtil.isNotNull(params.getIndustryId()), SysCompanyEntity::getIndustryId, params.getIndustryId());
        companyWrapper.eq(ObjectUtil.isNotNull(params.getMarketId()), SysCompanyEntity::getMarketId, params.getMarketId());
        companyWrapper.like(StrUtil.isNotBlank(params.getCompanyName()), SysCompanyEntity::getCompanyName, params.getCompanyName());
        PageUtils.startPage(params);
        List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyWrapper);
        //操作人
        Map<Long, String> userIdNameMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(companyList)) {
            Set<Long> userIdSet = companyList.stream().filter(v -> null != v.getModifyUser()).map(SysCompanyEntity::getModifyUser).collect(Collectors.toSet());
            userIdNameMap = handleSysUserIdName(userIdSet);
        }
        //数据转换
        List<SysCompanyDto> dtoList = new ArrayList<>();
        for (SysCompanyEntity c : companyList) {
            SysCompanyDto dto = new SysCompanyDto();
            BeanUtil.copyProperties(c,dto);
            dto.setTargetAmount(NumberUtil.div(c.getTargetAmount(), BigDecimal.valueOf(10000)));
            dto.setModifyUser(userIdNameMap.get(c.getModifyUser()));
            dtoList.add(dto);
        }
        return convertPageData(companyList, dtoList, SysCompanyPageResult.class,params);
    }

    /**
     * 新增企业
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addSysCompany(SysCompanyAddParams params) {
        checkInvitationCode(params.getInvitationCode());
        if (ObjectUtil.isNotNull(params.getMarketId())) {
            SysMarketEntity sysMarketEntity = sysMarketMapper.selectById(params.getMarketId());
            if (ObjectUtil.isNotEmpty(sysMarketEntity) && ObjectUtil.equals(ManagementModelEnum.MANAGEMENT_MODEL_1.getValue(), sysMarketEntity.getManagementModel())) {
                if (ObjectUtil.isNull(params.getStatisticAmount())) {
                    throw new BusinessException(SysCompanyErrorEnum.STATISTIC_AMOUNT_NULL);
                }
                if (params.getStatisticAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessException(SysCompanyErrorEnum.STATISTIC_AMOUNT_ERROR);
                }
                if (ObjectUtil.isNull(params.getStatisticGrade())) {
                    throw new BusinessException(SysCompanyErrorEnum.STATISTIC_GRADE_NULL);
                }
            }
        }
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //新增企业信息
        SysCompanyEntity company = new SysCompanyEntity();
        BeanUtil.copyProperties(params, company);
        company.setTargetAmount(NumberUtil.mul(params.getTargetAmount(), BigDecimal.valueOf(10000)));
        company.setStatisticAmount(NumberUtil.mul(params.getStatisticAmount(), BigDecimal.valueOf(10000)));
        //当前登录的企业信息
        if (SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            company.setCompanyType(CompanyTypeEnum.ANCESTORS.getValue());
        } else {
            company.setCompanyType(CompanyTypeEnum.BRANCH.getValue());
        }
        if (ObjectUtil.isNotNull(params.getStatisticGrade())) {
            List<SysCompanyEntity> modifyCompanyList = new ArrayList<>();
            List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(new LambdaQueryWrapper<SysCompanyEntity>().eq(SysCompanyEntity::getMarketId, params.getMarketId()));
            if (ObjectUtil.isNotEmpty(companyList)) {
                for (SysCompanyEntity c : companyList) {
                    if (ObjectUtil.isNotNull(c.getStatisticGrade()) && c.getStatisticGrade() >= params.getStatisticGrade()) {
                        c.setStatisticGrade(c.getStatisticGrade() + 1);
                        modifyCompanyList.add(c);
                    }
                }
            }
            sysCompanyMapper.updateBatchById(modifyCompanyList);
        }
        company.setParentCompanyId(loginUser.getCompanyId());
        company.setCreateUser(loginUser.getId());
        company.setModifyUser(loginUser.getId());
        sysCompanyMapper.insert(company);
        return Result.ok();
    }
    /**
     * 修改企业
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateSysCompany(SysCompanySaveParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //旧数据
        SysCompanyEntity oldCompany = sysCompanyMapper.selectById(params.getId());
        if (null == oldCompany) {
            return Result.fail(SysCompanyErrorEnum.ID_NULL_ERROR);
        }
        //修改数据
        SysCompanyEntity entity = new SysCompanyEntity();
        if (ObjectUtil.isNotNull(params.getStatisticGrade())) {
            List<SysCompanyEntity> modifyCompanyList = new ArrayList<>();
            List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(new LambdaQueryWrapper<SysCompanyEntity>().eq(SysCompanyEntity::getMarketId, params.getMarketId()));
            if (ObjectUtil.isNotEmpty(companyList)) {
                for (SysCompanyEntity c : companyList) {
                    if (ObjectUtil.isNotNull(c.getStatisticGrade()) && c.getStatisticGrade() < oldCompany.getStatisticGrade()
                            && c.getStatisticGrade() >= params.getStatisticGrade()) {
                        c.setStatisticGrade(c.getStatisticGrade() + 1);
                        modifyCompanyList.add(c);
                    }
                }
            }
            sysCompanyMapper.updateBatchById(modifyCompanyList);
        }
        BeanUtil.copyProperties(params, entity);
        if (ObjectUtil.isNotNull(params.getTargetAmount()) && (params.getTargetAmount().multiply(BigDecimal.valueOf(10000))).compareTo(oldCompany.getTargetAmount()) > 0
                && ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_2.getValue(), oldCompany.getStatisticStatus())) {
            entity.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_0.getValue());
        }
        if (ObjectUtil.isNotNull(params.getTaxType()) && ObjectUtil.notEqual(params.getTaxType(), oldCompany.getTaxType())
                && ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_2.getValue(), oldCompany.getStatisticStatus())) {
            long allDay = 0L;
            long day = 0L;
            QueryWrapper<BusSaleListEntity> saleListQuery = new QueryWrapper<>();
            // 纳统类型,1-按月,2-按季度,3-按年
            if (ObjectUtil.equals(TaxTypeEnum.YEAR.getValue(), params.getTaxType())) {
                allDay = DateUtil.lengthOfYear(DateUtil.year(DateUtil.date()));
                day = DateUtil.betweenDay(DateUtil.beginOfYear(DateUtil.date()), DateUtil.date(), true);
                saleListQuery.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfYear(new Date()));
            } else if (ObjectUtil.equals(TaxTypeEnum.SEASON.getValue(), params.getTaxType())) {
                allDay = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.endOfQuarter(DateUtil.date()), true);
                day = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.date(), true);
                saleListQuery.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfQuarter(new Date()));
            } else if (ObjectUtil.equals(TaxTypeEnum.MONTH.getValue(), params.getTaxType())) {
                allDay = DateUtil.lengthOfMonth(DateUtil.month(DateUtil.date()), DateUtil.isLeapYear(DateUtil.year(DateUtil.date())));
                day = DateUtil.betweenDay(DateUtil.beginOfMonth(DateUtil.date()), DateUtil.date(), true);
                saleListQuery.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfMonth(new Date()));
            }
            saleListQuery.lambda().eq(BusSaleListEntity::getCompanyId, params.getId());
            saleListQuery.lambda().eq(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_2.getCode());
            saleListQuery.select("shop_unique as shopUnique", "ROUND(IFNULL(sum(sale_list_actually_received),0),2) as saleListActuallyReceivedSum", "COUNT(DISTINCT DATE_FORMAT(sale_list_datetime,'%Y-%m-%d')) as saleListDatetimeCount");
            saleListQuery.lambda().groupBy(BusSaleListEntity::getShopUnique);

            List<BusShopEntity> busShopList = busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, params.getId()).eq(BusShopEntity::getCooperateType,BusShopCooperateTypeEnum.COOPERATE_TYPE_2.getValue()));

            //企业周期内联营订单
            List<Map<String, Object>> saleList = busSaleListMapper.selectMaps(saleListQuery);
            if (ObjectUtil.isNotEmpty(saleList)) {
                // 企业周期内联营订单总金额
                BigDecimal allAmount = saleList.stream().map(m -> (BigDecimal) m.get("saleListActuallyReceivedSum")).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal allAmountMonth = allAmount;
                // 企业内“联营”方式供货商日均营业额倒序（大→小）
                List<SaleListDailyAverageTurnoverDto> saleListDailyAverageTurnoverDtoList = saleList.stream().filter(
                                v -> (ObjectUtil.isNotEmpty(busShopList) ? busShopList.stream().anyMatch(s -> Long.valueOf(String.valueOf(v.get("shopUnique"))).equals(s.getShopUnique())) : true))
                        .map(m -> {
                            SaleListDailyAverageTurnoverDto dailyAverageTurnoverDto = new SaleListDailyAverageTurnoverDto();
                            dailyAverageTurnoverDto.setShopUnique(Long.valueOf(String.valueOf(m.get("shopUnique"))));
                            if (ObjectUtil.isNotNull(m.get("saleListActuallyReceivedSum")) && ObjectUtil.isNotNull(m.get("saleListDatetimeCount"))
                                    && ObjectUtil.notEqual(BigDecimal.ZERO, (ObjectUtil.isNotNull(m.get("saleListDatetimeCount")) ? new BigDecimal(String.valueOf(m.get("saleListDatetimeCount"))) : BigDecimal.ZERO))) {
                                dailyAverageTurnoverDto.setDailyAverageTurnover((ObjectUtil.isNotNull(m.get("saleListActuallyReceivedSum")) ? new BigDecimal(String.valueOf(m.get("saleListActuallyReceivedSum"))) : BigDecimal.ZERO).divide(new BigDecimal(String.valueOf(m.get("saleListDatetimeCount"))), 2, RoundingMode.HALF_UP));
                            } else {
                                dailyAverageTurnoverDto.setDailyAverageTurnover(BigDecimal.ZERO);
                            }
                            return dailyAverageTurnoverDto;
                        }).sorted(Comparator.comparing(SaleListDailyAverageTurnoverDto::getDailyAverageTurnover, Comparator.reverseOrder())).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(saleListDailyAverageTurnoverDtoList)) {
                    for (SaleListDailyAverageTurnoverDto saleListDailyAverageTurnoverDto : saleListDailyAverageTurnoverDtoList) {
                        // 当前企业期末预计完成金额
                        allAmountMonth = allAmountMonth.add(saleListDailyAverageTurnoverDto.getDailyAverageTurnover().multiply(BigDecimal.valueOf(allDay - day)));
                    }
                }
                if (allAmountMonth.compareTo(oldCompany.getTargetAmount()) > 0) {
                    LambdaQueryWrapper<SysCompanyEntity> statingCompanyQuery = new LambdaQueryWrapper<>();
                    statingCompanyQuery.eq(SysCompanyEntity::getStatisticStatus, SysCompanyStatisticStatusEnum.STATUS_1.getValue());
                    statingCompanyQuery.eq(SysCompanyEntity::getMarketId, oldCompany.getMarketId());
                    statingCompanyQuery.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
                    statingCompanyQuery.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
                    List<SysCompanyEntity> statingCompanyList = sysCompanyMapper.selectList(statingCompanyQuery);
                    Long statingComanyId = null;
                    if (ObjectUtil.isNotEmpty(statingCompanyList)) {
                        statingComanyId = statingCompanyList.get(0).getId();
                    }
                    if (ObjectUtil.isNotNull(statingComanyId)) {
                        for (BusShopEntity shopEntity : busShopList) {
                            SysMigrateEntity sysMigrateEntity = new SysMigrateEntity();
                            sysMigrateEntity.setShopUnique(shopEntity.getShopUnique());
                            sysMigrateEntity.setOutCompanyId(oldCompany.getId());
                            shopEntity.setModifyTime(DateUtil.date());
                            shopEntity.setCompanyId(statingComanyId);
                            busShopMapper.updateById(shopEntity);

                            sysMigrateEntity.setInCompanyId(statingComanyId);
                            sysMigrateEntity.setAuditStatus(SysMigrateAuditStatusEnm.AUDIT_SUCCESS.getValue());
                            sysMigrateEntity.setAuditTime(DateUtil.date());
                            sysMigrateMapper.insert(sysMigrateEntity);
                            try {
                                ShopPayChangeParams shopPayChangeParams = new ShopPayChangeParams();
                                shopPayChangeParams.setShopUnique(shopEntity.getShopUnique());
                                shopPayChangeParams.setCompanyId(statingComanyId);
                                busShopApi.shopPayChangeList(shopPayChangeParams);
                            } catch (Exception e) {
                                log.error("店铺支付信息变更失败", e);
                            }
                        }
                    }
                } else if (allAmountMonth.compareTo(oldCompany.getStatisticAmount()) < 0) {
                    entity.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_0.getValue());
                }
            } else {
                entity.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_0.getValue());
            }
        }
        entity.setInvitationCode(oldCompany.getInvitationCode());
        entity.setTargetAmount(NumberUtil.mul(params.getTargetAmount(), BigDecimal.valueOf(10000)));
        entity.setStatisticAmount(NumberUtil.mul(params.getStatisticAmount(), BigDecimal.valueOf(10000)));
        entity.setModifyUser(loginUser.getId());
        sysCompanyMapper.updateById(entity);
        return Result.ok();
    }
    /**
     * 根据公司ID获取详情
     * @param id
     * @return
     */
    @Override
    public Result<SysCompanyDetailResult> getCompanyById(Long id) {
        SysCompanyEntity entity = sysCompanyMapper.selectById(id);
        if (null == entity) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
        }
        SysCompanyDetailResult result = new SysCompanyDetailResult();
        BeanUtil.copyProperties(entity, result);
        result.setTargetAmount(NumberUtil.div(entity.getTargetAmount(), BigDecimal.valueOf(10000)));
        result.setStatisticAmount(NumberUtil.div(entity.getStatisticAmount(), BigDecimal.valueOf(10000)));
        if (ObjectUtil.isNotNull(entity.getMarketId())) {
            SysMarketEntity market = sysMarketMapper.selectById(entity.getMarketId());
            if (ObjectUtil.isNotEmpty(market)) {
                result.setManagementModel(market.getManagementModel());
            }
        }
        return Result.ok(result);
    }

    @Override
    public Result<List<SelectDataDto>> companySelectData(SysCompanyQueryParams queryParams) {
        LambdaQueryWrapper<SysCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SysCompanyEntity::getId, SysCompanyEntity::getCompanyName);
        queryWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(ObjectUtil.isNotNull(queryParams.getEnableStatus()), SysCompanyEntity::getEnableStatus, queryParams.getEnableStatus());
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        queryWrapper.and(wrapper -> wrapper.eq(SysCompanyEntity::getId, companyId).or().eq(SysCompanyEntity::getParentCompanyId, companyId));
        List<SysCompanyEntity> list = sysCompanyMapper.selectList(queryWrapper);
        List<SelectDataDto> resultList = list.stream().map(v -> {
            SelectDataDto dto = new SelectDataDto();
            dto.setValue(v.getId());
            dto.setLabel(v.getCompanyName());
            return dto;
        }).collect(Collectors.toList());
        return Result.ok(resultList);
    }

    /**
     * 纳统系统设置
     * @return
     */
    @Override
    public Result<BusSettingDto> getBusSettings() {
        SysCompanyEntity companyEntity = sysCompanyMapper.selectById(SatokenUtil.getLoginUserCompanyId());
        BusSettingDto dto = new BusSettingDto();
        dto.setInvitationUrl(shopProperties.getRegisterUrl() + "?invitationCode=" + companyEntity.getInvitationCode());
        dto.setTargetAmount(ObjectUtil.isEmpty(companyEntity.getTargetAmount()) ? BigDecimal.ZERO : NumberUtil.div(companyEntity.getTargetAmount(),new BigDecimal("10000")));
        dto.setTaxType(companyEntity.getTaxType());
        return Result.ok(dto);
    }

    /**
     * 保存纳统设置
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> saveBusSettings(SaveBusSettingParams params) {
        SysCompanyEntity company = new SysCompanyEntity();
        company.setId(SatokenUtil.getLoginUserCompanyId());
        company.setTaxType(params.getTaxType());
        company.setTargetAmount(NumberUtil.mul(params.getTargetAmount(),new BigDecimal("10000")));
        sysCompanyMapper.updateById(company);

        return Result.ok();
    }

    /**
     * 查询银行信息
     * @return
     */
    @Override
    public Result<GetShopSettleSettingResult> getShopSettleSetting() {
        SysCompanyEntity company = sysCompanyMapper.selectById(SatokenUtil.getLoginUserCompanyId());
        GetShopSettleSettingResult result = new GetShopSettleSettingResult();
        BeanUtil.copyProperties(company,result);
        return Result.ok(result);
    }

    /**
     * 保存银行信息
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> saveShopSettleSetting(SaveShopSettleSettingParams params) {
        SysCompanyEntity company = new SysCompanyEntity();
        BeanUtil.copyProperties(params,company);
        company.setId(SatokenUtil.getLoginUserCompanyId());
        sysCompanyMapper.updateById(company);
        return Result.ok();
    }

    /**
     * 删除-纳管开户
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteById(SysCompanyIdParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();

        //旧数据
        SysCompanyEntity delCompany = sysCompanyMapper.selectById(params.getId());
        if (null == delCompany) {
            return Result.fail(SysCompanyErrorEnum.ID_NULL_ERROR);
        }
        if (!SatokenUtil.isPlatform(loginUser.getCompanyType()) && ObjectUtil.notEqual(loginUser.getId(), delCompany.getCreateUser())) {
            return Result.fail(SysCompanyErrorEnum.NOT_DELETE);
        }
        //逻辑删除
        SysCompanyEntity upd = new SysCompanyEntity();
        upd.setId(params.getId());
        upd.setDelFlag(DelFlagEnum.DELETED.getCode());
        upd.setModifyUser(loginUser.getId());
        sysCompanyMapper.updateById(upd);

        return Result.ok();
    }

    @Override
    public Result<List<SelectDataDto>> queryCompanyByMarket(SysCompanyQueryByMarketIdParams params) {
        LambdaQueryWrapper<SysCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SysCompanyEntity::getId, SysCompanyEntity::getCompanyName);
        queryWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getMarketId()), SysCompanyEntity::getMarketId, params.getMarketId());
        LoginUser loginUser = SatokenUtil.getLoginUser();
        if (!SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            Long companyId = SatokenUtil.getLoginUserCompanyId();
            queryWrapper.and(wrapper -> wrapper.eq(SysCompanyEntity::getId, companyId).or().eq(SysCompanyEntity::getParentCompanyId, companyId));

        }
        List<SysCompanyEntity> list = sysCompanyMapper.selectList(queryWrapper);
        List<SelectDataDto> resultList = list.stream().map(v -> {
            SelectDataDto dto = new SelectDataDto();
            dto.setValue(v.getId());
            dto.setLabel(v.getCompanyName());
            return dto;
        }).collect(Collectors.toList());
        return Result.ok(resultList);
    }

    @Override
    public void export(SysCompanyExportParams params, HttpServletResponse response) {
        LambdaQueryWrapper<SysCompanyEntity> companyWrapper = new LambdaQueryWrapper<>();
        companyWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        companyWrapper.ne(SysCompanyEntity::getCompanyType, CompanyTypeEnum.PLATFORM.getValue());
        companyWrapper.eq(ObjectUtil.isNotNull(params.getEnableStatus()), SysCompanyEntity::getEnableStatus, params.getEnableStatus());
        companyWrapper.eq(ObjectUtil.isNotNull(params.getIndustryId()), SysCompanyEntity::getIndustryId, params.getIndustryId());
        companyWrapper.eq(ObjectUtil.isNotNull(params.getMarketId()), SysCompanyEntity::getMarketId, params.getMarketId());
        companyWrapper.like(StrUtil.isNotBlank(params.getCompanyName()), SysCompanyEntity::getCompanyName, params.getCompanyName());
        List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyWrapper);

        Set<Long> marketIdSet = companyList.stream().map(SysCompanyEntity::getMarketId).collect(Collectors.toSet());
        List<SysMarketEntity> marketList = sysMarketMapper.selectList(new LambdaQueryWrapper<SysMarketEntity>().in(SysMarketEntity::getId, marketIdSet));
        Set<Long> industryIdSet = companyList.stream().map(SysCompanyEntity::getIndustryId).collect(Collectors.toSet());
        List<SysIndustryEntity> industryList = sysIndustryMapper.selectList(new LambdaQueryWrapper<SysIndustryEntity>().in(SysIndustryEntity::getId, industryIdSet));

        List<CompanyExcel> excelList = new ArrayList<>();
        List<SysMarketEntity> finalMarketList = marketList;
        List<SysIndustryEntity> finalIndustryList = industryList;
        if (CollectionUtil.isNotEmpty(companyList)) {
            excelList = companyList.stream().map(v -> {
                CompanyExcel e = new CompanyExcel();
                BeanUtil.copyProperties(v, e);
                e.setTargetAmount(NumberUtil.div(v.getTargetAmount(), BigDecimal.valueOf(10000)));
                e.setStatisticAmount(NumberUtil.div(v.getStatisticAmount(), BigDecimal.valueOf(10000)));
                e.setTaxType(TaxTypeEnum.getLabel(String.valueOf(v.getTaxType())));
                if (ObjectUtil.isNotEmpty(finalIndustryList)
                        && finalIndustryList.stream().anyMatch(i -> i.getId().equals(v.getIndustryId()))) {
                    e.setIndustryName(finalIndustryList.stream().filter(i -> i.getId().equals(v.getIndustryId())).findFirst().get().getIndustryName());
                }
                if (ObjectUtil.isNotEmpty(finalMarketList)
                        && finalMarketList.stream().anyMatch(i -> i.getId().equals(v.getMarketId()))) {
                    e.setMarketName(finalMarketList.stream().filter(i -> i.getId().equals(v.getMarketId())).findFirst().get().getMarketName());
                }
                return e;
            }).collect(Collectors.toList());
        }
        String excelName = "企业_" + DateUtil.current() + ".xlsx";
        try {
            // 设置响应头，指定文件类型及编码
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName));
            // 清除响应
            response.reset();
            // 使用 EasyExcel 写入数据到 response 输出流
            EasyExcel.write(response.getOutputStream(), CompanyExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("Sheet1")
                    .doWrite(excelList); // dataList 是你要导出的数据列表
        } catch (IOException e) {
            log.error("导出企业Excel异常");
        }
    }

    /**
     * 校验邀请码
     * @param invitationCode
     */
    private void checkInvitationCode(String invitationCode) {
        LambdaQueryWrapper<SysCompanyEntity> compWrapper = new LambdaQueryWrapper<>();
        compWrapper.eq(SysCompanyEntity::getInvitationCode,invitationCode);
        boolean exists = sysCompanyMapper.exists(compWrapper);
        if (exists) {
            throw new BusinessException(SysCompanyErrorEnum.INVITATION_CODE_ERROR);
        }
    }
}
