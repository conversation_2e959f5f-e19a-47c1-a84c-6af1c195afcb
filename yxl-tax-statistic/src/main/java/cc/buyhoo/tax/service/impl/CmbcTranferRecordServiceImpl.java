package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusTaxRecordMapper;
import cc.buyhoo.tax.dao.CmbcTranferRecordMapper;
import cc.buyhoo.tax.entity.BusTaxRecordEntity;
import cc.buyhoo.tax.enums.SeasonEnum;
import cc.buyhoo.tax.enums.TaxStatusEnum;
import cc.buyhoo.tax.enums.TaxTypeEnum;
import cc.buyhoo.tax.params.busTaxRecord.BusTaxRecordParams;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busTaxRecord.BusTaxRecordDto;
import cc.buyhoo.tax.result.busTaxRecord.BusTaxRecordResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusTaxRecordService;
import cc.buyhoo.tax.service.CmbcTranferRecordService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 申报记录
 * @ClassName BusTaxRecordServiceImpl
 * <AUTHOR>
 * @Date 2023/8/11 11:29
 **/
@Slf4j
@Service
public class CmbcTranferRecordServiceImpl extends BaseService implements CmbcTranferRecordService {

}
