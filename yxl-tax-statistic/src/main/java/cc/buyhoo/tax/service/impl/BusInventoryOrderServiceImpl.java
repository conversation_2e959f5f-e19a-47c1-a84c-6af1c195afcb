package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.CompanyProfitRuleTypeEnum;
import cc.buyhoo.tax.enums.InventoryTypeEnum;
import cc.buyhoo.tax.facade.enums.SaleListProfitStatusEnum;
import cc.buyhoo.tax.params.inventoryOrder.InventoryOrderParams;
import cc.buyhoo.tax.result.inventoryOrder.InventoryOrderDto;
import cc.buyhoo.tax.result.inventoryOrder.InventoryOrderListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusInventoryOrderService;
import cc.buyhoo.tax.service.BusSaleListService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName BusInventoryOrderServiceImpl
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class BusInventoryOrderServiceImpl extends BaseService implements BusInventoryOrderService {
    private final BusInventoryOrderMapper busInventoryOrderMapper;
    private final BusSaleListDetailMapper busSaleListDetailMapper;
    private final BusGoodsMapper busGoodsMapper;
    private final BusInventoryBatchMapper busInventoryBatchMapper;
    private final BusInventoryBatchDetailMapper busInventoryBatchDetailMapper;

    private final BusSaleListService busSaleListService;

    @Override
    public Result<InventoryOrderListResult> pageList(InventoryOrderParams params) {
        PageUtils.startPage(params);
        LambdaQueryWrapper<BusInventoryOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusInventoryOrderEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.ne(BusInventoryOrderEntity::getInventoryType, InventoryTypeEnum.FOOD_SALE.getInventoryType());
        if (StrUtil.isNotBlank(params.getOrderNo())) {
            queryWrapper.like(BusInventoryOrderEntity::getOrderNo, params.getOrderNo());
        }
        if (null != params.getCreateTime() && params.getCreateTime().length == 2) {
            queryWrapper.between(BusInventoryOrderEntity::getCreateTime, params.getCreateTime()[0], params.getCreateTime()[1]);
        }
        queryWrapper.orderByDesc(BusInventoryOrderEntity::getId);
        List<BusInventoryOrderEntity> entityList = busInventoryOrderMapper.selectList(queryWrapper);
        List<InventoryOrderDto> list = entityList.stream().map(v -> {
            InventoryOrderDto dto = new InventoryOrderDto();
            BeanUtils.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());
        return convertPageData(entityList, list, InventoryOrderListResult.class, params);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void createInventoryOrder(Map<Long, List<BusSaleListEntity>> saleListMap, Map<Long, BusShopEntity> shopMap, SysCompanyEntity companyEntity) {
        BigDecimal orderTotalMoney = BigDecimal.ZERO;
        Date curentDate = DateUtil.date();
        List<BusInventoryBatchEntity> batchList = new ArrayList<>();
        List<BusInventoryBatchDetailEntity> batchDetailList = new ArrayList<>();
        long temId = 0;
        for (Map.Entry<Long, List<BusSaleListEntity>> m : saleListMap.entrySet()) {
            List<BusSaleListEntity> saleListEntityList = m.getValue();
            Long shopUnique = m.getKey();
            if (!CollectionUtil.contains(shopMap.keySet(), shopUnique)) {
                continue;
            }
            Set<String> saleListUniqueList = saleListEntityList.stream().map(BusSaleListEntity::getSaleListUnique).collect(Collectors.toSet());
            BigDecimal shopTotalMoney = saleListEntityList.stream().map(BusSaleListEntity::getSaleListActuallyReceived).reduce(BigDecimal.ZERO, BigDecimal::add);

            BusInventoryBatchEntity batchEntity = new BusInventoryBatchEntity();
            batchEntity.setCompanyId(companyEntity.getId());
            batchEntity.setShopUnique(shopUnique);
            batchEntity.setBatchNo(StringUtils.join(DateUtil.format(new Date(), "yyyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
            batchEntity.setTotalMoney(shopTotalMoney);
            batchEntity.setCostMoney(shopTotalMoney);
            batchEntity.setProfitMoney(BigDecimal.ZERO);
            batchEntity.setBatchDate(DateUtil.formatDate(DateUtil.offsetDay(curentDate, -1)));
            batchEntity.setInventoryType(InventoryTypeEnum.SHOP_SALE.getInventoryType());

            orderTotalMoney = NumberUtil.add(orderTotalMoney, shopTotalMoney);

            batchEntity.setId(temId);
            batchList.add(batchEntity);

            // 处理订单商品明细

            List<BusSaleListDetailEntity> saleListDetailList = busSaleListDetailMapper.selectList(new LambdaQueryWrapper<BusSaleListDetailEntity>().in(BusSaleListDetailEntity::getSaleListUnique, saleListUniqueList));
            if (CollectionUtil.isNotEmpty(saleListDetailList)) {
                Map<String, List<BusSaleListDetailEntity>> saleListDetailMap = saleListDetailList.stream().collect(Collectors.groupingBy(BusSaleListDetailEntity::getGoodsBarcode));
                List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(new LambdaQueryWrapper<BusGoodsEntity>().eq(BusGoodsEntity::getCompanyId, companyEntity.getId()).eq(BusGoodsEntity::getShopUnique, shopUnique).eq(BusGoodsEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
                Map<String, BusGoodsEntity> goodsMap = goodsList.stream().collect(Collectors.toMap(BusGoodsEntity::getGoodsBarcode, v -> v));
                for (Map.Entry<String, List<BusSaleListDetailEntity>> sdm : saleListDetailMap.entrySet()) {
                    String goodsBarcode = sdm.getKey();
                    List<BusSaleListDetailEntity> saleListDetails = sdm.getValue();
                    if (CollectionUtil.isNotEmpty(saleListDetails)) {
                        BusInventoryBatchDetailEntity batchDetailEntity = new BusInventoryBatchDetailEntity();
                        batchDetailEntity.setBatchId(temId);
                        batchDetailEntity.setCompanyId(companyEntity.getId());
                        batchDetailEntity.setGoodsBarcode(goodsBarcode);
                        BigDecimal goodsCount = saleListDetails.stream().map(BusSaleListDetailEntity::getSaleListDetailCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal totalMoney = saleListDetails.stream().map(
                                g -> ObjectUtil.isEmpty(g.getSaleListDetailSubtotal()) ? NumberUtil.div(NumberUtil.mul(g.getSaleListDetailCount(), g.getSaleListDetailPrice()), BigDecimal.ONE, 2, RoundingMode.FLOOR) : g.getSaleListDetailSubtotal()
                        ).reduce(BigDecimal.ZERO, BigDecimal::add);
                        batchDetailEntity.setGoodsCount(goodsCount);
                        batchDetailEntity.setTotalMoney(totalMoney);
                        BusGoodsEntity busGoodsEntity = goodsMap.get(goodsBarcode);
                        if (ObjectUtil.isNotNull(busGoodsEntity)) {
                            batchDetailEntity.setCategoryId(busGoodsEntity.getCategoryId());
                            batchDetailEntity.setCategoryTwoId(busGoodsEntity.getCategoryTwoId());
                            batchDetailEntity.setGoodsId(busGoodsEntity.getGoodsId());
                            batchDetailEntity.setGoodsName(busGoodsEntity.getGoodsName());
                        } else {
                            batchDetailEntity.setGoodsId(saleListDetails.get(0).getGoodsId());
                            batchDetailEntity.setGoodsName(saleListDetails.get(0).getGoodsName());
                        }
                        batchDetailList.add(batchDetailEntity);
                    }
                }
            }
            temId++;
        }

        BusInventoryOrderEntity orderEntity = new BusInventoryOrderEntity();
        orderEntity.setOrderNo(StringUtils.join("CG", DateUtil.format(curentDate, "yyyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
        orderEntity.setCompanyId(companyEntity.getId());
        orderEntity.setTotalMoney(orderTotalMoney);
        orderEntity.setCostMoney(orderTotalMoney);
        orderEntity.setProfitMoney(BigDecimal.ZERO);
        orderEntity.setInventoryType(InventoryTypeEnum.SHOP_SALE.getInventoryType());
        int n = busInventoryOrderMapper.insert(orderEntity);
        if (n > 0) {
            for (BusInventoryBatchEntity batchEntity : batchList) {
                batchEntity.setOrderId(orderEntity.getId());
                List<BusInventoryBatchDetailEntity> batchDetailEntityList = batchDetailList.stream().filter(v -> ObjectUtil.equals(v.getBatchId(), batchEntity.getId())).collect(Collectors.toList());
                batchEntity.setId(null);
                int ib = busInventoryBatchMapper.insert(batchEntity);
                if (ib > 0) {
                    batchDetailEntityList.stream().forEach(b -> {
                        b.setBatchId(batchEntity.getId());
                        b.setOrderId(orderEntity.getId());
                    });
                    busInventoryBatchDetailMapper.insertBatch(batchDetailEntityList);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void createInventoryOrderProfit(BigDecimal monthSaleListMoney, BigDecimal monthTotalCostMoney, BusCompanyProfitRuleEntity profitRule, Map<Long, List<BusSaleListEntity>> saleListMap, Map<Long, BusShopEntity> shopMap, SysCompanyEntity companyEntity) {
        BigDecimal orderTotalMoney = BigDecimal.ZERO;
        BigDecimal orderCostMoney = BigDecimal.ZERO;
        Date curentDate = DateUtil.date();
        List<BusInventoryBatchEntity> batchList = new ArrayList<>();
        List<BusInventoryBatchDetailEntity> batchDetailList = new ArrayList<>();
        long temId = 0;
        List<BusSaleListEntity> updateSaleList = new ArrayList<>();
        for (Map.Entry<Long, List<BusSaleListEntity>> sl : saleListMap.entrySet()) {
            List<BusSaleListEntity> saleListEntityList = sl.getValue();
            Long shopUnique = sl.getKey();
            if (!CollectionUtil.contains(shopMap.keySet(), shopUnique)) {
                continue;
            }
            Set<String> saleListUniqueList = saleListEntityList.stream().map(BusSaleListEntity::getSaleListUnique).collect(Collectors.toSet());

            BigDecimal shopTotalMoney = BigDecimal.ZERO;
            BigDecimal shopCostMoney = BigDecimal.ZERO;
            List<BusSaleListDetailEntity> saleListDetailList = busSaleListDetailMapper.selectList(new LambdaQueryWrapper<BusSaleListDetailEntity>().in(BusSaleListDetailEntity::getSaleListUnique, saleListUniqueList));

            if (ObjectUtil.equals(CompanyProfitRuleTypeEnum.BY_ORDER.getValue(), profitRule.getRuleType())) {
                // 按订单金额计算
                for (BusSaleListEntity saleListEntity : saleListEntityList) {
                    shopTotalMoney = NumberUtil.add(shopTotalMoney, saleListEntity.getSaleListActuallyReceived());
                    // 按订单比例计算成本
                    BigDecimal profitRate = calProfitRate(monthTotalCostMoney, monthSaleListMoney, saleListEntity.getSaleListActuallyReceived(), profitRule);
                    BigDecimal costMoney = NumberUtil.mul(saleListEntity.getSaleListActuallyReceived(), profitRate);
                    shopCostMoney = NumberUtil.add(shopCostMoney, costMoney);
                    monthTotalCostMoney = NumberUtil.add(monthTotalCostMoney, costMoney);
                    monthSaleListMoney = NumberUtil.add(monthSaleListMoney, saleListEntity.getSaleListActuallyReceived());
                    saleListEntity.setProfitTotal(costMoney);
                    saleListEntity.setProfitStatus(SaleListProfitStatusEnum.DONE.getValue());
                    updateSaleList.add(saleListEntity);
                }
            } else {
                // 按订单明细数量计算
                for (BusSaleListEntity saleListEntity : saleListEntityList) {
                    shopTotalMoney = NumberUtil.add(shopTotalMoney, saleListEntity.getSaleListActuallyReceived());
                    // 按订单商品数量
                    BigDecimal goodsCount = saleListDetailList.stream().filter(s -> ObjectUtil.equals(s.getSaleListUnique(), saleListEntity.getSaleListUnique())).map(BusSaleListDetailEntity::getSaleListDetailCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal profitMoney = NumberUtil.mul(goodsCount, profitRule.getProfitRate()).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal costMoney = NumberUtil.sub(saleListEntity.getSaleListActuallyReceived(), profitMoney);
                    shopCostMoney = NumberUtil.add(shopCostMoney, costMoney);
                    saleListEntity.setProfitTotal(costMoney);
                    saleListEntity.setProfitStatus(SaleListProfitStatusEnum.DONE.getValue());
                    updateSaleList.add(saleListEntity);
                }
            }
            BusInventoryBatchEntity batchEntity = new BusInventoryBatchEntity();
            batchEntity.setCompanyId(companyEntity.getId());
            batchEntity.setShopUnique(shopUnique);
            batchEntity.setBatchNo(StringUtils.join(DateUtil.format(new Date(), "yyyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
            batchEntity.setTotalMoney(shopTotalMoney);
            batchEntity.setCostMoney(shopCostMoney);
            batchEntity.setProfitMoney(NumberUtil.sub(shopTotalMoney, shopCostMoney));
            batchEntity.setBatchDate(DateUtil.formatDate(DateUtil.offsetDay(curentDate, -1)));
            batchEntity.setInventoryType(InventoryTypeEnum.SHOP_SALE.getInventoryType());
            batchEntity.setId(temId);
            batchList.add(batchEntity);

            orderTotalMoney = NumberUtil.add(orderTotalMoney, shopTotalMoney);
            orderCostMoney = NumberUtil.add(orderCostMoney, shopCostMoney);

            // 处理订单商品明细
            if (CollectionUtil.isNotEmpty(saleListDetailList)) {
                Map<String, List<BusSaleListDetailEntity>> saleListDetailMap = saleListDetailList.stream().collect(Collectors.groupingBy(BusSaleListDetailEntity::getGoodsBarcode));
                List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(new LambdaQueryWrapper<BusGoodsEntity>().eq(BusGoodsEntity::getCompanyId, companyEntity.getId()).eq(BusGoodsEntity::getShopUnique, shopUnique).eq(BusGoodsEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
                Map<String, BusGoodsEntity> goodsMap = goodsList.stream().collect(Collectors.toMap(BusGoodsEntity::getGoodsBarcode, v -> v));
                for (Map.Entry<String, List<BusSaleListDetailEntity>> sdm : saleListDetailMap.entrySet()) {
                    String goodsBarcode = sdm.getKey();
                    List<BusSaleListDetailEntity> saleListDetails = sdm.getValue();
                    if (CollectionUtil.isNotEmpty(saleListDetails)) {
                        BusInventoryBatchDetailEntity batchDetailEntity = new BusInventoryBatchDetailEntity();
                        batchDetailEntity.setBatchId(temId);
                        batchDetailEntity.setCompanyId(companyEntity.getId());
                        batchDetailEntity.setGoodsBarcode(goodsBarcode);
                        BigDecimal goodsCount = saleListDetails.stream().map(BusSaleListDetailEntity::getSaleListDetailCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal totalMoney = saleListDetails.stream().map(
                                g -> ObjectUtil.isEmpty(g.getSaleListDetailSubtotal()) ? NumberUtil.div(NumberUtil.mul(g.getSaleListDetailCount(), g.getSaleListDetailPrice()), BigDecimal.ONE, 2, RoundingMode.FLOOR) : g.getSaleListDetailSubtotal()
                        ).reduce(BigDecimal.ZERO, BigDecimal::add);
                        batchDetailEntity.setGoodsCount(goodsCount);
                        batchDetailEntity.setTotalMoney(totalMoney);
                        BusGoodsEntity busGoodsEntity = goodsMap.get(goodsBarcode);
                        if (ObjectUtil.isNotNull(busGoodsEntity)) {
                            batchDetailEntity.setCategoryId(busGoodsEntity.getCategoryId());
                            batchDetailEntity.setCategoryTwoId(busGoodsEntity.getCategoryTwoId());
                            batchDetailEntity.setGoodsId(busGoodsEntity.getGoodsId());
                            batchDetailEntity.setGoodsName(busGoodsEntity.getGoodsName());
                        } else {
                            batchDetailEntity.setGoodsId(saleListDetails.get(0).getGoodsId());
                            batchDetailEntity.setGoodsName(saleListDetails.get(0).getGoodsName());
                        }
                        batchDetailList.add(batchDetailEntity);
                    }
                }
            }
            temId++;
        }

        BusInventoryOrderEntity orderEntity = new BusInventoryOrderEntity();
        orderEntity.setOrderNo(StringUtils.join("CG", DateUtil.format(curentDate, "yyyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
        orderEntity.setCompanyId(companyEntity.getId());
        orderEntity.setTotalMoney(orderTotalMoney);
        orderEntity.setCostMoney(orderCostMoney);
        orderEntity.setProfitMoney(NumberUtil.sub(orderTotalMoney, orderCostMoney));
        orderEntity.setInventoryType(InventoryTypeEnum.SHOP_SALE.getInventoryType());
        int n = busInventoryOrderMapper.insert(orderEntity);
        if (n > 0) {
            for (BusInventoryBatchEntity batchEntity : batchList) {
                batchEntity.setOrderId(orderEntity.getId());
                List<BusInventoryBatchDetailEntity> batchDetailEntityList = batchDetailList.stream().filter(v -> ObjectUtil.equals(v.getBatchId(), batchEntity.getId())).collect(Collectors.toList());
                batchEntity.setId(null);
                int ib = busInventoryBatchMapper.insert(batchEntity);
                if (ib > 0) {
                    batchDetailEntityList.stream().forEach(b -> {
                        b.setBatchId(batchEntity.getId());
                        b.setOrderId(orderEntity.getId());
                    });
                    busInventoryBatchDetailMapper.insertBatch(batchDetailEntityList);
                }
            }

            busSaleListService.updateProfitRecords(updateSaleList);
        }
    }

    public static void main(String[] args) {
        BusInventoryOrderServiceImpl a = new BusInventoryOrderServiceImpl(null, null, null, null, null, null);
        BusCompanyProfitRuleEntity e = new BusCompanyProfitRuleEntity();
        e.setMonthProfitRate(BigDecimal.valueOf(0.3));
        e.setFirstProfitRate(BigDecimal.valueOf(1));
        e.setSecondProfitRate(BigDecimal.valueOf(0.1));
        e.setAmount(BigDecimal.valueOf(1000));
        BigDecimal b = a.calProfitRate(BigDecimal.valueOf(1942.14), BigDecimal.valueOf(1947.98), BigDecimal.valueOf(25.00), e);
        System.out.println(b);
    }

    /**
     * 计算当前订单成本利润率
     * @param monthTotalCostMoney 本月成本总额
     * @param saleListTotalMoney 本月订单总额
     * @param saleListActuallyReceived 当前订单金额
     * @param profitRule 成本计算规则
     * @return
     */
    private BigDecimal calProfitRate(BigDecimal monthTotalCostMoney, BigDecimal saleListTotalMoney, BigDecimal saleListActuallyReceived, BusCompanyProfitRuleEntity profitRule) {
        BigDecimal monthMaxRate = NumberUtil.div(profitRule.getMonthProfitRate(), BigDecimal.valueOf(100));
        BigDecimal profitRate;
        if (saleListActuallyReceived.compareTo(profitRule.getAmount()) <= 0) {
            profitRate = NumberUtil.div(profitRule.getFirstProfitRate(), BigDecimal.valueOf(100));
        } else {
            profitRate = NumberUtil.div(profitRule.getSecondProfitRate(), BigDecimal.valueOf(100));
        }
        BigDecimal totalMoney = NumberUtil.add(saleListActuallyReceived, saleListTotalMoney);
        BigDecimal monthMaxProfit = NumberUtil.mul(totalMoney, monthMaxRate);
        BigDecimal currentSaleListProfit = NumberUtil.add(monthMaxProfit, NumberUtil.sub(saleListTotalMoney, monthTotalCostMoney));
        BigDecimal currentSaleListRate = NumberUtil.div(currentSaleListProfit, saleListActuallyReceived, 5, RoundingMode.HALF_UP);
        if (currentSaleListRate.compareTo(profitRate) == -1) {
            profitRate = currentSaleListRate;
        }
        BigDecimal profitRateValue = NumberUtil.sub(BigDecimal.ONE, profitRate);
        if (BigDecimal.ZERO.compareTo(profitRateValue) == 1) {
            profitRateValue = BigDecimal.ZERO;
        }
        return profitRateValue;
    }
}
