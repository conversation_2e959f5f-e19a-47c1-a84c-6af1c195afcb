package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryAddParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryEditParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryPageParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryQueryParams;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysIndustry.SysIndustryDto;
import cc.buyhoo.tax.result.sysIndustry.SysIndustryPageResult;

import java.util.List;

/**
 * 行业管理表
 * @ClassName SysIndustryService
 * <AUTHOR> 
 * @since 1.0.0 2024-08-23
 */
public interface SysIndustryService {

    Result<SysIndustryPageResult> pageList(SysIndustryPageParams pageParams);

    Result<Void> addIndustry(SysIndustryAddParams addParams);

    Result<Void> editIndustry(SysIndustryEditParams updateParams);

    Result<Void> deleteByIds(DeleteIdsParams idsParams);

    Result<SysIndustryDto> selectById(Long id);

    Result<List<SelectDataDto>> industrySelectData(SysIndustryQueryParams queryParams);
}