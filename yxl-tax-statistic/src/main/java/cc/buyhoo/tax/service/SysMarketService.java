package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysCompany.SysCompanyExportParams;
import cc.buyhoo.tax.params.sysMarket.*;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysMarket.MarketSelectDataDto;
import cc.buyhoo.tax.result.sysMarket.SysMarketDto;
import cc.buyhoo.tax.result.sysMarket.SysMarketPageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 市场管理表
 * @ClassName SysMarketService
 * <AUTHOR> 
 * @since 1.0.0 2024-08-23
 */
public interface SysMarketService {

    Result<SysMarketPageResult> pageList(SysMarketPageParams pageParams);

    Result<Void> addMarket(SysMarketAddParams addParams);

    Result<Void> editMarket(SysMarketEditParams updateParams);

    Result<Void> deleteByIds(DeleteIdsParams idsParams);

    Result<SysMarketDto> selectById(Long id);

    Result<List<MarketSelectDataDto>> marketSelectData(SysMarketQueryParams queryParams);

    void export(SysMarketExportParams params, HttpServletResponse response);
}