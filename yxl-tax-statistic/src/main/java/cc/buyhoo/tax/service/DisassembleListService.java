package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.disassembleList.QueryDisassembleDetailListParams;
import cc.buyhoo.tax.params.disassembleList.QueryDisassembleListParams;
import cc.buyhoo.tax.params.disassembleList.RetryDisassembleParams;
import cc.buyhoo.tax.result.disassembleList.QueryDisassembleDetailListResult;
import cc.buyhoo.tax.result.disassembleList.QueryDisassembleListResult;

public interface DisassembleListService {


    /**
     * 查询拆单任务列表
     * @param params
     * @return
     */
    public Result<QueryDisassembleListResult> queryDisassembleList(QueryDisassembleListParams params);

    /**
     * 查询拆单任务详情列表
     * @param params
     * @return
     */
    public Result<QueryDisassembleDetailListResult> queryDisassembleDetailList(QueryDisassembleDetailListParams params);

    /**
     * 重新拆单
     * @param params
     * @return
     */
    public Result<Void> retryDisassemble(RetryDisassembleParams params);
}
