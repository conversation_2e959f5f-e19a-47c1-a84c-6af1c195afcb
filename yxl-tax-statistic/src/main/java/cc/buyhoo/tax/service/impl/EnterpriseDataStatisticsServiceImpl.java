package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.Cnarea2023Mapper;
import cc.buyhoo.tax.dao.EnterpriseDataStatisticsMapper;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.Cnarea2023Entity;
import cc.buyhoo.tax.params.busShop.ShopListExportParams;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.enterpriseDataStatistics.Statistics;
import cc.buyhoo.tax.result.enterpriseDataStatistics.TreeItemVO;
import cc.buyhoo.tax.result.enterpriseDataStatistics.TreeVo;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.CommonService;
import cc.buyhoo.tax.service.EnterpriseDataStatisticsServer;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName EnterpriseDataStatisticsServiceImpl
 * @Description 企业数据统计功能
 * <AUTHOR>
 * @Date 2025/6/16 上午11:36
 * @Version 1.0
 */
@Service
public class EnterpriseDataStatisticsServiceImpl extends BaseService implements EnterpriseDataStatisticsServer {
    @Autowired
    private EnterpriseDataStatisticsMapper enterpriseDataStatisticsMapper;
    @Autowired
    private Cnarea2023Mapper cnarea2023Mapper;
    @Autowired
    private CommonService commonService;


    @Override
    public Result<TreeVo> dataStatistics(ShopListExportParams params) {
        //先将要返回的写出来,并返回,就完成一半了
        TreeVo treeVo = new TreeVo();
        //获取登录人
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //1.首先判断当前用户是平台用户还是企业用户,
        if (SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            //平台用户登录,默认查询全部,可以选择企业查询条件
            //获取所有门店
            LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<BusShopEntity>()
                    .eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());

            // 判断企业id如果存在的话,就加入这个条件
            if (ObjectUtil.isNotNull(params.getCompanyId())) {
                queryWrapper.eq(BusShopEntity::getCompanyId, params.getCompanyId());
            }
            // 根据市、区、街道条件过滤
            if (StrUtil.isNotBlank(params.getTownCode())) {
                queryWrapper.eq(BusShopEntity::getTownCode, params.getTownCode().substring(0, 9)); // 去除补的000
                //区级匹配,(精确查询)
            } else if (StrUtil.isNotBlank(params.getCountyCode())) {
                queryWrapper.eq(BusShopEntity::getCountyCode, params.getCountyCode().substring(0, 6)); // 去除补的000000

            } else if (StrUtil.isNotBlank(params.getCityCode())) {
                //判断是市级还是区级
                if (params.getCityCode().length()==12 ) {
                    //按区查询
                    queryWrapper.eq(BusShopEntity::getCountyCode, params.getCityCode().substring(0, 6)); // 前缀匹配市编码
                }else {
                    //按市查询
                    queryWrapper.likeRight(BusShopEntity::getCountyCode, params.getCityCode().substring(0, 4));
                }

            } else if (StrUtil.isNotBlank(params.getProvinceCode())){
                queryWrapper.likeRight(BusShopEntity::getCountyCode, params.getProvinceCode().substring(0, 2)); // 前缀匹配省编码
            }
            // 获取所有门店
            List<BusShopEntity> busShopEntities = enterpriseDataStatisticsMapper.selectList(queryWrapper);

            //如果门店不为空的话,就获取每个门店的的唯一标识符
            if (!busShopEntities.isEmpty()) {
                List<Long> shopUnique = busShopEntities.stream()
                        .map(BusShopEntity::getShopUnique)
                        .collect(Collectors.toList());


                /*查询商户数,今日新增商户数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数*/
                List<Statistics> merchant = new ArrayList<>();
                //查询每个门店下的sale_list:销售额,今日销售额,订单数,今日订单数
                List<Statistics> market = new ArrayList<>();
                //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数
                List<Statistics> busShopInvoice = new ArrayList<>();


                //查询每个门店下的bus_shop的 : 商户数,今日新增商户数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数,今日新增个体工商户数
                merchant = enterpriseDataStatisticsMapper.selectMerchant(shopUnique, params.getCreateTime(), params.getModifyTime(), params.getCompanyId());
                //查询每个门店下的sale_list:销售额,今日销售额,订单数,今日订单数
                market = enterpriseDataStatisticsMapper.selectOrderForm(shopUnique, params.getCreateTime(), params.getModifyTime(), params.getCompanyId());
                //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数
                busShopInvoice = enterpriseDataStatisticsMapper.selectmakeOutAnInvoice(shopUnique, params.getCreateTime(), params.getModifyTime(), params.getCompanyId());

//去重
                List<Statistics> statisticsList = filterByTownCode(merchant);
                List<Statistics> saleList = filterByTownCode(market);
                List<Statistics> invoiceList = filterByTownCode(busShopInvoice);

                //将街道编码长度不为12补12
                statisticsList.stream().forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));
                saleList.stream().forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));
                invoiceList.stream().forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));

                //获取街道名称和编码
                List<Cnarea2023Entity> townCodesAndNames = getTownCodesAndNames(busShopEntities);
                //获取区县编码和名称
                List<Cnarea2023Entity> countyCodeAndNames = getCountyCodeAndNames(busShopEntities);
                //获取城市编码和名称
                List<Cnarea2023Entity> cityCodesAndNames = getCityCodesAndNames(busShopEntities);
                //获取省编码和名称
                List<Cnarea2023Entity> provinceCodesAndNames = getProvinceCodesAndNames(busShopEntities);

                List<TreeItemVO> streetNodes = new ArrayList<>();
                for (Cnarea2023Entity town : townCodesAndNames) {
                    TreeItemVO treeItemVO = new TreeItemVO();
                    treeItemVO.setId(String.valueOf(town.getAreaCode()));
                    treeItemVO.setParentId(town.getAreaCode().toString().substring(0, 6) + "000000");
                    treeItemVO.setLabel(town.getName());
                    streetNodes.add(treeItemVO);

                    Statistics statistics = new Statistics();

                    //1、第一种用法： BeanUtils.copyProperties(三个参数) 不为空则拷贝，空则用旧值
                    CopyOptions options = CopyOptions.create()
                            .setIgnoreNullValue(true)  // 忽略源对象属性为空的情况
                            .setIgnoreError(true);//忽略复制过程中的出现的错组

                    //查询的商家数,今日新增商家数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数,今日新增个体工商户数
                    Statistics shop = statisticsList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                    BeanUtil.copyProperties(shop, statistics,options);
                    //查询每个门店下的sale_list:销售额,今日销售额,订单数,今日订单数
                    Statistics orderForm = saleList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                    BeanUtil.copyProperties(orderForm, statistics,options);
                    //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数
                    Statistics invoice = invoiceList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                    BeanUtil.copyProperties(invoice, statistics,options);

                    treeItemVO.setStatistics(statistics);
                }



                //构建区县节点
                HashMap<Long, TreeItemVO> countyNodeMap = new HashMap<>();
                for (Cnarea2023Entity county : countyCodeAndNames) {
                    TreeItemVO treeItemVO = new TreeItemVO();
                    treeItemVO.setId(county.getAreaCode().toString());


                    treeItemVO.setParentId(county.getAreaCode().toString().substring(0, 4) + "00000000");
                    treeItemVO.setLabel(county.getName());
                    // 过滤出属于当前区县的街道
                    String countyCode = county.getAreaCode().toString().substring(0, 6) + "000000";

                    List<TreeItemVO> countyStreets = streetNodes.stream()
                            .filter(street -> street.getParentId().startsWith(county.getAreaCode().toString().substring(0, 6)))
                            .collect(Collectors.toList());


                    treeItemVO.setChildren(countyStreets);

                    countyNodeMap.put(county.getAreaCode(), treeItemVO);
                }

                //遍历区县节点,汇总区下所有街道的统计数据
                for (TreeItemVO countyNode : countyNodeMap.values()) {
                    List<TreeItemVO> children = countyNode.getChildren();
                    //如果没有子节点(街道),则直接返回
                    if (children == null || children.isEmpty()) {
                        continue;
                    }
                    //收集该区县下所有街道的统计数据
                    List<Statistics> statisticsLists = children.stream()
                            .map(TreeItemVO::getStatistics)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    Statistics countyStats = mergeStatistics(statisticsLists);
                    countyNode.setStatistics(countyStats);
                }

                // 构建市级节点
                HashMap<String, TreeItemVO> cityNodeMap = new HashMap<>();
                for (Cnarea2023Entity city : cityCodesAndNames) {
                    TreeItemVO cityNode = new TreeItemVO();
                    cityNode.setId(String.valueOf(city.getAreaCode()));
                    cityNode.setParentId(city.getAreaCode().toString().substring(0, 2) + "0000000000");
                    cityNode.setLabel(city.getName());

                    List<TreeItemVO> countyStreets = countyNodeMap.values().stream()
                            .filter(county -> county.getId().startsWith(city.getAreaCode().toString().substring(0, 4)))
                            .collect(Collectors.toList());

                    cityNode.setChildren(countyStreets);
                    cityNodeMap.put(String.valueOf(city.getAreaCode()), cityNode);
                    //遍历区县节点,汇总市下所有区县的统计数据
                    for (TreeItemVO count : cityNodeMap.values()) {
                        List<TreeItemVO> children = count.getChildren();
                        //判断如果没有子节点,则返回
                        if (children == null || children.isEmpty()) {
                            continue;
                        }
                        //收集该市下所有区县的统计数据
                        List<Statistics> statistics = children.stream()
                                .map(TreeItemVO::getStatistics)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        Statistics cityStats = mergeStatistics(statistics);
                        count.setStatistics(cityStats);
                    }
                }
                //构建省级节点
                HashMap<String, TreeItemVO> provinceNodeMap = new HashMap<>();
                for (Cnarea2023Entity province : provinceCodesAndNames) {
                    TreeItemVO provinceNode = new TreeItemVO();
                    provinceNode.setId(String.valueOf(province.getAreaCode()));
                    provinceNode.setParentId("0"); // 省级作为顶层节点
                    provinceNode.setLabel(province.getName());

                    List<TreeItemVO> countyStreets = cityNodeMap.values().stream()
                            .filter(county -> county.getId().startsWith(province.getAreaCode().toString().substring(0, 2)))
                            .collect(Collectors.toList());
                    provinceNode.setChildren(countyStreets);

                    provinceNodeMap.put(String.valueOf(province.getAreaCode()), provinceNode);
                }
                //遍历区县节点,汇总市下所有区县的统计数据
                for (TreeItemVO count : provinceNodeMap.values()) {
                    List<TreeItemVO> province = count.getChildren();
                    //判断如果没有子节点,则返回
                    if (province == null || province.isEmpty()) {
                        continue;
                    }
                    //收集该市下所有区县的统计数据
                    List<Statistics> statistics = province.stream()
                            .map(TreeItemVO::getStatistics)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    Statistics cityStats = mergeStatistics(statistics);
                    count.setStatistics(cityStats);
                }
                treeVo.setItems(new ArrayList<>(cityNodeMap.values()));
            }
            return Result.ok(treeVo);
        }
        //判断当前用户是否是企业用户
        if (!SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            //企业用户登录后,默认查询本企业的数据,不能选择其他企业

            //通过企业id来查询企业下的所有门店
            List<BusShopEntity> busShopEntities = enterpriseDataStatisticsMapper.selectList(new LambdaQueryWrapper<BusShopEntity>()
                    .eq(BusShopEntity::getCompanyId, loginUser.getCompanyId())
                    .eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
            //获取企业id
            Long companyId = loginUser.getCompanyId();
            //判断如果门店不为空的话,就获取每个门店的唯一标识符
            if (!busShopEntities.isEmpty()) {
                List<Long> shopUniques = busShopEntities.stream()
                        .map(BusShopEntity::getShopUnique)
                        .collect(Collectors.toList());

                //根据企业id来获取企业所有的区县和街道编码
                List<String> areaCodes = enterpriseDataStatisticsMapper.DistrictAndCountyStreet(companyId);
                /*查询商户数,今日新增商户数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数*/
                List<Statistics> merchant = new ArrayList<>();
                //查询每个门店下的sale_list:销售额,今日销售额,订单数,今日订单数
                List<Statistics> market = new ArrayList<>();
                //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数
                List<Statistics> busShopInvoice = new ArrayList<>();


                //查询每个门店下的bus_shop的 : 商户数,今日新增商户数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数,今日新增个体工商户数
                merchant = enterpriseDataStatisticsMapper.selectMerchant(shopUniques, params.getCreateTime(), params.getModifyTime(), companyId);
                //查询每个门店下的sale_list:销售额,今日销售额,订单数,今日订单数
                market = enterpriseDataStatisticsMapper.selectOrderForm(shopUniques, params.getCreateTime(), params.getModifyTime(), companyId);
                //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数
                busShopInvoice = enterpriseDataStatisticsMapper.selectmakeOutAnInvoice(shopUniques, params.getCreateTime(), params.getModifyTime(), companyId);
                //去重
                List<Statistics> statisticsList = filterByTownCode(merchant);
                List<Statistics> saleList = filterByTownCode(market);
                List<Statistics> invoiceList = filterByTownCode(busShopInvoice);

                //将街道编码长度不为12补12
                statisticsList.stream().forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));
                saleList.stream().forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));
                invoiceList.stream().forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));

                //获取街道名称和编码
                List<Cnarea2023Entity> townCodesAndNames = getTownCodesAndNames(busShopEntities);
                //获取区县编码和名称
                List<Cnarea2023Entity> countyCodeAndNames = getCountyCodeAndNames(busShopEntities);
                //获取城市编码和名称
                List<Cnarea2023Entity> cityCodesAndNames = getCityCodesAndNames(busShopEntities);
                //获取省编码和名称
                List<Cnarea2023Entity> provinceCodesAndNames = getProvinceCodesAndNames(busShopEntities);


                List<TreeItemVO> streetNodes = new ArrayList<>();
                for (Cnarea2023Entity town : townCodesAndNames) {
                    TreeItemVO treeItemVO = new TreeItemVO();
                    treeItemVO.setId(String.valueOf(town.getAreaCode()));
                    treeItemVO.setParentId(town.getAreaCode().toString().substring(0, 6) + "000000");
                    treeItemVO.setLabel(town.getName());
                    streetNodes.add(treeItemVO);

                    Statistics statistics = new Statistics();

                    //1、第一种用法： BeanUtils.copyProperties(三个参数) 不为空则拷贝，空则用旧值
                    CopyOptions options = CopyOptions.create()
                            .setIgnoreNullValue(true)  // 忽略源对象属性为空的情况
                            .setIgnoreError(true);//忽略复制过程中的出现的错组

                    //查询的商家数,今日新增商家数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数,今日新增个体工商户数
                    Statistics shop = statisticsList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                    BeanUtil.copyProperties(shop, statistics,options);

                    //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数
                    Statistics invoice = invoiceList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                    BeanUtil.copyProperties(invoice, statistics,options);

                    //查询每个门店下的sale_list:销售额,今日销售额,订单数,今日订单数
                    Statistics orderForm = saleList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                    if (orderForm != null){
                        BeanUtil.copyProperties(orderForm, statistics,options);
                    }
                    treeItemVO.setStatistics(statistics);

                }

                //构建区县节点
                HashMap<Long, TreeItemVO> countyNodeMap = new HashMap<>();

                for (Cnarea2023Entity county : countyCodeAndNames) {
                    TreeItemVO treeItemVO = new TreeItemVO();
                    treeItemVO.setId(county.getAreaCode().toString());
                    treeItemVO.setParentId(county.getAreaCode().toString().substring(0, 4) + "00000000");
                    treeItemVO.setLabel(county.getName());


                    // 过滤出属于当前区县的街道
                    List<TreeItemVO> countyStreets = streetNodes.stream()
                            .filter(street -> street.getParentId().startsWith(county.getAreaCode().toString().substring(0,6)))
                            .collect(Collectors.toList());


                    treeItemVO.setChildren(countyStreets);

                    countyNodeMap.put(county.getAreaCode(), treeItemVO);
                }

                //遍历区县节点,汇总区下所有街道的统计数据
                for (TreeItemVO countyNode : countyNodeMap.values()) {
                    List<TreeItemVO> children = countyNode.getChildren();
                    //如果没有子节点(街道),则直接返回
                    if (children == null || children.isEmpty()) {
                        continue;
                    }
                    //收集该区县下所有街道的统计数据
                    List<Statistics> statisticsLists = children.stream()
                            .map(TreeItemVO::getStatistics)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    Statistics countyStats = mergeStatistics(statisticsLists);
                    countyNode.setStatistics(countyStats);
                }


                // 构建市级节点
                HashMap<String, TreeItemVO> cityNodeMap = new HashMap<>();
                for (Cnarea2023Entity city : cityCodesAndNames) {
                        TreeItemVO cityNode = new TreeItemVO();
                        cityNode.setId(String.valueOf(city.getAreaCode()));
                        cityNode.setParentId(city.getAreaCode().toString().substring(0, 2) + "0000000000");
                        cityNode.setLabel(city.getName());

                        List<TreeItemVO> countyStreets = countyNodeMap.values().stream()
                                .filter(county -> county.getId().startsWith(city.getAreaCode().toString().substring(0, 4)))
                                .collect(Collectors.toList());

                        cityNode.setChildren(countyStreets);
                        cityNodeMap.put(String.valueOf(city.getAreaCode()), cityNode);

                    //遍历区县节点,汇总市下所有区县的统计数据
                    for (TreeItemVO count : cityNodeMap.values()) {
                        List<TreeItemVO> children = count.getChildren();
                        //判断如果没有子节点,则返回
                        if (children == null || children.isEmpty()) {
                            continue;
                        }
                        //收集该市下所有区县的统计数据
                        List<Statistics> statistics = children.stream()
                                .map(TreeItemVO::getStatistics)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        Statistics cityStats = mergeStatistics(statistics);
                        count.setStatistics(cityStats);
                    }
                }
                //构建省级节点
                HashMap<String, TreeItemVO> provinceNodeMap = new HashMap<>();
                for (Cnarea2023Entity province : provinceCodesAndNames) {
                    TreeItemVO provinceNode = new TreeItemVO();
                    provinceNode.setId(String.valueOf(province.getAreaCode()));
                    provinceNode.setParentId("0"); // 省级作为顶层节点
                    provinceNode.setLabel(province.getName());

                    List<TreeItemVO> countyStreets = cityNodeMap.values().stream()
                            .filter(county -> county.getId().startsWith(province.getAreaCode().toString().substring(0, 2)))
                            .collect(Collectors.toList());
                    provinceNode.setChildren(countyStreets);

                    provinceNodeMap.put(String.valueOf(province.getAreaCode()), provinceNode);
                }
                //遍历区县节点,汇总市下所有区县的统计数据
                for (TreeItemVO count : provinceNodeMap.values()) {
                    List<TreeItemVO> province = count.getChildren();
                    //判断如果没有子节点,则返回
                    if (province == null || province.isEmpty()) {
                        continue;
                    }
                    //收集该市下所有区县的统计数据
                    List<Statistics> statistics = province.stream()
                            .map(TreeItemVO::getStatistics)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    Statistics cityStats = mergeStatistics(statistics);
                    count.setStatistics(cityStats);
                }

                treeVo.setItems(new ArrayList<>(provinceNodeMap.values()));
            }
            return Result.ok(treeVo);

        }
        return Result.fail();
    }




    /**
     * 根据前端传递的条件查询
     * @param params
     * @return
     */
    @Override
    public Result<TreeVo> getStatisticsByArea(ShopListExportParams params) {
        TreeVo treeVo = new TreeVo();
        LoginUser loginUser = SatokenUtil.getLoginUser();

        // 获取所有门店
        LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<BusShopEntity>()
                .eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());

        // 判断企业id如果存在的话,就加入这个条件
        if (ObjectUtil.isNotNull(params.getCompanyId())) {
            queryWrapper.eq(BusShopEntity::getCompanyId, params.getCompanyId());
        }

        // 根据市、区、街道条件过滤
        if (StrUtil.isNotBlank(params.getTownCode())) {
            queryWrapper.eq(BusShopEntity::getTownCode, params.getTownCode().substring(0, 9)); // 去除补的000
        } else if (StrUtil.isNotBlank(params.getCountyCode())) {
            queryWrapper.eq(BusShopEntity::getCountyCode, params.getCountyCode().substring(0, 6)); // 去除补的000000
        } else if (StrUtil.isNotBlank(params.getCityCode())) {
            queryWrapper.likeRight(BusShopEntity::getCountyCode, params.getCityCode().substring(0, 4)); // 前缀匹配市编码
        } else if (StrUtil.isNotBlank(params.getProvinceCode())){
            queryWrapper.likeRight(BusShopEntity::getCountyCode, params.getProvinceCode().substring(0, 2));
        }

        List<BusShopEntity> busShopEntities = enterpriseDataStatisticsMapper.selectList(queryWrapper);

        if (!busShopEntities.isEmpty()) {
            List<Long> shopUnique = busShopEntities.stream()
                    .map(BusShopEntity::getShopUnique)
                    .collect(Collectors.toList());

            // 查询统计信息（同原有逻辑）
            List<Statistics> merchant = enterpriseDataStatisticsMapper.selectMerchant(shopUnique, params.getCreateTime(), params.getModifyTime(), params.getCompanyId());
            List<Statistics> market = enterpriseDataStatisticsMapper.selectOrderForm(shopUnique, params.getCreateTime(), params.getModifyTime(), params.getCompanyId());
            List<Statistics> busShopInvoice = enterpriseDataStatisticsMapper.selectmakeOutAnInvoice(shopUnique, params.getCreateTime(), params.getModifyTime(), params.getCompanyId());

            // 去重
            List<Statistics> statisticsList = filterByTownCode(merchant);
            List<Statistics> saleList = filterByTownCode(market);
            List<Statistics> invoiceList = filterByTownCode(busShopInvoice);

            // 将街道编码长度不为12补12
            statisticsList.forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));
            saleList.forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));
            invoiceList.forEach(stat -> stat.setTownCode(ensureTownCodeLength(stat.getTownCode())));

            //获取街道名称和编码
            List<Cnarea2023Entity> townCodesAndNames = getTownCodesAndNames(busShopEntities);
            //获取区县编码和名称
            List<Cnarea2023Entity> countyCodeAndNames = getCountyCodeAndNames(busShopEntities);
            //获取城市编码和名称
            List<Cnarea2023Entity> cityCodesAndNames = getCityCodesAndNames(busShopEntities);
            //获取省编码和名称
            List<Cnarea2023Entity> provinceCodesAndNames = getProvinceCodesAndNames(busShopEntities);

            // 构建街道节点数据
            List<TreeItemVO> streetNodes = new ArrayList<>();
            for (Cnarea2023Entity town : townCodesAndNames) {
                TreeItemVO treeItemVO = new TreeItemVO();
                treeItemVO.setId(String.valueOf(town.getAreaCode()));
                treeItemVO.setParentId(town.getAreaCode().toString().substring(0, 6) + "000000");
                treeItemVO.setLabel(town.getName());
                streetNodes.add(treeItemVO);
                //1、第一种用法： BeanUtils.copyProperties(三个参数) 不为空则拷贝，空则用旧值
                CopyOptions options = CopyOptions.create()
                        .setIgnoreNullValue(true)  // 忽略源对象属性为空的情况
                        .setIgnoreError(true);//忽略复制过程中的出现的错组

                //将三个查询结果放到一个对象中
                Statistics statistics = new Statistics();
                Statistics shop = statisticsList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                BeanUtil.copyProperties(shop, statistics,options);
                Statistics orderForm = saleList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                BeanUtil.copyProperties(orderForm, statistics,options);
                Statistics invoice = invoiceList.stream().filter(v -> v.getTownCode().equals(town.getAreaCode().toString())).findFirst().orElse(null);
                BeanUtil.copyProperties(invoice, statistics,options);

                treeItemVO.setStatistics(statistics);
            }

            // 根据条件构建返回结果
            if (StrUtil.isNotBlank(params.getTownCode())) {
                // 只查询指定街道的数据
                TreeItemVO streetNode = streetNodes.stream()
                        .filter(street -> street.getId().equals(params.getTownCode()))
                        .findFirst()
                        .orElse(null);
                if (streetNode != null) {
                    treeVo.setItems(Collections.singletonList(streetNode));
                }
            } else if (StrUtil.isNotBlank(params.getCountyCode())) {
                // 查询指定区下面的街道，并统计区下面所有街道的数据
                String countyCode = params.getCountyCode().substring(0, 6);

                List<TreeItemVO> countyStreets = streetNodes.stream()
                        .filter(street -> street.getParentId().startsWith(countyCode))
                        .collect(Collectors.toList());
                //构建区节点
                TreeItemVO countyNode = new TreeItemVO();
                countyNode.setId(countyCode);
                countyNode.setParentId(countyCode.substring(0, 4) + "00000000");
                // 构建区名称
                countyNode.setLabel(countyCodeAndNames.stream()
                        .filter(county -> county.getAreaCode().toString().startsWith(countyCode))
                        .findFirst()
                        .map(Cnarea2023Entity::getName)
                        .orElse("未知区县"));
                // 汇总区下所有街道的统计数据
                Statistics countyStats = mergeStatistics(countyStreets.stream()
                        .map(TreeItemVO::getStatistics)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));

                countyNode.setChildren(countyStreets);
                countyNode.setStatistics(countyStats);

                treeVo.setItems(Collections.singletonList(countyNode));
                //构建市节点
            } else if (StrUtil.isNotBlank(params.getCityCode())) {
                //获取市编码
                String cityCode = params.getCityCode().substring(0, 4) + "00000000" ;
                // 从区级数据源过滤
                List<TreeItemVO> cityCounties = countyCodeAndNames.stream()
                        .filter(county -> county.getAreaCode().toString().startsWith(cityCode.substring(0, 4)))
                        .map(county -> {
                            //获取区级编码
                            String countyCode = county.getAreaCode().toString().substring(0, 6)+"000000";

                            //获取该区县下的所有街道
                            List<TreeItemVO> countyStreets = streetNodes.stream()
                                    .filter(street -> street.getParentId().equals(countyCode))
                                    .collect(Collectors.toList());

                            //构建区节点
                            TreeItemVO countyNode = new TreeItemVO();
                            countyNode.setId(countyCode);
                            countyNode.setParentId(cityCode);
                            countyNode.setLabel(county.getName());

                            // 汇总该区县下所有街道的统计数据
                            if (!countyStreets.isEmpty()) {
                                Statistics countyStats = mergeStatistics(countyStreets.stream()
                                        .map(TreeItemVO::getStatistics)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList())
                                );
                                countyNode.setStatistics(countyStats);
                            }
                            countyNode.setChildren(countyStreets);
                            return countyNode;
                        })
                        .collect(Collectors.toList());


                // 构建市节点
                TreeItemVO cityNode = new TreeItemVO();
                cityNode.setId(cityCode);
                cityNode.setParentId(cityCode.substring(0,2) + "0000000000"); // 市级作为顶层节点
                cityNode.setLabel(cityCodesAndNames.stream()
                        .filter(province -> province.getAreaCode().toString().equals(cityCode))
                        .findFirst()
                        .map(Cnarea2023Entity::getName)
                        .orElse("未知区县")); // 这里可以根据实际需求获取市名
                // 汇总市下所有区县的统计数据
                Statistics cityStats = mergeStatistics(cityCounties.stream()
                        .map(TreeItemVO::getStatistics)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));

                cityNode.setChildren(cityCounties);
                cityNode.setStatistics(cityStats);

                treeVo.setItems(Collections.singletonList(cityNode));


            }else if (StrUtil.isNotBlank(params.getProvinceCode())){
                // 查询指定省下面所有市，并统计所有市的数据和市下面所有的区县
                String provinceCodePrefix = params.getProvinceCode().substring(0, 2);

                // 获取该省下的所有市级节点
                List<TreeItemVO> provinceCities = cityCodesAndNames.stream()
                        .filter(city -> city.getAreaCode().toString().startsWith(provinceCodePrefix))
                        .map(city -> {
                            TreeItemVO cityNode = new TreeItemVO();
                            cityNode.setId(city.getAreaCode().toString());
                            cityNode.setParentId(city.getAreaCode().toString().substring(0, 2) + "0000000000");
                            cityNode.setLabel(city.getName());

                            // 获取该市下的所有区县
                            List<TreeItemVO> cityCounties = countyCodeAndNames.stream()
                                    .filter(county -> county.getAreaCode().toString().startsWith(city.getAreaCode().toString().substring(0, 4)))
                                    .map(county -> {
                                        TreeItemVO countyNode = new TreeItemVO();
                                        countyNode.setId(county.getAreaCode().toString());
                                        countyNode.setParentId(county.getAreaCode().toString().substring(0, 4) + "00000000");
                                        countyNode.setLabel(county.getName());

                                        // 获取该区县下的街道
                                        String countyCode = county.getAreaCode().toString().substring(0, 6) + "000000";
                                        List<TreeItemVO> countyStreets = streetNodes.stream()
                                                .filter(street -> street.getParentId().equals(countyCode))
                                                .collect(Collectors.toList());

                                        // 汇总统计数据
                                        Statistics countyStats = mergeStatistics(countyStreets.stream()
                                                .map(TreeItemVO::getStatistics)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));

                                        countyNode.setChildren(countyStreets);
                                        countyNode.setStatistics(countyStats);
                                        return countyNode;
                                    })
                                    .collect(Collectors.toList());

                            // 汇总市统计数据
                            Statistics cityStats = mergeStatistics(cityCounties.stream()
                                    .map(TreeItemVO::getStatistics)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList()));

                            cityNode.setChildren(cityCounties);
                            cityNode.setStatistics(cityStats);
                            return cityNode;
                        })
                        .collect(Collectors.toList());

                // 汇总省级统计数据
                Statistics provinceStats = mergeStatistics(provinceCities.stream()
                        .map(TreeItemVO::getStatistics)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));

                // 构建省级节点
                TreeItemVO provinceNode = new TreeItemVO();
                provinceNode.setId(provinceCodePrefix + "0000000000");
                provinceNode.setParentId("0");
                provinceNode.setLabel(provinceCodesAndNames.stream()
                        .filter(p -> p.getAreaCode().toString().equals(provinceCodePrefix + "0000000000"))
                        .findFirst()
                        .map(Cnarea2023Entity::getName)
                        .orElse("未知省份"));
                provinceNode.setChildren(provinceCities);
                provinceNode.setStatistics(provinceStats);

                treeVo.setItems(Collections.singletonList(provinceNode));
            }
            return Result.ok(treeVo);
        }
        return Result.fail();
    }







    /*汇总,区县统计街道下面的数据*/
    public static Statistics mergeStatistics(List<Statistics> list) {
        Statistics result = new Statistics();

        result.setSalesAmount(BigDecimal.ZERO);
        result.setTodaySalesAmount(BigDecimal.ZERO);
        result.setOrderCount(0);
        result.setTodayOrderCount(0);
        result.setTodayInvoiceAmount(BigDecimal.ZERO);
        result.setInvoiceAmount(BigDecimal.ZERO);
        result.setInvoiceCount(0);
        result.setTodayInvoiceCount(0);
        result.setMerchantCount(0);
        result.setTodayNewMerchantCount(0);
        result.setSmallScaleTaxpayerCount(0);
        result.setTodayNewSmallScaleTaxpayerCount(0);
        result.setGeneralTaxpayerCount(0);
        result.setTodayNewGeneralTaxpayerCount(0);
        result.setIndividualBusinessCount(0);
        result.setTodayNewIndividualBusinessCount(0);

        if (list == null || list.isEmpty()) return result;

        for (Statistics stats : list) {
            if (stats == null) continue;

            // BigDecimal类型的安全加法
            if (stats.getSalesAmount() != null) {
                result.setSalesAmount(result.getSalesAmount().add(stats.getSalesAmount()));
            }
            if (stats.getTodaySalesAmount() != null) {
                result.setTodaySalesAmount(result.getTodaySalesAmount().add(stats.getTodaySalesAmount()));
            }
            if (stats.getInvoiceAmount() != null) {
                result.setInvoiceAmount(result.getInvoiceAmount().add(stats.getInvoiceAmount()));
            }
            if (stats.getTodayInvoiceAmount() != null) {
                result.setTodayInvoiceAmount(result.getTodayInvoiceAmount().add(stats.getTodayInvoiceAmount()));
            }
            if(stats.getOrderCount() != null){
                result.setOrderCount(result.getOrderCount() + stats.getOrderCount());
            }
            if(stats.getTodayOrderCount() != null){
                result.setTodayOrderCount(result.getTodayOrderCount() + stats.getTodayOrderCount());
            }
            // 开票单数
            if(stats.getInvoiceCount() != null){
                result.setInvoiceCount(result.getInvoiceCount() + stats.getInvoiceCount());
            }
// 今日开票单数
            if(stats.getTodayInvoiceCount() != null){
                result.setTodayInvoiceCount(result.getTodayInvoiceCount() + stats.getTodayInvoiceCount());
            }
            // 商家数
            if(stats.getMerchantCount() != null){
                result.setMerchantCount(result.getMerchantCount() + stats.getMerchantCount());
            }
            // 今日新增商家数
            if(stats.getTodayNewMerchantCount() != null){
                result.setTodayNewMerchantCount(result.getTodayNewMerchantCount() + stats.getTodayNewMerchantCount());
            }
            // 一般纳税人商家数
            if(stats.getGeneralTaxpayerCount() != null){
                result.setGeneralTaxpayerCount(result.getGeneralTaxpayerCount() + stats.getGeneralTaxpayerCount());
            }
            // 今日新增一般纳税人
            if(stats.getTodayNewGeneralTaxpayerCount() != null){
                result.setTodayNewGeneralTaxpayerCount(result.getTodayNewGeneralTaxpayerCount() + stats.getTodayNewGeneralTaxpayerCount());
            }
            // 小规模商家数
            if(stats.getSmallScaleTaxpayerCount() != null){
                result.setSmallScaleTaxpayerCount(result.getSmallScaleTaxpayerCount() + stats.getSmallScaleTaxpayerCount());
            }
            // 今日新增小规模纳税人
            if(stats.getTodayNewSmallScaleTaxpayerCount() != null){
                result.setTodayNewSmallScaleTaxpayerCount(result.getTodayNewSmallScaleTaxpayerCount() + stats.getTodayNewSmallScaleTaxpayerCount());
            }
        }
        return result;
    }
    //过滤无效的townCode数据
    private List<Statistics> filterByTownCode(List<Statistics> list) {
        if (list == null) return Collections.emptyList();
        return list.stream()
                .filter(Objects::nonNull)
                .filter(stat -> StrUtil.isNotBlank(stat.getTownCode()))
                .collect(Collectors.toList());
    }
    private String ensureTownCodeLength(String townCode) {
        if (StrUtil.isBlank(townCode)) {
            return null;
        }
        return townCode.length() < 12 ? StrUtil.padAfter(townCode, 12, '0') : townCode;
    }
    // 从街道编码提取区县编码（假设前6位是区县编码）
    /**
     * 获取街道编码及对应名称
     */
    private List<Cnarea2023Entity> getTownCodesAndNames(List<BusShopEntity> busShopEntities) {
        List<String> townCodes = busShopEntities.stream()
                .map(v -> v.getTownCode() == null ? null : StrUtil.concat(true, v.getTownCode(), "000"))
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        return cnarea2023Mapper.selectList(
                new LambdaQueryWrapper<Cnarea2023Entity>()
                        .in(Cnarea2023Entity::getAreaCode, townCodes)
                        .select(Cnarea2023Entity::getName, Cnarea2023Entity::getAreaCode));
    }
    /**
     * 获取区县编码及对应名称
     */
    private List<Cnarea2023Entity> getCountyCodeAndNames(List<BusShopEntity> busShopEntities) {
        List<String> countyCode = busShopEntities.stream()
                .map(v -> v.getCountyCode() == null ? null : StrUtil.concat(true, v.getCountyCode(), "000000"))
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        return cnarea2023Mapper.selectList(
                new LambdaQueryWrapper<Cnarea2023Entity>()
                        .in(Cnarea2023Entity::getAreaCode, countyCode)
                        .select(Cnarea2023Entity::getName, Cnarea2023Entity::getAreaCode));
    }
    /*private List<Cnarea2023Entity> getCountyCodeAndNames(List<BusShopEntity> busShopEntities) {
        List<String> countyCodes = busShopEntities.stream()
                .map(BusShopEntity::getCountyCode)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        return cnarea2023Mapper.selectList(
                new LambdaQueryWrapper<Cnarea2023Entity>()
                        .in(Cnarea2023Entity::getAreaCode, countyCodes)
                        .select(Cnarea2023Entity::getName, Cnarea2023Entity::getAreaCode));
    }*/
    /**
     * 获取市级编码及对应名称
     */
    private List<Cnarea2023Entity> getCityCodesAndNames(List<BusShopEntity> busShopEntities) {
        List<String> cityCodes = busShopEntities.stream()
                .map(v -> v.getCountyCode() == null ? null : v.getCountyCode().substring(0, 4) + "00000000")
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        return cnarea2023Mapper.selectList(
                new LambdaQueryWrapper<Cnarea2023Entity>()
                        .in(Cnarea2023Entity::getAreaCode, cityCodes)
                        .select(Cnarea2023Entity::getName, Cnarea2023Entity::getAreaCode));
    }
    /**
     * 获取省级编码及对应名称
     */
    private List<Cnarea2023Entity> getProvinceCodesAndNames(List<BusShopEntity> busShopEntities) {
        List<String> provinceCodes = busShopEntities.stream()
                .map(v -> v.getCountyCode() == null ? null : v.getCountyCode().substring(0, 2) + "0000000000") // 12位省级编码
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        return cnarea2023Mapper.selectList(
                new LambdaQueryWrapper<Cnarea2023Entity>()
                        .in(Cnarea2023Entity::getAreaCode, provinceCodes)
                        .select(Cnarea2023Entity::getName, Cnarea2023Entity::getAreaCode));
    }



}


