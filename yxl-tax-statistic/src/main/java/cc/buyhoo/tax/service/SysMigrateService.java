package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.sysCompany.SysCompanyExportParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateAddParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateAuditParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateExportParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigratePageParams;
import cc.buyhoo.tax.result.sysMigrate.SysCompanyQueryDto;
import cc.buyhoo.tax.result.sysMigrate.SysMigrateDetailResult;
import cc.buyhoo.tax.result.sysMigrate.SysMigratePageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 迁入迁出管理
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-08-29
 */
public interface SysMigrateService {

    Result<SysMigratePageResult> selectPageList(SysMigratePageParams pageParams);

    Result<SysMigrateDetailResult> selectById(Long id);

    Result<String> addSysMigrate(SysMigrateAddParams addParams);

    Result<String> auditSysMigrate(SysMigrateAuditParams auditParams);

    Result<String> deleteByIds(DeleteIdsParams idsParams);

    void export(SysMigrateExportParams params, HttpServletResponse response);

    Result<List<SysCompanyQueryDto>> queryCompanyList();
}