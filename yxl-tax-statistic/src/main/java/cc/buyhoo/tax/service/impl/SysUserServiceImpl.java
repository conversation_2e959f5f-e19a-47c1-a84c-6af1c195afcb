package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.StbusUserDataPermissionRelMapper;
import cc.buyhoo.tax.dao.SysRoleMapper;
import cc.buyhoo.tax.dao.SysUserMapper;
import cc.buyhoo.tax.dao.SysUserRoleMapper;
import cc.buyhoo.tax.entity.StbusUserDataPermissionRelEntity;
import cc.buyhoo.tax.entity.SysRoleEntity;
import cc.buyhoo.tax.entity.SysUserEntity;
import cc.buyhoo.tax.entity.SysUserRoleEntity;
import cc.buyhoo.tax.enums.AreaCodeCheckEnum;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.enums.LevelEnum;
import cc.buyhoo.tax.enums.SysUserErrorEnum;
import cc.buyhoo.tax.enums.sys.UserTypeEnum;
import cc.buyhoo.tax.params.sysUser.*;
import cc.buyhoo.tax.params.sysUserRole.SysUserRoleQueryParams;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.sysUser.*;
import cc.buyhoo.tax.result.userArea.AreaDictQueryDto;
import cc.buyhoo.tax.result.userArea.DataScopeTmpDto;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.SysUserService;
import cc.buyhoo.tax.service.UserAreaService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SysUserServiceImpl extends BaseService implements SysUserService {

    private final SysUserMapper sysUserMapper;

    private final SysRoleMapper sysRoleMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final StbusUserDataPermissionRelMapper stbusUserDataPermissionRelMapper;

    private final UserAreaService userAreaService;

    /**
     * 用户列表
     *
     * @param params
     * @return
     */
    @Override
    public Result<SysUserPageResult> pageList(UserPageParams params) {
        //查询数据
        LoginUser loginUser = SatokenUtil.getLoginUser();
        LambdaQueryWrapper<SysUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(params.getUsername()), SysUserEntity::getUsername, params.getUsername());
        queryWrapper.like(StrUtil.isNotBlank(params.getMobile()), SysUserEntity::getMobile, params.getMobile());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getEnableStatus()), SysUserEntity::getEnableStatus, params.getEnableStatus());
        queryWrapper.eq(SysUserEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        if (SatokenUtil.isPlatform(loginUser.getCompanyType()) && ObjectUtil.isNotNull(params.getCompanyId())) {
            queryWrapper.eq(SysUserEntity::getCompanyId, params.getCompanyId());
            queryWrapper.eq(SysUserEntity::getUserType, UserTypeEnum.SUPER_ADMIN.getValue());
        } else {
            queryWrapper.and(wrapper -> wrapper.eq(SysUserEntity::getCompanyId, loginUser.getCompanyId()).or().eq(SysUserEntity::getCreateUser, loginUser.getId()));
        }
        //非超管不可查询超管账号
        if (!SatokenUtil.isSuperAdmin(loginUser.getUserType())) {
            queryWrapper.ne(SysUserEntity::getUserType, UserTypeEnum.SUPER_ADMIN.getValue());
        }
        queryWrapper.orderByDesc(SysUserEntity::getId);
        PageUtils.startPage(params);
        List<SysUserEntity> userList = sysUserMapper.selectList(queryWrapper);

        //操作人
        Set<Long> userIdList = userList.stream().map(SysUserEntity::getModifyUser).collect(Collectors.toSet());
        Map<Long, String> userIdNameMap = handleSysUserIdName(userIdList);

        List<SysUserListDto> dtoList = new ArrayList<>();
        for (SysUserEntity user : userList) {
            SysUserListDto dto = new SysUserListDto();
            BeanUtils.copyProperties(user, dto);
            dto.setModifyUser(userIdNameMap.get(user.getModifyUser()));
            dto.setModifyTime(DateUtil.format(user.getModifyTime(), DatePattern.NORM_DATETIME_PATTERN));
            dtoList.add(dto);
        }

        return convertPageData(userList, dtoList, SysUserPageResult.class, params);
    }

    /**
     * 新增用户
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addUser(SysUserAddParams params) {
        SysUserEntity entity = new SysUserEntity();
        LoginUser loginUser = SatokenUtil.getLoginUser();
        BeanUtils.copyProperties(params, entity);
        if (ObjectUtil.isNull(entity.getCompanyId())) {
            entity.setCompanyId(loginUser.getCompanyId());
        }
        //校验用户名
        checkUsername(entity);
        //校验角色
        checkRoleId(entity, params.getRoleId(), loginUser);
        //保存用户
        entity.setCreateUser(loginUser.getId());
        entity.setModifyUser(loginUser.getId());
        if (SatokenUtil.isPlatform(loginUser.getCompanyType()) && ObjectUtil.isNotNull(params.getCompanyId())) {
            entity.setUserType(UserTypeEnum.SUPER_ADMIN.getValue());
        } else {
            entity.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        entity.setPassword(BCrypt.hashpw(params.getPassword()));
        int n = sysUserMapper.insert(entity);
        if (n > 0) {
            SysUserRoleEntity userRole = new SysUserRoleEntity();
            userRole.setRoleId(params.getRoleId());
            userRole.setUserId(entity.getId());
            sysUserRoleMapper.insert(userRole);
            if (ObjectUtil.isNotEmpty(params.getDataScopeList()) && ObjectUtil.isNotEmpty(params.getLevel())) {
                addOrUpdateUserArea(entity.getId(), params.getDataScopeList(), params.getLevel());
            }
            return Result.ok();
        }
        return Result.fail();
    }

    /**
     * 修改用户
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateUser(SysUserEditParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        SysUserEntity newEntity = new SysUserEntity();
        BeanUtils.copyProperties(params, newEntity);
        if (ObjectUtil.isNull(newEntity.getCompanyId())) {
            newEntity.setCompanyId(loginUser.getCompanyId());
        }
        //用户校验
        SysUserEntity oldUser = sysUserMapper.selectById(params.getId());
        //修改了用户名
        if (!oldUser.getUsername().equals(params.getUsername())) {
            checkUsername(newEntity);
        }
        checkRoleId(newEntity, params.getRoleId(), loginUser);
        //保存新的
        oldUser.setUsername(params.getUsername());
        oldUser.setMobile(params.getMobile());
        oldUser.setEmail(params.getEmail());
        oldUser.setEnableStatus(params.getEnableStatus());
        oldUser.setRemark(params.getRemark());
        oldUser.setModifyUser(loginUser.getId());
        int n = sysUserMapper.updateById(oldUser);
        if (n > 0) {
            //删除旧的
            sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, newEntity.getId()));
            SysUserRoleEntity userRole = new SysUserRoleEntity();
            userRole.setUserId(oldUser.getId());
            userRole.setRoleId(params.getRoleId());
            sysUserRoleMapper.insert(userRole);
            if (ObjectUtil.isNotEmpty(params.getDataScopeList()) && ObjectUtil.isNotEmpty(params.getLevel())) {
                addOrUpdateUserArea(params.getId(), params.getDataScopeList(), params.getLevel());
            }
            return Result.ok();
        }
        return Result.fail();
    }

    /**
     * 用户详情
     *
     * @param id
     * @return
     */
    @Override
    public Result<SysUserDetailResult> selectById(Long id) {
        SysUserEntity entity = sysUserMapper.selectById(id);
        SysUserDetailResult result = new SysUserDetailResult();
        if (ObjectUtil.isNotNull(entity)) {
            BeanUtil.copyProperties(entity, result);
            List<SysUserRoleEntity> list = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, entity.getId()));
            if (CollectionUtil.isNotEmpty(list)) {
                result.setRoleId(list.get(0).getRoleId());
            }
            List<StbusUserDataPermissionRelEntity> relList = stbusUserDataPermissionRelMapper.selectList(new LambdaQueryWrapper<StbusUserDataPermissionRelEntity>().eq(StbusUserDataPermissionRelEntity::getUserId, entity.getId()));
            if (ObjectUtil.isNotEmpty(relList)) {
                result.setLevel(relList.get(0).getLevel());
                List<AreaDictQueryDto> areaDictQueryList = userAreaService.queryAreaDict().getData().getList();
                List<DataScopeTmpDto> dataScopeTmpList = BeanUtil.copyToList(JSONUtil.parseArray(relList.get(0).getDataPermission()), DataScopeTmpDto.class);
                AtomicBoolean isCheckedFlag = new AtomicBoolean(false);
                dataScopeTmpList.forEach(v -> {
                    if (ObjectUtil.equals(v.getLevel(), LevelEnum.COUNTRY.getCode())) {
                        isCheckedFlag.set(true);
                    }
                });
                if (isCheckedFlag.get()) {
                    getDataLevelList(areaDictQueryList, AreaCodeCheckEnum.CHECKED.getCode(), dataScopeTmpList, LevelEnum.COUNTRY.getCode());
                } else {
                    getDataLevelList(areaDictQueryList, AreaCodeCheckEnum.UNCHECKED.getCode(), dataScopeTmpList, LevelEnum.COUNTRY.getCode());
                }
                result.setDataScopeList(areaDictQueryList);
            }
        }
        return Result.ok(result);
    }


    /**
     * 用户角色列表
     *
     * @return
     */
    @Override
    public Result<List<UserRoleListDto>> userRoleList(SysUserRoleQueryParams queryParams) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //角色查询
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        if (SatokenUtil.isPlatform(loginUser.getCompanyType()) && SatokenUtil.isSuperAdmin(loginUser.getUserType())) {
            // 平台超管
            if (ObjectUtil.isNotNull(queryParams.getCompanyId())) {
                roleWrapper.eq(SysRoleEntity::getCompanyId, queryParams.getCompanyId());
            } else {
                roleWrapper.eq(SysRoleEntity::getCompanyId, loginUser.getCompanyId());
            }
        } else if (SatokenUtil.isPlatform(loginUser.getCompanyType())) {
            // 平台用户
            if (ObjectUtil.isNotNull(queryParams.getCompanyId())) {
                roleWrapper.eq(SysRoleEntity::getCompanyId, queryParams.getCompanyId());
            } else {
                List<SysUserRoleEntity> list = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, loginUser.getId()));
                if (CollectionUtil.isEmpty(list)) {
                    return Result.ok(Collections.EMPTY_LIST);
                } else {
                    roleWrapper.and(wrapper -> wrapper.eq(SysRoleEntity::getId, list.get(0).getRoleId()).or().eq(SysRoleEntity::getCompanyId, loginUser.getCompanyId())).or(wrapper -> wrapper.eq(SysRoleEntity::getCreateUser, loginUser.getId()));
                }
            }
        } else if (SatokenUtil.isSuperAdmin(loginUser.getUserType())){
            roleWrapper.eq(SysRoleEntity::getCompanyId, loginUser.getCompanyId()).or(wrapper -> wrapper.eq(SysRoleEntity::getCreateUser, loginUser.getId()));
        } else {
            List<SysUserRoleEntity> list = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, loginUser.getId()));
            if (CollectionUtil.isEmpty(list)) {
                return Result.ok(Collections.EMPTY_LIST);
            }
            roleWrapper.eq(SysRoleEntity::getId, list.get(0).getRoleId()).or(wrapper -> wrapper.eq(SysRoleEntity::getCreateUser, loginUser.getId()));
        }
        roleWrapper.orderByDesc(SysRoleEntity::getId);
        List<SysRoleEntity> roleList = sysRoleMapper.selectList(roleWrapper);
        //数据转换
        List<UserRoleListDto> dtoList = new ArrayList<>();
        for (SysRoleEntity role : roleList) {
            UserRoleListDto dto = new UserRoleListDto();
            dto.setRoleId(role.getId());
            dto.setRoleName(role.getRoleName());
            dtoList.add(dto);
        }
        return Result.ok(dtoList);
    }

    /**
     * 删除用户
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteUser(DeleteUserParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //角色校验
        if (!SatokenUtil.isSuperAdmin(loginUser.getUserType())) {
            return Result.fail(SysUserErrorEnum.USER_NO_PERMISSION);
        }
        if (params.getIds().contains(loginUser.getId())) {
            return Result.fail(SysUserErrorEnum.USER_ERROR, "无法删除自己账号");
        }
        //删除用户
        List<SysUserEntity> updList = new ArrayList<>();
        for (Long uid : params.getIds()) {
            SysUserEntity u = new SysUserEntity();
            u.setId(uid);
            u.setModifyUser(loginUser.getId());
            u.setDelFlag(DelFlagEnum.DELETED.getCode());
            updList.add(u);
        }
        sysUserMapper.updateBatchById(updList);
        sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRoleEntity>().in(SysUserRoleEntity::getUserId, params.getIds()));
        stbusUserDataPermissionRelMapper.delete(new LambdaQueryWrapper<StbusUserDataPermissionRelEntity>().in(StbusUserDataPermissionRelEntity::getUserId,params.getIds()));

        return Result.ok();
    }

    /**
     * 重置密码
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> resetPwd(ResetPwdParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //数据校验
        SysUserEntity oldUser = sysUserMapper.selectById(params.getId());
        //修改密码
        SysUserEntity upd = new SysUserEntity();
        upd.setId(oldUser.getId());
        upd.setPassword(BCrypt.hashpw(params.getPwd()));
        sysUserMapper.updateById(upd);
        return Result.ok();
    }

    /**
     * 修改密码
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updatePwd(UpdatePwdParams params) {
        SysUserEntity entity = sysUserMapper.selectById(SatokenUtil.getLoginUserId());
        if (null == entity) {
            return Result.fail(SysUserErrorEnum.USER_ERROR);
        }
        //校验旧密码
        boolean checkpw = BCrypt.checkpw(params.getOldPwd(), entity.getPassword());
        if (!checkpw) {
            return Result.fail(SysUserErrorEnum.OLD_PWD_ERROR);
        }
        //修改新密码
        entity.setPassword(BCrypt.hashpw(params.getNewPwd()));
        sysUserMapper.updateById(entity);
        return Result.ok();
    }

    /**
     * 校验用户名
     *
     * @param entity
     */
    private void checkUsername(SysUserEntity entity) {
        LambdaQueryWrapper<SysUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserEntity::getUsername, entity.getUsername());
        queryWrapper.eq(SysUserEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.ne(ObjectUtil.isNotNull(entity.getId()), SysUserEntity::getId, entity.getId());
        Long count = sysUserMapper.selectCount(queryWrapper);
        if (null != count && count > 0) {
            throw new BusinessException(SysUserErrorEnum.USERNAME_REPEAT_ERROR);
        }
    }

    /**
     * 校验角色
     *
     * @param entity
     * @param roleId
     */
    private void checkRoleId(SysUserEntity entity, Long roleId, LoginUser loginUser) {
        LambdaQueryWrapper<SysRoleEntity> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(SysRoleEntity::getCompanyId, entity.getCompanyId());
        roleWrapper.eq(SysRoleEntity::getId, roleId);
        roleWrapper.or(wrapper -> wrapper.eq(SysRoleEntity::getCreateUser, loginUser.getId()));
        Long count = sysRoleMapper.selectCount(roleWrapper);
        if (null == count || count == 0) {
            throw new BusinessException(CommonErrorEnum.PARAM_ERROR);
        }
    }

    /**
     * 根据保存的数据权限组合树形结构
     * @param dataScopeList
     * @param isChecked
     * @param dataScopeTmpList
     * @param level
     * @return
     */
    private Integer getDataLevelList(List<AreaDictQueryDto> dataScopeList, Integer isChecked, List<DataScopeTmpDto> dataScopeTmpList, Integer level) {
        AtomicReference<Integer> parentIsChecked = new AtomicReference<>(AreaCodeCheckEnum.UNCHECKED.getCode());
        dataScopeList.forEach(v -> {
            v.setIsChecked(AreaCodeCheckEnum.UNCHECKED.getCode());
            if (ObjectUtil.equals(AreaCodeCheckEnum.CHECKED.getCode(), isChecked) ||
                    (ObjectUtil.isNotEmpty(dataScopeTmpList) && dataScopeTmpList.stream().anyMatch(w -> ObjectUtil.equals(w.getLevel(), level))
                            && dataScopeTmpList.stream().filter(w -> ObjectUtil.equals(w.getLevel(), level)).findFirst().isPresent()
                            && ObjectUtil.isNotEmpty(dataScopeTmpList.stream().filter(w -> ObjectUtil.equals(w.getLevel(), level)).findFirst().get().getCodeList())
                            && dataScopeTmpList.stream().filter(w -> ObjectUtil.equals(w.getLevel(), level)).findFirst().get().getCodeList().contains(String.valueOf(v.getAreaCode())))) {
                v.setIsChecked(AreaCodeCheckEnum.CHECKED.getCode());
                if (ObjectUtil.isNotEmpty(v.getChildList())) {
                    getDataLevelList(v.getChildList(), AreaCodeCheckEnum.CHECKED.getCode(), dataScopeTmpList, level + 1);
                }
                parentIsChecked.set(AreaCodeCheckEnum.PARTIAL_CHECKED.getCode());
            } else {
                if (ObjectUtil.isNotEmpty(v.getChildList())) {
                    v.setIsChecked(getDataLevelList(v.getChildList(), AreaCodeCheckEnum.UNCHECKED.getCode(), dataScopeTmpList, level + 1));
                    if (ObjectUtil.equals(v.getIsChecked(), AreaCodeCheckEnum.PARTIAL_CHECKED.getCode())) {
                        parentIsChecked.set(AreaCodeCheckEnum.PARTIAL_CHECKED.getCode());
                    }
                }
            }
        });
        return parentIsChecked.get();
    }

    /**
     * 新增或更新数据权限
     * @param userId
     * @param dataScopeList
     * @param level
     */
    private void addOrUpdateUserArea(Long userId, List<AreaDictQueryDto> dataScopeList, Integer level) {
        LambdaQueryWrapper<StbusUserDataPermissionRelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StbusUserDataPermissionRelEntity::getUserId, userId);
        List<StbusUserDataPermissionRelEntity> list = stbusUserDataPermissionRelMapper.selectList(queryWrapper);
        StbusUserDataPermissionRelEntity entity = new StbusUserDataPermissionRelEntity();
        if (ObjectUtil.isNotEmpty(list)) {
            entity = list.get(0);
        } else {
            entity.setUserId(userId);
        }
        entity.setLevel(level);
        List<DataScopeTmpDto> dtoList = new ArrayList<>();
        entity.setDataPermission(JSONUtil.toJsonStr(JSONUtil.parseArray(getDataScopeTmpList(dataScopeList, LevelEnum.COUNTRY.getCode(), dtoList))));
        stbusUserDataPermissionRelMapper.insertOrUpdate(entity);
    }

    /**
     * 获取数据权限
     * @param dataScopeList
     * @param level
     * @param dataScopeTmpList
     * @return
     */
    private List<DataScopeTmpDto> getDataScopeTmpList(List<AreaDictQueryDto> dataScopeList,Integer level, List<DataScopeTmpDto> dataScopeTmpList) {
        if (CollectionUtil.isEmpty(dataScopeList)){
            return dataScopeTmpList;
        }
        dataScopeList.forEach(item -> {
            if (ObjectUtil.equals(AreaCodeCheckEnum.CHECKED.getCode(), item.getIsChecked())) {
                if (ObjectUtil.isNotEmpty(dataScopeTmpList) && dataScopeTmpList.stream().anyMatch(tmp -> ObjectUtil.equals(tmp.getLevel(), level))) {
                    dataScopeTmpList.stream().filter(tmp -> ObjectUtil.equals(tmp.getLevel(), level)).findFirst().get().getCodeList().add(String.valueOf(item.getAreaCode()));
                } else {
                    DataScopeTmpDto dataScopeTmpDto = new DataScopeTmpDto();
                    dataScopeTmpDto.setLevel(level);
                    dataScopeTmpDto.setCodeList(new ArrayList<>(Arrays.asList(String.valueOf(item.getAreaCode()))));
                    dataScopeTmpList.add(dataScopeTmpDto);
                }
            } else {
                if (ObjectUtil.isNotEmpty(item.getChildList())) {
                    getDataScopeTmpList(item.getChildList(), level + 1, dataScopeTmpList);
                }
            }
        });
        return dataScopeTmpList;
    }
}
