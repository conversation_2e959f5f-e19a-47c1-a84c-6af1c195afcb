package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.inventoryBatchDetail.InventoryBatchDetailParams;
import cc.buyhoo.tax.result.inventoryBatchDetail.InventoryBatchDetailDto;
import cc.buyhoo.tax.result.inventoryBatchDetail.InventoryBatchDetailListResult;

import java.util.List;

/**
 * 入库批次明细
 * @ClassName BusInventoryBatchDetailService
 * <AUTHOR>
 * @Date 2023/7/30 16:42
 **/
public interface BusInventoryBatchDetailService {
    Result<InventoryBatchDetailListResult> pageList(InventoryBatchDetailParams params);

    Result<List<InventoryBatchDetailDto>> listAll(InventoryBatchDetailParams params);
}
