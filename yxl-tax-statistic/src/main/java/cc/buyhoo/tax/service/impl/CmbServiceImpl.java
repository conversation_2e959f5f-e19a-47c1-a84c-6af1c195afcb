package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.constant.Constants;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.entity.cmb.BackInfoEntity;
import cc.buyhoo.tax.entity.cmb.CMBNoteDataDetail;
import cc.buyhoo.tax.entity.cmb.TrsInfoEntity;
import cc.buyhoo.tax.enums.CmbTransferStatusEnum;
import cc.buyhoo.tax.enums.DisassembleStatusEnum;
import cc.buyhoo.tax.enums.InventoryTypeEnum;
import cc.buyhoo.tax.enums.SaleListMonitorSettledStatusEnum;
import cc.buyhoo.tax.enums.cmb.GenerateDataEnums;
import cc.buyhoo.tax.params.cmb.CreateBusInventoryOrderParams;
import cc.buyhoo.tax.params.cmb.CreateNewInventoryParams;
import cc.buyhoo.tax.params.cmb.CreateNewOrderParams;
import cc.buyhoo.tax.service.CmbService;
import cc.buyhoo.tax.util.CommonUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cc.buyhoo.tax.result.disassembleList.CreateOrderSigleResult;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description 招商银行相关功能
 * @ClassName IndexServiceImpl
 * <AUTHOR>
 * @Date 2023/7/31 17:26
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CmbServiceImpl implements CmbService {
    private final BusSaleListMapper busSaleListMapper;
    private final BusSaleListDetailMapper busSaleListDetailMapper;
    private final BusSaleListPayDetailMapper busSaleListPayDetailMapper;
    private final BusShopMapper busShopMapper;
    private final BusShopBillMapper busShopBillMapper;
    private final BusShopBillDetailMapper busShopBillDetailMapper;
    private final BusGoodsMapper busGoodsMapper;
    private final SysBankTrscodeMapper sysBankTrscodeMapper;
    private final SysCompanyBankAccountMapper sysCompanyBankAccountMapper;
    private final BusShopInvoiceMapper busShopInvoiceMapper;
    private final BusShopInvoiceDetailMapper busShopInvoiceDetailMapper;
    private final SysCompanyMapper sysCompanyMapper;
    private final BusInventoryOrderMapper busInventoryOrderMapper;
    private final BusInventoryBatchMapper busInventoryBatchMapper;
    private final BusInventoryBatchDetailMapper busInventoryBatchDetailMapper;
    private final BusCompanyProfitRuleMapper busCompanyProfitRuleMapper;
    private final BusShopSaleListMonitorMapper busShopSaleListMonitorMapper;
    private final RedisCache redisCache;
    private final BusSaleListDisassembleMapper busSaleListDisassembleMapper;
    private final DisassembleListMapper disassembleListMapper;

    /**
     * 创建新的进货订单
     * @param createNewInventoryParams
     * @return
     */
    public Result<Void> createNewInventory(CreateNewInventoryParams createNewInventoryParams) {
        log.info("创建进货订单{}" ,createNewInventoryParams);
        /**
         * 根据子商户信息查询对应的供应商（超市）信息
         */
        String redisId = Constants.CREATE_NEW_INVENTORY_REDIS_KEY + createNewInventoryParams.getAccnbr() + createNewInventoryParams.getTradeNo();
        if (null != redisCache.getCacheObject(redisId)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"订单数据已生成，请勿重复申请");
        }

        redisCache.setCacheObject(redisId,createNewInventoryParams,Constants.CMB_CALLBACK_NOTE_TIMEOUT, TimeUnit.SECONDS);

        //查看数据库是否已生成相关订单
        LambdaQueryWrapper<BusInventoryOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusInventoryOrderEntity::getThirdTradeNo, createNewInventoryParams.getTradeNo());
        queryWrapper.last("LIMIT 1");
        BusInventoryOrderEntity busInventoryOrderEntity = busInventoryOrderMapper.selectOne(queryWrapper);

        if (ObjectUtil.isNotEmpty(busInventoryOrderEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"订单数据已生成，请勿重复申请");
        }

        //查询该类型的数据是否需要生成进货订单
        LambdaQueryWrapper<SysBankTrscodeEntity> trscodeQueryWrapper = new LambdaQueryWrapper<>();
        trscodeQueryWrapper.eq(SysBankTrscodeEntity::getBankTrscode, createNewInventoryParams.getBankTrscode().trim());
        trscodeQueryWrapper.eq(SysBankTrscodeEntity::getBankId, createNewInventoryParams.getBankId());
        trscodeQueryWrapper.eq(SysBankTrscodeEntity::getIsGenerateInventoryOrder, GenerateDataEnums.YES.getCode());

        SysBankTrscodeEntity sysBankTrscodeEntity = sysBankTrscodeMapper.selectOne(trscodeQueryWrapper);
        if (ObjectUtil.isEmpty(sysBankTrscodeEntity)) {
            //不需要生成订单，
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"该笔交易不需要生成订单");
        }

        //获取打款账号对应的店铺信息
        //根据账号信息查询对应的供应商信息
        LambdaQueryWrapper<BusShopEntity> shopQueryWrapper = new LambdaQueryWrapper<>();
        shopQueryWrapper.eq(BusShopEntity::getSubAccount, createNewInventoryParams.getTradingAccount());
        shopQueryWrapper.last("LIMIT 1");
        BusShopEntity shopEntity = busShopMapper.selectOne(shopQueryWrapper);
        if (ObjectUtil.isEmpty(shopEntity)) {
            //可以在此处修改，将供应商信息自动生成
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "供应商信息不存在");
        }

        //可以生成订单，并对应的生成入库订单
        CreateBusInventoryOrderParams  createBusInventoryOrderParams = new CreateBusInventoryOrderParams();
        createBusInventoryOrderParams.setCompanyId(shopEntity.getCompanyId());
        createBusInventoryOrderParams.setShopUnique(shopEntity.getShopUnique());
        createBusInventoryOrderParams.setThirdTradeNo(createNewInventoryParams.getTradeNo());
        createBusInventoryOrderParams.setTotalMoney(createNewInventoryParams.getPayAmount());
        createBusInventoryOrderParams.setInventoryType(InventoryTypeEnum.SYSTEM_LISTEN.getInventoryType());

        createBusInventoryOrder(createBusInventoryOrderParams,createNewInventoryParams);

        return Result.ok();
    }


    public void createBusInventoryOrder(CreateBusInventoryOrderParams createBusInventoryOrderParams,CreateNewInventoryParams createNewInventoryParams) {
        BusInventoryOrderEntity busInventoryOrderEntity = BeanUtil.copyProperties(createBusInventoryOrderParams, BusInventoryOrderEntity.class);
        busInventoryOrderEntity.setOrderNo(StringUtils.join("CG", DateUtil.format(new Date(), "yyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
        busInventoryOrderEntity.setCreateTime(DateUtil.date());

        busInventoryOrderEntity = setCostProfitMoney(busInventoryOrderEntity);
        log.info("生成入库订单{}",busInventoryOrderEntity);
        busInventoryOrderMapper.insert(busInventoryOrderEntity);

        //添加出入库记录
        BusInventoryBatchEntity busInventoryBatchEntity = BeanUtil.copyProperties(createBusInventoryOrderParams, BusInventoryBatchEntity.class);
        busInventoryBatchEntity.setCompanyId(createBusInventoryOrderParams.getCompanyId());
        busInventoryBatchEntity.setShopUnique(createBusInventoryOrderParams.getShopUnique());
        busInventoryBatchEntity.setBatchNo(StringUtils.join(DateUtil.format(new Date(), "yyyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
        busInventoryBatchEntity.setCostMoney(busInventoryOrderEntity.getCostMoney());
        busInventoryBatchEntity.setProfitMoney(busInventoryOrderEntity.getProfitMoney());
        busInventoryBatchEntity.setOrderId(busInventoryOrderEntity.getId());
        busInventoryBatchEntity.setBatchDate(DateUtil.format(DateUtil.date(),"yyyyMMdd"));
        busInventoryBatchEntity.setCreateTime(DateUtil.date());
        log.info("生成入库{}",busInventoryBatchEntity);
        busInventoryBatchMapper.insert(busInventoryBatchEntity);

        List<BusInventoryBatchDetailEntity> detailEntities = chooseGoodsForBatchDetail(createNewInventoryParams,busInventoryBatchEntity);
        busInventoryBatchDetailMapper.insertBatch(detailEntities);
    }


    public List<BusInventoryBatchDetailEntity> chooseGoodsForBatchDetail(CreateNewInventoryParams createNewInventoryParams,BusInventoryBatchEntity busInventoryBatchEntity) {
        //根据账号信息查询对应的供应商信息
        LambdaQueryWrapper<BusShopEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BusShopEntity::getSubAccount, createNewInventoryParams.getTradingAccount());

        List<BusInventoryBatchDetailEntity> detailEntities = new ArrayList<>();

        BusShopEntity shopEntity = busShopMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(shopEntity)) {
            //店铺信息不存在，返回错误
            return detailEntities;
        }
        //店铺信息存在，查询店铺内满足条件的商品列表
        LambdaQueryWrapper<BusGoodsEntity> goodsQueryWrapper = new LambdaQueryWrapper<>();
        goodsQueryWrapper.lt(BusGoodsEntity::getGoodsSalePrice, createNewInventoryParams.getPayAmount());
        goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());

        List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
        if (ObjectUtil.isEmpty(goodsList)) {
            goodsQueryWrapper.clear();
            goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
            goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
        }

        if (ObjectUtil.isEmpty(goodsList)) {
            return detailEntities;
        }

        BigDecimal saleListTotal = createNewInventoryParams.getPayAmount();
        List<Map<String,Object>> goodsMapList = chooseGoodsMsgBase(saleListTotal, goodsList , 2, createNewInventoryParams.getTradeNo());
        for (Map<String,Object> map : goodsMapList) {
            BusInventoryBatchDetailEntity detailEntity = JSONObject.parseObject(JSONObject.toJSONString(map), BusInventoryBatchDetailEntity.class);
            detailEntity.setId(null);
            detailEntity.setCategoryId((Long) map.get("categoryId"));
            detailEntity.setCategoryTwoId((Long) map.get("categoryTwoId"));
            detailEntity.setGoodsId((Long) map.get("goodsId"));
            detailEntity.setGoodsName((String) map.get("goodsName"));
            detailEntity.setGoodsBarcode((String) map.get("goodsBarcode"));
            detailEntity.setGoodsCount((BigDecimal) map.get("goodsCount"));
            detailEntity.setTotalMoney((BigDecimal) map.get("totalMoney"));
            detailEntity.setOrderId(busInventoryBatchEntity.getOrderId());
            detailEntity.setBatchId(busInventoryBatchEntity.getId());
            detailEntity.setCompanyId(busInventoryBatchEntity.getCompanyId());
            detailEntities.add(detailEntity);
        }
        return detailEntities;
    }




    public static List<Map<String,Object>> chooseGoodsMsgBase(BigDecimal saleListTotal, List<BusGoodsEntity> goodsList , Integer count, String saleListUnique) {
        List<Map<String,Object>> detailEntities = new ArrayList<>();

        if (count == 2) {
            /**
             * 选两个商品
             * 随机选择，如果成功匹配整数，则使用两个商品，否则，尝试一个
             */
            Integer listSize = goodsList.size();
            if (listSize < 2) {
                return chooseGoodsMsgBase(saleListTotal, goodsList, 1, saleListUnique);
            }
            Random random = new Random();
            Integer index1 = random.nextInt(listSize);
            Integer index2 = random.nextInt(listSize);

            BusGoodsEntity goods1 = goodsList.get(index1);
            BusGoodsEntity goods2 = goodsList.get(index2);
            BigDecimal balance = saleListTotal;

            for (int i = 1; i < saleListTotal.divide(goods1.getGoodsSalePrice(),BigDecimal.ROUND_HALF_DOWN).intValue(); i++) {
                balance = balance.subtract(goods1.getGoodsSalePrice().multiply(new BigDecimal(i)));
                if (balance.doubleValue() % goods2.getGoodsSalePrice().doubleValue() == 0) {
                    Map<String,Object> detail1 = new HashMap<>();
                    Map<String,Object> detail2 = new HashMap<>();

                    BeanUtil.copyProperties(goods1, detail1);
                    BeanUtil.copyProperties(goods2, detail2);

                    detail1.put("goodsCount",new BigDecimal(i));
                    detail2.put("goodsCount", balance.divide(goods2.getGoodsSalePrice()));
                    detail1.put("totalMoney",saleListTotal.subtract(balance).setScale(2,BigDecimal.ROUND_HALF_UP));
                    detail2.put("totalMoney",balance);
                    detail1.put("saleListUnique", saleListUnique);
                    detail2.put("saleListUnique", saleListUnique);
                    detail1.put("categoryId",goods1.getCategoryId());
                    detail2.put("categoryId",goods2.getCategoryId());
                    detail1.put("categoryTwoId",goods1.getCategoryTwoId());
                    detail2.put("categoryTwoId",goods2.getCategoryTwoId());
                    detail1.put("goodsBarcode",goods1.getGoodsBarcode());
                    detail2.put("goodsBarcode",goods2.getGoodsBarcode());
                    detail1.put("goodsId",goods1.getGoodsId());
                    detail2.put("goodsId",goods2.getGoodsId());
                    detail1.put("goodsName",goods1.getGoodsName());
                    detail2.put("goodsName",goods2.getGoodsName());

                    detailEntities.add(detail1);
                    detailEntities.add(detail2);

                    log.info("匹配成功，商品1：{}，商品2：{}",goods1,goods2);
                    break;
                }
            }

        } else if (count == 1) {
            for (BusGoodsEntity goods : goodsList) {
                if (saleListTotal.doubleValue() % goods.getGoodsSalePrice().doubleValue() == 0) {

                    Map<String,Object> detailEntity = new HashMap<>();
                    BeanUtil.copyProperties(detailEntity, detailEntity);
                    detailEntity.put("totalMoney",saleListTotal);
                    detailEntity.put("goodsCount",saleListTotal.divide(goods.getGoodsSalePrice(),BigDecimal.ROUND_HALF_UP));
                    detailEntity.put("saleListDetailPrice",goods.getGoodsSalePrice());
                    detailEntity.put("saleListUnique", saleListUnique);
                    detailEntity.put("createTime", DateUtil.date());
                    detailEntity.put("categoryId",goods.getCategoryId());
                    detailEntity.put("categoryTwoId",goods.getCategoryTwoId());
                    detailEntity.put("goodsBarcode",goods.getGoodsBarcode());
                    detailEntity.put("goodsId",goods.getGoodsId());
                    detailEntity.put("goodsName",goods.getGoodsName());
                    detailEntity.put("totalMoney",saleListTotal);
                    detailEntities.add(detailEntity);
                    log.info("匹配成功，商品：{}",goods);
                    break;
                }
            }
        } else if (count == 0) {
            //随机取一个商品返回，数量为小数
            Integer listSize = goodsList.size();
            Random random = new Random();
            Integer index = random.nextInt(listSize);
            BusGoodsEntity goods = goodsList.get(index);
            BigDecimal detailCount = saleListTotal.divide(goods.getGoodsSalePrice(),0, BigDecimal.ROUND_HALF_DOWN);
            if (detailCount == BigDecimal.ZERO) {
                //数量不能小于0
                detailCount = BigDecimal.ONE;
            }
            BigDecimal goodsInPrice = saleListTotal.divide(detailCount,2,BigDecimal.ROUND_HALF_UP);
            goods.setGoodsInPrice(goodsInPrice);

            Map<String,Object> detailEntity = new HashMap<>();
            BeanUtil.copyProperties(detailEntity, detailEntity);
            detailEntity.put("totalMoney",saleListTotal);
            detailEntity.put("goodsCount",saleListTotal.divide(goods.getGoodsSalePrice(),BigDecimal.ROUND_HALF_UP));
            detailEntity.put("saleListDetailPrice",goods.getGoodsSalePrice());
            detailEntity.put("saleListUnique", saleListUnique);
            detailEntity.put("createTime", DateUtil.date());
            detailEntity.put("categoryId",goods.getCategoryId());
            detailEntity.put("categoryTwoId",goods.getCategoryTwoId());
            detailEntity.put("goodsBarcode",goods.getGoodsBarcode());
            detailEntity.put("goodsId",goods.getGoodsId());
            detailEntity.put("goodsName",goods.getGoodsName());
            detailEntity.put("totalMoney",saleListTotal);

            log.info("匹配成功，商品：{}",goods);
            detailEntities.add(detailEntity);
        } else if (count < 0) {
            return detailEntities;
        }

        if (ObjectUtil.isEmpty(detailEntities)) {
            return chooseGoodsMsgBase(saleListTotal, goodsList, --count, saleListUnique);
        }
        log.info("匹配结果：{}",detailEntities);
        return detailEntities;
    }
    /**
     *
     * @param busInventoryOrderEntity
     * @return
     */
    public BusInventoryOrderEntity setCostProfitMoney(BusInventoryOrderEntity busInventoryOrderEntity) {
        busInventoryOrderEntity.setCostMoney(null);
        busInventoryOrderEntity.setProfitMoney(null);

        //根据设置的利润区间和已有的订单信息，合理的安排利润
        LambdaQueryWrapper<BusCompanyProfitRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusCompanyProfitRuleEntity::getCompanyId, busInventoryOrderEntity.getCompanyId());
        queryWrapper.last("LIMIT 1");

        BusCompanyProfitRuleEntity profitRule = busCompanyProfitRuleMapper.selectOne(queryWrapper);

        if (ObjectUtil.isEmpty(profitRule) || profitRule.getRuleType() == 2) {
            log.info("未设置利润比率或者设置的为按订单{}",profitRule);
            //如果为设置，则平平进平出
            busInventoryOrderEntity.setCostMoney(busInventoryOrderEntity.getTotalMoney());
            busInventoryOrderEntity.setProfitMoney(BigDecimal.ZERO);
        } else {
            log.info("设置利润比率{}",profitRule);
            //如果有设置，则按照设置的区间进行计算
            Integer ruleType = profitRule.getRuleType();
            log.info("设置利润比率类型{}",ruleType);
            if (ruleType == 1) {
                BigDecimal profitRate = profitRule.getProfitRate();
                BigDecimal costMoney = busInventoryOrderEntity.getTotalMoney().multiply(BigDecimal.ONE.subtract(profitRate));
                busInventoryOrderEntity.setCostMoney(costMoney);
                busInventoryOrderEntity.setProfitMoney(busInventoryOrderEntity.getTotalMoney().subtract(costMoney));
            }
        }

        return busInventoryOrderEntity;
    }

    /**
     * 接受到通知后，生成订单信息
     * @param createNewOrderParams
     * @return
     */
    public Result<BigDecimal> createNewOrder(CreateNewOrderParams createNewOrderParams, CMBNoteDataDetail cmbNoteDataDetail) {
        /**
         * 根据子商户信息查询对应的供应商（超市）信息
         * 查询交易流水号，判断订单是否已存在，防止重复添加
         * 根据订单金额匹配商品信息
         */
        String redisId = Constants.CREATE_NEW_ORDER_REDIS_KEY + createNewOrderParams.getFrmcod() + createNewOrderParams.getTradeNo();

        BigDecimal balance = BigDecimal.ZERO;
        BigDecimal totalMoney = createNewOrderParams.getPayAmount();
        BigDecimal goodsMoney = BigDecimal.ZERO;

        if (null != redisCache.getCacheObject(redisId)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR,"订单数据已生成，请勿重复申请");
        }
        redisCache.setCacheObject(redisId,createNewOrderParams,Constants.CMB_CALLBACK_NOTE_TIMEOUT, TimeUnit.SECONDS);

        //查询数据库，查看该流水号是否已生成订单
        LambdaQueryWrapper<BusSaleListEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BusSaleListEntity::getTradeNo, createNewOrderParams.getTradeNo());
        lambdaQueryWrapper.last("limit 1");

        BusSaleListEntity busSaleListEntity = busSaleListMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(busSaleListEntity)) {
            //信息已存在，不允许继续操作
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "订单数据已生成，请勿重复申请");
        }
        //校验该信息是否可以生成订单信息
        LambdaQueryWrapper<SysBankTrscodeEntity> trscodeQueryWrapper = new LambdaQueryWrapper<>();
        trscodeQueryWrapper.eq(SysBankTrscodeEntity::getBankTrscode, createNewOrderParams.getBankTrscode().trim());
        trscodeQueryWrapper.eq(SysBankTrscodeEntity::getBankId, createNewOrderParams.getBankId());
        trscodeQueryWrapper.eq(SysBankTrscodeEntity::getIsGenerateSaleOrder, GenerateDataEnums.YES.getCode());

        SysBankTrscodeEntity sysBankTrscodeEntity = sysBankTrscodeMapper.selectOne(trscodeQueryWrapper);
        if (ObjectUtil.isEmpty(sysBankTrscodeEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "该笔交易不需要生成订单");
        }


        //根据账号信息查询对应的供应商信息，此处不建议使用店铺名称查询，同一个纳统企业下有可能有多个相同名称供货商
        LambdaQueryWrapper<BusShopEntity> shopQueryWrapper = new LambdaQueryWrapper<>();
        shopQueryWrapper.eq(BusShopEntity::getSubAccount, createNewOrderParams.getFrmcod());

        BusShopEntity shopEntity = busShopMapper.selectOne(shopQueryWrapper);
        if (ObjectUtil.isEmpty(shopEntity)) {
            //根据收款账号，获取对应的纳统企业信息
            LambdaQueryWrapper<SysCompanyEntity> companyQueryWrapper = new LambdaQueryWrapper<>();
            companyQueryWrapper.eq(SysCompanyEntity::getBankCard, createNewOrderParams.getAccnbr());
            SysCompanyEntity companyEntity = sysCompanyMapper.selectOne(companyQueryWrapper);

            if (ObjectUtil.isEmpty(companyEntity)) {
                return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "纳统企业信息不存在");
            }

            //此处可以做一些处理，比如创建店铺信息
            shopEntity = new BusShopEntity();
            shopEntity.setShopUnique(null);
            shopEntity.setShopName(cmbNoteDataDetail.getRpynam());
            shopEntity.setShopType(1);
            shopEntity.setSubAccount(cmbNoteDataDetail.getRpyacc());
            shopEntity.setCompanyId(companyEntity.getId());
            shopEntity.setInvitationCode(companyEntity.getInvitationCode());
            shopEntity.setBankCard(createNewOrderParams.getFrmcod());
            shopEntity.setSubAccount(createNewOrderParams.getFrmcod());
            shopEntity.setCreateTime(DateUtil.date());

            //添加新店铺
            busShopMapper.insert(shopEntity);
            //给店铺添加新商品
            BusGoodsEntity goodsEntity = new BusGoodsEntity();
            goodsEntity.setCompanyId(companyEntity.getId());
            goodsEntity.setShopUnique(shopEntity.getShopUnique());
            goodsEntity.setGoodsName("通用商品");
            goodsEntity.setGoodsBarcode("1000000");
            goodsEntity.setGoodsInPrice(new BigDecimal(100));
            goodsEntity.setGoodsSalePrice(new BigDecimal(100));
            goodsEntity.setGoodsWebSalePrice(new BigDecimal(100));

            busGoodsMapper.insert(goodsEntity);
        }

        List<BusSaleListDetailEntity> detailEntities = null;
        //根据订单金额，晒选商品信息
        String saleListUnique = CommonUtil.createOrder(redisCache);
        log.info("当前商户信息{}", createNewOrderParams.getAccnbr());
        if (createNewOrderParams.getAccnbr().equals("803041401421002321")) {
            //指定店铺选择为餐饮，其他正常数据
            detailEntities = queryGoodsListForOrderCanyin(createNewOrderParams,saleListUnique);
        } else {
            detailEntities = queryGoodsListForOrder(createNewOrderParams,saleListUnique);
        }

        //计算商品的总金额
        for (BusSaleListDetailEntity detailEntity : detailEntities) {
            goodsMoney = goodsMoney.add(detailEntity.getSaleListDetailCount().multiply(detailEntity.getSaleListDetailPrice()));
        }

        balance = totalMoney.subtract(goodsMoney);

        //创建订单信息
        BusSaleListEntity saleList = new BusSaleListEntity();
        saleList.setCompanyId(shopEntity.getCompanyId());
        saleList.setShopUnique(shopEntity.getShopUnique());
        saleList.setShopName(shopEntity.getShopName());
        saleList.setSaleType(0);
        saleList.setSaleListUnique(saleListUnique);
        saleList.setSaleListTotal(goodsMoney);
        saleList.setSaleListName("");
        saleList.setSaleListActuallyReceived(goodsMoney);
        //服务费，按百分比计算
        saleList.setSaleListServiceFee(BigDecimal.ZERO);
        saleList.setTradeNo(createNewOrderParams.getTradeNo());

        System.out.println(createNewOrderParams.getCreateTime());
        saleList.setSaleListDatetime(DateUtil.parse(createNewOrderParams.getCreateTime()));

        saleList.setPayTime(createNewOrderParams.getCreateTime() == null ? DateUtil.date() : DateUtil.parse(createNewOrderParams.getCreateTime()));
        saleList.setSaleListDatetime(createNewOrderParams.getCreateTime() == null ? DateUtil.date() : DateUtil.parse(createNewOrderParams.getCreateTime()));
        saleList.setSaleListState(3);
        saleList.setSaleListPayment(13);
        saleList.setCreateTime(DateUtil.date());
        saleList.setModifyTime(DateUtil.date());
        saleList.setSettledStatus(1);
        saleList.setProfitStatus(1);

        //商品详情
        BusSaleListPayDetailEntity payDetail = new BusSaleListPayDetailEntity();
        payDetail.setSaleListUnique(saleListUnique);
        payDetail.setCompanyId(shopEntity.getCompanyId());
        payDetail.setPayMethod(17);
        payDetail.setPayMoney(goodsMoney);
        payDetail.setServerType(7);
        payDetail.setMchId(createNewOrderParams.getFrmcod());
        payDetail.setPayTime(DateUtil.date());
        payDetail.setCreateTime(DateUtil.date());
        //保存订单，订单详情，订单支付详情
        busSaleListMapper.insert(saleList);
        busSaleListDetailMapper.insertBatch(detailEntities);
        busSaleListPayDetailMapper.insert(payDetail);

        //根据订单信息，生成开票申请记录
        BusShopInvoiceEntity invoiceEntity = new BusShopInvoiceEntity();
        invoiceEntity.setCompanyId(shopEntity.getCompanyId());
        invoiceEntity.setSaleListUnique(saleListUnique);
        invoiceEntity.setShopUnique(shopEntity.getShopUnique());
        //1、进项票；2、销项票
        invoiceEntity.setInvoiceType(2);
        //发票介质：1、电子；2、纸质
        invoiceEntity.setMediumType(1);
        //发票类型：1、普通发票；2、专用发票
        invoiceEntity.setInvoiceKind(1);
        //采购方信息：1、个人；2、企业
        invoiceEntity.setPurchaseType(1);

        //商品不能确定税额，暂时不处理税率的问题
        //不含税金额
        invoiceEntity.setOrderMoney(goodsMoney);
        //税额总计
        invoiceEntity.setTaxMoney(BigDecimal.ZERO);
        //价税合计
        invoiceEntity.setOrderTaxMoney(goodsMoney);
        //发票申请记录
        busShopInvoiceMapper.insert(invoiceEntity);
        //发票申请详情
        List<BusShopInvoiceDetailEntity> invoiceDetailEntities = createInvoiceDetail(detailEntities, invoiceEntity.getId());
        busShopInvoiceDetailMapper.insertBatch(invoiceDetailEntities);
        return Result.ok(balance);
    }

    public List<BusShopInvoiceDetailEntity> createInvoiceDetail( List<BusSaleListDetailEntity> detailEntities, Long invoiceId) {
        System.out.println("转换前的商品列表" + detailEntities);
        List<BusShopInvoiceDetailEntity> invoiceDetailEntities = detailEntities.stream().map(detailEntity -> {
            BusShopInvoiceDetailEntity invoiceDetailEntity = new BusShopInvoiceDetailEntity();

            invoiceDetailEntity.setInvoiceId(invoiceId);
            invoiceDetailEntity.setGoodsName(detailEntity.getGoodsName());
            invoiceDetailEntity.setAmount(detailEntity.getSaleListDetailSubtotal());

            return invoiceDetailEntity;
        }).collect(Collectors.toList());
        System.out.println("转换后的商品列表" + invoiceDetailEntities);

        return invoiceDetailEntities;
    }

    /**
     * 如果转账成功，通过这里回调
     * @param trsInfo
     * @return
     */
    @Override
    @Transactional
    public Result<Void> resetFinSuccShopBill(TrsInfoEntity trsInfo) {
        if (ObjectUtil.isNotEmpty(trsInfo.getYurRef())) {
            LambdaQueryWrapper<BusShopBillDetailEntity> billDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
            billDetailLambdaQueryWrapper.eq(BusShopBillDetailEntity::getBillNo, trsInfo.getYurRef());
            BusShopBillDetailEntity busShopBillDetailEntity = busShopBillDetailMapper.selectOne(billDetailLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(busShopBillDetailEntity) && ObjectUtil.notEqual(busShopBillDetailEntity.getSettledStatus(), CmbTransferStatusEnum.FINS.getStatus())) {
                String settledStatus = busShopBillDetailEntity.getSettledStatus();
                busShopBillDetailEntity.setSettledStatus(CmbTransferStatusEnum.FINS.getStatus());
                busShopBillDetailMapper.updateById(busShopBillDetailEntity);
                //异步更新订单监控表中结算状态为结算成功
                ThreadUtil.execAsync(() -> updateSaleListMonitor(busShopBillDetailEntity));
                if (ObjectUtil.isNotEmpty(busShopBillDetailEntity.getBillId())) {
                    BusShopBillEntity busShopBillEntity = busShopBillMapper.selectById(busShopBillDetailEntity.getBillId());
                    if (ObjectUtil.isNotEmpty(busShopBillEntity) && getSettledStatus().contains(settledStatus)
                            && ObjectUtil.isNotEmpty(busShopBillEntity.getSettledingAmount())
                            && busShopBillEntity.getSettledingAmount().compareTo(new BigDecimal(trsInfo.getTrsAmt())) >= 0) {
                        busShopBillEntity.setSettledingAmount(busShopBillEntity.getSettledingAmount().subtract(new BigDecimal(trsInfo.getTrsAmt())));
                        busShopBillEntity.setSettledAmount(busShopBillEntity.getSettledAmount().add(new BigDecimal(trsInfo.getTrsAmt())));
                        busShopBillMapper.updateById(busShopBillEntity);
                    }
                }
            }
        }
        return Result.ok();
    }

    /**
     * 回调通知里，如果转账失败，通过这里回调
     * @param trsInfo
     * @return
     */
    @Override
    @Transactional
    public Result<Void> resetFinBackShopBill(TrsInfoEntity trsInfo) {
        if (ObjectUtil.isNotEmpty(trsInfo.getYurRef())) {
            LambdaQueryWrapper<BusShopBillDetailEntity> billDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
            billDetailLambdaQueryWrapper.eq(BusShopBillDetailEntity::getBillNo, trsInfo.getYurRef());
            BusShopBillDetailEntity busShopBillDetailEntity = busShopBillDetailMapper.selectOne(billDetailLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(busShopBillDetailEntity)) {
                String settledStatus = busShopBillDetailEntity.getSettledStatus();
                busShopBillDetailEntity.setSettledStatus(CmbTransferStatusEnum.FINF.getStatus());
                busShopBillDetailEntity.setFailReason(trsInfo.getRtnNar());
                busShopBillDetailMapper.updateById(busShopBillDetailEntity);
                //异步更新订单监控表中结算状态为结算失败
                ThreadUtil.execAsync(() -> updateSaleListMonitor(busShopBillDetailEntity));
                if (ObjectUtil.isNotEmpty(busShopBillDetailEntity.getBillId())) {
                    BusShopBillEntity busShopBillEntity = busShopBillMapper.selectById(busShopBillDetailEntity.getBillId());
                    if (ObjectUtil.isNotEmpty(busShopBillEntity)) {
                        if (ObjectUtil.equals(settledStatus, CmbTransferStatusEnum.FINS.getStatus())) {
                            busShopBillEntity.setSettledAmount(busShopBillEntity.getSettledingAmount().subtract(new BigDecimal(trsInfo.getTrsAmt())));
                            busShopBillEntity.setUnsettledAmount(busShopBillEntity.getUnsettledAmount().add(new BigDecimal(trsInfo.getTrsAmt())));
                            busShopBillMapper.updateById(busShopBillEntity);
                        } else if (getSettledStatus().contains(settledStatus)) {
                            busShopBillEntity.setSettledingAmount(busShopBillEntity.getSettledingAmount().subtract(new BigDecimal(trsInfo.getTrsAmt())));
                            busShopBillEntity.setUnsettledAmount(busShopBillEntity.getUnsettledAmount().add(new BigDecimal(trsInfo.getTrsAmt())));
                            busShopBillMapper.updateById(busShopBillEntity);
                        }
                    }
                }
            }
        }
        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Void> resetFinBackShopBill(BackInfoEntity backInfo) {
        if (ObjectUtil.isNotEmpty(backInfo.getYurRef())) {
            LambdaQueryWrapper<BusShopBillDetailEntity> billDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
            billDetailLambdaQueryWrapper.eq(BusShopBillDetailEntity::getBillNo, backInfo.getYurRef());
            BusShopBillDetailEntity busShopBillDetailEntity = busShopBillDetailMapper.selectOne(billDetailLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(busShopBillDetailEntity)) {
                String settledStatus = busShopBillDetailEntity.getSettledStatus();
                busShopBillDetailEntity.setSettledStatus(CmbTransferStatusEnum.FINB.getStatus());
                busShopBillDetailEntity.setFailReason(backInfo.getRtnNar());
                busShopBillDetailMapper.updateById(busShopBillDetailEntity);
                //异步更新订单监控表中结算状态为结算失败
                ThreadUtil.execAsync(() -> updateSaleListMonitor(busShopBillDetailEntity));
                if (ObjectUtil.isNotEmpty(busShopBillDetailEntity.getBillId())) {
                    BusShopBillEntity busShopBillEntity = busShopBillMapper.selectById(busShopBillDetailEntity.getBillId());
                    if (ObjectUtil.isNotEmpty(busShopBillEntity)) {
                        if (ObjectUtil.equals(settledStatus, CmbTransferStatusEnum.FINS.getStatus())) {
                            busShopBillEntity.setSettledAmount(busShopBillEntity.getSettledAmount().subtract(new BigDecimal(backInfo.getTrsAmt())));
                            busShopBillEntity.setUnsettledAmount(busShopBillEntity.getUnsettledAmount().add(new BigDecimal(backInfo.getTrsAmt())));
                            busShopBillMapper.updateById(busShopBillEntity);
                        } else if (getSettledStatus().contains(settledStatus)) {
                            busShopBillEntity.setSettledingAmount(busShopBillEntity.getSettledingAmount().subtract(new BigDecimal(backInfo.getTrsAmt())));
                            busShopBillEntity.setUnsettledAmount(busShopBillEntity.getUnsettledAmount().add(new BigDecimal(backInfo.getTrsAmt())));
                            busShopBillMapper.updateById(busShopBillEntity);
                        }
                    }
                }
            }
        }
        return Result.ok();
    }

    @Override
    public SysCompanyBankAccountEntity getCompanyBankAccount(Long companyId) {
        SysCompanyBankAccountEntity sysCompanyBankAccountEntity = sysCompanyBankAccountMapper.selectById(companyId);
        return sysCompanyBankAccountEntity;
    }

    /**
     * 餐饮行业相关，商品只选择一个，
     * @param createNewOrderParams
     * @param saleListUnique
     * @return
     */
    public List<BusSaleListDetailEntity> queryGoodsListForOrderCanyin(CreateNewOrderParams createNewOrderParams, String saleListUnique) {
        LambdaQueryWrapper<BusShopEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BusShopEntity::getSubAccount, createNewOrderParams.getFrmcod());

        List<BusSaleListDetailEntity> detailEntities = new ArrayList<>();

        BusShopEntity shopEntity = busShopMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(shopEntity)) {
            //店铺信息不存在，返回错误
            return detailEntities;
        }

        /**
         * 判断是否生成套餐商品
         * 1、判断金额是否大于套餐最小金额，如果小于，则不允许生成套餐商品
         * 2、使用随机数，判断是否使用套餐商品
         * 3、如果使用套餐商品，则随机抽取一个满足条件的套餐
         * 4、如果不使用套餐，选择家常菜，随机选择菜品数量，然后随机抽取几个菜，
         * 5、尾数处理，如果生成订单后余额不足够10元，则为订单添加餐巾纸或者米饭。
         */
        detailEntities = chooseGoods(createNewOrderParams, saleListUnique, shopEntity);

        return detailEntities;
    }

    public List<BusSaleListDetailEntity> chooseGoods(CreateNewOrderParams createNewOrderParams, String saleListUnique, BusShopEntity shopEntity) {
        //1、判断总金额是否大约套餐价格
        BigDecimal saleListTotal =createNewOrderParams.getPayAmount();

        List<BusSaleListDetailEntity> detailEntities = new ArrayList<>();
        boolean flag = true;

        BusGoodsEntity minPriceGoods;
        BigDecimal minPrice = new BigDecimal("0");

        String minPriceRedisId = Constants.MIN_CANYIN_PRICE + shopEntity.getShopUnique();
        if (redisCache.hasKey(minPriceRedisId)) {
            minPrice = new BigDecimal(redisCache.getCacheObject(minPriceRedisId).toString());
        } else {
            LambdaQueryWrapper<BusGoodsEntity> minPriceWrapper = new LambdaQueryWrapper<>();
            minPriceWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
            minPriceWrapper.orderByDesc(BusGoodsEntity::getGoodsSalePrice);
            minPriceWrapper.last("LIMIT 1");
            minPriceWrapper.eq(BusGoodsEntity::getCategoryTwoId, 100);
            minPriceGoods = busGoodsMapper.selectOne(minPriceWrapper);
            minPrice = minPriceGoods.getGoodsSalePrice();
            redisCache.setCacheObject(minPriceRedisId, minPrice);
        }

        //随机数判断是否使用套餐
        BigDecimal random = new BigDecimal(Math.random());
        if (flag && random.compareTo(new BigDecimal("0.5")) >= 0) {
            //如果当前订单价格低于最小套餐价格，则不生成套餐
            if (saleListTotal.compareTo(minPrice) >= 0) {
                //使用套餐
                flag = true;
            } else {
                //不使用套餐
                flag = false;
            }
        }

        if (flag) {
            log.info("使用套餐");
            //使用套餐
            LambdaQueryWrapper<BusGoodsEntity> goodsQueryWrapper = new LambdaQueryWrapper<>();
            goodsQueryWrapper.lt(BusGoodsEntity::getGoodsSalePrice, createNewOrderParams.getPayAmount());
            goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
            goodsQueryWrapper.eq(BusGoodsEntity::getCategoryTwoId, 100);

            //判断余额是否enough，如果足够生成下一单，则返回，如果不够，添加餐巾纸或者米饭
            List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
            if (goodsList.size() <= 0) {
                //没有可选择的套餐，重新选择
                return chooseGoods(createNewOrderParams, saleListUnique, shopEntity);
            } else {
                //有满足条件的套餐，随机选择一个
                Integer index = RandomUtil.randomInt(goodsList.size());
                BusGoodsEntity goods = goodsList.get(index);
                BusSaleListDetailEntity detailEntity = new BusSaleListDetailEntity();
                BeanUtil.copyProperties(goods, detailEntity);
                detailEntity.setId(null);
                detailEntity.setSaleListDetailSubtotal(goods.getGoodsSalePrice());
                detailEntity.setSaleListDetailCount(BigDecimal.ONE);
                detailEntity.setSaleListDetailPrice(goods.getGoodsSalePrice());
                detailEntity.setSaleListUnique(saleListUnique);
                detailEntity.setCreateTime(DateUtil.date());

                detailEntities.add(detailEntity);

                saleListTotal = saleListTotal.subtract(goods.getGoodsSalePrice());

                //如果是套餐，将余额加回去
                //查看余额是否足够，如果够，则返回，如果不够，添加餐巾纸或者米饭
                if (saleListTotal.compareTo(new BigDecimal("10")) < 0) {
                    //添加若干饮料
                    //需要提前去掉价格限制
                    goodsQueryWrapper.clear();
                    goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
                    goodsQueryWrapper.eq(BusGoodsEntity::getCategoryTwoId, 105);
                    List<BusGoodsEntity> drinkList = busGoodsMapper.selectList(goodsQueryWrapper);
                    //随机选择一个
                    BusGoodsEntity drinkGoods = drinkList.get(RandomUtil.randomInt(drinkList.size()));

                    BigDecimal goodsCount = saleListTotal.divide(drinkGoods.getGoodsSalePrice(), 0,BigDecimal.ROUND_DOWN);

                    if (goodsCount.compareTo(BigDecimal.ZERO) >= 1) {
                        BusSaleListDetailEntity drinkDetailEntity = new BusSaleListDetailEntity();
                        BeanUtil.copyProperties(drinkGoods, drinkDetailEntity);
                        drinkDetailEntity.setId(null);
                        drinkDetailEntity.setSaleListDetailSubtotal(goodsCount.multiply(drinkGoods.getGoodsSalePrice()));
                        drinkDetailEntity.setSaleListDetailCount(goodsCount);
                        drinkDetailEntity.setSaleListDetailPrice(drinkGoods.getGoodsSalePrice());
                        drinkDetailEntity.setSaleListUnique(saleListUnique);
                        drinkDetailEntity.setCreateTime(DateUtil.date());
                        detailEntities.add(drinkDetailEntity);

                        saleListTotal = saleListTotal.subtract(drinkDetailEntity.getSaleListDetailSubtotal());
                    }

                    if (saleListTotal.compareTo(BigDecimal.ZERO) > 0) {
                        //将剩余的金额选择为餐巾纸或者米饭
                        goodsQueryWrapper.clear();
                        goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
                        goodsQueryWrapper.eq(BusGoodsEntity::getCategoryTwoId, 103);
                        List<BusGoodsEntity> roundList = busGoodsMapper.selectList(goodsQueryWrapper);
                        //随机选择一个
                        BusGoodsEntity roundGoods = roundList.get(RandomUtil.randomInt(roundList.size()));

                        BusSaleListDetailEntity roundDetailEntity = new BusSaleListDetailEntity();
                        BeanUtil.copyProperties(roundGoods, roundDetailEntity);
                        roundDetailEntity.setId(null);
                        roundDetailEntity.setSaleListDetailSubtotal(saleListTotal);
                        roundDetailEntity.setSaleListDetailCount(saleListTotal.divide(roundGoods.getGoodsSalePrice(), 2, BigDecimal.ROUND_HALF_UP));
                        roundDetailEntity.setSaleListDetailPrice(roundGoods.getGoodsSalePrice());
                        roundDetailEntity.setSaleListUnique(saleListUnique);
                        roundDetailEntity.setCreateTime(DateUtil.date());

                        detailEntities.add(roundDetailEntity);
                    }
                }
            }

        } else {
            log.info("不使用套餐");
            //不使用套餐
            LambdaQueryWrapper<BusGoodsEntity> goodsQueryWrapper = new LambdaQueryWrapper<>();
            goodsQueryWrapper.lt(BusGoodsEntity::getGoodsSalePrice, createNewOrderParams.getPayAmount());
            goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
            goodsQueryWrapper.eq(BusGoodsEntity::getCategoryTwoId, 101);

            List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
            List<Integer> indexList = new ArrayList<>();

            //选择菜品数量
            Integer count = RandomUtil.randomInt(2, 12);
            log.info("当前选择商品数量{}", count);
            for (int i = 0; i < goodsList.size() && (i < count || saleListTotal.compareTo(BigDecimal.ZERO) > 0) ; i++) {
                log.info("当前选择的序号{}", i);
                Integer index = getIndex(goodsList.size(), indexList);
                indexList.add(index);
                log.info("选择的商品序号为{}" , index);
                //需要校验当前菜品加个是否超过可用金额
                BusGoodsEntity goods = goodsList.get(index);
                log.info("选择的商品为{}" , goods);
                //如果余额不足，可以选饮料
                if (saleListTotal.compareTo(goods.getGoodsSalePrice()) < 0 && saleListTotal.compareTo(new BigDecimal("20")) <= 0) {
                    //余额不足，选择饮料
                    //需要提前去掉价格限制
                    goodsQueryWrapper.clear();
                    goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
                    goodsQueryWrapper.eq(BusGoodsEntity::getCategoryTwoId, 105);
                    List<BusGoodsEntity> drinkList = busGoodsMapper.selectList(goodsQueryWrapper);
                    //随机选择一个
                    BusGoodsEntity drinkGoods = drinkList.get(RandomUtil.randomInt(drinkList.size()));
                    log.info("余额不足{}，选择饮料{}", saleListTotal, drinkGoods);
                    BigDecimal goodsCount = saleListTotal.divide(drinkGoods.getGoodsSalePrice(), 0,BigDecimal.ROUND_DOWN);
                    log.info("选择饮料的内容{}和数量{}", drinkGoods, goodsCount);

                    if (goodsCount.compareTo(BigDecimal.ZERO) >= 1) {
                        BusSaleListDetailEntity drinkDetailEntity = new BusSaleListDetailEntity();
                        BeanUtil.copyProperties(drinkGoods, drinkDetailEntity);
                        drinkDetailEntity.setId(null);
                        drinkDetailEntity.setSaleListDetailSubtotal(goodsCount.multiply(drinkGoods.getGoodsSalePrice()));
                        drinkDetailEntity.setSaleListDetailCount(goodsCount);
                        drinkDetailEntity.setSaleListDetailPrice(drinkGoods.getGoodsSalePrice());
                        drinkDetailEntity.setSaleListUnique(saleListUnique);
                        drinkDetailEntity.setCreateTime(DateUtil.date());
                        detailEntities.add(drinkDetailEntity);

                        saleListTotal = saleListTotal.subtract(drinkDetailEntity.getSaleListDetailSubtotal());
                    }


                    //查看是否还有余额，如果有，用米饭补足
                    if (saleListTotal.compareTo(BigDecimal.ZERO) > 0) {
                        goodsQueryWrapper.clear();
                        goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
                        goodsQueryWrapper.eq(BusGoodsEntity::getCategoryTwoId, 103);
                        List<BusGoodsEntity> roundList = busGoodsMapper.selectList(goodsQueryWrapper);
                        //随机选择一个
                        BusGoodsEntity roundGoods = roundList.get(RandomUtil.randomInt(roundList.size()));

                        BusSaleListDetailEntity roundDetailEntity = new BusSaleListDetailEntity();
                        BeanUtil.copyProperties(roundGoods, roundDetailEntity);
                        roundDetailEntity.setId(null);
                        roundDetailEntity.setSaleListDetailSubtotal(saleListTotal);
                        roundDetailEntity.setSaleListDetailCount(saleListTotal.divide(roundGoods.getGoodsSalePrice(), 2, BigDecimal.ROUND_HALF_UP));
                        roundDetailEntity.setSaleListDetailPrice(roundGoods.getGoodsSalePrice());
                        roundDetailEntity.setSaleListUnique(saleListUnique);
                        roundDetailEntity.setCreateTime(DateUtil.date());
                        detailEntities.add(roundDetailEntity);
                    }
                    //已经不需要再加商品了
                    break;
                } else if (saleListTotal.compareTo(goods.getGoodsSalePrice()) < 0 && saleListTotal.compareTo(new BigDecimal("20")) > 0) {
                    //重新选择商品
                    continue;
                }

                BusSaleListDetailEntity detailEntity = new BusSaleListDetailEntity();
                BeanUtil.copyProperties(goods, detailEntity);
                detailEntity.setId(null);
                detailEntity.setSaleListDetailSubtotal(goods.getGoodsSalePrice());
                detailEntity.setSaleListDetailCount(BigDecimal.ONE);
                detailEntity.setSaleListDetailPrice(goods.getGoodsSalePrice());
                detailEntity.setSaleListUnique(saleListUnique);
                detailEntity.setCreateTime(DateUtil.date());

                saleListTotal = saleListTotal.subtract(goods.getGoodsSalePrice());

                detailEntities.add(detailEntity);
            }
        }

        log.info("生成的菜品详情为{}", detailEntities);

        return detailEntities;
    }

    /**
     * 获取随机数
     * @param size
     * @param indexList
     * @return
     */
    public static Integer getIndex (Integer size,  List<Integer> indexList) {
        Integer index = RandomUtil.randomInt(size);
        if (indexList.contains(index)) {
            return getIndex(size, indexList);
        } else {
            return index;
        }
    }

    //创建单条记录
    public CreateOrderSigleResult createOrderSigle(String oldSaleListUnique,String saleListUnique,BigDecimal saleListTotal, BigDecimal saleListPayTotal, Long shopUnique, List<BusSaleListPayDetailEntity> saleListPayDetailEntities, Boolean isLast) {
        //拆单后的商品成本可能高于实际订单金额，因为时间不一样，如果出现负数，则改为0
        if (saleListPayTotal.compareTo(BigDecimal.ZERO) <= 0) {
            saleListPayTotal = BigDecimal.ZERO;
        }
        List<BusSaleListPayDetailEntity> subPayDetails = new ArrayList<>();
        CreateOrderSigleResult createOrderSigleResult = new CreateOrderSigleResult();
        createOrderSigleResult.setSaleListTotal(saleListTotal);
        //校验该订单号是否已存在，防止重复创建
        LambdaQueryWrapper<BusSaleListEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusSaleListEntity::getSaleListUnique, saleListUnique);
        queryWrapper.eq(BusSaleListEntity::getShopUnique, shopUnique);
        BusSaleListEntity saleList = busSaleListMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotNull(saleList)) {
            //订单号已存在，创建订单信息失败
            createOrderSigleResult.setFlag(false);
            createOrderSigleResult.setMsg("订单号已存在，创建订单信息失败");
            return createOrderSigleResult;
        }

        LambdaQueryWrapper<BusSaleListEntity> busSaleListQuery = new LambdaQueryWrapper<>();
        busSaleListQuery.eq(BusSaleListEntity::getSaleListUnique, oldSaleListUnique);
        busSaleListQuery.eq(BusSaleListEntity::getShopUnique, shopUnique);
        BusSaleListEntity saleListObjEntity = busSaleListMapper.selectOne(busSaleListQuery);

        //筛选出满足条件的商品信息
        List<BusSaleListDetailEntity> detailEntities = queryGoodsListForOrderByPrice(saleListUnique, saleListTotal,shopUnique,isLast);
        log.info("筛选的商品信息detailEntities:{}"+ detailEntities + ObjectUtil.isEmpty(detailEntities));
        //如果没有合适的商品信息，则修改为失败
        if (ObjectUtil.isEmpty(detailEntities)) {
            //修改订单信息为失败
            DisassembleListEntity disassembleEntity = new DisassembleListEntity();
            BeanUtil.copyProperties(saleListObjEntity, disassembleEntity);
            disassembleEntity.setSaleListUnique(saleListUnique);
            disassembleEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_FAIL.getValue());
            disassembleEntity.setDisassmebleRemarks("没有满足条件的商品信息");
            LambdaUpdateWrapper<DisassembleListEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DisassembleListEntity::getSaleListUnique, saleListUnique);
            disassembleListMapper.update(disassembleEntity, updateWrapper);

            createOrderSigleResult.setFlag(false);
            createOrderSigleResult.setMsg("没有满足条件的商品信息");
            return createOrderSigleResult;
        }
        //统计商品总销售金额,修改订单详情的小计
        BigDecimal goodsMoney = BigDecimal.ZERO;
        for (BusSaleListDetailEntity d: detailEntities) {
            BigDecimal subTotal = d.getSaleListDetailPrice().multiply(d.getSaleListDetailCount()).setScale(2,BigDecimal.ROUND_HALF_UP);
            goodsMoney = goodsMoney.add(subTotal).setScale(2,BigDecimal.ROUND_HALF_UP);
            d.setSaleListDetailSubtotal(subTotal);
        }

        //店铺信息
        BusShopEntity shopEntity = busShopMapper.selectOne(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getShopUnique,shopUnique));

        //创建订单信息
        BusSaleListEntity saleListEntity = new BusSaleListEntity();
        BeanUtil.copyProperties(saleListObjEntity,saleListEntity);
        saleListEntity.setCompanyId(shopEntity.getCompanyId());
        saleListEntity.setShopUnique(shopEntity.getShopUnique());
        saleListEntity.setShopName(shopEntity.getShopName());
        saleListEntity.setSaleListUnique(saleListUnique);
        saleListEntity.setSaleListTotal(goodsMoney);
        saleListEntity.setParentListUnique(saleListObjEntity.getSaleListUnique());
        saleListEntity.setSaleListActuallyReceived(goodsMoney);
        saleListEntity.setProfitTotal(saleListPayTotal);

        //支付详情
        BusSaleListPayDetailEntity payDetail = new BusSaleListPayDetailEntity();
        payDetail.setSaleListUnique(saleListUnique);
        payDetail.setCompanyId(shopEntity.getCompanyId());
        payDetail.setPayMethod(saleListObjEntity.getSaleListPayment());
        payDetail.setPayMoney(goodsMoney);
        payDetail.setServerType(7);
        payDetail.setMchId(null);
        payDetail.setPayTime(DateUtil.date());
        payDetail.setCreateTime(DateUtil.date());
        //保存订单，订单详情，订单支付详情，支付详情应由上一级传过来


        //支付详情，需要根据新的订单金额计算
        //根据原订单的支付信息，创建新订单的支付信息
        BigDecimal saleListTotalBalance = goodsMoney;
        while (saleListTotalBalance.compareTo(BigDecimal.ZERO) > 0) {

            for (Integer i = 0; i < saleListPayDetailEntities.size(); i++) {
                if (saleListPayDetailEntities.get(i).getPayMoney().compareTo(BigDecimal.ZERO) <= 0) {
                    //如果当前支付方式的余额为0，跳过
                    continue;
                }
                if (saleListPayDetailEntities.get(i).getPayMoney().compareTo(saleListTotalBalance) >= 0 ) {
                    BusSaleListPayDetailEntity payDetailEntity = new BusSaleListPayDetailEntity();
                    BeanUtil.copyProperties(saleListPayDetailEntities.get(i), payDetailEntity);
                    payDetailEntity.setSaleListUnique(saleListUnique);
                    payDetailEntity.setPayMoney(saleListTotalBalance);
                    saleListPayDetailEntities.get(i).setPayMoney(saleListPayDetailEntities.get(i).getPayMoney().subtract(saleListTotalBalance).setScale(2,BigDecimal.ROUND_HALF_UP));
                    payDetailEntity.setId(null);
                    subPayDetails.add(payDetailEntity);
                    saleListTotalBalance = BigDecimal.ZERO;
                    break;
                } else {
                    //扣除部分余额，继续下一次
                    BusSaleListPayDetailEntity payDetailEntity = new BusSaleListPayDetailEntity();
                    BeanUtil.copyProperties(saleListPayDetailEntities.get(i), payDetailEntity);
                    payDetailEntity.setSaleListUnique(saleListUnique);
                    saleListPayDetailEntities.get(i).setPayMoney(BigDecimal.ZERO);
                    payDetailEntity.setId(null);
                    subPayDetails.add(payDetailEntity);
                    saleListTotalBalance = saleListTotalBalance.subtract(payDetailEntity.getPayMoney()).setScale(2,BigDecimal.ROUND_HALF_UP);
                }
            }
        }


        //保存订单，保存订单详情
        saleListEntity.setId(null);
        busSaleListMapper.insert(saleListEntity);
        busSaleListDetailMapper.insertBatch(detailEntities);
        busSaleListPayDetailMapper.insertBatch( subPayDetails);

        createOrderSigleResult.setFlag(true);
        createOrderSigleResult.setSaleListTotal(goodsMoney);
        createOrderSigleResult.setSaleListPayDetailEntities(saleListPayDetailEntities);
        return createOrderSigleResult;
    }

    public List<BusSaleListDetailEntity> queryGoodsListForOrderByPrice(String saleListUnique, BigDecimal saleListTotal, Long shopUnique, Boolean isLast) {

        LambdaQueryWrapper<BusShopEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BusShopEntity::getShopUnique, shopUnique);
        BusShopEntity shopEntity = busShopMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(shopEntity)) {
            //店铺信息不存在，返回错误
            return new ArrayList<>();
        }
        //店铺信息存在，查询店铺内满足条件的商品列表
        LambdaQueryWrapper<BusGoodsEntity> goodsQueryWrapper = new LambdaQueryWrapper<>();
        goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());

        List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
        if (ObjectUtil.isEmpty(goodsList)) {
            //商品信息不存在，返回错误
            return new ArrayList<>();
        }

        //从当前商品中选择满足添加的商品信息，如果是最后一单，则修改最后的商品价格数据

        List<BusSaleListDetailEntity> detailEntities = new ArrayList<>();

        detailEntities = chooseGoodsMsgByPrice(detailEntities,saleListTotal,goodsList,saleListUnique,isLast);

        return detailEntities;
    }

    //筛选商品信息
    public static List<BusSaleListDetailEntity> chooseGoodsMsgByPrice(List<BusSaleListDetailEntity> detailEntities, BigDecimal saleListTotal, List<BusGoodsEntity> goodsList , String saleListUnique, Boolean isLast) {
        BigDecimal disparities = BigDecimal.ZERO;
        BusSaleListDetailEntity detailEntity = new BusSaleListDetailEntity();
        BusGoodsEntity goodsEntity = new BusGoodsEntity();
        log.info("chooseGoodsMsgByPrice: isLast == {}", isLast);
        if (isLast) {
            goodsEntity = goodsList.get(0);
            disparities = goodsList.get(0).getGoodsSalePrice();
            //如果是最后一个商品，则修找到一个价格最相近的商品，修改商品的销售价格
            for (BusGoodsEntity goods : goodsList) {
                if (goods.getGoodsSalePrice().compareTo(saleListTotal) == 0) {
                    log.info("有价格相同的产品{}", goods);
                    goodsEntity = goods;
                    break;
                }
                if (disparities.compareTo(goods.getGoodsSalePrice().subtract(saleListTotal).abs()) > 0) {
                    //原差价大于最新商品的差价，将最新商品的信息赋值到临时差距
                    disparities = goods.getGoodsSalePrice().subtract(saleListTotal).abs();
                    goodsEntity = goods;
                    log.info("选择价格相近的产品{}",goods);
                }
            }
            //获取到最接近的差值，修改商品的销售价格
            //如果差价为0，则直接复制商品，否则，修改商品价格
            if (disparities.compareTo(BigDecimal.ZERO) != 0) {
                goodsEntity.setGoodsSalePrice(saleListTotal);
            }

            //如果存在相同的商品，则修改数量，如果没有，则增加新记录
            Boolean flag = false;
            String goodsBarcode = goodsEntity.getGoodsBarcode();
            for (BusSaleListDetailEntity d : detailEntities) {
                //防止同一个商品，价格不一样
                if (goodsBarcode.equals(d.getGoodsBarcode()) ) {
                    if (goodsEntity.getGoodsSalePrice().compareTo(d.getSaleListDetailPrice()) == 0) {
                        d.setSaleListDetailCount(d.getSaleListDetailCount().add(BigDecimal.ONE));
                        flag = true;
                    }
                    break;
                }
            }
            if (!flag) {
                //将筛选的商品添加到数组中
                BeanUtil.copyProperties(goodsEntity, detailEntity);
                detailEntity.setId(null);
                detailEntity.setSaleListDetailPrice(goodsEntity.getGoodsSalePrice());
                detailEntity.setSaleListDetailCount(BigDecimal.ONE);
                detailEntity.setSaleListUnique(saleListUnique);
                detailEntity.setCreateTime(DateUtil.date());
                detailEntities.add(detailEntity);
            }
            return detailEntities;
        } else {
            //如果是多个商品，则递归调用
            //从商品列表里随机筛选一个金额小于商品总额的商品
            BigDecimal tempSaleListTotal = saleListTotal;
            List<BusGoodsEntity> smallGoodsList = goodsList.stream().filter(v -> {
                if (v.getGoodsSalePrice().compareTo(tempSaleListTotal) <= 0) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());

            //从商品列表里随机选择一个商品
            if (ObjectUtil.isNotEmpty(smallGoodsList)) {
                Integer index = RandomUtil.randomInt(smallGoodsList.size());
                goodsEntity = smallGoodsList.get(index);
            }else {
                goodsEntity = null;
                log.info("没有适合的产品信息{}", goodsEntity);
            }
        }

        log.info("当前选择的商品为goodsEntity:{}",goodsEntity);

        if (ObjectUtil.isNull(goodsEntity)) {
            return detailEntities;
        }
        //如果存在相同的商品，则修改数量，如果没有，则增加新记录
        Boolean flag = false;
        String goodsBarcode = goodsEntity.getGoodsBarcode();
        for (BusSaleListDetailEntity d : detailEntities) {
            if (goodsBarcode.equals(d.getGoodsBarcode())) {
                d.setSaleListDetailCount(d.getSaleListDetailCount().add(BigDecimal.ONE));
                saleListTotal = saleListTotal.subtract(goodsEntity.getGoodsSalePrice());
                flag = true;
                break;
            }
        }
        //将数据添加到返回数组中
        if (!flag) {
            //将筛选的商品添加到数组中
            BeanUtil.copyProperties(goodsEntity, detailEntity);
            detailEntity.setId(null);
            detailEntity.setSaleListDetailPrice(goodsEntity.getGoodsSalePrice());
            detailEntity.setSaleListDetailCount(BigDecimal.ONE);
            detailEntity.setSaleListUnique(saleListUnique);
            detailEntity.setCreateTime(DateUtil.date());
            detailEntities.add(detailEntity);
            saleListTotal = saleListTotal.subtract(goodsEntity.getGoodsSalePrice());
        }
        return chooseGoodsMsgByPrice(detailEntities, saleListTotal, goodsList , saleListUnique,isLast);
    }


    /**
     * 正常的逻辑
     * @param createNewOrderParams
     * @param saleListUnique
     * @return
     */
    public List<BusSaleListDetailEntity> queryGoodsListForOrder(CreateNewOrderParams createNewOrderParams, String saleListUnique) {
        //根据账号信息查询对应的供应商信息
        LambdaQueryWrapper<BusShopEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BusShopEntity::getSubAccount, createNewOrderParams.getFrmcod());

        List<BusSaleListDetailEntity> detailEntities = new ArrayList<>();

        BusShopEntity shopEntity = busShopMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(shopEntity)) {
            //店铺信息不存在，返回错误
            return detailEntities;
        }
        //店铺信息存在，查询店铺内满足条件的商品列表
        LambdaQueryWrapper<BusGoodsEntity> goodsQueryWrapper = new LambdaQueryWrapper<>();
        goodsQueryWrapper.lt(BusGoodsEntity::getGoodsSalePrice, createNewOrderParams.getPayAmount());
        goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());

        List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
        if (ObjectUtil.isEmpty(goodsList)) {
            goodsQueryWrapper.clear();
            goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());
            goodsList = busGoodsMapper.selectList(goodsQueryWrapper);
        }

        if (ObjectUtil.isEmpty(goodsList)) {
            return detailEntities;
        }

        BigDecimal saleListTotal = createNewOrderParams.getPayAmount();

        detailEntities = chooseGoodsMsg(saleListTotal, goodsList, 2, saleListUnique);

        return detailEntities;
    }

    public static List<BusSaleListDetailEntity> chooseGoodsMsg(BigDecimal saleListTotal, List<BusGoodsEntity> goodsList , Integer count, String saleListUnique) {
        log.info("当前订单剩余金额{}" ,saleListTotal);
        List<BusSaleListDetailEntity> detailEntities = new ArrayList<>();
        if (count == 2) {
            log.info("当前所需商品数量{}" ,count);
            /**
             * 选两个商品
             * 随机选择，如果成功匹配整数，则使用两个商品，否则，尝试一个
             */
            Integer listSize = goodsList.size();
            if (listSize < 2) {
                return chooseGoodsMsg(saleListTotal, goodsList, 1, saleListUnique);
            }
            Random random = new Random();
            Integer index1 = random.nextInt(listSize);
            Integer index2 = random.nextInt(listSize);

            BusGoodsEntity goods1 = goodsList.get(index1);
            BusGoodsEntity goods2 = goodsList.get(index2);
            BigDecimal balance = saleListTotal;

            for (int i = 1; i < saleListTotal.divide(goods1.getGoodsSalePrice(),BigDecimal.ROUND_HALF_DOWN).intValue(); i++) {
                balance = balance.subtract(goods1.getGoodsSalePrice().multiply(new BigDecimal(i)));
                if (balance.doubleValue() % goods2.getGoodsSalePrice().doubleValue() == 0) {
                    BusSaleListDetailEntity detail1 = new BusSaleListDetailEntity();
                    BusSaleListDetailEntity detail2 = new BusSaleListDetailEntity();

                    BeanUtil.copyProperties(goods1, detail1);
                    BeanUtil.copyProperties(goods2, detail2);
                    detail1.setId(null);
                    detail2.setId(null);

                    detail1.setSaleListDetailPrice(goods1.getGoodsSalePrice());
                    detail2.setSaleListDetailPrice(goods2.getGoodsSalePrice());
                    detail1.setSaleListDetailCount(new BigDecimal(i));
                    detail2.setSaleListDetailCount(balance.divide(goods2.getGoodsSalePrice(), 2 , BigDecimal.ROUND_HALF_UP));
                    detail1.setSaleListDetailSubtotal(saleListTotal.subtract(balance).setScale(2,BigDecimal.ROUND_HALF_UP));
                    detail2.setSaleListDetailSubtotal(balance);
                    detail1.setSaleListUnique(saleListUnique);
                    detail2.setSaleListUnique(saleListUnique);
                    detail1.setCreateTime(DateUtil.date());
                    detail2.setCreateTime(DateUtil.date());

                    detail1.setId(null);
                    detail2.setId(null);

                    detailEntities.add(detail1);
                    detailEntities.add(detail2);
                    break;
                }
            }

        } else if (count == 1) {
            log.info("当前所需商品数量{}" ,count);
            for (BusGoodsEntity goods : goodsList) {
                if (saleListTotal.doubleValue() % goods.getGoodsSalePrice().doubleValue() == 0) {
                    BusSaleListDetailEntity detailEntity = new BusSaleListDetailEntity();
                    BeanUtil.copyProperties(goods, detailEntity);
                    detailEntity.setId(null);
                    detailEntity.setSaleListDetailSubtotal(saleListTotal);
                    log.info("当前商品单价{}" ,goods.getGoodsSalePrice());
                    log.info("当前商品数量{}" ,saleListTotal.divide(goods.getGoodsSalePrice(),4,BigDecimal.ROUND_HALF_UP));
                    detailEntity.setSaleListDetailCount(saleListTotal.divide(goods.getGoodsSalePrice(),4,BigDecimal.ROUND_HALF_UP));
                    detailEntity.setSaleListDetailPrice(goods.getGoodsSalePrice());
                    detailEntity.setSaleListUnique(saleListUnique);
                    detailEntity.setCreateTime(DateUtil.date());

                    detailEntity.setId(null);

                    detailEntities.add(detailEntity);
                    break;
                }
            }
        } else if (count == 0) {
            //随机取一个商品返回，数量为小数
            Integer listSize = goodsList.size();
            Random random = new Random();
            Integer index = random.nextInt(listSize);
            BusGoodsEntity goods = goodsList.get(index);
            BigDecimal detailCount = saleListTotal.divide(goods.getGoodsSalePrice(),4, BigDecimal.ROUND_HALF_DOWN);
            if (detailCount == BigDecimal.ZERO) {
                //数量不能小于0
                detailCount = BigDecimal.ONE;
            }
            BigDecimal goodsInPrice = saleListTotal.divide(detailCount,2,BigDecimal.ROUND_HALF_UP);
            goods.setGoodsInPrice(goodsInPrice);
            BusSaleListDetailEntity detailEntity = new BusSaleListDetailEntity();
            BeanUtil.copyProperties(goods, detailEntity);
            detailEntity.setId(null);
            detailEntity.setSaleListDetailPrice(goods.getGoodsSalePrice());
            detailEntity.setSaleListDetailCount(detailCount);
            detailEntity.setSaleListDetailSubtotal(saleListTotal);
            detailEntity.setSaleListUnique(saleListUnique);
            detailEntity.setCreateTime(DateUtil.date());
            detailEntity.setId(null);
            detailEntities.add(detailEntity);
        } else if (count < 0) {
            return detailEntities;
        }

        if (ObjectUtil.isEmpty(detailEntities)) {
            return chooseGoodsMsg(saleListTotal, goodsList, --count, saleListUnique);
        }

        return detailEntities;
    }

    private List<String> getSettledStatus() {
        List<String> settledStatus = new ArrayList<>();
        settledStatus.add(CmbTransferStatusEnum.YTJ.getStatus());
        settledStatus.add(CmbTransferStatusEnum.AUT.getStatus());
        settledStatus.add(CmbTransferStatusEnum.NTE.getStatus());
        settledStatus.add(CmbTransferStatusEnum.BNK.getStatus());
        settledStatus.add(CmbTransferStatusEnum.FIN.getStatus());
        settledStatus.add(CmbTransferStatusEnum.OPR.getStatus());
        settledStatus.add(CmbTransferStatusEnum.APW.getStatus());
        settledStatus.add(CmbTransferStatusEnum.WRF.getStatus());

        return settledStatus;
    }

    /**
     * 更新订单监控状态为结算成功或失败
     * @param billDetail
     */
    private void updateSaleListMonitor(BusShopBillDetailEntity billDetail) {
        try {
            List<BusShopSaleListMonitorEntity> updateList = new ArrayList<>();
            LambdaQueryWrapper<BusShopSaleListMonitorEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusShopSaleListMonitorEntity::getShopUnique, billDetail.getShopUnique());
            queryWrapper.in(BusShopSaleListMonitorEntity::getSettledStatus, SaleListMonitorSettledStatusEnum.SETTLEING.getValue());
            queryWrapper.eq(BusShopSaleListMonitorEntity::getBillNo, billDetail.getBillNo());
            List<BusShopSaleListMonitorEntity> list = busShopSaleListMonitorMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(list)) {
                BigDecimal settledAmount = list.stream().map(BusShopSaleListMonitorEntity::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (billDetail.getSettledAmount().compareTo(settledAmount) != 0) {
                    log.error("商户【" + billDetail.getShopUnique() + "】的结算金额【" + billDetail.getSettledAmount() + "】与订单监控中的金额【" + settledAmount + "】不一致");
                }
                for (BusShopSaleListMonitorEntity monitor : list) {
                    if (ObjectUtil.equals(CmbTransferStatusEnum.FINS.getStatus(), billDetail.getSettledStatus())) {
                        monitor.setSettledStatus(SaleListMonitorSettledStatusEnum.SETTLED_SUCCESS.getValue());
                    } else if (ObjectUtil.equals(CmbTransferStatusEnum.FINF.getStatus(), billDetail.getSettledStatus())
                            || ObjectUtil.equals(CmbTransferStatusEnum.FINB.getStatus(), billDetail.getSettledStatus())) {
                        monitor.setSettledStatus(SaleListMonitorSettledStatusEnum.SETTLED_FAIL.getValue());
                    }
                    monitor.setModifyUser(billDetail.getModifyUser());
                    monitor.setModifyTime(new Date());
                    updateList.add(monitor);
                }
            } else {
                log.error("未找到商户【" + billDetail.getShopUnique() + "】-【" + billDetail.getBillNo() + "】的订单监控信息");
            }
            if (ObjectUtil.isNotEmpty(updateList)) {
                busShopSaleListMonitorMapper.updateBatchById(updateList);
            }
        } catch (Exception e) {
            log.error("更新订单监控结算状态异常", e);
        }
    }
}
