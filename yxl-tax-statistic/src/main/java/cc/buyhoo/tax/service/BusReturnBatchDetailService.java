package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnBatchDetail.ReturnBatchDetailListParams;
import cc.buyhoo.tax.result.returnBatchDetail.ReturnBatchDetailListResult;

public interface BusReturnBatchDetailService {

    /**
     * 批次详情
     * @param params
     * @return
     */
    public Result<ReturnBatchDetailListResult> pageList(ReturnBatchDetailListParams params);

}
