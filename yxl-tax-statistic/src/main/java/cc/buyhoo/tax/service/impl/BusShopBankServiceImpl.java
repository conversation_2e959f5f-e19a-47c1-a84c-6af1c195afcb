package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusShopBankMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.dao.SysBankListMapper;
import cc.buyhoo.tax.entity.BusShopBankEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.SysBankListEntity;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.params.busShopBank.BusShopBankAddParams;
import cc.buyhoo.tax.params.busShopBank.BusShopBankUpdateParams;
import cc.buyhoo.tax.params.busShopBank.ShopBankListParam;
import cc.buyhoo.tax.result.busShopBank.BusShopBankDto;
import cc.buyhoo.tax.result.busShopBank.BusShopBankListResult;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopBankService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 供货商银行卡管理
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 08:50
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class BusShopBankServiceImpl extends BaseService implements BusShopBankService {

    private final BusShopBankMapper busShopBankMapper;
    private final SysBankListMapper sysBankListMapper;
    private final BusShopMapper busShopMapper;

    @Override
    public Result<List<BusShopBankDto>> list(ShopBankListParam param) {
        LambdaQueryWrapper<BusShopBankEntity> queryWrapper = Wrappers.lambdaQuery(BusShopBankEntity.class)
                .eq(BusShopBankEntity::getShopUnique, param.getShopUnique())
                .eq(ObjectUtil.isNotNull(param.getEnableStatus()), BusShopBankEntity::getEnableStatus, param.getEnableStatus())
                .orderByDesc(BusShopBankEntity::getCreateTime);
        List<BusShopBankEntity> list = busShopBankMapper.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            List<SysBankListEntity> bankList = sysBankListMapper.selectList(Wrappers.lambdaQuery(SysBankListEntity.class).select(SysBankListEntity::getId, SysBankListEntity::getBankName));
            Map<Long, String> bankMap = bankList.stream().collect(Collectors.toMap(SysBankListEntity::getId, SysBankListEntity::getBankName));
            List<BusShopBankDto> dtoList = list.stream().map(entity -> {
                BusShopBankDto dto = new BusShopBankDto();
                BeanUtil.copyProperties(entity, dto);
                dto.setHeadBankName(bankMap.get(entity.getBankId()));
                return dto;
            }).collect(Collectors.toList());
           return Result.ok(dtoList);
        }
        return Result.ok(Collections.EMPTY_LIST);
    }

    @Override
    @Transactional
    public Result<String> addShopBank(BusShopBankAddParams param) {
        if (ObjectUtil.equal(EnableStatusEnum.AVAILABLE.getCode(), param.getEnableStatus())) {
            Long count = busShopBankMapper.selectCount(Wrappers.lambdaQuery(BusShopBankEntity.class)
                    .eq(BusShopBankEntity::getShopUnique, param.getShopUnique())
                    .eq(BusShopBankEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode()));
            if (count > 0) {
                return Result.fail(CommonErrorEnum.PARAM_ERROR, "该供货商已有有效银行卡记录");
            }
            busShopMapper.update(null, Wrappers.lambdaUpdate(BusShopEntity.class)
                    .set(BusShopEntity::getBankName, param.getBankName()).set(BusShopEntity::getBankCity, param.getBankCity())
                    .set(BusShopEntity::getBankCard, param.getBankCard()).set(BusShopEntity::getLegalPerson, param.getLegalPerson())
                    .set(BusShopEntity::getLegalPhone, param.getLegalPhone()).eq(BusShopEntity::getShopUnique, param.getShopUnique()));
        }
        BusShopBankEntity entity = new BusShopBankEntity();
        BeanUtil.copyProperties(param, entity);
        entity.setCreateUser(SatokenUtil.getLoginUserId());
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        int flag = busShopBankMapper.insert(entity);
        return flag > 0 ? Result.ok() : Result.fail();
    }

    @Override
    public Result<String> updateShopBank(BusShopBankUpdateParams param) {
        if (ObjectUtil.equal(EnableStatusEnum.AVAILABLE.getCode(), param.getEnableStatus())) {
            Long count = busShopBankMapper.selectCount(Wrappers.lambdaQuery(BusShopBankEntity.class)
                    .eq(BusShopBankEntity::getShopUnique, param.getShopUnique())
                    .ne(BusShopBankEntity::getId, param.getId())
                    .eq(BusShopBankEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode()));
            if (count > 0) {
                return Result.fail(CommonErrorEnum.PARAM_ERROR, "该供货商已有有效银行卡记录");
            }
            busShopMapper.update(null, Wrappers.lambdaUpdate(BusShopEntity.class)
                    .set(BusShopEntity::getBankName, param.getBankName()).set(BusShopEntity::getBankCity, param.getBankCity())
                    .set(BusShopEntity::getBankCard, param.getBankCard()).set(BusShopEntity::getLegalPerson, param.getLegalPerson())
                    .set(BusShopEntity::getLegalPhone, param.getLegalPhone()).eq(BusShopEntity::getShopUnique, param.getShopUnique()));
        }
        BusShopBankEntity entity = new BusShopBankEntity();
        BeanUtil.copyProperties(param, entity);
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        int flag = busShopBankMapper.updateById(entity);
        return flag > 0 ? Result.ok() : Result.fail();
    }

    @Override
    public Result<String> deleteShopBank(Long id) {
        int flag = busShopBankMapper.deleteById(id);
        return flag > 0 ? Result.ok() : Result.fail();
    }

    @Override
    public Result<BusShopBankDto> selectById(Long id) {
        BusShopBankEntity entity = busShopBankMapper.selectById(id);
        if (ObjectUtil.isNotNull(entity)) {
            BusShopBankDto dto = new BusShopBankDto();
            BeanUtil.copyProperties(entity, dto);
            return Result.ok(dto);
        }
        return Result.fail();
    }
}
