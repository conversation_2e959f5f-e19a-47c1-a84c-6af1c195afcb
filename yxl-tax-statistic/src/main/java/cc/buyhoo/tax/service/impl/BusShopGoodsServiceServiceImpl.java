package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusShopInvoiceGoodsServiceMapper;
import cc.buyhoo.tax.entity.BusShopInvoiceGoodsServiceEntity;
import cc.buyhoo.tax.params.busInvoiceGoodsService.InvoiceGoodsServiceParams;
import cc.buyhoo.tax.params.busInvoiceGoodsService.InvoiceGoodsServiceSaveParams;
import cc.buyhoo.tax.result.busInvoiceGoodsService.InvoiceGoodsServiceDto;
import cc.buyhoo.tax.result.busInvoiceGoodsService.InvoiceGoodsServiceResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopGoodsServiceService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 开票服务
 * @ClassName BusShopGoodsServiceServiceImpl
 * <AUTHOR>
 * @Date 2023/9/6 14:18
 **/
@Service
public class BusShopGoodsServiceServiceImpl extends BaseService implements BusShopGoodsServiceService {
    @Resource
    private BusShopInvoiceGoodsServiceMapper busShopInvoiceGoodsServiceMapper;

    @Override
    public Result<InvoiceGoodsServiceResult> pageList(InvoiceGoodsServiceParams params) {
        PageUtils.startPage(params);
        LambdaQueryWrapper<BusShopInvoiceGoodsServiceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopInvoiceGoodsServiceEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (StrUtil.isNotBlank(params.getGoodsName())) {
            queryWrapper.like(BusShopInvoiceGoodsServiceEntity::getGoodsName, params.getGoodsName());
        }
        if (StrUtil.isNotBlank(params.getCategoryNo())) {
            queryWrapper.like(BusShopInvoiceGoodsServiceEntity::getCategoryNo, params.getCategoryNo());
        }
        if (StrUtil.isNotBlank(params.getGoodsSpec())) {
            queryWrapper.like(BusShopInvoiceGoodsServiceEntity::getGoodsSpec, params.getGoodsSpec());
        }
        queryWrapper.orderByDesc(BusShopInvoiceGoodsServiceEntity::getCreateTime);
        List<BusShopInvoiceGoodsServiceEntity> list = busShopInvoiceGoodsServiceMapper.selectList(queryWrapper);
        List<InvoiceGoodsServiceDto> dtoList = list.stream().map(v -> {
            InvoiceGoodsServiceDto dto = new InvoiceGoodsServiceDto();
            BeanUtils.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());
        return convertPageData(list, dtoList, InvoiceGoodsServiceResult.class, params);
    }

    @Override
    public Result<Void> save(InvoiceGoodsServiceSaveParams params) {
        BusShopInvoiceGoodsServiceEntity entity;
        int n;
        if (ObjectUtil.isNull(params.getId())) {
            entity = new BusShopInvoiceGoodsServiceEntity();
            BeanUtils.copyProperties(params, entity);
            entity.setCreateUser(SatokenUtil.getLoginUserId());
            entity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
            n = busShopInvoiceGoodsServiceMapper.insert(entity);
        } else {
            entity = busShopInvoiceGoodsServiceMapper.selectById(params.getId());
            if (ObjectUtil.isEmpty(entity)) {
                return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST, "记录不存在");
            }
            entity.setCategoryNo(params.getCategoryNo());
            entity.setGoodsName(params.getGoodsName());
            entity.setTaxItem(params.getTaxItem());
            entity.setGoodsSpec(params.getGoodsSpec());
            entity.setGoodsUnit(params.getGoodsUnit());
            entity.setTaxRate(params.getTaxRate());
            entity.setEnableStatus(params.getEnableStatus());
            entity.setModifyUser(SatokenUtil.getLoginUserId());
            entity.setModifyTime(DateUtil.date());
            n = busShopInvoiceGoodsServiceMapper.updateById(entity);
        }
        if (n > 0) {
            if (ObjectUtil.equals(EnableStatusEnum.AVAILABLE.getCode(), params.getEnableStatus())) {
                LambdaQueryWrapper<BusShopInvoiceGoodsServiceEntity> queryWrapper = new LambdaQueryWrapper<BusShopInvoiceGoodsServiceEntity>().eq(BusShopInvoiceGoodsServiceEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId()).eq(BusShopInvoiceGoodsServiceEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode()).ne(BusShopInvoiceGoodsServiceEntity::getId, entity.getId());
                List<BusShopInvoiceGoodsServiceEntity> list = busShopInvoiceGoodsServiceMapper.selectList(queryWrapper);
                if (CollUtil.isNotEmpty(list) && list.size() > 0) {
                    list.forEach(v -> {
                        v.setEnableStatus(EnableStatusEnum.UNAVAILABLE.getCode());
                        v.setModifyUser(SatokenUtil.getLoginUserId());
                    });
                    busShopInvoiceGoodsServiceMapper.updateBatchById(list);
                }
            }
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result<Void> delete(List<Long> ids) {
        busShopInvoiceGoodsServiceMapper.deleteBatchIds(ids);
        return Result.ok();
    }

    @Override
    public Result<Void> enable(InvoiceGoodsServiceSaveParams params) {
        if (ObjectUtil.isNull(params.getId()) || ObjectUtil.isNull(params.getEnableStatus())) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_EMPTY, "记录ID或者启停用值不能为空");
        }
        BusShopInvoiceGoodsServiceEntity entity = busShopInvoiceGoodsServiceMapper.selectById(params.getId());
        if (ObjectUtil.isNull(entity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST, "记录不存在");
        }
        if (ObjectUtil.equals(EnableStatusEnum.AVAILABLE.getCode(), params.getEnableStatus())) {
            List<BusShopInvoiceGoodsServiceEntity> list = busShopInvoiceGoodsServiceMapper.selectList(new LambdaQueryWrapper<BusShopInvoiceGoodsServiceEntity>().eq(BusShopInvoiceGoodsServiceEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId()).eq(BusShopInvoiceGoodsServiceEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode()).ne(BusShopInvoiceGoodsServiceEntity::getId, params.getId()));
            if (CollUtil.isNotEmpty(list) && list.size() > 0) {
                list.forEach(v -> {
                    v.setEnableStatus(EnableStatusEnum.UNAVAILABLE.getCode());
                    v.setModifyUser(SatokenUtil.getLoginUserId());
                });
                busShopInvoiceGoodsServiceMapper.updateBatchById(list);
            }
            entity.setEnableStatus(EnableStatusEnum.AVAILABLE.getCode());
        } else {
            entity.setEnableStatus(EnableStatusEnum.UNAVAILABLE.getCode());
        }
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        entity.setModifyTime(DateUtil.date());
        busShopInvoiceGoodsServiceMapper.updateById(entity);
        return Result.ok();
    }
}
