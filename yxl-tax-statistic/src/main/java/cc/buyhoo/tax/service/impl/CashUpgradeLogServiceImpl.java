package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.service.CashUpgradeLogService;
import cc.buyhoo.upgrade.UpgradeLogFacade;
import cc.buyhoo.upgrade.params.cashUpgradeLog.UpgradeLogListParams;
import cc.buyhoo.upgrade.result.cashUpgradeLog.UpgradeLogListResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
public class CashUpgradeLogServiceImpl implements CashUpgradeLogService {

    @DubboReference
    private UpgradeLogFacade upgradeLogFacade;

    /**
     * 纪录列表
     *
     * @param params
     * @return
     */
    @Override
    public Result<UpgradeLogListResult> logList(UpgradeLogListParams params) {
        return upgradeLogFacade.logList(params);
    }
}
