package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusReturnBatchDetailMapper;
import cc.buyhoo.tax.entity.BusReturnBatchDetailEntity;
import cc.buyhoo.tax.params.returnBatchDetail.ReturnBatchDetailListParams;
import cc.buyhoo.tax.result.returnBatchDetail.ReturnBatchDetailListDto;
import cc.buyhoo.tax.result.returnBatchDetail.ReturnBatchDetailListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusReturnBatchDetailService;
import cc.buyhoo.tax.util.SatokenUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BusReturnBatchDetailServiceImpl extends BaseService implements BusReturnBatchDetailService {

    private final BusReturnBatchDetailMapper busReturnBatchDetailMapper;

    /**
     * 批次详情
     * @param params
     * @return
     */
    @Override
    public Result<ReturnBatchDetailListResult> pageList(ReturnBatchDetailListParams params) {
        LambdaQueryWrapper<BusReturnBatchDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(BusReturnBatchDetailEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        detailWrapper.eq(BusReturnBatchDetailEntity::getBatchId,params.getBatchId());
        PageUtils.startPage(params);
        List<BusReturnBatchDetailEntity> detailList = busReturnBatchDetailMapper.selectList(detailWrapper);

        List<ReturnBatchDetailListDto> detoList = BeanUtils.copyList(detailList,ReturnBatchDetailListDto.class);

        return convertPageData(detailList,detoList,ReturnBatchDetailListResult.class,params);
    }
}
