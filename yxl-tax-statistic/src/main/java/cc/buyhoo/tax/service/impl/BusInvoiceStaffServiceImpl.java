package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.StringUtils;
import cc.buyhoo.tax.dao.BusInvoiceStaffMapper;
import cc.buyhoo.tax.dao.BusShopInvoiceSettingMapper;
import cc.buyhoo.tax.entity.BusInvoiceStaffEntity;
import cc.buyhoo.tax.entity.BusShopInvoiceSettingEntity;
import cc.buyhoo.tax.entity.invoice.*;
import cc.buyhoo.tax.enums.IsLoginEnum;
import cc.buyhoo.tax.enums.MethodNameEnum;
import cc.buyhoo.tax.enums.SysUserErrorEnum;
import cc.buyhoo.tax.enums.ValidTypeEnum;
import cc.buyhoo.tax.params.invoice.*;
import cc.buyhoo.tax.result.invoice.QueryInvoiceStaffListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusInvoiceStaffService;
import cc.buyhoo.tax.util.InvoiceUtil;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class BusInvoiceStaffServiceImpl extends BaseService implements BusInvoiceStaffService {
    private final BusInvoiceStaffMapper busInvoiceStaffMapper;
    private final BusShopInvoiceSettingMapper busShopInvoiceSettingMapper;

    /**
     * 获取员工实名认证状态
     * @param params
     * @return
     */
    public Result<RpaAuthStatusData> rpaAuthStatus(RpaAuthStatusParams params) {
        BusInvoiceStaffEntity entity = busInvoiceStaffMapper.selectById(params.getId());
        if (entity == null) {
            return Result.fail(SysUserErrorEnum.USERNAME_REPEAT_ERROR,"用户信息不存在");
        }

        //获取企业税号
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, entity.getCompanyId());
        //获取企业税号
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(queryWrapper);

        RpaAuthStatusEntity rpaAuthStatusEntity = new RpaAuthStatusEntity();
        rpaAuthStatusEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
        rpaAuthStatusEntity.setRzId(params.getRzId());
        rpaAuthStatusEntity.setElectricAccount(entity.getStaffAccount());

        RpaAuthStatusResult  res = InvoiceUtil.rpaAuthStatus(rpaAuthStatusEntity);

        //如果认证成功，需要修改会员认证状态
        if (res.getData().getAuthStatus().equals("2")) {
            entity.setAuthStatus(1);
            busInvoiceStaffMapper.updateById(entity);
        }


        return Result.ok(res.getData());
    }

    /**
     * 获取实名认证二维码
     * @param params
     * @return
     */
    public Result<RpaQrCodeData> rpaQrCode(RpaQrCodeParams params) {
        BusInvoiceStaffEntity entity = busInvoiceStaffMapper.selectById(params.getId());
        if (entity == null) {
            return Result.fail(SysUserErrorEnum.USERNAME_REPEAT_ERROR,"用户信息不存在");
        }

        //获取企业税号
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, entity.getCompanyId());
        //获取企业税号
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(queryWrapper);

        if (null == settingEntity || settingEntity.getCompanyTaxNo() == null) {
            return Result.fail(SysUserErrorEnum.USERNAME_REPEAT_ERROR,"企业未配置开票信息");
        }

        RpaQrCodeEntity rpaQrCodeEntity = new RpaQrCodeEntity();
        rpaQrCodeEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
        rpaQrCodeEntity.setElectricAccount(entity.getStaffAccount());

        RpaQrCodeResult res = InvoiceUtil.rpaQrCode(rpaQrCodeEntity);

        //需要记录认证ID对应的员工信息，认证完成后修改员工认证状态

        entity.setRzId(res.getData().getRzId());
        busInvoiceStaffMapper.updateById(entity);

        return Result.ok(res.getData());
    }
    /**
     * 登录全电账号
     * 所有登录步骤都走一个接口
     *
     * @param params
     * @return
     */
    public Result<EleUserLoginData> eleUserLogin(EleUserLoginParams params) {
        //获取用户信息

        BusInvoiceStaffEntity entity = busInvoiceStaffMapper.selectById(params.getId());
        if (entity == null) {
            return Result.fail(SysUserErrorEnum.USERNAME_REPEAT_ERROR,"用户信息不存在");
        }

        //获取税号信息
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, entity.getCompanyId());
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(wrapper);
        if (settingEntity == null) {
            return Result.fail(SysUserErrorEnum.EXIST_CANNOT_DEL_ERROR,"未配置开票信息");
        }
        EleUserLoginEntity eleUserLoginEntity = new EleUserLoginEntity();
        eleUserLoginEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
        eleUserLoginEntity.setElectricAccount(entity.getStaffAccount());
        eleUserLoginEntity.setElectricPassword(entity.getStaffPwd());
        eleUserLoginEntity.setDutyType(entity.getDutyType());

        if (ObjectUtil.isNotEmpty(params.getSmsCode())) {
            eleUserLoginEntity.setSmsCode(params.getSmsCode());
            eleUserLoginEntity.setMethodName(MethodNameEnum.SMS_LOGIN.getValue());
        } else {
            //所有的操作都先验证一次是否已登录，如果已经登录，后续的所有操作都可以跳过
            eleUserLoginEntity.setMethodName(MethodNameEnum.LOGIN.getValue());
        }

        EleUserLoginData resultData = null;
        EleUserLoginResult loginResult = InvoiceUtil.eleUserLogin(eleUserLoginEntity);

        if (loginResult.getCode() == 200) {
            resultData = loginResult.getData();
            if (resultData.getHXFF() == 0) {
                //将员工设置为登录状态，记录登录时间
                entity.setIsLogin(IsLoginEnum.YES.getValue());
                busInvoiceStaffMapper.updateById(entity);
                //已经登录直接返回
                return Result.ok(resultData);
            } else if (resultData.getHXFF() == 3 || StringUtils.isNotEmpty(params.getSmsCode())) {
                //需要短信验证
                if (null == params.getSmsCode()) {
                    return Result.fail(SysUserErrorEnum.OLD_PWD_ERROR,"请输入短信验证码");
                }
                //短信登录
                eleUserLoginEntity.setMethodName(MethodNameEnum.SMS_LOGIN.getValue());
                eleUserLoginEntity.setSmsCode(params.getSmsCode());
                loginResult = InvoiceUtil.eleUserLogin(eleUserLoginEntity);
                resultData = loginResult.getData();

                if (loginResult.getCode() == 200) {
                    if (resultData.getHXFF() == 2) {
                        //此处可能直接登录成功，不需要再选择责任人
                        eleUserLoginEntity.setMethodName(MethodNameEnum.SELECT_ZRRLX.getValue());
                        loginResult = InvoiceUtil.eleUserLogin(eleUserLoginEntity);
                        resultData = loginResult.getData();
                    }
                    //如果是登录成功，直接返回
                } else {
                    return Result.fail(SysUserErrorEnum.OLD_PWD_ERROR, loginResult.getMsg());
                }
            } else if (resultData.getHXFF() == 2) {
                //需要选择责任人
                eleUserLoginEntity.setMethodName(MethodNameEnum.SELECT_ZRRLX.getValue());
                loginResult = InvoiceUtil.eleUserLogin(eleUserLoginEntity);
                resultData = loginResult.getData();
            } else if (resultData.getHXFF() == 1) {
                //需要获取验证码
                //第一次登录，主动发送验证码
                eleUserLoginEntity.setMethodName(MethodNameEnum.SENDSMS.getValue());
                loginResult = InvoiceUtil.eleUserLogin(eleUserLoginEntity);
                resultData = loginResult.getData();
                //此处需要根据code继续验证吗？
            }
        }
        return Result.ok(resultData);
    }
    /**
     * 更新或删除员工信息
     * @param params
     * @return
     */
    public Result<Void> updateInvoiceStaff(UpdateInvoiceStaffParams params) {
        BusInvoiceStaffEntity entity = new BusInvoiceStaffEntity();
        BeanUtils.copyProperties(params, entity);
        Integer count = busInvoiceStaffMapper.updateById(entity);
        if (count == 0) {
            return Result.fail(SysUserErrorEnum.USERNAME_REPEAT_ERROR,"用户信息不存在");
        }
        return Result.ok();
    }

    /**
     * 添加新的员工信息
     * @param params
     * @return
     */
    public Result<BusInvoiceStaffEntity> saveInvoiceStaff(SaveInvoiceStaffParams params) {
        //校验用户信息是否已存在
        LambdaQueryWrapper<BusInvoiceStaffEntity> wrapper = new LambdaQueryWrapper<BusInvoiceStaffEntity>();
        wrapper.eq(BusInvoiceStaffEntity::getStaffAccount, params.getStaffAccount());

        BusInvoiceStaffEntity entity = busInvoiceStaffMapper.selectOne(wrapper);
        if (entity != null) {
            return Result.fail(SysUserErrorEnum.USERNAME_REPEAT_ERROR,"用户账号已存在");
        }

        BusInvoiceStaffEntity busInvoiceStaffEntity = new BusInvoiceStaffEntity();
        BeanUtils.copyProperties(params, busInvoiceStaffEntity);
        busInvoiceStaffEntity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
        busInvoiceStaffEntity.setValidType(ValidTypeEnum.USER.getValue());

        busInvoiceStaffMapper.insert(busInvoiceStaffEntity);
        BusInvoiceStaffEntity res = busInvoiceStaffMapper.selectOne(wrapper);

        return Result.ok(res);
    }
    /**
     * 获取指定员工的信息
     * @param params
     * @return
     */
    public Result<BusInvoiceStaffEntity> queryInvoiceStaffById(QueryInvoiceStaffByIdParams params) {
        return Result.ok(busInvoiceStaffMapper.selectById(params.getId()));
    }

    /**
     * 获取指定店铺的员工列表信息
     * @param params
     * @return
     */
    public Result<QueryInvoiceStaffListResult> queryInvoiceStaffList(QueryInvoiceStaffListParams params) {
        PageUtils.startPage(params);
        //获取当前店铺的员工列表信息
        LambdaQueryWrapper<BusInvoiceStaffEntity> wrapper = new LambdaQueryWrapper<BusInvoiceStaffEntity>();
        wrapper.eq(BusInvoiceStaffEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        wrapper.eq(BusInvoiceStaffEntity::getValidType, ValidTypeEnum.USER.getValue());

        List<BusInvoiceStaffEntity> list = busInvoiceStaffMapper.selectList(wrapper);

        //员工登录状态需要请求接口获取
        list.stream().forEach(item -> {
            EleUserLoginParams eleUserLoginParams = new EleUserLoginParams();
            eleUserLoginParams.setId(item.getId());
            boolean flag = checkIsLogin(eleUserLoginParams);
            if (flag) {
                item.setIsLogin(IsLoginEnum.YES.getValue());
            } else {
                item.setIsLogin(IsLoginEnum.NO.getValue());
            }
       });

        return convertPageData(list,QueryInvoiceStaffListResult.class,params);
    }

    /**
     * 校验员工是否登录
     * @param params
     */
    public boolean checkIsLogin(EleUserLoginParams params) {
        BusInvoiceStaffEntity entity = busInvoiceStaffMapper.selectById(params.getId());
        if (entity == null) {
            return false;
        }

        //获取税号信息
        LambdaQueryWrapper<BusShopInvoiceSettingEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusShopInvoiceSettingEntity::getCompanyId, entity.getCompanyId());
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(wrapper);
        if (settingEntity == null) {
            return false;
        }
        EleUserLoginEntity eleUserLoginEntity = new EleUserLoginEntity();
        eleUserLoginEntity.setNsrsbh(settingEntity.getCompanyTaxNo());
        eleUserLoginEntity.setElectricAccount(entity.getStaffAccount());
        eleUserLoginEntity.setElectricPassword(entity.getStaffPwd());
        eleUserLoginEntity.setDutyType(entity.getDutyType());

        //所有的操作都先验证一次是否已登录，如果已经登录，后续的所有操作都可以跳过
        eleUserLoginEntity.setMethodName(MethodNameEnum.LOGIN.getValue());
        EleUserLoginData resultData = null;
        EleUserLoginResult loginResult = InvoiceUtil.eleUserLogin(eleUserLoginEntity);

        if (loginResult.getCode() == 200) {
            EleUserLoginData  data = loginResult.getData();
            if (data.getHXFF() == 0) {
                //登录状态
                return true;
            }
       }

        return false;
    }

}
