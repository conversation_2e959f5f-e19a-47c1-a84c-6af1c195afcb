package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.params.busShop.*;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListExportParams;
import cc.buyhoo.tax.result.busShop.BusShopListResult;
import cc.buyhoo.tax.result.busShop.BusShopMigrateInCompanyListQueryResult;
import cc.buyhoo.tax.result.busShop.BusShopSelectDto;
import cc.buyhoo.tax.result.busShop.GetBankInfoResult;
import cc.buyhoo.tax.result.subAccount.CreateSubAccountResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description 供应商管理
 * @ClassName BusShopService
 * <AUTHOR>
 * @Date 2023/7/26 13:54
 **/
public interface BusShopService {
    Result<Void> syncShopData(BusShopEntity shopEntity);

    Result<BusShopListResult> pageList(ShopListParams shopListParams);

    Result<BusShopListResult>  saveShop(ShopParams shopParams);

    Result<BusShopListResult> deleteByIds(List<Long> ids);

    Result<List<BusShopSelectDto>> selectList(ShopListParams shopListParams);

    /**
     * 获取店铺银行信息
     * @param params
     * @return
     */
    Result<GetBankInfoResult> getBankInfo(GetBankInfoParams params);

    Result<BusShopListResult> saveServiceRateBatch(ShopServiceRateParams params);

    void exportShop(ShopListExportParams params, HttpServletResponse response);

    Result<BusShopMigrateInCompanyListQueryResult> queryMigrateInCompanyList(BusShopMigrateInCompanyListQueryParams params);

    Result<Void> companyMigrateIo(ShopCompanyMigrateIoParams params);

    Result<CreateSubAccountResult> addsubAccount(AddSybAccountParams params);

    Result<Void> allAcmGetSubAccount(AddSybAccountParams params);

    Result<Void> allAcmUpdateSubAccount(AddSybAccountParams params);

    /**
     * 根据店铺id获取商户信息
     * @param shopUniques
     * @return
     */
    List<BusShopEntity> selectBusShopByshopUniques(List<String> shopUniques);
}
