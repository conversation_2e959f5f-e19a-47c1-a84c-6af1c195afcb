package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusGoodsCategoryMapper;
import cc.buyhoo.tax.dao.BusReturnOrderCategoryMapper;
import cc.buyhoo.tax.entity.BusGoodsCategoryEntity;
import cc.buyhoo.tax.entity.BusReturnOrderCategoryEntity;
import cc.buyhoo.tax.params.returnOrderCategory.ReturnOrderCategoryParams;
import cc.buyhoo.tax.result.returnOrderCategory.ReturnOrderCategoryDto;
import cc.buyhoo.tax.result.returnOrderCategory.ReturnOrderCategoryListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusReturnOrderCategoryService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BusReturnOrderCategoryServiceImpl extends BaseService implements BusReturnOrderCategoryService {

    private final BusReturnOrderCategoryMapper busReturnOrderCategoryMapper;

    private final BusGoodsCategoryMapper busGoodsCategoryMapper;

    /**
     * 退货单详情
     * @param params
     * @return
     */
    @Override
    public Result<ReturnOrderCategoryListResult> pageList(ReturnOrderCategoryParams params) {
        //查询退货单分类
        LambdaQueryWrapper<BusReturnOrderCategoryEntity> cateWrapper = new LambdaQueryWrapper<>();
        cateWrapper.eq(BusReturnOrderCategoryEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        cateWrapper.eq(BusReturnOrderCategoryEntity::getReturnOrderId,params.getOrderId());
        PageUtils.startPage(params);
        List<BusReturnOrderCategoryEntity> list = busReturnOrderCategoryMapper.selectList(cateWrapper);

        //查询分类
        Map<Long, String> categoryIdNameMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(list)) {
            Set<Long> cateSet = list.stream().map(BusReturnOrderCategoryEntity::getCategoryId).collect(Collectors.toSet());
            cateSet.addAll(list.stream().map(BusReturnOrderCategoryEntity::getCategoryTwoId).collect(Collectors.toSet()));
            List<BusGoodsCategoryEntity> cateList = busGoodsCategoryMapper.selectBatchIds(cateSet);
            categoryIdNameMap = cateList.stream().collect(Collectors.toMap(BusGoodsCategoryEntity::getId, BusGoodsCategoryEntity::getCategoryName));
        }

        //参数构建
        List<ReturnOrderCategoryDto> dtoList = new ArrayList<>();
        for (BusReturnOrderCategoryEntity c : list) {
            ReturnOrderCategoryDto dto = new ReturnOrderCategoryDto();
            BeanUtils.copy(c,dto);
            dto.setCategoryName(categoryIdNameMap.get(c.getCategoryId()));
            dto.setCategoryTwoName(categoryIdNameMap.get(c.getCategoryTwoId()));

            dtoList.add(dto);
        }

        return convertPageData(list,dtoList,ReturnOrderCategoryListResult.class,params);
    }
}
