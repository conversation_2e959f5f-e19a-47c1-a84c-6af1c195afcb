package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnOrderCategory.ReturnOrderCategoryParams;
import cc.buyhoo.tax.result.returnOrderCategory.ReturnOrderCategoryListResult;

public interface BusReturnOrderCategoryService {

    /**
     * 退货单详情
     * @param params
     * @return
     */
    public Result<ReturnOrderCategoryListResult> pageList(ReturnOrderCategoryParams params);

}
