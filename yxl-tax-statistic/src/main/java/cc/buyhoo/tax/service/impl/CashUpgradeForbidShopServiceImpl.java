package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.service.CashUpgradeForbidShopService;
import cc.buyhoo.upgrade.UpgradeForbidShopFacade;
import cc.buyhoo.upgrade.params.cashUpgradeForbidShop.BindUpgradeForbidShopParams;
import cc.buyhoo.upgrade.result.cashUpgradeForbidShop.QueryUpgradeForbidShopResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
public class CashUpgradeForbidShopServiceImpl implements CashUpgradeForbidShopService {

    @DubboReference
    private UpgradeForbidShopFacade shopFacade;

    /**
     * 禁止升级店铺查询
     * @return
     */
    @Override
    public Result<QueryUpgradeForbidShopResult> queryUpgradeForbidShop() {
        return shopFacade.queryUpgradeForbidShop();
    }

    /**
     * 绑定禁止升级店铺
     * @param params
     * @return
     */
    @Override
    public Result<Void> bindUpgradeForbidShop(BindUpgradeForbidShopParams params) {
        return shopFacade.bindUpgradeForbidShop(params);
    }
}
