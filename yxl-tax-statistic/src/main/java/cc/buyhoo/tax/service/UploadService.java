package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.result.busShopInvoice.AnalysisInvoiceDto;
import cc.buyhoo.tax.result.common.UploadFileDto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface UploadService {

    /**
     * 文件上传
     * @param request
     * @return
     */
    Result<UploadFileDto> uploadFile(HttpServletRequest request);

    Result<List<UploadFileDto>> uploadFileList(HttpServletRequest request);

    Result<AnalysisInvoiceDto> analysisInvoice(HttpServletRequest request);
}
