package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysRole.*;
import cc.buyhoo.tax.result.sysRole.RoleDetailResult;
import cc.buyhoo.tax.result.sysRole.SysRolePageResult;

public interface SysRoleService {

    /**
     * 角色管理
     * @param params
     * @return
     */
    public Result<SysRolePageResult> pageList(SysRolePageParams params);

    /**
     * 新增角色
     * @param params
     * @return
     */
    public Result<Void> addRole(SysRoleAddParams params);

    /**
     * 修改角色
     * @param params
     * @return
     */
    public Result<Void> updateRole(SysRoleEditParams params);

    /**
     * 角色详情
     * @param id
     * @return
     */
    public Result<RoleDetailResult> selectById(Long id);

    /**
     * 删除角色
     * @param params
     * @return
     */
    public Result<Void> deleteRole(DeleteRoleParams params);

}
