package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusContractMapper;
import cc.buyhoo.tax.dao.BusSaleListMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusContractEntity;
import cc.buyhoo.tax.entity.BusSaleListEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.enums.BusContractStatusEnum;
import cc.buyhoo.tax.enums.BusContractTypeEnum;
import cc.buyhoo.tax.params.busContract.*;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busContract.BusContractDto;
import cc.buyhoo.tax.result.busContract.BusContractExcel;
import cc.buyhoo.tax.result.busContract.BusContractListResult;
import cc.buyhoo.tax.result.busContract.BusContractNoDto;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.BusContractService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 合同管理
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-07-13
 */
@Service
@AllArgsConstructor
public class BusContractServiceImpl extends BaseService implements BusContractService {

    private final BusContractMapper busContractMapper;
    private final BusShopMapper busShopMapper;
    private final BusSaleListMapper busSaleListMapper;

    @Override
    public Result<BusContractListResult> pageList(BusContractListParams params) {
        PageUtils.startPage(params);
        LambdaQueryWrapper<BusContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusContractEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusContractEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getShopUnique()), BusContractEntity::getShopUnique, params.getShopUnique());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getContractStatus()), BusContractEntity::getContractStatus, params.getContractStatus());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getContractType()), BusContractEntity::getContractType, params.getContractType());
        if (null != params.getSignTime() && ArrayUtil.isNotEmpty(params.getSignTime()) && params.getSignTime().length == 2) {
            queryWrapper.between(BusContractEntity::getSignTime, params.getSignTime()[0], params.getSignTime()[1]);
        }
        queryWrapper.like(StrUtil.isNotBlank(params.getOrderNo()), BusContractEntity::getOrderNo, params.getOrderNo());
        queryWrapper.like(StrUtil.isNotBlank(params.getContractNo()), BusContractEntity::getContractNo, params.getContractNo());
        queryWrapper.like(StrUtil.isNotBlank(params.getContractName()), BusContractEntity::getContractName, params.getContractName());
        queryWrapper.orderByDesc(BusContractEntity::getId);
        List<BusContractEntity> list = busContractMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            List<BusContractDto> dtoList = BeanUtil.copyToList(list, BusContractDto.class);
            return convertPageData(list, dtoList, BusContractListResult.class, params);
        }
        return convertPageData(list, BusContractListResult.class, params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addContract(BusContractAddParams addParams) {
        BusContractEntity entity = new BusContractEntity();
        BeanUtils.copyProperties(addParams, entity);
        if (ArrayUtil.isNotEmpty(addParams.getDateArray()) && addParams.getDateArray().length == 2) {
            entity.setStartTime(DateUtil.parseDate(addParams.getDateArray()[0]));
            entity.setEndTime(DateUtil.parseDate(addParams.getDateArray()[1]));
        }
        entity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
        entity.setCreateUser(SatokenUtil.getLoginUserId());
        dealSaleListContractNo(null, addParams.getContractNo(), addParams.getOrderNo());
        int n = busContractMapper.insert(entity);
        return n > 0 ? Result.ok() : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateContract(BusContractUpdateParams updateParams) {
        BusContractEntity entity = busContractMapper.selectById(updateParams.getId());
        if (ObjectUtil.isNull(entity) || ObjectUtil.notEqual(entity.getCompanyId(), SatokenUtil.getLoginUserCompanyId())) {
            return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
        }
        // 处理订单绑定问题
        dealSaleListContractNo(entity, updateParams.getContractNo(), updateParams.getOrderNo());
        entity.setOrderNo(updateParams.getOrderNo());
        entity.setTotalAmount(updateParams.getTotalAmount());
        entity.setContractStatus(updateParams.getContractStatus());
        entity.setSignTime(updateParams.getSignTime());
        entity.setFileUrl(updateParams.getFileUrl());
        if (ArrayUtil.isNotEmpty(updateParams.getDateArray()) && updateParams.getDateArray().length == 2) {
            entity.setStartTime(DateUtil.parseDate(updateParams.getDateArray()[0]));
            entity.setEndTime(DateUtil.parseDate(updateParams.getDateArray()[1]));
        } else {
            entity.setStartTime(null);
            entity.setEndTime(null);
        }
        entity.setModifyUser(SatokenUtil.getLoginUserId());
        entity.setCompanyId(SatokenUtil.getLoginUserCompanyId());
        int n = busContractMapper.updateById(entity);
        return n > 0 ? Result.ok() : Result.fail();
    }

    /**
     * 处理订单、合同绑定
     * @param entity
     * @param contractNo
     * @param orderNo
     */
    private void dealSaleListContractNo(BusContractEntity entity, String contractNo, String orderNo) {
        Set<String> oldSaleListUniqueList = new HashSet<>();
        if (null != entity && StrUtil.isNotBlank(entity.getContractNo())) {
            List<String> oldList = StrUtil.split(entity.getOrderNo(), ",");
            oldSaleListUniqueList.addAll(oldList);
        }
        Set<String> newSaleListUniqueList = new HashSet<>();
        if (StrUtil.isNotBlank(orderNo)) {
            List<String> newList = StrUtil.split(orderNo, ",");
            newSaleListUniqueList.addAll(newList);
        }
        Set<String> addList = new HashSet<>(),delList = new HashSet<>();
        addList = newSaleListUniqueList.stream().filter(v -> !ArrayUtil.contains(oldSaleListUniqueList.toArray(), v)).collect(Collectors.toSet());
        delList = oldSaleListUniqueList.stream().filter(v -> !ArrayUtil.contains(newSaleListUniqueList.toArray(), v)).collect(Collectors.toSet());

        if (CollectionUtil.isNotEmpty(addList)) {
            LambdaUpdateWrapper<BusSaleListEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BusSaleListEntity::getContractNo, contractNo);
            updateWrapper.in(BusSaleListEntity::getSaleListUnique, addList);
            busSaleListMapper.update(null, updateWrapper);
        }
        if (CollectionUtil.isNotEmpty(delList)) {
            LambdaUpdateWrapper<BusSaleListEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BusSaleListEntity::getContractNo, null);
            updateWrapper.in(BusSaleListEntity::getSaleListUnique, delList);
            busSaleListMapper.update(null, updateWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteContract(DeleteIdsParams idsParams) {

        List<BusContractEntity> list = busContractMapper.selectBatchIds(idsParams.getIds());
        if (CollUtil.isNotEmpty(list)) {
            List<String> orderNoList = new ArrayList<>();
            list.forEach(v -> {
                v.setDelFlag(DelFlagEnum.DELETED.getCode());
                v.setModifyTime(DateUtil.date());
                if (StrUtil.isNotBlank(v.getOrderNo())) {
                    orderNoList.add(v.getContractNo());
                }
            });
            // 删除合同、订单管理
            if (CollectionUtil.isNotEmpty(orderNoList)) {
                busSaleListMapper.update(null, new LambdaUpdateWrapper<BusSaleListEntity>().set(BusSaleListEntity::getContractNo, null).in(BusSaleListEntity::getContractNo, orderNoList));
            }
            boolean flag = busContractMapper.updateBatchById(list);
            return flag ? Result.ok() : Result.fail();
        }
        return Result.fail(SystemErrorEnum.SYSTEM_REQUEST_PARAMS_ID_NOT_EXIST);
    }

    @Override
    public Result<BusContractNoDto> createContractNoAndName(BusContractNoParams params) {
        String prefix = BusContractTypeEnum.getCode(params.getContractType());
        if (StrUtil.isBlank(prefix)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "合同类型不存在");
        }
        LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.eq(BusShopEntity::getShopUnique, params.getShopUnique());
        queryWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.last("limit 1");
        BusShopEntity shopEntity = busShopMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(shopEntity)) {
            return Result.fail(SystemErrorEnum.SYSTEM_ERROR, "合作方不存在");
        }
        StringBuilder cn = new StringBuilder(prefix);
        StringBuilder nn = new StringBuilder(prefix);
        String randomN = DateUtil.format(DateUtil.date(), "yyMMddSSS");
        cn.append("-").append(shopEntity.getShopUnique()).append("-").append(randomN);
        nn.append("-").append(shopEntity.getShopName()).append("-").append(randomN);
        if (StrUtil.equalsAny(prefix, BusContractTypeEnum.XS.getCode(), BusContractTypeEnum.CG.getCode(), BusContractTypeEnum.LY.getCode())) {
            nn.append("-").append(BusContractTypeEnum.getName(params.getContractType()));
        }
        BusContractNoDto dto = new BusContractNoDto();
        dto.setContractNo(cn.toString());
        dto.setContractName(nn.toString());
        return Result.ok(dto);
    }

    @Override
    public Result<BigDecimal> checkOrderNo(BusContractCheckParams checkParams) {
        Set<String> saleListUniqueList = new HashSet<>(Arrays.asList(StrUtil.splitToArray(checkParams.getOrderNo(), ",")));
        if (CollectionUtil.isEmpty(saleListUniqueList)) {
            return Result.ok();
        }
        LambdaQueryWrapper<BusSaleListEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BusSaleListEntity::getSaleListUnique, BusSaleListEntity::getContractNo, BusSaleListEntity::getSaleListActuallyReceived);
        queryWrapper.eq(BusSaleListEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.eq(BusSaleListEntity::getShopUnique, checkParams.getShopUnique());
        queryWrapper.in(BusSaleListEntity::getSaleListUnique, saleListUniqueList);
        List<BusSaleListEntity> list = busSaleListMapper.selectList(queryWrapper);
        Set<String> notHasList = new HashSet<>();
        Set<String> hasBindList = new HashSet<>();
        BigDecimal totalMoney = BigDecimal.ZERO;
        if (CollectionUtil.isEmpty(list)) {
            notHasList = saleListUniqueList;
        } else {
            totalMoney = list.stream().filter(v -> ObjectUtil.isNotNull(v.getSaleListActuallyReceived())).map(BusSaleListEntity::getSaleListActuallyReceived).reduce(BigDecimal.ZERO, BigDecimal::add);
            hasBindList = list.stream().filter(v -> StrUtil.isNotBlank(v.getContractNo())).map(BusSaleListEntity::getSaleListUnique).collect(Collectors.toSet());
            Set<String> existList = list.stream().map(BusSaleListEntity::getSaleListUnique).collect(Collectors.toSet());
            notHasList = saleListUniqueList.stream().filter(v -> !ArrayUtil.contains(existList.toArray(), v)).collect(Collectors.toSet());
        }
        String msg = null;
        if (CollectionUtil.isNotEmpty(notHasList)) {
            msg = "订单号：" + String.join(",", notHasList) + " 不存在";
        }
        if (CollectionUtil.isNotEmpty(hasBindList)) {
            if (null == msg) {
                msg = "";
            } else {
                msg += ";";
            }
            msg += "订单号：" + String.join(",", hasBindList) + " 已关联合同";
        }
        Result r = Result.ok();
        r.setData(totalMoney);
        if (StrUtil.isNotBlank(msg)) {
            msg += ";是否继续?";
            r.setMessage(msg.toString());
        }
        return r;
    }

    @Override
    public void export(BusContractListParams params, HttpServletResponse response) {
        LambdaQueryWrapper<BusContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusContractEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusContractEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getShopUnique()), BusContractEntity::getShopUnique, params.getShopUnique());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getContractStatus()), BusContractEntity::getContractStatus, params.getContractStatus());
        queryWrapper.eq(ObjectUtil.isNotNull(params.getContractType()), BusContractEntity::getContractType, params.getContractType());
        if (null != params.getSignTime() && ArrayUtil.isNotEmpty(params.getSignTime()) && params.getSignTime().length == 2) {
            queryWrapper.between(BusContractEntity::getSignTime, params.getSignTime()[0], params.getSignTime()[1]);
        }
        queryWrapper.like(StrUtil.isNotBlank(params.getOrderNo()), BusContractEntity::getOrderNo, params.getOrderNo());
        queryWrapper.like(StrUtil.isNotBlank(params.getContractNo()), BusContractEntity::getContractNo, params.getContractNo());
        queryWrapper.like(StrUtil.isNotBlank(params.getContractName()), BusContractEntity::getContractName, params.getContractName());
        queryWrapper.orderByDesc(BusContractEntity::getId);
        List<BusContractEntity> list = busContractMapper.selectList(queryWrapper);
        List<BusContractExcel> excelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
            shopWrapper.select(BusShopEntity::getShopUnique, BusShopEntity::getShopName);
            shopWrapper.eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
            List<BusShopEntity> shopEntityList = busShopMapper.selectList(shopWrapper);
            Map<Long, String> shopMap = shopEntityList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, BusShopEntity::getShopName));
            excelList = list.stream().map(v -> {
                BusContractExcel e = new BusContractExcel();
                e.setContractTypeName(BusContractTypeEnum.getName(v.getContractType()));
                e.setShopName(MapUtil.getStr(shopMap, v.getShopUnique(), StrUtil.EMPTY));
                e.setContractName(v.getContractName());
                e.setContractNo(v.getContractNo());
                e.setOrderNo(StrUtil.nullToEmpty(v.getOrderNo()));
                e.setTotalAmount(ObjectUtil.defaultIfNull(v.getTotalAmount(), BigDecimal.ZERO));
                e.setContractStatusName(StrUtil.nullToEmpty(BusContractStatusEnum.getName(v.getContractStatus())));
                e.setStartTime(ObjectUtil.isNotNull(v.getStartTime()) ? DateUtil.formatDate(v.getStartTime()) : StrUtil.EMPTY);
                e.setEndTime(ObjectUtil.isNotNull(v.getEndTime()) ? DateUtil.formatDate(v.getEndTime()) : StrUtil.EMPTY);
                e.setSignTime(StrUtil.nullToEmpty(v.getSignTime()));
                return e;
            }).collect(Collectors.toList());
        }
        String excelName = "合同_" + DateUtil.current() + ".xlsx";
        try {
            // 设置响应头，指定文件类型及编码
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName));

            // 清除响应
            response.reset();

            // 使用 EasyExcel 写入数据到 response 输出流
            EasyExcel.write(response.getOutputStream(), BusContractExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("Sheet1")
                    .doWrite(excelList); // dataList 是你要导出的数据列表
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result<BusContractDto> selectByContractNo(String contractNo) {
        LambdaQueryWrapper<BusContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusContractEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId())
                .eq(BusContractEntity::getContractNo, contractNo)
                .eq(BusContractEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        BusContractEntity entity = busContractMapper.selectOne(queryWrapper);
        BusContractDto dto = new BusContractDto();
        if (null != entity) {
            BeanUtils.copyProperties(entity, dto);
        }
        return Result.ok(dto);
    }
}