package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.dao.BusShopServiceFeeMapper;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.BusShopServiceFeeEntity;
import cc.buyhoo.tax.params.busShopServiceFee.ShopServiceFeeListParams;
import cc.buyhoo.tax.result.busShopServiceFee.ShopServiceFeeListDto;
import cc.buyhoo.tax.result.busShopServiceFee.ShopServiceFeeListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusShopServiceFeeService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BusShopServiceFeeServiceImpl extends BaseService implements BusShopServiceFeeService {

    private final BusShopServiceFeeMapper busShopServiceFeeMapper;

    private final BusShopMapper busShopMapper;

    /**
     * 服务费列表
     * @param params
     * @return
     */
    @Override
    public Result<ShopServiceFeeListResult> list(ShopServiceFeeListParams params) {
        Long companyId = SatokenUtil.getLoginUserCompanyId();

        //查询参数
        LambdaQueryWrapper<BusShopServiceFeeEntity> feeWrapper = new LambdaQueryWrapper<>();
        feeWrapper.eq(BusShopServiceFeeEntity::getCompanyId, companyId);
        if (ObjectUtil.isNotEmpty(params.getShopUnique())){
            feeWrapper.eq(BusShopServiceFeeEntity::getShopUnique,params.getShopUnique());
        }

        PageUtils.startPage(params);
        //查询
        List<BusShopServiceFeeEntity> feeList = busShopServiceFeeMapper.selectList(feeWrapper);

        //查询店铺信息
        Set<Long> shopUniqueSet = feeList.stream().map(BusShopServiceFeeEntity::getShopUnique).collect(Collectors.toSet());
        Map<Long,String> shopUniqueShopNameMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(shopUniqueSet)) {
            LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
            shopWrapper.in(BusShopEntity::getShopUnique,shopUniqueSet);
            List<BusShopEntity> shopList = busShopMapper.selectList(shopWrapper);
            shopUniqueShopNameMap = shopList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique,BusShopEntity::getShopName));
        }

        //参数构建
        List<ShopServiceFeeListDto> dtoList = new ArrayList<>();
        for (BusShopServiceFeeEntity entity : feeList) {
            ShopServiceFeeListDto dto = new ShopServiceFeeListDto();
            BeanUtils.copy(entity,dto);
            dto.setShopName(shopUniqueShopNameMap.get(entity.getShopUnique()));
            dtoList.add(dto);
        }

        return convertPageData(feeList,dtoList,ShopServiceFeeListResult.class,params);
    }
}
