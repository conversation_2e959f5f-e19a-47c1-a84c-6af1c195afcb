package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.config.properties.PayCenterProperties;
import cc.buyhoo.tax.dao.BusShopBillDetailMapper;
import cc.buyhoo.tax.dao.CmbcTranferRecordMapper;
import cc.buyhoo.tax.dao.SysCompanyMapper;
import cc.buyhoo.tax.entity.BusShopBillDetailEntity;
import cc.buyhoo.tax.entity.CmbcTranferRecordEntity;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.entity.cmb.TrsInfoEntity;
import cc.buyhoo.tax.enums.CmbcTranferRecordEnum;
import cc.buyhoo.tax.facade.result.busSyncRecord.BusSyncRecordDto;
import cc.buyhoo.tax.result.minsheng.CmbcQueryBatchTranferParams;
import cc.buyhoo.tax.result.minsheng.CmbcQueryTranferParams;
import cc.buyhoo.tax.service.CmbService;
import cc.buyhoo.tax.service.CmbcQueryTranferResultService;
import cc.buyhoo.tax.temporary.PayCenterPayHelper;
import cc.buyhoo.tax.temporary.params.CmbcQueryBatchTransferParams;
import cc.buyhoo.tax.temporary.params.CmbcQueryTransferParams;
import cc.buyhoo.tax.temporary.result.PayCenterBaseResp;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CmbcQueryTranferResultServiceImpl implements CmbcQueryTranferResultService {

    @Resource
    private CmbcTranferRecordMapper cmbcTranferRecordMapper;
    @Resource
    private PayCenterPayHelper payCenterPayHelper;
    @Resource
    private SysCompanyMapper sysCompanyMapper;
    @Autowired
    private CmbService cmbService;
    @Resource
    private PayCenterProperties payCenterProperties;
    @Resource
    private BusShopBillDetailMapper busShopBillDetailMapper;

    @Override
    public Result<BusSyncRecordDto> queryTransferAndUpdate() {
        //单条记录
        List<CmbcTranferRecordEntity> settingEntities = cmbcTranferRecordMapper.selectList(new LambdaQueryWrapper<CmbcTranferRecordEntity>().in(CmbcTranferRecordEntity::getStatus,
                        Arrays.asList(CmbcTranferRecordEnum.BEGIN.getValue(), CmbcTranferRecordEnum.PROCESSING.getValue()))
                .eq(CmbcTranferRecordEntity::getMultipleFlg, 0));
        log.info("民生银行查询单条转账结果:{}", JSONUtil.toJsonStr(settingEntities));
        if (CollectionUtil.isNotEmpty(settingEntities)){
            //公司id集合
            List<Long> companyId = settingEntities.stream().map(CmbcTranferRecordEntity::getCompanyId).collect(Collectors.toList());
            List<SysCompanyEntity> companies = sysCompanyMapper.selectBatchIds(companyId);
            //企业id-企业信息map
            Map<Long, SysCompanyEntity> companiesMap = companies.stream()
                    .collect(Collectors.toMap(SysCompanyEntity::getId, company -> company));
            //转账成功的账号
            List<CmbcTranferRecordEntity> successInsId = new ArrayList<>();
            //转账失败的账号
            List<CmbcTranferRecordEntity> failInsId = new ArrayList<>();
            for (CmbcTranferRecordEntity settingEntity : settingEntities) {
                CmbcQueryTransferParams params = new CmbcQueryTransferParams();
                params.setInsId(settingEntity.getInsId());
                params.setBankKey("CMBC");
                SysCompanyEntity sysCompanyEntity = companiesMap.get(settingEntity.getCompanyId());
                if (sysCompanyEntity != null) {
                    params.setMchBankId(sysCompanyEntity.getMchBankId());
                    params.setMchId(sysCompanyEntity.getMchId());
                    PayCenterBaseResp payCenterBaseResp = payCenterPayHelper.queryTransfer(payCenterProperties.getUrl(),params, sysCompanyEntity.getPayCenterSecretKey());
                    if (payCenterBaseResp.getCode()== HttpStatus.HTTP_OK){
                        String data = payCenterBaseResp.getData();
                        CmbcQueryTranferParams bean = JSONUtil.toBean(data, CmbcQueryTranferParams.class);
                        //转账成功
                        if (ObjectUtil.equals(bean.getCode(),"0")){
                            successInsId.add(settingEntity);
                            settingEntity.setStatus(CmbcTranferRecordEnum.SUCCESS.getValue());
                            settingEntity.setResult(bean.getMessage());
                        } else if (ObjectUtil.equals(bean.getCode(),"2")){
                            //转账失败
                            settingEntity.setStatus(CmbcTranferRecordEnum.TRANSFER_FAIL.getValue());
                            settingEntity.setResult(bean.getMessage());
                            failInsId.add(settingEntity);
                        } else {
                            //转账失败
                            settingEntity.setStatus(CmbcTranferRecordEnum.PROCESSING.getValue());
                            settingEntity.setResult(bean.getMessage());
                            failInsId.add(settingEntity);
                        }
                    }else {
                        //转账失败
                        settingEntity.setStatus(CmbcTranferRecordEnum.FAIL.getValue());
                        settingEntity.setResult(payCenterBaseResp.getMsg());
                        failInsId.add(settingEntity);
                    }
                }
            }
            //更新状态
            cmbcTranferRecord( successInsId);
            //更新状态
            cmbcTranferRecord( failInsId);
            //转账成功
            if (CollectionUtil.isNotEmpty(successInsId)) {
                for (CmbcTranferRecordEntity cmbcTranferRecordEntity : successInsId) {
                    TrsInfoEntity trsInfo = new TrsInfoEntity();
                    trsInfo.setYurRef(cmbcTranferRecordEntity.getYurRef());
                    trsInfo.setTrsAmt(ObjectUtil.isNotEmpty(cmbcTranferRecordEntity.getTranferAmount())? cmbcTranferRecordEntity.getTranferAmount().toString():"0");
                    cmbService.resetFinSuccShopBill(trsInfo);
                }

            }
            //转账失败
            if (CollectionUtil.isNotEmpty(failInsId)) {
                for (CmbcTranferRecordEntity cmbcTranferRecordEntity : failInsId) {
                    TrsInfoEntity trsInfo = new TrsInfoEntity();
                    trsInfo.setYurRef(cmbcTranferRecordEntity.getYurRef());
                    trsInfo.setTrsAmt(ObjectUtil.isNotEmpty(cmbcTranferRecordEntity.getTranferAmount())? cmbcTranferRecordEntity.getTranferAmount().toString():"0");
                    trsInfo.setRtnNar(cmbcTranferRecordEntity.getResult());
                    cmbService.resetFinBackShopBill(trsInfo);
                }
            }

        }
        return Result.ok();
    }

    @Override
    public Result queryBatchTransferAndUpdate() {
        //单条记录
        List<CmbcTranferRecordEntity> settingEntities = cmbcTranferRecordMapper.selectList(new LambdaQueryWrapper<CmbcTranferRecordEntity>().in(
                CmbcTranferRecordEntity::getStatus, Arrays.asList(CmbcTranferRecordEnum.BEGIN.getValue(), CmbcTranferRecordEnum.PROCESSING.getValue()))
                .eq(CmbcTranferRecordEntity::getMultipleFlg, 1));
        log.info("民生银行批量查询转账结果:{}", JSONUtil.toJsonStr(settingEntities));
        if (CollectionUtil.isNotEmpty(settingEntities)){
            //公司id集合
            List<Long> companyId = settingEntities.stream().map(CmbcTranferRecordEntity::getCompanyId).collect(Collectors.toList());
            List<SysCompanyEntity> companies = sysCompanyMapper.selectBatchIds(companyId);
            //企业id-企业信息map
            Map<Long, SysCompanyEntity> companiesMap = companies.stream()
                    .collect(Collectors.toMap(SysCompanyEntity::getId, company -> company));
            //转账成功的账号
            List<CmbcTranferRecordEntity> successInsId = new ArrayList<>();
            //转账失败的账号
            List<CmbcTranferRecordEntity> failInsId = new ArrayList<>();
            //转账成功的交易信息
            List<TrsInfoEntity> successTrsInfos = new ArrayList<>();
            //转账失败的交易信息
            List<TrsInfoEntity> failTrsInfos = new ArrayList<>();
            for (CmbcTranferRecordEntity settingEntity : settingEntities) {
                CmbcQueryBatchTransferParams params = new CmbcQueryBatchTransferParams();
                params.setInsId(settingEntity.getInsId());
                params.setBankKey("CMBC");
                SysCompanyEntity sysCompanyEntity = companiesMap.get(settingEntity.getCompanyId());
                if (sysCompanyEntity != null) {
                    params.setMchBankId(sysCompanyEntity.getMchBankId());
                    params.setMchId(sysCompanyEntity.getMchId());
                    PayCenterBaseResp payCenterBaseResp = payCenterPayHelper.queryBatchTransfer(payCenterProperties.getUrl(),params, sysCompanyEntity.getPayCenterSecretKey());
                    String data = payCenterBaseResp.getData();
                    CmbcQueryBatchTranferParams bean = JSONUtil.toBean(data, CmbcQueryBatchTranferParams.class);
                    if (payCenterBaseResp.getCode()== HttpStatus.HTTP_OK){
                        //转账申请结束
                        if (ObjectUtil.equals(bean.getCode(),"0")){
                            List<CmbcQueryBatchTranferParams.BillInfo> billInfos = bean.getBillInfos();
                            if (CollectionUtil.isNotEmpty(billInfos)){
                                for (CmbcQueryBatchTranferParams.BillInfo billInfo : billInfos) {
                                    if (billInfo.getTransactionStatus().equals("10")){
                                        TrsInfoEntity successTrsInfo = new TrsInfoEntity();
                                        successTrsInfo.setYurRef(billInfo.getTransactionReferenceNumber());
                                        successTrsInfo.setTrsAmt(billInfo.getAmount().toString());
                                        successTrsInfos.add(successTrsInfo);
                                    }else {
                                        TrsInfoEntity failTrsInfo = new TrsInfoEntity();
                                        failTrsInfo.setYurRef(billInfo.getTransactionReferenceNumber());
                                        failTrsInfo.setRtnNar(billInfo.getErrorMsg());
                                        failTrsInfo.setTrsAmt(billInfo.getAmount().toString());
                                        failTrsInfos.add(failTrsInfo);
                                    }

                                }
                            }
                            settingEntity.setStatus(CmbcTranferRecordEnum.SUCCESS.getValue());
                            settingEntity.setResult(bean.getMessage());
                            successInsId.add(settingEntity);
                        } else if (ObjectUtil.equals(bean.getCode(), "2")) {
                            List<CmbcQueryBatchTranferParams.BillInfo> billInfos = bean.getBillInfos();
                            if (CollectionUtil.isNotEmpty(billInfos)){
                                for (CmbcQueryBatchTranferParams.BillInfo billInfo : billInfos) {
                                    if (billInfo.getTransactionStatus().equals("10")){
                                        TrsInfoEntity successTrsInfo = new TrsInfoEntity();
                                        successTrsInfo.setYurRef(billInfo.getTransactionReferenceNumber());
                                        successTrsInfo.setTrsAmt(billInfo.getAmount().toString());
                                        successTrsInfos.add(successTrsInfo);
                                    }else {
                                        TrsInfoEntity failTrsInfo = new TrsInfoEntity();
                                        failTrsInfo.setYurRef(billInfo.getTransactionReferenceNumber());
                                        failTrsInfo.setRtnNar(billInfo.getErrorMsg());
                                        failTrsInfo.setTrsAmt(billInfo.getAmount().toString());
                                        failTrsInfos.add(failTrsInfo);
                                    }

                                }
                            }
                            //转账失败
                            settingEntity.setStatus(CmbcTranferRecordEnum.TRANSFER_FAIL.getValue());
                            settingEntity.setResult(bean.getMessage());
                            failInsId.add(settingEntity);
                        } else {
                            //转账失败
                            settingEntity.setStatus(CmbcTranferRecordEnum.PROCESSING.getValue());
                            settingEntity.setResult(bean.getMessage());
                            failInsId.add(settingEntity);
                        }
                    }else {
                        List<BusShopBillDetailEntity> billDetailList = busShopBillDetailMapper.selectList(Wrappers.lambdaQuery(BusShopBillDetailEntity.class).eq(BusShopBillDetailEntity::getBthNbr, params.getInsId()));
                        if (CollectionUtil.isNotEmpty(billDetailList)) {
                            for (BusShopBillDetailEntity e : billDetailList) {
                                TrsInfoEntity failTrsInfo = new TrsInfoEntity();
                                failTrsInfo.setYurRef(e.getBillNo());
                                failTrsInfo.setRtnNar(bean.getMessage());
                                failTrsInfo.setTrsAmt(e.getSettledAmount().toString());
                                failTrsInfos.add(failTrsInfo);
                            }
                        }
                        //转账失败
                        settingEntity.setStatus(CmbcTranferRecordEnum.FAIL.getValue());
                        settingEntity.setResult(payCenterBaseResp.getMsg());
                        failInsId.add(settingEntity);
                    }
                }
            }
            //更新状态
            cmbcTranferRecord(successInsId);
            //更新状态
            cmbcTranferRecord(failInsId);
            //转账成功
            if (CollectionUtil.isNotEmpty(successTrsInfos)) {
                for (TrsInfoEntity trsInfo : successTrsInfos) {
                    cmbService.resetFinSuccShopBill(trsInfo);
                }
            }
            //转账失败
            if (CollectionUtil.isNotEmpty(failTrsInfos)) {
                for (TrsInfoEntity trsInfo : failTrsInfos) {
                    cmbService.resetFinBackShopBill(trsInfo);
                }
            }
        }
        return Result.ok();
    }



    private void cmbcTranferRecord( List<CmbcTranferRecordEntity> settingEntities){
        cmbcTranferRecordMapper.updateBatchById(settingEntities);
    }
}
