package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.upgrade.params.cashUpgradeForbidShop.BindUpgradeForbidShopParams;
import cc.buyhoo.upgrade.result.cashUpgradeForbidShop.QueryUpgradeForbidShopResult;

public interface CashUpgradeForbidShopService {

    /**
     * 禁止升级店铺查询
     * @return
     */
    public Result<QueryUpgradeForbidShopResult> queryUpgradeForbidShop();

    /**
     * 绑定禁止升级店铺
     * @param params
     * @return
     */
    public Result<Void> bindUpgradeForbidShop(BindUpgradeForbidShopParams params);

}
