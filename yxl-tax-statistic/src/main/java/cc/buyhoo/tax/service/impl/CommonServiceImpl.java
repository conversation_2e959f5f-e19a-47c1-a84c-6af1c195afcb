package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.Cnarea2023Mapper;
import cc.buyhoo.tax.entity.Cnarea2023Entity;
import cc.buyhoo.tax.entity.common.AreaEntity;
import cc.buyhoo.tax.params.common.GetAreaListSubParam;
import cc.buyhoo.tax.result.common.GetAreaListResult;
import cc.buyhoo.tax.result.common.GetAreaListSubResult;
import cc.buyhoo.tax.result.userArea.AreaDictQueryDto;
import cc.buyhoo.tax.service.CommonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class CommonServiceImpl implements CommonService {
    @Resource
    private Cnarea2023Mapper cnarea2023Mapper;
    @Resource
    private RedisCache redisCache;
    /**
     * 获取省市区县信息列表
     * @return
     */
    public Result< List<AreaEntity>> getAreaListResult() {

        if (redisCache.hasKey("areaListCommon")) {
            return Result.ok(redisCache.getCacheObject("areaListCommon"));
        }
        List<AreaEntity> list = cnarea2023Mapper.queryAreaList();
        redisCache.setCacheObject("areaListCommon",list);
        return Result.ok(list);
    }

    /**
     * 获取下级省市区县信息列表
     * @param getAreaListSubParam
     * @return
     */
    public Result<List<AreaEntity>> getAreaListSub(GetAreaListSubParam getAreaListSubParam) {
        Cnarea2023Entity entity = new Cnarea2023Entity();
        entity.setParentCode(getAreaListSubParam.getCode());
        List<Cnarea2023Entity> list = cnarea2023Mapper.findList(entity);
        List<AreaEntity> resList = new ArrayList<>();
        if (list.size() == 0) {
            return Result.ok(resList);
        } else {
            resList = list.stream().map(v -> {
                AreaEntity areaEntity = new AreaEntity();
                areaEntity.setName(v.getName());
                areaEntity.setCode(v.getAreaCode());
                areaEntity.setLevel(v.getLevel());
                return areaEntity;
            }).collect(Collectors.toList());
        }
        return Result.ok(resList);
    }
}
