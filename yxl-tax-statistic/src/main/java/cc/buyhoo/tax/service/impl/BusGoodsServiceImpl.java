package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.facade.params.saleList.SaleListDetail;
import cc.buyhoo.tax.params.busGoods.GoodsCategoryBindParams;
import cc.buyhoo.tax.params.busGoods.GoodsListParams;
import cc.buyhoo.tax.result.busGoods.BusGoodsDto;
import cc.buyhoo.tax.result.busGoods.BusGoodsListResult;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.BusGoodsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @Description 商品管理表
* @ClassName BusGoods
* <AUTHOR> 
* @Date 2023-07-29
**/
@Service
@AllArgsConstructor
public class BusGoodsServiceImpl extends BaseService implements BusGoodsService {

    @Resource
    private BusGoodsMapper busGoodsMapper;
    @Resource
    private BusGoodsCategoryMapper busGoodsCategoryMapper;
    @Resource
    private BusShopMapper busShopMapper;
    @Resource
    private BusInventoryBatchDetailMapper busInventoryBatchDetailMapper;
    @Resource
    private BusReturnBatchDetailMapper busReturnBatchDetailMapper;
    @Resource
    private BusInventoryOrderCategoryMapper busInventoryOrderCategoryMapper;
    @Resource
    private BusReturnOrderCategoryMapper busReturnOrderCategoryMapper;

    @Override
    public Result<BusGoodsListResult> pageList(GoodsListParams goodsListParams) {
        PageUtils.startPage(goodsListParams);
        LambdaQueryWrapper<BusGoodsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusGoodsEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusGoodsEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        if (ObjectUtil.isNotEmpty(goodsListParams.getShopUnique())) {
            queryWrapper.eq(BusGoodsEntity::getShopUnique, goodsListParams.getShopUnique());
        }
        if (ObjectUtil.isNotEmpty(goodsListParams.getCategoryId())) {
            queryWrapper.eq(BusGoodsEntity::getCategoryId, goodsListParams.getCategoryId());
        }
        if (ObjectUtil.isNotEmpty(goodsListParams.getCategoryTwoId())) {
            queryWrapper.eq(BusGoodsEntity::getCategoryTwoId, goodsListParams.getCategoryTwoId());
        }
        if (StrUtil.isNotBlank(goodsListParams.getGoodsName())) {
            queryWrapper.like(BusGoodsEntity::getGoodsName, goodsListParams.getGoodsName());
        }
        if (null != goodsListParams.getCategoryTwoName() && goodsListParams.getCategoryTwoName().length == 2) {
            queryWrapper.eq(BusGoodsEntity::getCategoryId, goodsListParams.getCategoryTwoName()[0]);
            queryWrapper.eq(BusGoodsEntity::getCategoryTwoId, goodsListParams.getCategoryTwoName()[1]);
        }
        queryWrapper.orderByAsc(BusGoodsEntity::getGoodsName);
        List<BusGoodsEntity> list = busGoodsMapper.selectList(queryWrapper);
        List<BusGoodsCategoryEntity> categoryEntities = busGoodsCategoryMapper.selectList(new LambdaQueryWrapper<BusGoodsCategoryEntity>().eq(BusGoodsCategoryEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId()));
        Map<Long, String> categoryMap = categoryEntities.stream().collect(Collectors.toMap(BusGoodsCategoryEntity::getId, BusGoodsCategoryEntity::getCategoryName));
        List<BusShopEntity> shopEntityList = busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId()));
        Map<Long, String> shopMap = shopEntityList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, BusShopEntity::getShopName));
        List<BusGoodsDto> dtoList = list.stream().map(v -> {
            BusGoodsDto dto = new BusGoodsDto();
            BeanUtil.copyProperties(v, dto);
            dto.setCategoryName(categoryMap.get(v.getCategoryId()));
            dto.setCategoryTwoName(categoryMap.get(v.getCategoryTwoId()));
            dto.setShopUniqueName(shopMap.getOrDefault(v.getShopUnique(), StrUtil.EMPTY));
            return dto;
        }).collect(Collectors.toList());
        return convertPageData(list, dtoList, BusGoodsListResult.class, goodsListParams);
    }

    /**
     * 商品分类绑定
     * @param goodsCategoryBindParams
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> bindCategory(GoodsCategoryBindParams goodsCategoryBindParams) {
        List<Long> ids = goodsCategoryBindParams.getGoodsIds();

        //商品校验
        List<BusGoodsEntity> checkGoods = checkGoods(ids);

        //查询默认分类
        List<BusGoodsCategoryEntity> cateList = queryDefaultCategory();
        Long cateId = cateList.stream().filter(c -> c.getParentId() == 0).map(BusGoodsCategoryEntity::getId).findFirst().orElse(-1L);
        Long cateTwoId = cateList.stream().filter(c -> c.getParentId() != 0).map(BusGoodsCategoryEntity::getId).findFirst().orElse(-1L);

        List<BusGoodsEntity> goodsList = checkGoods.stream().filter(g -> g.getCategoryId().equals(cateId)).collect(Collectors.toList());

        //更新商品分类
        updateGoodsCategory(ids,goodsCategoryBindParams);

        if (ObjectUtil.isEmpty(goodsList)) return Result.ok();

        //商品id
        List<Long> gidList = goodsList.stream().map(BusGoodsEntity::getGoodsId).collect(Collectors.toList());

        //更新进货单批次
        updateBatchDetail(goodsCategoryBindParams,cateId,cateTwoId,gidList);

        //更新退货单批次
        updateReturnBatchDetail(goodsCategoryBindParams,cateId,cateTwoId,gidList);

        return Result.ok();
    }

    /**
     * 校验商品
     * @return
     */
    private List<BusGoodsEntity> checkGoods(List<Long> ids) {
        LambdaQueryWrapper<BusGoodsEntity> goodsWrapper = new LambdaQueryWrapper<>();
        goodsWrapper.in(BusGoodsEntity::getId,ids);
        goodsWrapper.eq(BusGoodsEntity::getCompanyId,SatokenUtil.getLoginUserCompanyId());
        List<BusGoodsEntity> checkGoods = busGoodsMapper.selectList(goodsWrapper);
        if (checkGoods.size() != ids.size()) throw new BusinessException(CommonErrorEnum.PARAM_ERROR);

        return checkGoods;
    }

    /**
     * 查询默认分类
     * @return
     */
    private List<BusGoodsCategoryEntity> queryDefaultCategory() {
        LambdaQueryWrapper<BusGoodsCategoryEntity> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(BusGoodsCategoryEntity::getCategoryType,1); //默认分类
        categoryWrapper.eq(BusGoodsCategoryEntity::getCompanyId,SatokenUtil.getLoginUserCompanyId());
        List<BusGoodsCategoryEntity> cateList = busGoodsCategoryMapper.selectList(categoryWrapper);
        return cateList;
    }

    /**
     * 更新商品分类
     * @param ids
     */
    private void updateGoodsCategory(List<Long> ids,GoodsCategoryBindParams goodsCategoryBindParams) {
        List<BusGoodsEntity> entities = ids.stream().map(v -> {
            BusGoodsEntity entity = new BusGoodsEntity();
            entity.setId(v);
            entity.setCategoryId(goodsCategoryBindParams.getCategoryId());
            entity.setCategoryTwoId(goodsCategoryBindParams.getCategoryTwoId());
            return entity;
        }).collect(Collectors.toList());
        busGoodsMapper.updateBatchById(entities);
    }

    /**
     * 更新进货单详情
     */
    private void updateBatchDetail(GoodsCategoryBindParams goodsCategoryBindParams,Long cateId, Long cateTwoId,List<Long> gidList) {
        LambdaQueryWrapper<BusInventoryBatchDetailEntity> batchDetailWrapper = new LambdaQueryWrapper<>();
        batchDetailWrapper.eq(BusInventoryBatchDetailEntity::getCategoryId,cateId);
        batchDetailWrapper.eq(BusInventoryBatchDetailEntity::getCategoryTwoId,cateTwoId);
        batchDetailWrapper.in(BusInventoryBatchDetailEntity::getGoodsId,gidList);
        List<BusInventoryBatchDetailEntity> batchDetailList = busInventoryBatchDetailMapper.selectList(batchDetailWrapper);

        if (ObjectUtil.isEmpty(batchDetailList)) return;

        List<Long> detailIdList = batchDetailList.stream().map(BusInventoryBatchDetailEntity::getId).collect(Collectors.toList());
        List<BusInventoryBatchDetailEntity> detailList = new ArrayList<>();
        for (Long id : detailIdList) {
            BusInventoryBatchDetailEntity bd = new BusInventoryBatchDetailEntity();
            bd.setId(id);
            bd.setCategoryId(goodsCategoryBindParams.getCategoryId());
            bd.setCategoryTwoId(goodsCategoryBindParams.getCategoryTwoId());
            detailList.add(bd);
        }

        //批次分类更新
        busInventoryBatchDetailMapper.updateBatchById(detailList);

        //批次统计更新
        List<Long> orderCategoryIds = batchDetailList.stream().map(BusInventoryBatchDetailEntity::getOrderId).collect(Collectors.toList());

        //删除旧的统计
        LambdaQueryWrapper<BusInventoryOrderCategoryEntity> ocWrapper = new LambdaQueryWrapper<>();
        ocWrapper.in(BusInventoryOrderCategoryEntity::getOrderId,orderCategoryIds);
        busInventoryOrderCategoryMapper.delete(ocWrapper);

        //保存新的统计
        List<BusInventoryOrderCategoryEntity> orderCategoryList = new ArrayList<>();
        for (Long id : orderCategoryIds) {
            LambdaQueryWrapper<BusInventoryBatchDetailEntity> bdWrapper = new LambdaQueryWrapper<>();
            bdWrapper.eq(BusInventoryBatchDetailEntity::getOrderId,id);
            List<BusInventoryBatchDetailEntity> allBatch = busInventoryBatchDetailMapper.selectList(bdWrapper);
            Map<Long, List<BusInventoryBatchDetailEntity>> groupByCateTwo = allBatch.parallelStream().collect(Collectors.groupingBy(BusInventoryBatchDetailEntity::getCategoryTwoId));
            Iterator<Map.Entry<Long, List<BusInventoryBatchDetailEntity>>> iter = groupByCateTwo.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry<Long, List<BusInventoryBatchDetailEntity>> next = iter.next();
                Long twoId = next.getKey();
                List<BusInventoryBatchDetailEntity> dList = next.getValue();
                BusInventoryBatchDetailEntity detail = dList.get(0);

                BusInventoryOrderCategoryEntity orderCategory = new BusInventoryOrderCategoryEntity();
                orderCategory.setOrderId(id);
                orderCategory.setCategoryId(detail.getCategoryId());
                orderCategory.setCategoryTwoId(twoId);
                orderCategory.setTotalGoodsCount(dList.stream().map(BusInventoryBatchDetailEntity::getGoodsCount).reduce(BigDecimal.ZERO, BigDecimal::add));
                orderCategory.setTotalMoney(dList.stream().map(BusInventoryBatchDetailEntity::getTotalMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
                orderCategory.setCompanyId(detail.getCompanyId());

                orderCategoryList.add(orderCategory);
            }
        }
        busInventoryOrderCategoryMapper.insertBatch(orderCategoryList);

    }

    /**
     * 更新退货单批次详情
     */
    private void updateReturnBatchDetail(GoodsCategoryBindParams goodsCategoryBindParams,Long cateId, Long cateTwoId,List<Long> gidList) {
        //查询退款批次中的默认分类
        LambdaQueryWrapper<BusReturnBatchDetailEntity> returnBatchDetailWrapper = new LambdaQueryWrapper<>();
        returnBatchDetailWrapper.eq(BusReturnBatchDetailEntity::getCategoryId,cateId);
        returnBatchDetailWrapper.eq(BusReturnBatchDetailEntity::getCategoryTwoId,cateTwoId);
        returnBatchDetailWrapper.in(BusReturnBatchDetailEntity::getGoodsId,gidList);
        List<BusReturnBatchDetailEntity> returnList = busReturnBatchDetailMapper.selectList(returnBatchDetailWrapper);

        if (ObjectUtil.isEmpty(returnList)) return;

        List<Long> detailIdList = returnList.stream().map(BusReturnBatchDetailEntity::getId).collect(Collectors.toList());
        List<BusReturnBatchDetailEntity> detailList = new ArrayList<>();
        for (Long id : detailIdList) {
            BusReturnBatchDetailEntity d = new BusReturnBatchDetailEntity();
            d.setId(id);
            d.setCategoryId(goodsCategoryBindParams.getCategoryId());
            d.setCategoryTwoId(goodsCategoryBindParams.getCategoryTwoId());
            detailList.add(d);
        }
        //更新详情
        busReturnBatchDetailMapper.updateBatchById(detailList);

        //批次统计更新
        List<Long> orderCategoryIds = returnList.stream().map(BusReturnBatchDetailEntity::getOrderId).collect(Collectors.toList());

        //删除旧的统计
        LambdaQueryWrapper<BusReturnOrderCategoryEntity> ocWrapper = new LambdaQueryWrapper<>();
        ocWrapper.in(BusReturnOrderCategoryEntity::getReturnOrderId,orderCategoryIds);
        busReturnOrderCategoryMapper.delete(ocWrapper);

        //保存新的统计
        List<BusReturnOrderCategoryEntity> orderCategoryList = new ArrayList<>();
        for (Long id : orderCategoryIds) {
            LambdaQueryWrapper<BusReturnBatchDetailEntity> bdWrapper = new LambdaQueryWrapper<>();
            bdWrapper.eq(BusReturnBatchDetailEntity::getOrderId,id);
            List<BusReturnBatchDetailEntity> allBatch = busReturnBatchDetailMapper.selectList(bdWrapper);
            Map<Long, List<BusReturnBatchDetailEntity>> groupByCateTwo = allBatch.parallelStream().collect(Collectors.groupingBy(BusReturnBatchDetailEntity::getCategoryTwoId));
            Iterator<Map.Entry<Long, List<BusReturnBatchDetailEntity>>> iter = groupByCateTwo.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry<Long, List<BusReturnBatchDetailEntity>> next = iter.next();
                Long twoId = next.getKey();
                List<BusReturnBatchDetailEntity> dList = next.getValue();
                BusReturnBatchDetailEntity detail = dList.get(0);

                BusReturnOrderCategoryEntity orderCategory = new BusReturnOrderCategoryEntity();
                orderCategory.setReturnOrderId(id);
                orderCategory.setCategoryId(detail.getCategoryId());
                orderCategory.setCategoryTwoId(twoId);
                orderCategory.setTotalGoodsCount(dList.stream().map(BusReturnBatchDetailEntity::getGoodsCount).reduce(BigDecimal.ZERO, BigDecimal::add));
                orderCategory.setTotalMoney(dList.stream().map(BusReturnBatchDetailEntity::getTotalMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
                orderCategory.setCompanyId(detail.getCompanyId());

                orderCategoryList.add(orderCategory);
            }
        }
        busReturnOrderCategoryMapper.insertBatch(orderCategoryList);
    }

}