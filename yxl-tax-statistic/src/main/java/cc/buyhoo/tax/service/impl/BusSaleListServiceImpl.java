package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.datasource.utils.PageUtils;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.PageParams;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.CommonErrorEnum;
import cc.buyhoo.tax.enums.DisassembleStatusEnum;
import cc.buyhoo.tax.enums.ShopNatureEnum;
import cc.buyhoo.tax.enums.sys.UserTypeEnum;
import cc.buyhoo.tax.params.saleList.AutoDisassembleSaleListParams;
import cc.buyhoo.tax.params.saleList.DisassembleSaleListParams;
import cc.buyhoo.tax.params.saleList.SaleListDetailParams;
import cc.buyhoo.tax.params.saleList.SaleListParams;
import cc.buyhoo.tax.result.busShop.ShopExcel;
import cc.buyhoo.tax.result.common.LoginUser;
import cc.buyhoo.tax.result.disassembleList.CreateOrderSigleResult;
import cc.buyhoo.tax.result.saleList.*;
import cc.buyhoo.tax.service.BaseService;
import cc.buyhoo.tax.service.BusSaleListService;
import cc.buyhoo.tax.service.CmbService;
import cc.buyhoo.tax.util.CommonUtil;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BusSaleListServiceImpl extends BaseService implements BusSaleListService {

    @Resource
    private BusSaleListMapper busSaleListMapper;

    @Resource
    private BusShopMapper busShopMapper;

    @Resource
    private BusSaleListDetailMapper busSaleListDetailMapper;

    @Resource
    private Cnarea2023Mapper cnarea2023Mapper;

    @Resource
    private BusSaleListPayDetailMapper busSaleListPayDetailMapper;

    @Resource
    private BusSaleListDisassembleMapper busSaleListDisassembleMapper;
    @Resource
    private BusSaleListDisassembleDetailMapper busSaleListDisassembleDetailMapper;
    @Resource
    private BusSaleListDisassemblePayDetailMapper busSaleListDisassemblePayDetailMapper;
    @Resource
    private DisassembleListMapper disassembleListMapper;

    @Resource
    private CmbService cmbService;

    @Resource
    private RedisCache redis;


    public void exportSaleList(@Validated @RequestBody SaleListParams params, HttpServletResponse response) {
        List<SaleListExcel> excelList = new ArrayList<>();

        LoginUser loginUser = SatokenUtil.getLoginUser();
        params.setPageIndex(1);
        params.setPageSize(60000);
        //查询订单数据，并转换为excel数据
        Result<SaleListResult> res = saleList(params);
        SaleListResult saleListResult = res.getData();
        List<SaleListDto> rows = saleListResult == null ? new ArrayList<>() : saleListResult.getRows();
        if (CollectionUtil.isNotEmpty(rows)) {
            for (SaleListDto saleListDto : rows) {
                SaleListExcel excel = new SaleListExcel();
                BeanUtil.copyProperties(saleListDto, excel);
                excelList.add(excel);
            }
        }
        String excelName = "供货商_" + DateUtil.current() + ".xlsx";
        List<SaleListBaseExcel> baseExcelList = new ArrayList<>();
        if (loginUser.getUserType().equals(UserTypeEnum.ADMIN.getValue())) {
            baseExcelList = excelList.stream().map(v -> {
                SaleListBaseExcel base = new SaleListBaseExcel();
                BeanUtil.copyProperties(v, base);
                return base;
            }).collect(Collectors.toList());
        }

        try {
            // 设置响应头，指定文件类型及编码
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName));
            // 清除响应
            response.reset();
            // 使用 EasyExcel 写入数据到 response 输出流
            if (loginUser.getUserType().equals(UserTypeEnum.SUPER_ADMIN.getValue())) {
                EasyExcel.write(response.getOutputStream(), SaleListExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("Sheet1")
                        .doWrite(excelList); // dataList 是你要导出的数据列表
            } else {
                EasyExcel.write(response.getOutputStream(), SaleListBaseExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("Sheet1")
                        .doWrite(baseExcelList); // dataList 是你要导出的数据列表
            }
        } catch (IOException e) {
            log.error("导出供货商Excel异常");
        }
    }
    /**
     *
     * @param disassembleParams
     * @return
     */
    @Transactional
    public Result<Void> disassembleSaleList(DisassembleSaleListParams disassembleParams) {
        //获取当前订单的信息
        LambdaQueryWrapper<BusSaleListEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusSaleListEntity::getSaleListUnique, disassembleParams.getSaleListUnique());

        BusSaleListEntity entity = busSaleListMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(entity)) {
            return Result.fail(CommonErrorEnum.PARAM_ERROR, "订单信息不存在");
        }

        //需要防止重复提交
        if (redis.hasKey(disassembleParams.getSaleListUnique())) {
            return Result.fail(CommonErrorEnum.PARAM_ERROR, "订单正在处理中，请稍后再试");
        }
        redis.setCacheObject(disassembleParams.getSaleListUnique(), 1,10, TimeUnit.SECONDS);

        //校验拆单信息是否存在
        LambdaQueryWrapper<DisassembleListEntity> disassembleListWrapper = new LambdaQueryWrapper<>();
        disassembleListWrapper.eq(DisassembleListEntity::getSaleListUnique, disassembleParams.getSaleListUnique());
        DisassembleListEntity disassembleListEntity = disassembleListMapper.selectOne(disassembleListWrapper);
        if (ObjectUtil.isNotNull(disassembleListEntity)) {
            return Result.fail(CommonErrorEnum.PARAM_ERROR, "该订单已经在拆单中");
        }

        Long userId = SatokenUtil.getLoginUserId();
        //在子线程内处理信息，并返回操作成功
        ThreadUtil.execAsync(() -> {
            System.out.println("执行拆解订单任务");
            disassembleOrder(disassembleParams, userId);
        });
        return Result.ok();
    }

    /**
     * 根据条件，自动拆单
     * @param params
     * @return
     */
    public Result<Void> autoDisassembleSaleList(AutoDisassembleSaleListParams params) {

        //获取当前的订单中，满足条件的订单，并且没有拆单记录的
        LambdaQueryWrapper<BusSaleListEntity> saleListEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        saleListEntityLambdaQueryWrapper.eq(BusSaleListEntity::getCompanyId,SatokenUtil.getLoginUserCompanyId());
        saleListEntityLambdaQueryWrapper.lt(ObjectUtil.isNotNull(params.getMaxOrderAmount()), BusSaleListEntity::getSaleListTotal, params.getMaxOrderAmount());
        saleListEntityLambdaQueryWrapper.gt(ObjectUtil.isNotNull(params.getMinOrderAmount()), BusSaleListEntity::getSaleListTotal, params.getMinOrderAmount());
        saleListEntityLambdaQueryWrapper.lt(ObjectUtil.isNotEmpty(params.getEndDate()), BusSaleListEntity::getSaleListDatetime, params.getEndDate());
        saleListEntityLambdaQueryWrapper.gt(ObjectUtil.isNotEmpty(params.getStartDate()), BusSaleListEntity::getSaleListDatetime, params.getStartDate());


        List<BusSaleListEntity> lists = busSaleListMapper.selectList(saleListEntityLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(lists)) {
            Long userId = SatokenUtil.getLoginUserId();
            //本次可执行拆单的订单数量不为空，分别执行拆单任务
            for (BusSaleListEntity entity : lists) {
                DisassembleSaleListParams disassembleSaleListParams = new DisassembleSaleListParams();
                disassembleSaleListParams.setSaleListUnique(entity.getSaleListUnique());
                disassembleSaleListParams.setMaxValue(params.getMaxValue());
                disassembleSaleListParams.setMinValue(params.getMinValue());
                autoDisassembleSaleListSub(disassembleSaleListParams, userId);
            }
        }
        return Result.ok();
    }


    /**
     * 依次执行各订单的拆单任务
     * @param disassembleParams
     * @param userId
     * @return
     */
    public void autoDisassembleSaleListSub(DisassembleSaleListParams disassembleParams, Long userId) {
        //校验拆单信息是否存在
        LambdaQueryWrapper<DisassembleListEntity> disassembleListWrapper = new LambdaQueryWrapper<>();
        disassembleListWrapper.eq(DisassembleListEntity::getSaleListUnique, disassembleParams.getSaleListUnique());
        DisassembleListEntity disassembleListEntity = disassembleListMapper.selectOne(disassembleListWrapper);
        if (ObjectUtil.isNotNull(disassembleListEntity)) {
            //拆单信息已存在，跳过
        }

        log.info("本次执行的拆单订单为{}",disassembleParams);
        //拆单信息不存在，执行拆单操作
        //在子线程内处理信息，并返回操作成功
        ThreadUtil.execAsync(() -> {
            System.out.println("执行拆解订单任务");
            disassembleOrder(disassembleParams, userId);
        });
    }


    public void disassembleOrder(DisassembleSaleListParams disassembleSaleListParams, Long userId) {
        try {
            //获取当前订单的信息
            LambdaQueryWrapper<BusSaleListEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BusSaleListEntity::getSaleListUnique, disassembleSaleListParams.getSaleListUnique());

            BusSaleListEntity entity = busSaleListMapper.selectOne(wrapper);

            //将信息存储到分解表
            BusSaleListDisassembleEntity disassembleEntity = new BusSaleListDisassembleEntity();
            BeanUtil.copyProperties(entity,disassembleEntity);

            //根据最小值和最大值，计算本订单赢拆分的商品明细
            BigDecimal minValue = disassembleSaleListParams.getMinValue();
            BigDecimal maxValue = disassembleSaleListParams.getMaxValue();

            DisassembleListEntity disassembleListEntity = new DisassembleListEntity();
            disassembleListEntity.setCompanyId(entity.getCompanyId());
            disassembleListEntity.setCreateTime(DateUtil.date());
            disassembleListEntity.setOrderAmount(entity.getSaleListActuallyReceived());
            disassembleListEntity.setCreateUser(userId);
            disassembleListEntity.setSaleListUnique(entity.getSaleListUnique());
            disassembleListEntity.setDisassembleCount(0);
            disassembleListEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_ING.getValue());
            disassembleListEntity.setMinAmountSet(minValue);
            disassembleListEntity.setMaxAmountSet(maxValue);
            disassembleListEntity.setMinAmountActual(BigDecimal.ZERO);
            disassembleListEntity.setMaxAmountActual(BigDecimal.ONE);
            disassembleListEntity.setDisassmebleRemarks("");
            //开启线程，拆解订单信息
            disassembleListMapper.insert(disassembleListEntity);

            ThreadUtil.execAsync(() -> {
                log.info("创建子订单信息开始");
                try {
                    disassembleOrderSub(minValue, maxValue, entity.getSaleListActuallyReceived(),entity.getProfitTotal() ,entity.getShopUnique(), disassembleEntity.getSaleListUnique());
                } catch (Exception e) {
                    log.error("-----[订单拆解异常]-------------处理订单异常：{}-------------------", e);
                    //失败了，修改状态
                    disassembleListEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_FAIL.getValue());
                    disassembleListEntity.setDisassmebleRemarks(e.getMessage());
                    disassembleListMapper.updateById(disassembleListEntity);
                }
            });
        } catch (Exception e) {
            log.info("-----[订单拆解异常]-------------处理订单异常：{}-------------------", e);
        }
    }


    /**
     * 拆解订单
     * @param minValue
     * @param maxValue
     * @param totalMoney
     * @param shopUnique
     * @param saleListUnique
     */
    @Transactional(timeout = 300)
    public void disassembleOrderSub(BigDecimal minValue, BigDecimal maxValue, BigDecimal totalMoney, BigDecimal proTotal,Long shopUnique,String saleListUnique) {

        if (totalMoney.compareTo(maxValue) <= 0) {
            //当前订单的金额小于等于拆单的最大金额，不需要拆单
            return;
        }

        List<BusSaleListPayDetailEntity> saleListPayDetailEntities = busSaleListPayDetailMapper.selectList(new LambdaQueryWrapper<BusSaleListPayDetailEntity>().eq(BusSaleListPayDetailEntity::getSaleListUnique, saleListUnique));

        LambdaQueryWrapper<BusSaleListEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusSaleListEntity::getShopUnique, shopUnique);
        wrapper.eq(BusSaleListEntity::getSaleListUnique, saleListUnique);
        BusSaleListEntity entity = busSaleListMapper.selectOne(wrapper);
        Boolean isLast = false;

        BigDecimal payTotal = BigDecimal.ZERO;
        for (BusSaleListPayDetailEntity v : saleListPayDetailEntities) {
            payTotal = payTotal.add(v.getPayMoney());
        }

        if (payTotal.compareTo(totalMoney) != 0) {
            log.info("支付金额与订单金额不一致，请检查");
            DisassembleListEntity disassembleListEntity = new DisassembleListEntity();
            disassembleListEntity.setSaleListUnique(saleListUnique);
            disassembleListEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_FAIL.getValue());
            disassembleListEntity.setDisassmebleRemarks("支付详情金额与订单金额不一致，请检查");
            LambdaUpdateWrapper<DisassembleListEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DisassembleListEntity::getSaleListUnique, saleListUnique);
            disassembleListMapper.update(disassembleListEntity, updateWrapper);
            return;
        }


        //支付详情，需要根据当前定的的支付详情处理
        BigDecimal minActualAmount = totalMoney;
        BigDecimal maxActualAmount = BigDecimal.ZERO;

        Integer disassembleCount = 0;
        BigDecimal totalBalance = totalMoney;
        if (ObjectUtil.isNull(proTotal)) {
            proTotal = BigDecimal.ZERO;
        }
        BigDecimal totalProTotal = proTotal;
        while (totalBalance.compareTo(BigDecimal.ZERO) > 0) {
            log.info("当前生成的是第" + disassembleCount + "次");

            log.info("当前的数据为maxValue=" + maxValue + "minValue=" + minValue + "totalMoney= " + totalBalance);
            disassembleCount += 1;
            //依次创建订单号，并生成对应的订单信息
            //订单金额由最小值和最大值直接随机取值
            BigDecimal saleListTotal;
            if (maxValue.subtract(minValue).compareTo(BigDecimal.ONE) < 0) {
                saleListTotal = maxValue;
            } else {
                Integer ran = RandomUtil.randomInt(maxValue.subtract(minValue).intValue());
                saleListTotal = minValue.add(new BigDecimal(ran));
            }
            BigDecimal saleListPayTotalTmp = proTotal.multiply(saleListTotal).setScale(2,BigDecimal.ROUND_HALF_UP);
            BigDecimal saleListPayTotal = saleListPayTotalTmp.divide(totalMoney, 2,BigDecimal.ROUND_HALF_UP);
            if (totalBalance.compareTo(saleListTotal) < 0) {
                saleListTotal = totalBalance;
                saleListPayTotal = totalProTotal;
            }
            String saleListNewOrderno = CommonUtil.createOrder(redis);


            //创建订单信息
            CreateOrderSigleResult flag = cmbService.createOrderSigle(saleListUnique, saleListNewOrderno, saleListTotal, saleListPayTotal, shopUnique, saleListPayDetailEntities,isLast);
            //只有成功，才减少余额，并进行下一次循环
            if (flag.isFlag()) {
                //修改逻辑，找不到满足金额的商品，则按商品实际的数据扣除金额
                BigDecimal actualSaleListTotal = flag.getSaleListTotal();
                totalBalance = totalBalance.subtract(actualSaleListTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                totalProTotal = totalProTotal.subtract(saleListPayTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                saleListPayDetailEntities = flag.getSaleListPayDetailEntities();
                log.info("本次创建订单的金额" + actualSaleListTotal);
                log.info("本次订单结束后，剩余可用金额" + totalBalance);
                log.info("本次创建订单的成本金额" + saleListPayTotal);
                log.info("本次订单结算后，剩余可用金额" + totalProTotal);

                if (totalBalance.compareTo(maxValue) <= 0) {
                    isLast = true;
                }

                if (minActualAmount.compareTo(actualSaleListTotal) > 0) {
                    minActualAmount = actualSaleListTotal;
                }

                if (maxActualAmount.compareTo(actualSaleListTotal) < 0) {
                    maxActualAmount = actualSaleListTotal;
                }
                continue;
            }

            throw new RuntimeException(flag.getMsg());
        }

        //所有订单拆单完成，更新拆单任务状态
        DisassembleListEntity disassembleListEntity = disassembleListMapper.selectOne(new LambdaQueryWrapper<DisassembleListEntity>().eq(DisassembleListEntity::getSaleListUnique, saleListUnique));
        disassembleListEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_SUCCESS.getValue());
        disassembleListEntity.setMinAmountActual(minActualAmount);
        disassembleListEntity.setMaxAmountActual(maxActualAmount);
        disassembleListEntity.setDisassembleCount(disassembleCount);

        //将订单信息存储到分解表
        List<BusSaleListDetailEntity> saleListDetailEntities = busSaleListDetailMapper.selectList(new LambdaQueryWrapper<BusSaleListDetailEntity>().eq(BusSaleListDetailEntity::getSaleListUnique, saleListUnique));

        //删除原本的数据，添加数据到分解表
        busSaleListMapper.deleteById(entity);
        busSaleListDetailMapper.deleteBatchIds(saleListDetailEntities.stream().map(BusSaleListDetailEntity::getId).collect(Collectors.toList()));
        busSaleListPayDetailMapper.deleteBatchIds(saleListPayDetailEntities.stream().map(BusSaleListPayDetailEntity::getId).collect(Collectors.toList()));


        List<BusSaleListDisassembleDetailEntity> saleListDisassembleDetailEntities = new ArrayList<>();
        List<BusSaleListDisassemblePayDetailEntity> saleListDisassemblePayDetailEntities = new ArrayList<>();

        saleListDisassembleDetailEntities = saleListDetailEntities.stream().map(v -> {
            BusSaleListDisassembleDetailEntity disassembleDetailEntity = new BusSaleListDisassembleDetailEntity();
            BeanUtil.copyProperties(v,disassembleDetailEntity);
            return disassembleDetailEntity;
        }).collect(Collectors.toList());

        saleListDisassemblePayDetailEntities = saleListPayDetailEntities.stream().map(v -> {
            BusSaleListDisassemblePayDetailEntity disassemblePayDetailEntity = new BusSaleListDisassemblePayDetailEntity();
            BeanUtil.copyProperties(v,disassemblePayDetailEntity);
            return disassemblePayDetailEntity;
        }).collect(Collectors.toList());

        BusSaleListDisassembleEntity disassembleEntity = new BusSaleListDisassembleEntity();
        BeanUtil.copyProperties(entity,disassembleEntity);
        busSaleListDisassembleMapper.insert(disassembleEntity);
        busSaleListDisassembleDetailMapper.insertBatch(saleListDisassembleDetailEntities);
        busSaleListDisassemblePayDetailMapper.insertBatch(saleListDisassemblePayDetailEntities);


        LambdaQueryWrapper<DisassembleListEntity> disassembleEntityWrapper = new LambdaQueryWrapper<>();
        disassembleEntityWrapper.eq(DisassembleListEntity::getSaleListUnique, disassembleListEntity.getSaleListUnique());
        disassembleListMapper.update(disassembleListEntity, disassembleEntityWrapper);
    }

    /**
     * 订单列表
     * @param params
     * @return
     */
    @Override
    public Result<SaleListResult> saleList(SaleListParams params) {

        //校验店铺的数据信息
        List<Long> shopUniqueList = null;
        if (ObjectUtil.isNotEmpty(params.getTownCode()) || ObjectUtil.isNotEmpty(params.getCountyCode()) || ObjectUtil.isNotEmpty(params.getShopName())) {
            LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
            shopWrapper.eq(BusShopEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
            shopWrapper.like(ObjectUtil.isNotEmpty(params.getShopName()), BusShopEntity::getShopName, params.getShopName());
            if (ObjectUtil.isNotEmpty(params.getCountyCode())) {
                shopWrapper.eq(BusShopEntity::getCountyCode, params.getCountyCode().substring(0,6));
            }
            if  (ObjectUtil.isNotEmpty(params.getTownCode())) {
                shopWrapper.eq(BusShopEntity::getTownCode, params.getTownCode().substring(0,9));
            }
            List<BusShopEntity> shopList = busShopMapper.selectList(shopWrapper);
            if (CollectionUtil.isNotEmpty(shopList)) {
                shopUniqueList = shopList.stream().map(BusShopEntity::getShopUnique).collect(Collectors.toList());
            } else {
                //没有满足条件的店铺信息
                return Result.ok(new SaleListResult());
            }
        }
        //查询数据
        LambdaQueryWrapper<BusSaleListEntity> saleListWrapper = handleListWrapper(params);
        saleListWrapper.in(ObjectUtil.isNotEmpty(shopUniqueList), BusSaleListEntity::getShopUnique, shopUniqueList);
        saleListWrapper.orderByDesc(BusSaleListEntity::getSaleListDatetime);
        if (ObjectUtil.isNotNull(params.getPageIndex())) {
            PageUtils.startPage(params);
        }
        List<BusSaleListEntity> saleList = busSaleListMapper.selectList(saleListWrapper);

        Map<String, BigDecimal> goodsCountMap = new HashMap<>();
        Map<String, String> payTypeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(saleList)) {
            Set<String> shopUniques = saleList.stream().map(v -> v.getShopUnique().toString()).collect(Collectors.toSet());
            shopUniqueList = shopUniques.stream().map(v -> Long.parseLong(v)).collect(Collectors.toList());
            QueryWrapper<BusSaleListDetailEntity> detailQuery = new QueryWrapper<>();
            detailQuery.select("sale_list_unique, sum(sale_list_detail_count) as count");
            detailQuery.lambda().eq(BusSaleListDetailEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId())
                    .in(BusSaleListDetailEntity::getSaleListUnique, shopUniques)
                    .groupBy(BusSaleListDetailEntity::getSaleListUnique);
            List<Map<String, Object>> countMap = busSaleListDetailMapper.selectMaps(detailQuery);
            for (Map<String, Object> m : countMap) {
                String shopUnique = (String) m.get("sale_list_unique");
                BigDecimal count = (BigDecimal) m.get("count");
                goodsCountMap.put(shopUnique, count);
            }

            QueryWrapper<BusSaleListPayDetailEntity> payDetailQuery = new QueryWrapper<>();
            payDetailQuery.select("DISTINCT sale_list_unique, server_type, pay_method");
            payDetailQuery.lambda().eq(BusSaleListPayDetailEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId())
                    .in(BusSaleListPayDetailEntity::getSaleListUnique, shopUniques);
            List<BusSaleListPayDetailEntity> payDetailList = busSaleListPayDetailMapper.selectList(payDetailQuery);
            for (BusSaleListPayDetailEntity entity : payDetailList) {
                String payType = convertPayType(entity.getPayMethod());
                if (StrUtil.isNotBlank(payType)) {
                    String v = payTypeMap.get(entity.getSaleListUnique());
                    if (StrUtil.isNotBlank(v)) {
                        payTypeMap.put(entity.getSaleListUnique(), v + "," + payType);
                    } else {
                        payTypeMap.put(entity.getSaleListUnique(), payType);
                    }
                }
            }
        }

        //数据转换
        List<SaleListDto> dtoList = new ArrayList<>();
        for (BusSaleListEntity entity : saleList) {
            SaleListDto dto = new SaleListDto();
            BeanUtil.copyProperties(entity,dto);
            dto.setSaleListDatetime(DateUtil.format(entity.getSaleListDatetime(), DatePattern.NORM_DATETIME_PATTERN));
            dto.setGoodsCount(MapUtil.get(goodsCountMap, entity.getSaleListUnique(), BigDecimal.class, BigDecimal.ONE));
            dto.setPayTypeName(MapUtil.getStr(payTypeMap, entity.getSaleListUnique(), StrUtil.EMPTY));
            if (ObjectUtil.isNull(dto.getParentListUnique())) {
                dto.setParentListUnique(dto.getSaleListUnique());
            }
            dtoList.add(dto);
        }

        //获取店铺的信息
        LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper.in(ObjectUtil.isNotEmpty(shopUniqueList),  BusShopEntity::getShopUnique, shopUniqueList);
        List<BusShopEntity> shops = busShopMapper.selectList(shopWrapper);

        Result<SaleListResult> result = convertPageData(saleList, dtoList, SaleListResult.class, params);
        SaleListResult data = result.getData();
        data.setTotalActuallyReceived(BigDecimal.ZERO);

        //获取店铺的区域信息
        List<Cnarea2023Entity> areaList = new ArrayList<>();
        List<Cnarea2023Entity> townList = new ArrayList<>();

        List<String> areaCodes = shops.stream().map(v -> v.getCountyCode() == null ? null:v.getCountyCode() + "000000").filter(v -> v != null).collect(Collectors.toList());
        List<String> townCodes = shops.stream().map(v -> v.getTownCode() == null ? null:v.getTownCode() + "000").filter(v -> v != null).collect(Collectors.toList());

        LambdaQueryWrapper<Cnarea2023Entity> areaQuery = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(areaCodes)) {
            areaQuery.in(ObjectUtil.isNotEmpty(areaCodes), Cnarea2023Entity::getAreaCode,  areaCodes);
            areaList = cnarea2023Mapper.selectList(areaQuery);
        }

        if (CollectionUtil.isNotEmpty(townCodes)) {
            areaQuery.clear();
            areaQuery.in(ObjectUtil.isNotEmpty(townCodes), Cnarea2023Entity::getAreaCode,  townCodes);
            townList = cnarea2023Mapper.selectList(areaQuery);
        }

        List<SaleListDto> rows =data.getRows();
        for (SaleListDto dto : rows) {
            BusShopEntity shop = shops.stream().filter(v -> ObjectUtil.equals(dto.getShopUnique().toString(), v.getShopUnique().toString())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(shop)) {
                if (ObjectUtil.isNotEmpty(shop.getCountyCode())) {
                    Cnarea2023Entity areaEntity = areaList.stream().filter(v -> ObjectUtil.equals(shop.getCountyCode().toString() + "000000", v.getAreaCode().toString())).findFirst().orElse(null);
                    if (ObjectUtil.isNotNull(areaEntity)) {
                        dto.setAreaName(areaEntity.getMergerName());
                    }
                }
                if (ObjectUtil.isNotEmpty(shop.getTownCode())) {
                    Cnarea2023Entity townEntity = townList.stream().filter(v -> ObjectUtil.equals(shop.getTownCode().toString() + "000", v.getAreaCode().toString())).findFirst().orElse(null);
                    if (ObjectUtil.isNotNull(townEntity)) {
                        dto.setTownName(townEntity.getName());
                    }
                }
                log.info("店铺性质:{}", shop.getShopNature());
                dto.setShopNatureDesc(ShopNatureEnum.getName(shop.getShopNature()));
            }
        }

        //查询统计数据
        QueryWrapper<BusSaleListEntity> queryWrapper = Wrappers.query();
        queryWrapper.select("IFNULL(sum(sale_list_actually_received),0) as count");

        queryWrapper.lambda().eq(BusSaleListEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        queryWrapper.lambda().eq(ObjectUtil.isNotNull(params.getShopUnique()), BusSaleListEntity::getShopUnique, params.getShopUnique());
        queryWrapper.lambda().eq(StrUtil.isNotBlank(params.getSaleListUnique()), BusSaleListEntity::getSaleListUnique, params.getSaleListUnique());
        queryWrapper.lambda().between(ObjectUtil.isNotEmpty(params.getDateStart()), BusSaleListEntity::getSaleListDatetime, params.getDateStart(), params.getDateEnd());
        queryWrapper.lambda().eq(StrUtil.isNotBlank(params.getOrderType()), BusSaleListEntity::getOrderType, params.getOrderType());
        queryWrapper.lambda().eq(ObjectUtil.isNotEmpty(shopUniqueList), BusSaleListEntity::getShopUnique, shopUniqueList);

        List<Object> resultData = busSaleListMapper.selectObjs(queryWrapper);
        data.setTotalActuallyReceived(CollectionUtil.isNotEmpty(resultData) ? (BigDecimal) resultData.get(0) : BigDecimal.ZERO);
        return result;
    }

    /**
     * 订单详情
     * @param params
     * @return
     */
    @Override
    public Result<SaleListDetailResult> saleListDetail(SaleListDetailParams params) {
        LoginUser loginUser = SatokenUtil.getLoginUser();
        //订单查询
        BusSaleListEntity saleList = busSaleListMapper.selectById(params.getId());
        if (ObjectUtil.isEmpty(saleList) || !saleList.getCompanyId().equals(loginUser.getCompanyId())) throw new BusinessException(CommonErrorEnum.PARAM_ERROR);

        //查询订单详情
        List<BusSaleListDetailEntity> detailList = querySaleListDetail(saleList.getSaleListUnique());

        //查询支付详情
        List<BusSaleListPayDetailEntity> payDetailList = querySaleListPayDetail(saleList.getSaleListUnique());

        SaleListDetailResult result = new SaleListDetailResult();
        List<SaleListDetailDto> saleListDetailDtos = BeanUtil.copyToList(detailList, SaleListDetailDto.class);
        for (SaleListDetailDto d : saleListDetailDtos) {
            d.setSaleListDetailSubtotal(NumberUtil.mul(d.getSaleListDetailPrice(),d.getSaleListDetailCount()));
        }
        result.setDetailList(saleListDetailDtos);
        List<SaleListDetailPayDto> payList = new ArrayList<>();
        for (BusSaleListPayDetailEntity pd : payDetailList) {
            SaleListDetailPayDto dto = new SaleListDetailPayDto();
            dto.setPayMoney(pd.getPayMoney());
            dto.setPayType(convertPayType(pd.getPayMethod()));
            payList.add(dto);
        }

        result.setPayList(payList);

        return Result.ok(result);
    }

    @Override
    public int updateProfitRecords(List<BusSaleListEntity> updateSaleList) {
        return busSaleListMapper.updateBatchProfitRecords(updateSaleList);
    }

    /**
     * 查询数据
     * @param params
     * @return
     */
    private LambdaQueryWrapper<BusSaleListEntity> handleListWrapper(SaleListParams params) {
        LambdaQueryWrapper<BusSaleListEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusSaleListEntity::getCompanyId, SatokenUtil.getLoginUserCompanyId());
        wrapper.eq(ObjectUtil.isNotNull(params.getShopUnique()), BusSaleListEntity::getShopUnique, params.getShopUnique());
        wrapper.eq(StrUtil.isNotBlank(params.getSaleListUnique()), BusSaleListEntity::getSaleListUnique, params.getSaleListUnique());
        wrapper.between(ObjectUtil.isNotEmpty(params.getDateStart()), BusSaleListEntity::getSaleListDatetime, params.getDateStart(), params.getDateEnd());
        wrapper.eq(StrUtil.isNotBlank(params.getOrderType()), BusSaleListEntity::getOrderType, params.getOrderType());
        return wrapper;
    }

    /**
     * 查询订单详情
     * @param saleListUnique
     * @return
     */
    private List<BusSaleListDetailEntity> querySaleListDetail(String saleListUnique) {
        LambdaQueryWrapper<BusSaleListDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusSaleListDetailEntity::getSaleListUnique,saleListUnique);
        return busSaleListDetailMapper.selectList(wrapper);
    }

    /**
     * 查询支付详情
     * @param saleListUnique
     * @return
     */
    private List<BusSaleListPayDetailEntity> querySaleListPayDetail(String saleListUnique) {
        LambdaQueryWrapper<BusSaleListPayDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusSaleListPayDetailEntity::getSaleListUnique,saleListUnique);
        return busSaleListPayDetailMapper.selectList(wrapper);
    }

    /**
     * 支付方式转换
     * @param payMethod
     * @return
     */
    private String convertPayType(Integer payMethod) {
        String resp = "在线支付";
        switch (payMethod) {
            case 1:
                resp = "现金";
                break;
            case 2:
                resp = "支付宝";
                break;
            case 3:
                resp = "微信";
                break;
            case 4:
                resp = "银行卡";
                break;
            case 5:
                resp = "储值卡";
                break;
            case 6:
                resp = "美团外卖";
                break;
            case 7:
                resp = "饿了么外卖";
                break;
            case 9:
                resp = "免密支付";
                break;
            case 10:
                resp = "积分兑换";
                break;
            case 11:
                resp = "百货豆";
                break;
            case 12:
                resp = "拉卡拉";
                break;
            case 13:
                resp = "易通支付";
                break;
            case 15:
                resp = "银联支付";
                break;
            case 16:
                resp = "合利宝";
                break;
            case 17:
                resp = "银行转账";
                break;
        }
        return resp;
    }
}
