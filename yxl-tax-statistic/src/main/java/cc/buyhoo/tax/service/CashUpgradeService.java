package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.upgrade.params.cashUpgrade.*;
import cc.buyhoo.upgrade.result.cashUpgrade.GetMaxVersionResult;
import cc.buyhoo.upgrade.result.cashUpgrade.QueryShopsResult;
import cc.buyhoo.upgrade.result.cashUpgrade.QueryUpgradeShopResult;
import cc.buyhoo.upgrade.result.cashUpgrade.UpgradeListResult;

public interface CashUpgradeService {

    /**
     * 版本列表
     * @param params
     * @return
     */
    public Result<UpgradeListResult> upgradeList(UpgradeListParams params);

    /**
     * 获取最高版本
     * @param params
     * @return
     */
    public Result<GetMaxVersionResult> getMaxVersion(GetMaxVersionParams params);

    /**
     * 新增版本
     * @param params
     * @return
     */
    public Result<Void> addUpgrade(AddUpgradeParams params);

    /**
     * 修改版本
     * @param params
     * @return
     */
    public Result<Void> updateUpgrade(UpdateUpgradeParams params);

    /**
     * 删除版本
     * @param params
     * @return
     */
    public Result<Void> deleteUpgrade(DeleteUpgradeParams params);

    /**
     * 刷新版本
     * @param params
     * @return
     */
    public Result<Void> refreshUpgrade(RefreshUpgradeParams params);

    /**
     * 版本店铺绑定
     * @param params
     * @return
     */
    public Result<Void> bindUpgradeShop(BindUpgradeShopParams params);

    /**
     * 店铺查询
     * @param params
     * @return
     */
    public Result<QueryShopsResult> queryShops(QueryShopsParams params);

    /**
     * 升级绑定店铺查询
     * @param params
     * @return
     */
    public Result<QueryUpgradeShopResult> queryUpgradeShop(QueryUpgradeShopParams params);
}
