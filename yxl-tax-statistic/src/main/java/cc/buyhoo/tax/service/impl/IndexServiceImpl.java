package cc.buyhoo.tax.service.impl;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.*;
import cc.buyhoo.tax.result.indexData.RevenueTargetResult;
import cc.buyhoo.tax.result.indexData.SaleListDataResult;
import cc.buyhoo.tax.service.IndexService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.YearMonth;
import java.util.*;

/**
 * @Description
 * @ClassName IndexServiceImpl
 * <AUTHOR>
 * @Date 2023/7/31 17:26
 **/
@Slf4j
@Service
public class IndexServiceImpl implements IndexService {

    @Resource
    private BusSaleListMapper busSaleListMapper;
    @Resource
    private SysCompanyMapper sysCompanyMapper;
    @Resource
    private BusInventoryOrderMapper busInventoryOrderMapper;

    @Resource
    private BusReturnOrderMapper busReturnOrderMapper;
    @Resource
    private BusReturnListMapper busReturnListMapper;
    @Resource
    private BusShopInvoiceMapper busShopInvoiceMapper;
    @Resource
    private BusShopMapper busShopMapper;
    @Autowired
    private BusShopCoverChargeMapper busShopCoverChargeMapper;

    @Override
    public Result<RevenueTargetResult> revenueTarget() {
        RevenueTargetResult targetResult = new RevenueTargetResult();
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        SysCompanyEntity companyEntity = sysCompanyMapper.selectById(companyId);
        String taxType = companyEntity.getTaxType();
        //计算开始结束时间
        Map<String, Date> dateMap = handleDateByTaxType(taxType);
        Date dateStart = dateMap.get("dateStart");
        Date dateEnd = dateMap.get("dateEnd");
        Date currentDate = DateUtil.date();

        long betweenDay = DateUtil.betweenDay(dateStart, dateEnd, true) + 1;
        long currentDays = DateUtil.betweenDay(dateStart, currentDate, true) + 1;

        //日目标
        BigDecimal dayTarget = NumberUtil.div(companyEntity.getTargetAmount(), betweenDay, 0, RoundingMode.CEILING);
        BigDecimal monthTarget = BigDecimal.ZERO;
        if (TaxTypeEnum.SEASON.getValue().equals(taxType) || TaxTypeEnum.YEAR.getValue().equals(taxType)) {
            //月目标
            int month = TaxTypeEnum.SEASON.getValue().equals(taxType) ? 3 : 12;
            monthTarget = NumberUtil.div(companyEntity.getTargetAmount(), month, 0, RoundingMode.CEILING);
        }
        BigDecimal quarterTarget = BigDecimal.ZERO;
        if (TaxTypeEnum.YEAR.getValue().equals(taxType)) {
            //季度目标
            quarterTarget = NumberUtil.div(companyEntity.getTargetAmount(), 4, 0, RoundingMode.CEILING);
        }
        //已完成纳统目标
        QueryWrapper<BusShopInvoiceEntity> invoiceQueryWrapper = new QueryWrapper<>();
        invoiceQueryWrapper.select("sum(order_money) as orderMoney");
        invoiceQueryWrapper.lambda()
                .eq(BusShopInvoiceEntity::getCompanyId, companyEntity.getId())
                .eq(BusShopInvoiceEntity::getStatus, InvoiceStatusEnum.INVOICE_STATUS_1.getValue())
                .eq(BusShopInvoiceEntity::getInvoiceType, ShopInvoiceTypeEnum.OUTPUT.getValue())
                .between(BusShopInvoiceEntity::getInvoiceDate, dateStart, dateEnd);
        List<Object> invoiceList = busShopInvoiceMapper.selectObjs(invoiceQueryWrapper);
        BigDecimal finishAmount = BigDecimal.ZERO;
        if (null != invoiceList && invoiceList.size() > 0 && ObjectUtil.isNotNull(invoiceList.get(0))) {
            finishAmount = (BigDecimal) invoiceList.get(0);
        }
        BigDecimal dayFinish = BigDecimal.ZERO;
        BigDecimal monthFinish = BigDecimal.ZERO;
        BigDecimal quarterFinish = BigDecimal.ZERO;
        targetResult.setTaxType(taxType);
        if (BigDecimal.ZERO.compareTo(finishAmount) == 0) {
            targetResult.setFinishDate(DateUtil.formatDate(dateEnd));
            targetResult.setFinishAmountFormat(BigDecimal.ZERO);
        } else {
            Integer needDays = NumberUtil.div(companyEntity.getTargetAmount(), NumberUtil.div(finishAmount, currentDays)).setScale(0, RoundingMode.UP).intValue();
            targetResult.setFinishDate(DateUtil.formatDate(DateUtil.offsetDay(dateStart, needDays)));
            targetResult.setFinishAmountFormat(formatWan(finishAmount));
            dayFinish = NumberUtil.div(finishAmount, currentDays).setScale(2, RoundingMode.HALF_DOWN);
            if (StrUtil.equals(TaxTypeEnum.YEAR.getValue(), taxType)) {
                monthFinish = NumberUtil.div(finishAmount, DateUtil.month(currentDate) + 1).setScale(2, RoundingMode.HALF_DOWN);
                quarterFinish = NumberUtil.div(finishAmount, DateUtil.quarter(currentDate)).setScale(2, RoundingMode.HALF_DOWN);
            } else if (StrUtil.equals(TaxTypeEnum.SEASON.getValue(), taxType)) {
                Date st = DateUtil.beginOfQuarter(currentDate);
                long m = DateUtil.betweenMonth(st, currentDate, true) + 1;
                monthFinish = NumberUtil.div(finishAmount, m).setScale(2, RoundingMode.HALF_DOWN);
                quarterFinish = finishAmount;
            } else {
                monthFinish = finishAmount;
            }
        }
        targetResult.setDateStart(DateUtil.formatDate(dateStart));
        targetResult.setDateEnd(DateUtil.formatDate(dateEnd));
        targetResult.setTargetAmount(companyEntity.getTargetAmount());
        targetResult.setTargetAmountFormat(formatWan(targetResult.getTargetAmount()));
        targetResult.setFinishAmount(finishAmount);
        targetResult.setFinishAmountRatio(formatRatio(targetResult.getFinishAmount(), targetResult.getTargetAmount()));
        targetResult.setDayTarget(dayTarget);
        targetResult.setDayTargetFormat(formatWan(dayTarget));
        targetResult.setDayFinish(dayFinish);
        targetResult.setDayFinishFormat(formatWan(dayFinish));
        targetResult.setDayFinishRatio(formatRatio(dayFinish, dayTarget));
        targetResult.setMonthTarget(monthTarget);
        targetResult.setMonthTargetFormat(formatWan(monthTarget));
        targetResult.setMonthFinish(monthFinish);
        targetResult.setMonthFinishFormat(formatWan(monthFinish));
        targetResult.setMonthFinishRatio(formatRatio(monthFinish, monthTarget));
        targetResult.setQuarterTarget(quarterTarget);
        targetResult.setQuarterTargetFormat(formatWan(quarterTarget));
        targetResult.setQuarterFinish(quarterFinish);
        targetResult.setQuarterFinishFormat(formatWan(quarterFinish));
        targetResult.setQuarterFinishRatio(formatRatio(quarterFinish, quarterTarget));
        return Result.ok(targetResult);
    }

    @Override
    public Result<SaleListDataResult> saleListInfo() {
        SaleListDataResult dataResult = new SaleListDataResult();
        Long companyId = SatokenUtil.getLoginUserCompanyId();
        SysCompanyEntity companyEntity = sysCompanyMapper.selectById(companyId);
        String taxType = companyEntity.getTaxType();
        //计算开始结束时间
        Map<String, Date> dateMap = handleDateByTaxType(taxType);
        Date dateStart = dateMap.get("dateStart");
        Date dateEnd = dateMap.get("dateEnd");
        Long shopCount = busShopMapper.selectCount(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, companyId).eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
        dataResult.setShopCount(shopCount);
        QueryWrapper<BusSaleListEntity> saleListQueryWrapper = new QueryWrapper<>();
        saleListQueryWrapper.select("order_type as orderType, count(1) as saleListCount, sum(sale_list_actually_received) totalMoney, sum(profit_total) as profitTotal, sum(pay_fee) as payFee, sum(sale_list_service_fee) as saleListServiceFee");
        saleListQueryWrapper.lambda().eq(BusSaleListEntity::getCompanyId, companyId)
                .between(BusSaleListEntity::getSaleListDatetime, dateStart, dateEnd)
                        .groupBy(BusSaleListEntity::getOrderType);
        List<Map<String, Object>> saleLists = busSaleListMapper.selectMaps(saleListQueryWrapper);
        if (CollectionUtil.isNotEmpty(saleLists)) {
            Long saleListCount = 0L;
            for (Map<String, Object> m : saleLists) {
                String orderType = (String) m.get("orderType");
                Long count = (Long) m.getOrDefault("saleListCount", 0);
                BigDecimal totalAmount = (BigDecimal) m.getOrDefault("totalMoney", BigDecimal.ZERO);
                if (StrUtil.equals(SaleListOrderTypeEnum.ORDER_TYPE_1.getCode(), orderType)) {
                    dataResult.setOtherSaleListCount(count);
                    saleListCount += count;
                    dataResult.setOtherTotalAmount(totalAmount);
                } else if (StrUtil.equals(SaleListOrderTypeEnum.ORDER_TYPE_2.getCode(), orderType)) {
                    BigDecimal profitAmount = (BigDecimal) m.getOrDefault("profitTotal", BigDecimal.ZERO);
                    dataResult.setTotalProfitAmount(profitAmount);
                    dataResult.setJoinSaleListCount(count);
                    BigDecimal payFee = (BigDecimal) m.getOrDefault("payFee", BigDecimal.ZERO);
                    dataResult.setPayFee(payFee);
                    saleListCount += count;
                }
                dataResult.setTotalAmount(NumberUtil.add(dataResult.getTotalAmount(), totalAmount));
            }
            dataResult.setSaleListCount(saleListCount);
        }

        // 其他服务支出
        QueryWrapper<BusShopCoverChargeEntity> queryShopCoverChargeWrapper = new QueryWrapper<>();
        queryShopCoverChargeWrapper.select("sum(cover_charge) as totalMoney");
        queryShopCoverChargeWrapper.lambda()
                .eq(BusShopCoverChargeEntity::getCompanyId, companyId)
                .between(BusShopCoverChargeEntity::getStatDate, dateStart, dateEnd);
        List<Object> coverChargeList = busShopCoverChargeMapper.selectObjs(queryShopCoverChargeWrapper);
        if (CollectionUtil.isNotEmpty(coverChargeList) && ObjectUtil.isNotEmpty(coverChargeList.get(0))) {
            dataResult.setSaleListServiceFee((BigDecimal) coverChargeList.get(0));
        }

        // 计算联营订单营业收入、不含税金额
        QueryWrapper<BusShopInvoiceEntity> invoiceQueryWrapper = new QueryWrapper<>();
        invoiceQueryWrapper.select("sum(order_tax_money) as orderTaxMoney, sum(order_money) orderMoney");
        invoiceQueryWrapper.lambda().eq(BusShopInvoiceEntity::getCompanyId, companyId)
                .eq(BusShopInvoiceEntity::getStatus, InvoiceStatusEnum.INVOICE_STATUS_1.getValue())
                .eq(BusShopInvoiceEntity::getInvoiceType, ShopInvoiceTypeEnum.OUTPUT.getValue())
                .between(BusShopInvoiceEntity::getInvoiceDate, dateStart, dateEnd);
        List<Map<String, Object>> joinList = busShopInvoiceMapper.selectMaps(invoiceQueryWrapper);
        if (CollectionUtil.isNotEmpty(joinList) && joinList.size() > 0 && ObjectUtil.isNotNull(joinList.get(0))) {
            Map<String, Object> joinM = joinList.get(0);
            dataResult.setJoinTotalAmount((BigDecimal) joinM.getOrDefault("orderTaxMoney", BigDecimal.ZERO));
            dataResult.setExcludingTaxTotalAmount((BigDecimal) joinM.getOrDefault("orderMoney", BigDecimal.ZERO));
        }
        dataResult.setFormatTotalAmount(formatWan(dataResult.getTotalAmount()));
        dataResult.setFormatJoinTotalAmount(formatWan(dataResult.getJoinTotalAmount()));
        dataResult.setFormatExcludingTaxTotalAmount(formatWan(dataResult.getExcludingTaxTotalAmount()));
        dataResult.setFormatOtherTotalAmount(formatWan(dataResult.getOtherTotalAmount()));

        dataResult.setTotalPayAmount(NumberUtil.add(dataResult.getTotalProfitAmount(), dataResult.getPayFee(), dataResult.getSaleListServiceFee()));
        dataResult.setFormatTotalPayAmount(formatWan(dataResult.getTotalPayAmount()));
        dataResult.setFormatPayFee(formatWan(dataResult.getPayFee()));
        dataResult.setFormatSaleListServiceFee(formatWan(dataResult.getSaleListServiceFee()));
        dataResult.setFormatTotalProfitAmount(formatWan(dataResult.getTotalProfitAmount()));

        // 所有已开进项票不含税金额之和
        invoiceQueryWrapper = new QueryWrapper<>();
        invoiceQueryWrapper.select("sum(order_money) as orderMoney");
        invoiceQueryWrapper.lambda().eq(BusShopInvoiceEntity::getCompanyId, companyId)
                .eq(BusShopInvoiceEntity::getStatus, InvoiceStatusEnum.INVOICE_STATUS_1.getValue())
                .eq(BusShopInvoiceEntity::getInvoiceType, ShopInvoiceTypeEnum.INCOME.getValue())
                .between(BusShopInvoiceEntity::getInvoiceDate, dateStart, dateEnd);
        List<Object> inList = busShopInvoiceMapper.selectObjs(invoiceQueryWrapper);
        BigDecimal inTotalAmount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(inList) && inList.size() > 0 && ObjectUtil.isNotNull(inList.get(0))) {
            inTotalAmount = (BigDecimal) inList.get(0);
        }
        // 增值税
        BigDecimal taxFee = NumberUtil.mul(NumberUtil.sub(dataResult.getExcludingTaxTotalAmount(), inTotalAmount), NumberUtil.div(companyEntity.getVatRate(), BigDecimal.valueOf(100))).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (BigDecimal.ZERO.compareTo(taxFee) == 1) {
            taxFee = BigDecimal.ZERO;
        }
        //增值税：=（不含税营业收入-所有已开进项票不含税金额之和）*企业增值税税率（按企业新增配置），若为负数，赋值0.00
        dataResult.setTaxFee(taxFee);
        dataResult.setFormatTaxFee(formatWan(taxFee));
        //城市维护建设税：=增值税*0.07，若为负数，赋值0.00
        dataResult.setCityFee(NumberUtil.mul(taxFee, BigDecimal.valueOf(0.07)).setScale(2, BigDecimal.ROUND_HALF_UP));
        dataResult.setFormatCityFee(formatWan(dataResult.getCityFee()));
        //教育费附加：=增值税*0.03，若为负数，赋值0.00
        dataResult.setEduFee(NumberUtil.mul(taxFee, BigDecimal.valueOf(0.03)).setScale(2, BigDecimal.ROUND_HALF_UP));
        dataResult.setFormatEduFee(formatWan(dataResult.getEduFee()));
        //地方教育费附加：=增值税*0.02，若为负数，赋值0.00
        dataResult.setLocalEduFee(NumberUtil.mul(taxFee, BigDecimal.valueOf(0.02)).setScale(2, BigDecimal.ROUND_HALF_UP));
        dataResult.setFormatLocalEduFee(formatWan(dataResult.getLocalEduFee()));
        //印花税：=（不含税营业收入+所有已开进项票不含税金额之和）*0.0003
        dataResult.setStampTax(NumberUtil.mul(NumberUtil.add(dataResult.getExcludingTaxTotalAmount(), inTotalAmount), BigDecimal.valueOf(0.0003)).setScale(2, BigDecimal.ROUND_HALF_UP));
        dataResult.setFormatStampTax(formatWan(dataResult.getStampTax()));

        //营业利润 = 不含税营业收入-所有已开进项票不含税金额之和-城市维护建设税-教育费附加-地方教育费附加-印花税-交易手续费
        dataResult.setOperatingProfit(NumberUtil.sub(dataResult.getExcludingTaxTotalAmount(), inTotalAmount, dataResult.getCityFee(), dataResult.getEduFee(), dataResult.getLocalEduFee(), dataResult.getStampTax(), dataResult.getPayFee()));
        dataResult.setFormatOperatingProfit(formatWan(dataResult.getOperatingProfit()));
        //毛利润：=不含税营业收入-所有已开进项票不含税金额之和
        dataResult.setGrossProfit(NumberUtil.sub(dataResult.getExcludingTaxTotalAmount(), inTotalAmount));
        dataResult.setFormatGrossProfit(formatWan(dataResult.getGrossProfit()));
        //毛利润率：=毛利润/不含税营业收入*100%
        if (BigDecimal.ZERO.compareTo(dataResult.getExcludingTaxTotalAmount()) != 0) {
            dataResult.setGrossProfitRate(NumberUtil.div(NumberUtil.mul(dataResult.getGrossProfit(), BigDecimal.valueOf(100)), dataResult.getExcludingTaxTotalAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
        } else {
            dataResult.setGrossProfitRate(BigDecimal.ZERO);
        }
        //企业所得税：企业利润*0.25，若为负数，赋值0.00
        BigDecimal businessTax = NumberUtil.mul(dataResult.getOperatingProfit(), BigDecimal.valueOf(0.25)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (BigDecimal.ZERO.compareTo(businessTax) == 1) {
            businessTax = BigDecimal.ZERO;
        }
        dataResult.setBusinessTax(businessTax);
        dataResult.setFormatBusinessTax(formatWan(dataResult.getBusinessTax()));
        dataResult.setTotalTaxFee(NumberUtil.add(dataResult.getTaxFee(), dataResult.getCityFee(), dataResult.getEduFee(), dataResult.getLocalEduFee(), dataResult.getStampTax(), dataResult.getBusinessTax()));
        dataResult.setFormatTotalTaxFee(formatWan(dataResult.getTotalTaxFee()));
        return Result.ok(dataResult);
    }

    /**
     * 根据纳统周期计算时间
     *
     * @param taxType
     * @return
     */
    private Map<String, Date> handleDateByTaxType(String taxType) {
        Date d = DateUtil.date();
        Map<String, Date> map = new HashMap<>();
        Date dateStart = null;
        Date dateEnd = null;
        if (TaxTypeEnum.MONTH.getValue().equals(taxType)) { //按月统计
            dateStart = DateUtil.beginOfMonth(d);
            dateEnd = DateUtil.endOfMonth(d);
        } else if (TaxTypeEnum.SEASON.getValue().equals(taxType)) { //按季度统计
            dateStart = DateUtil.beginOfQuarter(d);
            dateEnd = DateUtil.endOfQuarter(d);
        } else if (TaxTypeEnum.YEAR.getValue().equals(taxType)) { //按年统计
            dateStart = DateUtil.beginOfYear(d);
            dateEnd = DateUtil.endOfYear(d);
        } else {
            throw new BusinessException(SystemErrorEnum.SYSTEM_ERROR.getCode(), "纳统周期类型错误");
        }
        map.put("dateStart", dateStart);
        map.put("dateEnd", dateEnd);
        return map;
    }

    /**
     * 查询营业额相关
     *
     * @return
     */
    private Map<String, Object> countTurnover(Long companyId, String dateStart, String dateEnd) {
        Map<String, Object> map = new HashMap<>();
        BigDecimal inventoryTotalMoney = BigDecimal.ZERO, inventoryOnlineMoney = BigDecimal.ZERO, inventoryCashMoney = BigDecimal.ZERO;
        BigDecimal returnTotalMoney = BigDecimal.ZERO, returnOnlineMoney = BigDecimal.ZERO, returnCashMoney = BigDecimal.ZERO;
        BigDecimal serviceFee = BigDecimal.ZERO, returnServiceFee = BigDecimal.ZERO;

        //查询入库单
        QueryWrapper<BusInventoryOrderEntity> inventoryWrapper = new QueryWrapper<>();
        inventoryWrapper.select("sum(total_money) as total_money, 0 as online_money, 0 as cash_money");
        inventoryWrapper.lambda().eq(BusInventoryOrderEntity::getCompanyId, companyId)
                .between(BusInventoryOrderEntity::getCreateTime, dateStart, dateEnd)
                .in(BusInventoryOrderEntity::getInventoryType, getInventoryTypeList());
        List<Map<String, Object>> inventoryList = busInventoryOrderMapper.selectMaps(inventoryWrapper);

        //查询销售订单
        QueryWrapper<BusSaleListEntity> saleWrapper = new QueryWrapper<>();
        saleWrapper.select("sum(sale_list_actually_received) as total_money, 0 as cash_money, 0 as service_fee");
        saleWrapper.lambda().eq(BusSaleListEntity::getCompanyId, companyId)
                .between(BusSaleListEntity::getCreateTime, dateStart, dateEnd)
                .in(BusSaleListEntity::getSaleListState, getSaleListStateList());
        List<Map<String, Object>> saleListMap = busSaleListMapper.selectMaps(saleWrapper);
        if (ObjectUtil.isNotEmpty(saleListMap) && ObjectUtil.isNotEmpty(saleListMap.get(0))) {
            Map<String, Object> saleMap = saleListMap.get(0);
            inventoryTotalMoney = inventoryTotalMoney.add(new BigDecimal(saleMap.getOrDefault("total_money", "0").toString()));
        }


        if (ObjectUtil.isNotEmpty(inventoryList) && ObjectUtil.isNotEmpty(inventoryList.get(0))) {
            Map<String, Object> inventoryMap = inventoryList.get(0);
            inventoryOnlineMoney = new BigDecimal(inventoryMap.getOrDefault("online_money", "0").toString());
            inventoryCashMoney = new BigDecimal(inventoryMap.getOrDefault("cash_money", "0").toString());
        }

        //查询退货单
        QueryWrapper<BusReturnOrderEntity> returnWrapper = new QueryWrapper<>();
        returnWrapper.select("sum(total_money) as total_money, sum(online_money) as online_money, sum(cash_money) as cash_money");
        returnWrapper.lambda().eq(BusReturnOrderEntity::getCompanyId, companyId).between(BusReturnOrderEntity::getCreateTime, dateStart, dateEnd);
        List<Map<String, Object>> returnList = busReturnOrderMapper.selectMaps(returnWrapper);
        if (ObjectUtil.isNotEmpty(returnList) && ObjectUtil.isNotEmpty(returnList.get(0))) {
            Map<String, Object> returnMap = returnList.get(0);
            returnTotalMoney = new BigDecimal(returnMap.getOrDefault("total_money", "0").toString());
            returnOnlineMoney = new BigDecimal(returnMap.getOrDefault("online_money", "0").toString());
            returnCashMoney = new BigDecimal(returnMap.getOrDefault("cash_money", "0").toString());
        }

        //查询销售服务费
        QueryWrapper<BusSaleListEntity> saleListQueryWrapper = new QueryWrapper<>();
        saleListQueryWrapper.select("sum(sale_list_service_fee) as sale_list_service_fee");
        saleListQueryWrapper.lambda().eq(BusSaleListEntity::getCompanyId, companyId).between(BusSaleListEntity::getCreateTime, dateStart, dateEnd);
        List<Map<String, Object>> saleList = busSaleListMapper.selectMaps(saleListQueryWrapper);
        if (ObjectUtil.isNotEmpty(saleList) && ObjectUtil.isNotEmpty(saleList.get(0))) {
            Map<String, Object> serviceFeeMap = saleList.get(0);
            serviceFee = new BigDecimal(serviceFeeMap.getOrDefault("sale_list_service_fee", "0").toString());
        }

        //查询退还服务费
        QueryWrapper<BusReturnListEntity> returnListQueryWrapper = new QueryWrapper<>();
        returnListQueryWrapper.select("sum(return_sale_list_service_fee) as return_sale_list_service_fee");
        returnListQueryWrapper.lambda().eq(BusReturnListEntity::getCompanyId, companyId).between(BusReturnListEntity::getCreateTime, dateStart, dateEnd);
        List<Map<String, Object>> returnSaleList = busReturnListMapper.selectMaps(returnListQueryWrapper);
        if (ObjectUtil.isNotEmpty(returnSaleList) && ObjectUtil.isNotEmpty(returnSaleList.get(0))) {
            Map<String, Object> returnServiceFeeMap = returnSaleList.get(0);
            returnServiceFee = new BigDecimal(returnServiceFeeMap.getOrDefault("return_sale_list_service_fee", "0").toString());
        }

        //参数处理
        BigDecimal totalTurnover = NumberUtil.sub(inventoryTotalMoney, returnTotalMoney);
        BigDecimal totalOnlineMoney = NumberUtil.sub(inventoryOnlineMoney, returnOnlineMoney);
        BigDecimal totalCashMoney = NumberUtil.sub(inventoryCashMoney, returnCashMoney);
        int onlineRatio = formatRatio(totalOnlineMoney, totalTurnover);

        map.put("totalTurnover", totalTurnover);
        map.put("totalOnlineMoney", totalOnlineMoney);
        map.put("totalCashMoney", totalCashMoney);
        map.put("onlineRatio", onlineRatio);
        map.put("cashRatio", (100 - onlineRatio));
        map.put("totalProfit", NumberUtil.sub(serviceFee, returnServiceFee));

        return map;
    }

    /**
     * 统计税额相关
     *
     * @param companyId
     * @param dateStart
     * @param dateEnd
     * @return
     */
    private Map<String, Object> countTax(Long companyId, String dateStart, String dateEnd) {
        Map<String, Object> map = new HashMap<>();
        //纳税总额
        BigDecimal totalTaxableAmount = BigDecimal.ZERO;
        //已开进项税
        BigDecimal finishInputTax = BigDecimal.ZERO;
        //需开进项税
        BigDecimal needInputTax = BigDecimal.ZERO;
        //已开销项税
        BigDecimal finishOutputTax = BigDecimal.ZERO;
        //需开销项税
        BigDecimal needOutputTax = BigDecimal.ZERO;

        //查询应纳税额
        QueryWrapper<BusShopInvoiceEntity> invoiceWrapper = new QueryWrapper<>();
        invoiceWrapper.select("invoice_type,status,sum(tax_money) as tax_money");
        invoiceWrapper.lambda().eq(BusShopInvoiceEntity::getCompanyId, companyId)
                .between(BusShopInvoiceEntity::getCreateTime, dateStart, dateEnd);
        invoiceWrapper.groupBy("status", "invoice_type");
        List<Map<String, Object>> totalTaxList = busShopInvoiceMapper.selectMaps(invoiceWrapper);
        if (ObjectUtil.isNotEmpty(totalTaxList)) {
            for (Map<String, Object> m : totalTaxList) {
                if (ObjectUtil.isNotEmpty(m)) {
                    int invoiceType = (int) m.get("invoice_type");
                    int status = (int) m.get("status");
                    BigDecimal taxMoney = new BigDecimal(m.getOrDefault("tax_money", "0").toString());
                    totalTaxableAmount = NumberUtil.add(totalTaxableAmount, taxMoney);
                    if (invoiceType == 1) { //进项票
                        if (status == 1) { //已开票
                            finishInputTax = taxMoney;
                        }
                    } else { //销项票
                        if (status == 1) { //已开票
                            finishOutputTax = taxMoney;
                        } else {
                            needOutputTax = taxMoney;
                        }
                    }
                }
            }
        }
        //总销项税
        BigDecimal totalOutputTax = NumberUtil.add(finishOutputTax, needOutputTax);
        needInputTax = NumberUtil.sub(totalOutputTax, finishInputTax);
        //总进项税
        BigDecimal totalInputTax = NumberUtil.add(finishInputTax, needInputTax);
        //已开进项票占比
        int finishInputRatio = formatRatio(finishInputTax, totalInputTax);
        //已开销项票占比
        int finishOutRatio = formatRatio(finishOutputTax, totalOutputTax);

        map.put("totalTaxableAmount", totalOutputTax);
        map.put("totalInputTax", totalInputTax);
        map.put("totalOutputTax", totalOutputTax);
        map.put("finishInputTax", finishInputTax);
        map.put("needInputTax", needInputTax);
        map.put("finishOutputTax", finishOutputTax);
        map.put("needOutputTax", needOutputTax);
        map.put("finishInputTaxRatio", finishInputRatio);
        map.put("needInputTaxRatio", 100 - finishInputRatio);
        map.put("finishOutputTaxRatio", finishOutRatio);
        map.put("needOutPutTaxRatio", 100 - finishOutRatio);

        return map;
    }

    /**
     * 纳统目标相关
     *
     * @param company
     * @param dateStart
     * @param dateEnd
     * @return
     */
    private Map<String, Object> countTarget(SysCompanyEntity company, String dateStart, String dateEnd) {
        Map<String, Object> map = new HashMap<>();

        String taxType = company.getTaxType();
        DateTime ds = DateUtil.parse(dateStart, DatePattern.NORM_DATETIME_PATTERN);
        DateTime de = DateUtil.parse(dateEnd, DatePattern.NORM_DATETIME_PATTERN);
        long betweenDay = de.between(ds, DateUnit.DAY) + 1;

        DateTime today = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1));
        //今天是不是纳统周期起点
        boolean todayIsStart = today.getTime() == ds.getTime();

        //日目标
        BigDecimal dayTarget = NumberUtil.div(company.getTargetAmount(), betweenDay, 0, RoundingMode.CEILING);
        //日完成
        DateTime preDay = DateUtil.offsetDay(new Date(), -1);

        BigDecimal monthTarget = BigDecimal.ZERO;
        BigDecimal monthFinish = BigDecimal.ZERO;
        if (TaxTypeEnum.SEASON.getValue().equals(taxType) || TaxTypeEnum.YEAR.getValue().equals(taxType)) {
            //月目标
            int month = TaxTypeEnum.SEASON.getValue().equals(taxType) ? 3 : 12;
            monthTarget = NumberUtil.div(company.getTargetAmount(), month, 0, RoundingMode.CEILING);

            //月完成
        }

        BigDecimal quarterTarget = BigDecimal.ZERO;
        BigDecimal quarterFinish = BigDecimal.ZERO;
        if (TaxTypeEnum.YEAR.getValue().equals(taxType)) {
            //季度目标
            quarterTarget = NumberUtil.div(company.getTargetAmount(), 4, 0, RoundingMode.CEILING);
            //季度完成
        }

        map.put("dayTarget", dayTarget);
        map.put("monthTarget", monthTarget);
        map.put("quarterTarget", quarterTarget);
        map.put("monthFinish", monthFinish);
        map.put("quarterFinish", quarterFinish);
        map.put("monthFinishRatio", formatRatio(monthFinish, monthTarget));
        map.put("quarterFinishRatio", formatRatio(quarterFinish, quarterTarget));

        return map;
    }

    /**
     * 销售额柱状图统计
     *
     * @return
     */
    private Map<String, Object> countBarData(SysCompanyEntity company, String dateStart, String dateEnd) {
        Map<String, Object> map = new HashMap<>();

        String taxType = company.getTaxType();
        Map<String, BigDecimal> orderDataMap = new HashMap<>();
        Map<String, BigDecimal> returnOrderDataMap = new HashMap<>();
        List<String> xData = new ArrayList<>(); //x轴
        List<BigDecimal> yData = new ArrayList<>(); //y轴
        Date d = new Date();
        if (TaxTypeEnum.YEAR.getValue().equals(taxType) || TaxTypeEnum.SEASON.getValue().equals(taxType)) { //按月展示
            //订单
            QueryWrapper<BusInventoryOrderEntity> orderWrapper = new QueryWrapper<>();
            orderWrapper.select("DATE_FORMAT(create_time, '%Y-%m') as monthStr,sum(total_money) as total_money");
            orderWrapper.eq("company_id", company.getId())
                    .between("create_time", dateStart, dateEnd)
                    .in("inventory_type", getInventoryTypeList())
                    .groupBy("monthStr");
            List<Map<String, Object>> orderList = busInventoryOrderMapper.selectMaps(orderWrapper);
            if (ObjectUtil.isNotEmpty(orderList)) {
                for (Map<String, Object> m : orderList) {
                    if (ObjectUtil.isNotEmpty(m)) {
                        orderDataMap.put((String) m.get("monthStr"), (BigDecimal) m.get("total_money"));
                    }
                }
            }
            //退单
            QueryWrapper<BusReturnOrderEntity> returnOrderWrapper = new QueryWrapper<>();
            returnOrderWrapper.select("DATE_FORMAT(create_time, '%Y-%m') as monthStr,sum(total_money) as total_money");
            returnOrderWrapper.eq("company_id", company.getId())
                    .between("create_time", dateStart, dateEnd)
                    .groupBy("monthStr");
            List<Map<String, Object>> returnOrderList = busReturnOrderMapper.selectMaps(returnOrderWrapper);
            if (ObjectUtil.isNotEmpty(returnOrderList)) {
                for (Map<String, Object> m : returnOrderList) {
                    if (ObjectUtil.isNotEmpty(m)) {
                        returnOrderDataMap.put((String) m.get("monthStr"), (BigDecimal) m.get("total_money"));
                    }
                }
            }

            //数据构建
            if (TaxTypeEnum.YEAR.getValue().equals(taxType)) { //按年统计
                for (int i = 1; i < 13; i++) {
                    String month = StringUtils.join(DateUtil.year(d), "-", (i < 10 ? ("0" + i) : i));
                    xData.add(month);

                    BigDecimal orderD = orderDataMap.getOrDefault(month, BigDecimal.ZERO);
                    BigDecimal returnOrderD = returnOrderDataMap.getOrDefault(month, BigDecimal.ZERO);
                    yData.add(NumberUtil.sub(orderD, returnOrderD));
                }
            } else { //按季度统计
                Integer[] monthArr = {0, 3, 6, 9, 12};
                int quarter = DateUtil.quarter(new Date());
                Integer start = monthArr[quarter - 1] + 1;
                Integer end = monthArr[quarter];
                for (int i = start; i <= end; i++) {
                    String month = StringUtils.join(DateUtil.year(d), "-", (i < 10 ? ("0" + i) : i));
                    xData.add(month);
                    BigDecimal orderD = orderDataMap.getOrDefault(month, BigDecimal.ZERO);
                    BigDecimal returnOrderD = returnOrderDataMap.getOrDefault(month, BigDecimal.ZERO);
                    yData.add(NumberUtil.sub(orderD, returnOrderD));
                }

            }
        } else { //按天展示
            int month = DateUtil.month(d) + 1;
            int days = YearMonth.of(DateUtil.year(d), month).lengthOfMonth();
            //订单
            QueryWrapper<BusInventoryOrderEntity> orderWrapper = new QueryWrapper<>();
            orderWrapper.select("DATE_FORMAT(create_time, '%Y-%m-%d') as dayStr,sum(total_money) as total_money");
            orderWrapper.eq("company_id", company.getId())
                    .between("create_time", dateStart, dateEnd)
                    .in("inventory_type", getInventoryTypeList())
                    .groupBy("dayStr");
            List<Map<String, Object>> orderList = busInventoryOrderMapper.selectMaps(orderWrapper);
            if (ObjectUtil.isNotEmpty(orderList)) {
                for (Map<String, Object> m : orderList) {
                    if (ObjectUtil.isNotEmpty(m)) {
                        orderDataMap.put((String) m.get("dayStr"), (BigDecimal) m.get("total_money"));
                    }
                }
            }

            //退单
            QueryWrapper<BusReturnOrderEntity> returnOrderWrapper = new QueryWrapper<>();
            returnOrderWrapper.select("DATE_FORMAT(create_time, '%Y-%m-%d') as dayStr,sum(total_money) as total_money");
            returnOrderWrapper.eq("company_id", company.getId())
                    .between("create_time", dateStart, dateEnd)
                    .groupBy("dayStr");
            List<Map<String, Object>> returnOrderList = busReturnOrderMapper.selectMaps(returnOrderWrapper);
            if (ObjectUtil.isNotEmpty(returnOrderList)) {
                for (Map<String, Object> m : returnOrderList) {
                    if (ObjectUtil.isNotEmpty(m)) {
                        returnOrderDataMap.put((String) m.get("dayStr"), (BigDecimal) m.get("total_money"));
                    }
                }
            }

            for (int i = 1; i <= days; i++) {
                String day = StringUtils.join(DateUtil.year(d), "-", (month < 10 ? ("0" + month) : month), "-", (i < 10 ? ("0" + i) : i));
                xData.add(day);
                BigDecimal orderD = orderDataMap.getOrDefault(day, BigDecimal.ZERO);
                BigDecimal returnOrderD = returnOrderDataMap.getOrDefault(day, BigDecimal.ZERO);
                yData.add(NumberUtil.sub(orderD, returnOrderD));
            }

        }

        map.put("xData", xData);
        map.put("yData", yData);
        return map;
    }

    /**
     * 计算预计完成时间
     *
     * @param targetAmount
     * @param finishAmount
     * @return
     */
    private String calcFinishDate(Long companyId, String dateStart, String dateEnd, BigDecimal targetAmount, BigDecimal finishAmount) {
        LambdaQueryWrapper<BusInventoryOrderEntity> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(BusInventoryOrderEntity::getCompanyId, companyId);
        orderWrapper.between(BusInventoryOrderEntity::getCreateTime, dateStart, dateEnd);
        orderWrapper.orderByDesc(BusInventoryOrderEntity::getId);
        orderWrapper.gt(BusInventoryOrderEntity::getTotalMoney, 0);
        orderWrapper.in(BusInventoryOrderEntity::getInventoryType, getInventoryTypeList());
        orderWrapper.last("limit 1");
        BusInventoryOrderEntity order = busInventoryOrderMapper.selectOne(orderWrapper);
        if (ObjectUtil.isEmpty(order)) return "";

        //多少天能完成
        int day = NumberUtil.div(NumberUtil.sub(targetAmount, finishAmount), order.getTotalMoney(), 1, RoundingMode.CEILING).intValue();
        return DateUtil.format(DateUtil.offsetDay(new Date(), day - 1), DatePattern.NORM_DATE_PATTERN);
    }

    /**
     * 格式化日期时间
     *
     * @param dateTime
     * @return
     */
    private String formatDate(DateTime dateTime) {
        return DateUtil.format(DateUtil.offsetDay(dateTime, 1), DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 数据占比
     *
     * @return
     */
    private Integer formatRatio(BigDecimal finish, BigDecimal total) {
        if (total.compareTo(BigDecimal.ZERO) == 0) return 0;
        Double targetRatio = NumberUtil.mul(NumberUtil.div(finish, total), new BigDecimal("100")).doubleValue();
        return (targetRatio > 0 && targetRatio < 1) ? 1 : targetRatio.intValue() > 100 ? 100 : targetRatio.intValue();
    }

    /**
     * 格式化万
     *
     * @param num
     * @return
     */
    private BigDecimal formatWan(BigDecimal num) {
        return NumberUtil.div(num, new BigDecimal("10000"), 2, RoundingMode.DOWN);
    }

    /**
     * 查询入库单类型
     *
     * @return
     */
    private List<Integer> getInventoryTypeList() {
        List<Integer> inventoryTypeList = new ArrayList<>();
        inventoryTypeList.add(InventoryTypeEnum.FOOD_SALE.getInventoryType());
        inventoryTypeList.add(InventoryTypeEnum.SHOP_SALE.getInventoryType());

        return inventoryTypeList;
    }

    private List<Integer> getSaleListStateList() {
        List<Integer> saleListStateList = new ArrayList<>();
        saleListStateList.add(3);
        return saleListStateList;
    }
}
