package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.cmb.CMBNoteData;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListExportParams;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListQueryParams;
import cc.buyhoo.tax.result.busTradeRecord.TradeRecordListQueryResult;

import javax.servlet.http.HttpServletResponse;

public interface TradeRecordService {
    /**
     * 保存交易流水
     * @param cmbNoteData
     * @return
     */
    Result<Void> saveTradeRecord(CMBNoteData cmbNoteData);

    /**
     * 查询交易流水列表
     * @param params
     * @return
     */
    Result<TradeRecordListQueryResult> queryTradeRecordList(TradeRecordListQueryParams params);

    void exportTradeRecord(TradeRecordListExportParams params, HttpServletResponse response);
}
