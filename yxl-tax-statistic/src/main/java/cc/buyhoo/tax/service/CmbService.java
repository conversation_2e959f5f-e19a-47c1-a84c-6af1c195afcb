package cc.buyhoo.tax.service;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusSaleListPayDetailEntity;
import cc.buyhoo.tax.entity.SysCompanyBankAccountEntity;
import cc.buyhoo.tax.entity.cmb.BackInfoEntity;
import cc.buyhoo.tax.entity.cmb.CMBNoteDataDetail;
import cc.buyhoo.tax.entity.cmb.TrsInfoEntity;
import cc.buyhoo.tax.params.cmb.CreateNewInventoryParams;
import cc.buyhoo.tax.params.cmb.CreateNewOrderParams;
import cc.buyhoo.tax.result.disassembleList.CreateOrderSigleResult;

import java.math.BigDecimal;
import java.util.List;

public interface CmbService {


    //创建单条记录
    public CreateOrderSigleResult createOrderSigle(String oldSaleListUnique, String saleListUnique, BigDecimal saleListTotal, BigDecimal saleListPayTotal, Long shopUnique, List<BusSaleListPayDetailEntity> saleListPayDetailEntities,Boolean isLast);

    /**
     * 创建新的进货订单
     * @param createNewInventoryParams
     * @return
     */
    public Result<Void> createNewInventory(CreateNewInventoryParams createNewInventoryParams);
    /**
     * 接受到通知后，生成订单信息
     * @param createNewOrderParams
     * @return
     */
    public Result<BigDecimal> createNewOrder(CreateNewOrderParams createNewOrderParams, CMBNoteDataDetail cmbNoteDataDetail);

    Result<Void> resetFinSuccShopBill(TrsInfoEntity trsInfo);
    /**
     * 回调通知里，如果转账失败，通过这里回调
     * @param trsInfo
     * @return
     */
    Result<Void> resetFinBackShopBill(TrsInfoEntity trsInfo);

    Result<Void> resetFinBackShopBill(BackInfoEntity backInfo);

    SysCompanyBankAccountEntity getCompanyBankAccount(Long companyId);
}
