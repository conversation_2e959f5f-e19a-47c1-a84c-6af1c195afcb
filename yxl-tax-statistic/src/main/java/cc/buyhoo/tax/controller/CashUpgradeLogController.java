package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.service.CashUpgradeLogService;
import cc.buyhoo.upgrade.params.cashUpgradeLog.UpgradeLogListParams;
import cc.buyhoo.upgrade.result.cashUpgradeLog.UpgradeLogListResult;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 升级记录
 */
@RestController
@RequestMapping("/cashUpgradeLog")
public class CashUpgradeLogController {

    @Autowired
    private CashUpgradeLogService logService;

    /**
     * 纪录列表
     * @param params
     * @return
     */
    @GetMapping("/upgradeLogList")
    @SaCheckPermission("cashUpgrade:list")
    public Result<UpgradeLogListResult> upgradeLogList(@Validated UpgradeLogListParams params) {
        return logService.logList(params);
    }

}
