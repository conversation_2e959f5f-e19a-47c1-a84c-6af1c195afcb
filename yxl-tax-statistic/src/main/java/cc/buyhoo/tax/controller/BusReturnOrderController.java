package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnOrder.ReturnOrderParams;
import cc.buyhoo.tax.result.returnOrder.ReturnOrderListResult;
import cc.buyhoo.tax.service.BusReturnOrderService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 退货单管理
 */
@RestController
@RequestMapping("returnOrder")
public class BusReturnOrderController {

    @Autowired
    private BusReturnOrderService busReturnOrderService;

    /**
     * 退货单列表
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busReturnOrder:list")
    public Result<ReturnOrderListResult> pageList(@Validated ReturnOrderParams params) {
        return busReturnOrderService.pageList(params);
    }

}
