package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.login.LoginParams;
import cc.buyhoo.tax.result.login.CaptchaResult;
import cc.buyhoo.tax.result.login.LoginResult;
import cc.buyhoo.tax.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统登录
 */
@RestController
@RequestMapping("/login")
public class LoginController {

    @Autowired
    private LoginService loginService;

    /**
     * 获取验证码
     * @return
     */
    @GetMapping("/code")
    public Result<CaptchaResult> captcha() {
        return loginService.captcha();
    }

    /**
     * 用户登录
     * @param params
     * @return
     */
    @PostMapping("/login")
    public Result<LoginResult> login(@RequestBody @Validated LoginParams params) {
        return loginService.login(params);
    }

    /**
     * 退出登录
     * @return
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        return loginService.logout();
    }
}
