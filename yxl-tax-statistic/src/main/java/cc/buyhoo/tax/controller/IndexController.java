package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.result.indexData.SaleListDataResult;
import cc.buyhoo.tax.result.indexData.RevenueTargetResult;
import cc.buyhoo.tax.service.IndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页数据
 * @ClassName IndexController
 * <AUTHOR>
 * @Date 2023/7/31 17:24
 **/
@RestController
@RequestMapping("/indexData")
public class IndexController extends BaseController {

    @Autowired
    private IndexService indexService;

    /**
     * 首页-营收目标
     * @return
     */
    @GetMapping("/revenueTarget")
    public Result<RevenueTargetResult> revenueTarget() {
        return indexService.revenueTarget();
    }

    /**
     * 首页-订单统计
     * @return
     */
    @GetMapping("/saleListInfo")
    public Result<SaleListDataResult> saleListInfo() {
        return indexService.saleListInfo();
    }

}
