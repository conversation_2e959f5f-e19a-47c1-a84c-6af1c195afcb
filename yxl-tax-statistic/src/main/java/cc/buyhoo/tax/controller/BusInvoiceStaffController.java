package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusInvoiceStaffEntity;
import cc.buyhoo.tax.entity.invoice.EleUserLoginData;
import cc.buyhoo.tax.entity.invoice.EleUserLoginResult;
import cc.buyhoo.tax.entity.invoice.RpaAuthStatusData;
import cc.buyhoo.tax.entity.invoice.RpaQrCodeData;
import cc.buyhoo.tax.enums.InvoiceStaffDutyTypeEnum;
import cc.buyhoo.tax.params.invoice.*;
import cc.buyhoo.tax.result.invoice.InvoiceStaffDutyTypeListResult;
import cc.buyhoo.tax.result.invoice.QueryInvoiceStaffListResult;
import cc.buyhoo.tax.service.BusInvoiceStaffService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 财务管理/开票员管理
 */
@RequestMapping("/busInvoiceStaff")
@RestController
public class BusInvoiceStaffController {
    @Autowired
    private BusInvoiceStaffService busInvoiceStaffService;

    /**
     * 获取员工实名认证状态
     * @param params
     * @return
     */
    @PostMapping("/rpaAuthStatus")
    public Result<RpaAuthStatusData> rpaAuthStatus(@RequestBody @Validated RpaAuthStatusParams params) {
        return busInvoiceStaffService.rpaAuthStatus(params);
    }

    /**
     * 获取实名认证二维码
     * @param params
     * @return
     */
    @PostMapping("/rpaQrCode")
    public Result<RpaQrCodeData> rpaQrCode(@RequestBody @Validated RpaQrCodeParams params) {
        return busInvoiceStaffService.rpaQrCode(params);
    }

    /**
     * 登录全电账号
     * 所有登录步骤都走一个接口
     * @param params
     * @return
     */
    @PostMapping("/eleUserLogin")
    public Result<EleUserLoginData> eleUserLogin(@RequestBody @Validated EleUserLoginParams params) {
        return busInvoiceStaffService.eleUserLogin(params);
    }
    /**
     * 更新或删除员工信息
     * @param params
     * @return
     */
    @PostMapping("/updateInvoiceStaff")
    public Result<Void> updateInvoiceStaff(@RequestBody @Validated UpdateInvoiceStaffParams params) {
        return busInvoiceStaffService.updateInvoiceStaff(params);
    }

    /**
     * 添加新的员工信息
     * @param params
     * @return
     */
    @PostMapping("/saveInvoiceStaff")
    public Result<BusInvoiceStaffEntity> saveInvoiceStaff(@RequestBody @Validated SaveInvoiceStaffParams params) {
        return busInvoiceStaffService.saveInvoiceStaff(params);
    }

    /**
     * 获取开票员身份类型列表
     * @return
     */
    @PostMapping("/queryInvoiceStaffDutyTypeList")
    public Result<InvoiceStaffDutyTypeListResult> queryInvoiceStaffDutyTypeList(){
        InvoiceStaffDutyTypeListResult invoiceStaffDutyTypeListResult = InvoiceStaffDutyTypeEnum.convertList();
        return Result.ok(invoiceStaffDutyTypeListResult);
    }

    /**
     * 获取指定员工的信息
     * @param params
     * @return
     */
    @PostMapping("/queryInvoiceStaffById")
    public Result<BusInvoiceStaffEntity> queryInvoiceStaffById(@RequestBody QueryInvoiceStaffByIdParams params){
        return busInvoiceStaffService.queryInvoiceStaffById(params);
    }
    /**
     * 获取指定店铺的员工列表信息
     * @param params
     * @return
     */
    @RequestMapping("/queryInvoiceStaffList")
    @ResponseBody
    public Result<QueryInvoiceStaffListResult> queryInvoiceStaffList(@RequestBody QueryInvoiceStaffListParams params){
        return busInvoiceStaffService.queryInvoiceStaffList(params);
    }


}
