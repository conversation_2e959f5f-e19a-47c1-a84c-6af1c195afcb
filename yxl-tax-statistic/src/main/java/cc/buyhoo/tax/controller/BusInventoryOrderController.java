package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.inventoryOrder.InventoryOrderParams;
import cc.buyhoo.tax.result.inventoryOrder.InventoryOrderListResult;
import cc.buyhoo.tax.service.BusInventoryOrderService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 入库统计
 * @ClassName BusInventoryOrderController
 * <AUTHOR>
 * @Date 2023/7/30 16:41
 **/
@RestController
@RequestMapping("inventoryOrder")
public class BusInventoryOrderController extends BaseController {

    @Autowired
    private BusInventoryOrderService busInventoryOrderService;

    /**
     * 入库分页列表
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busInventoryOrder:list")
    public Result<InventoryOrderListResult> pageList(@Validated InventoryOrderParams params) {
        return busInventoryOrderService.pageList(params);
    }
}
