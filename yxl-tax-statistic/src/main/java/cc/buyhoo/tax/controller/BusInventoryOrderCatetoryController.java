package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.inventoryOrderCategory.InventoryOrderCategoryParams;
import cc.buyhoo.tax.result.inventoryOrderCategory.InventoryOrderCategoryListResult;
import cc.buyhoo.tax.service.BusInventoryOrderCatetoryService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 分类库存统计
 * @ClassName BusInventoryOrderCatetoryController
 * <AUTHOR>
 * @Date 2023/7/30 16:41
 **/
@RestController
@RequestMapping("inventoryOrderCategory")
public class BusInventoryOrderCatetoryController extends BaseController {

    @Autowired
    private BusInventoryOrderCatetoryService busInventoryOrderCatetoryService;

    /**
     * 库存分类分页列表
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busInventoryOrder:list")
    public Result<InventoryOrderCategoryListResult> pageList(@Validated InventoryOrderCategoryParams params) {
        return busInventoryOrderCatetoryService.pageList(params);
    }
}
