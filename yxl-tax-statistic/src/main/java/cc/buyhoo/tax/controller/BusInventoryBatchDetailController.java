package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.inventoryBatchDetail.InventoryBatchDetailParams;
import cc.buyhoo.tax.result.inventoryBatchDetail.InventoryBatchDetailDto;
import cc.buyhoo.tax.result.inventoryBatchDetail.InventoryBatchDetailListResult;
import cc.buyhoo.tax.service.BusInventoryBatchDetailService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 入库单批次明细
 * @ClassName BusInventoryBatchDetailController
 * <AUTHOR>
 * @Date 2023/7/31 15:36
 **/
@RestController
@RequestMapping("/inventoryBatchDetail")
public class BusInventoryBatchDetailController extends BaseController {

    @Autowired
    private BusInventoryBatchDetailService busInventoryBatchDetailService;

    /**
     * 入库批次明细分页查询
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busShopBill:supplyDetail")
    public Result<InventoryBatchDetailListResult> pageList(@Validated InventoryBatchDetailParams params) {
        return busInventoryBatchDetailService.pageList(params);
    }
    /**
     * 入库批次明细查询
     * @param params
     * @return
     */
    @GetMapping("/listAll")
    @SaCheckPermission("inventoryBatch:edit")
    public Result<List<InventoryBatchDetailDto>> listAll(@Validated InventoryBatchDetailParams params) {
        return busInventoryBatchDetailService.listAll(params);
    }

}
