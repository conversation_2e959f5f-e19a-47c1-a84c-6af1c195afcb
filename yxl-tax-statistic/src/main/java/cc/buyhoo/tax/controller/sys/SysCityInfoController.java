package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.result.sysCityInfo.SysCityInoDto;
import cc.buyhoo.tax.service.SysCityInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 城市表
*
* <AUTHOR> 
* @since 1.0.0 2024-08-23
*/
@RestController
@RequestMapping("/sysCityInfo")
@RequiredArgsConstructor
public class SysCityInfoController {
    private final SysCityInfoService sysCityInfoService;

    /**
     * 查询城市列表
     * @param pid
     * @return
     */
    @GetMapping("/list/{level}/{pid}")
    public Result<List<SysCityInoDto>> selectByParentId(@PathVariable("level") Integer level, @PathVariable("pid") Long pid) {
        return sysCityInfoService.selectList(pid, level);
    }

}