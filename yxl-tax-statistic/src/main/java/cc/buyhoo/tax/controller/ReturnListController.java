package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnList.ReturnListDetailParams;
import cc.buyhoo.tax.params.returnList.ReturnListParams;
import cc.buyhoo.tax.result.returnList.ReturnListDetailResult;
import cc.buyhoo.tax.result.returnList.ReturnListResult;
import cc.buyhoo.tax.service.BusReturnListService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 退单列表
 */
@RestController
@RequestMapping("/returnList")
public class ReturnListController {

    @Autowired
    private BusReturnListService busReturnListService;

    /**
     * 退单列表
     * @param params
     * @return
     */
    @GetMapping("/returnList")
    @SaCheckPermission("returnList:list")
    public Result<ReturnListResult> returnList(@Validated ReturnListParams params) {
        return busReturnListService.returnList(params);
    }

    /**
     * 退款单详情
     * @param params
     * @return
     */
    @GetMapping("/returnListDetail")
    @SaCheckPermission("returnList:list")
    public Result<ReturnListDetailResult> returnListDetail(@Validated ReturnListDetailParams params) {
        return busReturnListService.returnListDetail(params);
    }


}
