package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnBatchDetail.ReturnBatchDetailListParams;
import cc.buyhoo.tax.result.returnBatchDetail.ReturnBatchDetailListResult;
import cc.buyhoo.tax.service.BusReturnBatchDetailService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 批次详情
 */
@RestController
@RequestMapping("busReturnBatchDetail")
public class BusReturnBatchDetailController {

    @Autowired
    private BusReturnBatchDetailService busReturnBatchDetailService;

    /**
     * 批次详情
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busShopBill:return")
    public Result<ReturnBatchDetailListResult> pageList(ReturnBatchDetailListParams params) {
        return busReturnBatchDetailService.pageList(params);
    }

}
