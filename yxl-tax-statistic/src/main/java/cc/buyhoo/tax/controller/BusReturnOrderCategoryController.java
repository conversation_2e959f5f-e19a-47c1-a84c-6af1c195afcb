package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnOrderCategory.ReturnOrderCategoryParams;
import cc.buyhoo.tax.result.returnOrderCategory.ReturnOrderCategoryListResult;
import cc.buyhoo.tax.service.BusReturnOrderCategoryService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 退货单详情
 */
@RestController
@RequestMapping("/returnOrderCategory")
public class BusReturnOrderCategoryController {

    @Autowired
    private BusReturnOrderCategoryService busReturnOrderCategoryService;

    /**
     * 退货单详情
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busReturnOrder:list")
    public Result<ReturnOrderCategoryListResult> pageList(@Validated ReturnOrderCategoryParams params) {
        return busReturnOrderCategoryService.pageList(params);
    }

}
