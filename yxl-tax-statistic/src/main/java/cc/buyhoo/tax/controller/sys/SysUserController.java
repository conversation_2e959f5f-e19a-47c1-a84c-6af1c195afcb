package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysUser.*;
import cc.buyhoo.tax.params.sysUserRole.SysUserRoleQueryParams;
import cc.buyhoo.tax.result.sysUser.SysUserPageResult;
import cc.buyhoo.tax.result.sysUser.SysUserDetailResult;
import cc.buyhoo.tax.result.sysUser.UserRoleListDto;
import cc.buyhoo.tax.service.SysUserService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 平台端/用户管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sysUser")
public class SysUserController {

    private final SysUserService sysUserService;

    /**
     * 用户列表
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("sysUser:list")
    public Result<SysUserPageResult> pageList(@Validated UserPageParams params) {
        return sysUserService.pageList(params);
    }

    /**
     * 新增用户
     * @param params
     * @return
     */
    @PostMapping("/addUser")
    @SaCheckPermission("sysUser:add")
    public Result<Void> addUser(@RequestBody @Validated SysUserAddParams params) {
        return sysUserService.addUser(params);
    }

    /**
     * 修改用户
     * @param params
     * @return
     */
    @PostMapping("/updateUser")
    @SaCheckPermission("sysUser:edit")
    public Result<Void> updateUser(@RequestBody @Validated SysUserEditParams params) {
        return sysUserService.updateUser(params);
    }

    /**
     * 用户详情
     * @return
     */
    @GetMapping("/selectById/{id}")
    @SaCheckPermission(value = {"sysUser:add","sysUser:edit","sysUser:detail"}, mode = SaMode.OR)
    public Result<SysUserDetailResult> selectById(@PathVariable("id") Long id) {
        return sysUserService.selectById(id);
    }

    /**
     * 删除用户
     * @param params
     * @return
     */
    @PostMapping("/deleteUser")
    @SaCheckPermission("sysUser:delete")
    public Result<Void> deleteUser(@RequestBody @Validated DeleteUserParams params) {
        return sysUserService.deleteUser(params);
    }

    /**
     * 用户角色列表
     * @return
     */
    @GetMapping("/userRoleList")
    @SaCheckPermission(value = {"sysUser:add","sysUser:update","sysUser:detail"}, mode = SaMode.OR)
    public Result<List<UserRoleListDto>> userRoleList(SysUserRoleQueryParams queryParams) {
        return sysUserService.userRoleList(queryParams);
    }
    /**
     * 重置密码
     * @param params
     * @return
     */
    @PostMapping("/resetPwd")
    @SaCheckPermission("sysUser:resetpwd")
    public Result<Void> resetPwd(@RequestBody @Validated ResetPwdParams params) {
        return sysUserService.resetPwd(params);
    }

    /**
     * 修改密码
     * @param params
     * @return
     */
    @PostMapping("/updatePwd")
    public Result<Void> updatePwd(@RequestBody @Validated UpdatePwdParams params) {
        return sysUserService.updatePwd(params);
    }

}
