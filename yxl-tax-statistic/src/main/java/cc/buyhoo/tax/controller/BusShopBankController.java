package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.busShopBank.BusShopBankAddParams;
import cc.buyhoo.tax.params.busShopBank.BusShopBankIdParams;
import cc.buyhoo.tax.params.busShopBank.BusShopBankUpdateParams;
import cc.buyhoo.tax.params.busShopBank.ShopBankListParam;
import cc.buyhoo.tax.result.busShopBank.BusShopBankDto;
import cc.buyhoo.tax.service.BusShopBankService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供货商银行卡管理
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 08:48
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/shopBank")
public class BusShopBankController extends BaseController {

    private final BusShopBankService busShopBankService;

    /**
     * 供货商银行卡分页列表
     * @param param
     * @return
     */
    @GetMapping("/pageList")
    public Result<List<BusShopBankDto>> list(@Validated ShopBankListParam param) {
        return busShopBankService.list(param);
    }

    /**
     * 新增银行卡
     * @param param
     * @return
     */
    @PostMapping("/addShopBank")
    public Result<String> addShopBank(@Validated @RequestBody BusShopBankAddParams param) {
        return busShopBankService.addShopBank(param);
    }

    /**
     * 修改银行卡
     * @param param
     * @return
     */
    @PostMapping("/updateShopBank")
    public Result<String> updateShopBank(@Validated @RequestBody BusShopBankUpdateParams param) {
        return busShopBankService.updateShopBank(param);
    }

    /**
     * 修改银行卡
     * @param param
     * @return
     */
    @PostMapping("/deleteById")
    public Result<String> updateShopBank(@Validated @RequestBody BusShopBankIdParams param) {
        return busShopBankService.deleteShopBank(param.getId());
    }

    /**
     * 查看银行卡详情
     * @param id
     * @return
     */
    @GetMapping("selectById/{id}")
    public Result<BusShopBankDto> selectById(@PathVariable("id") Long id) {
        return busShopBankService.selectById(id);
    }
}
