package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.enums.InvoiceStatusEnum;
import cc.buyhoo.tax.params.busShopInvoice.*;
import cc.buyhoo.tax.params.invoice.InvoiceApplyParams;
import cc.buyhoo.tax.params.invoice.InvoiceResultQueryParams;
import cc.buyhoo.tax.params.invoice.ManualBatchInvoiceApplyParams;
import cc.buyhoo.tax.params.invoice.ManualInvoiceApplyParams;
import cc.buyhoo.tax.result.busShopInvoice.ShopGoodQueryList;
import cc.buyhoo.tax.result.busShopInvoice.ShopInvoiceDetailResult;
import cc.buyhoo.tax.result.busShopInvoice.ShopInvoiceListResult;
import cc.buyhoo.tax.result.invoice.GetInvoiceStatusListResult;
import cc.buyhoo.tax.service.BusShopInvoiceService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 税务发票管理
 */
@RestController
@RequestMapping("/busShopInvoice")
public class BusShopInvoiceController {

    @Autowired
    private BusShopInvoiceService busShopInvoiceService;

    /**
     * 测试minio上传文件
     * @return
     */
    @PostMapping("/testMinio")
    public Result<Void> testMinio(String path) {
        return busShopInvoiceService.testMinio(path);
    }
    /**
     * 获取发票信息
     * @return a
     */
    @PostMapping("/getInvoiceUrl")
    public Result<String> getInvoiceUrl(@Validated @RequestBody InvoiceApplyParams params) {
        return busShopInvoiceService.getInvoiceUrl(params);
    }

    /**
     * 重复发送发票信息
     * @return
     */
    @PostMapping("/resend")
    public Result<Void> resend(@Validated @RequestBody InvoiceApplyParams params) {
        return busShopInvoiceService.resend(params);
    }

    /**
     * 获取订单开票状态列表
     * @return a
     */
    @GetMapping("/invoiceStatusList")
    public Result<GetInvoiceStatusListResult> GetInvoiceStatusList() {
        GetInvoiceStatusListResult result = new GetInvoiceStatusListResult();
        result.setList(InvoiceStatusEnum.list());
        return Result.ok(result);
    }
    /**
     * 发票申请
     * @param params a
     * @return a
     */
    @PostMapping("/invoiceApply")
    public Result<Void> invoiceApply(@Validated @RequestBody InvoiceApplyParams params) {
        return busShopInvoiceService.invoiceApply(params);
    }
    /**
     *  税务发票管理
      * @param params a
     * @return a
     */
    @GetMapping("/list")
    @SaCheckPermission(value = {"busShopInvoice:outList","busShopInvoice:inList"}, mode = SaMode.OR)
    public Result<ShopInvoiceListResult> list(@Validated ShopInvoiceListParams params) {
        return busShopInvoiceService.list(params);
    }

    /**
     * 新增进项票
     * @param params a
     * @return a
     */
    @PostMapping("/add")
    @SaCheckPermission("busShopInvoice:inUpdate")
    public Result<Void> add(@Validated @RequestBody ShopInvoiceAddParams params) {
        return busShopInvoiceService.add(params);
    }

    /**
     * 发票服务明细
     * @param params a
     * @return a
     */
    @GetMapping("/invoiceDetail")
    @SaCheckPermission("busShopInvoice:outList")
    public Result<ShopInvoiceDetailResult> invoiceDetail(@Validated ShopInvoiceDetailParams params) {
        return busShopInvoiceService.invoiceDetail(params);
    }

    /**
     * 发票服务明细
     * @param params a
     * @return a
     */
    @GetMapping("/invoiceDetailByInvoiceIds")
    @SaCheckPermission("busShopInvoice:outList")
    public Result<ShopInvoiceDetailResult> invoiceDetailByInvoiceIds(@Validated ShopInvoiceDetailsParams params) {
        return busShopInvoiceService.invoiceDetailByInvoiceIds(params);
    }

    /**
     * 保存草稿
     * @param params a
     * @return a
     */
    @PostMapping("/saveShopInvoice")
    public Result<Void> saveShopInvoice(@Validated @RequestBody ShopInvoiceSaveParams params) {
        return busShopInvoiceService.saveShopInvoice(params);
    }
    /**
     * 手工开票
     * @param params a
     * @return a
     */
    @PostMapping("/manualInvoiceApply")
    public Result<Void> manualInvoiceApply(@Validated @RequestBody ManualInvoiceApplyParams params) {
        return busShopInvoiceService.manualInvoiceApply(params);
    }

    /**
     * 合并订单开票
     * @param params a
     * @return a
     */
    @PostMapping("/manualBatchInvoiceApply")
    public Result<Void> manualBatchInvoiceApply(@Validated @RequestBody ManualBatchInvoiceApplyParams params) {
        return busShopInvoiceService.manualBatchInvoiceApply(params);
    }

    /**
     * 查询商品
     * @param params a
     * @return a
     */
    @GetMapping("/queryGoodList")
    Result<ShopGoodQueryList> queryGoodList(@Validated ShopGoodQueryParams params){
        return busShopInvoiceService.queryGoodList(params);
    }

    /**
     * 查询发票申请结果
     * @param params a
     * @return a
     */
    @PostMapping("/queryInvoiceResult")
    Result<Void> queryInvoiceResult(@Validated @RequestBody InvoiceResultQueryParams params){
        return busShopInvoiceService.queryInvoiceResult(params);
    }

}
