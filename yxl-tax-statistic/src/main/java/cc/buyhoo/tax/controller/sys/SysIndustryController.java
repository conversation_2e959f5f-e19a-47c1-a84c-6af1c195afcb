package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryAddParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryEditParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryPageParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryQueryParams;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysIndustry.SysIndustryDto;
import cc.buyhoo.tax.result.sysIndustry.SysIndustryPageResult;
import cc.buyhoo.tax.service.SysIndustryService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 平台端/行业管理
*
* <AUTHOR> 
* @since 1.0.0 2024-08-23
*/
@RestController
@RequestMapping("/sysIndustry")
@RequiredArgsConstructor
public class SysIndustryController {
    private final SysIndustryService sysIndustryService;

    /**
     * 分页查询行业列表
     * @param pageParams
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("sysIndustry:list")
    public Result<SysIndustryPageResult> pageList(@Validated SysIndustryPageParams pageParams) {
        return sysIndustryService.pageList(pageParams);
    }

    /**
     * 行业详情
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission(value = {"sysIndustry:detail", "sysIndustry:edit"}, mode = SaMode.OR)
    public Result<SysIndustryDto> selectById(@PathVariable("id") Long id) {
        return sysIndustryService.selectById(id);
    }

    /**
     * 新增行业
     * @param addParams
     * @return
     */
    @PostMapping("/addIndustry")
    @SaCheckPermission("sysIndustry:add")
    public Result<Void> addIndustry(@Validated @RequestBody SysIndustryAddParams addParams) {
        return sysIndustryService.addIndustry(addParams);
    }

    /**
     * 修改行业
     * @param updateParams
     * @return
     */
    @PostMapping("/editIndustry")
    @SaCheckPermission("sysIndustry:edit")
    public Result<Void> editIndustry(@Validated @RequestBody SysIndustryEditParams updateParams) {
        return sysIndustryService.editIndustry(updateParams);
    }

    /**
     * 删除行业
     * @param idsParams
     * @return
     */
    @PostMapping("/deleteByIds")
    @SaCheckPermission("sysIndustry:delete")
    public Result<Void> deleteByIds(@Validated @RequestBody DeleteIdsParams idsParams) {
        return sysIndustryService.deleteByIds(idsParams);
    }

    /**
     * 行业下拉列表
     * @param enableStatus
     * @return
     */
    @GetMapping("/industrySelectData/{enableStatus}")
    @SaCheckPermission("sysIndustry:list")
    public Result<List<SelectDataDto>> industrySelectData(@PathVariable("enableStatus") Integer enableStatus) {
        SysIndustryQueryParams queryParams = new SysIndustryQueryParams();
        queryParams.setEnableStatus(enableStatus);
        return sysIndustryService.industrySelectData(queryParams);
    }
}