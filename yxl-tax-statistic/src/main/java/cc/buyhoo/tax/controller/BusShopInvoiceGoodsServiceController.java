package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busInvoiceGoodsService.InvoiceGoodsServiceParams;
import cc.buyhoo.tax.params.busInvoiceGoodsService.InvoiceGoodsServiceSaveParams;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busInvoiceGoodsService.InvoiceGoodsServiceResult;
import cc.buyhoo.tax.service.BusShopGoodsServiceService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 开票服务
 * @ClassName BusShopInvoiceGoodsServiceController
 * <AUTHOR>
 * @Date 2023/9/6 14:17
 **/
@RestController
@RequestMapping("/invoiceGoodsService")
public class BusShopInvoiceGoodsServiceController {

    @Autowired
    private BusShopGoodsServiceService busShopGoodsServiceService;

    /**
     * 分页查询
     * @param params
     * @return
     */
    @SaCheckPermission("invoiceGoodsService:list")
    @GetMapping("/pageList")
    public Result<InvoiceGoodsServiceResult> pageList(@Validated InvoiceGoodsServiceParams params) {
        return busShopGoodsServiceService.pageList(params);
    }

    /**
     * 保存
     * @param params
     * @return
     */
    @SaCheckPermission(value = {"invoiceGoodsService:add", "invoiceGoodsService:edit"})
    @PostMapping("/save")
    public Result<Void> save(@Validated @RequestBody InvoiceGoodsServiceSaveParams params) {
        return busShopGoodsServiceService.save(params);
    }

    /**
     * 删除
     * @param idsParams
     * @return
     */
    @SaCheckPermission("invoiceGoodsService:delete")
    @PostMapping("/delete")
    public Result<Void> delete(@Validated @RequestBody DeleteIdsParams idsParams) {
        return busShopGoodsServiceService.delete(idsParams.getIds());
    }

    /**
     * 启用/停用
     * @param params
     * @return
     */
    @SaCheckPermission("invoiceGoodsService:edit")
    @PostMapping("/enable")
    public Result<Void> enable(@Validated @RequestBody InvoiceGoodsServiceSaveParams params) {
        return busShopGoodsServiceService.enable(params);
    }
}
