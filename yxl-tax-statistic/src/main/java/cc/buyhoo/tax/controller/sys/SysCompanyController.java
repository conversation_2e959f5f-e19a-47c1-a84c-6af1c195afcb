package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.params.busShop.ShopListExportParams;
import cc.buyhoo.tax.params.sysCompany.*;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysCompany.SysCompanyDetailResult;
import cc.buyhoo.tax.result.sysCompany.SysCompanyPageResult;
import cc.buyhoo.tax.result.sysCompany.GetShopSettleSettingResult;
import cc.buyhoo.tax.service.SysCompanyService;
import cc.buyhoo.tax.util.SatokenUtil;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 平台端/企业管理
 * @ClassName SysCompanyController
 * <AUTHOR>
 * @Date 2023/7/26 16:22
 **/
@RestController
@RequestMapping("/sysCompany")
public class SysCompanyController extends BaseController {

    @Autowired
    private SysCompanyService sysCompanyService;

    /**
     * 纳统企业分页列表
     */
    @GetMapping("/pageList")
    @SaCheckPermission("sysCompany:list")
    public Result<SysCompanyPageResult> pageList(@Validated SysCompanyPageParams params) {
        return sysCompanyService.pageList(params);
    }

    /**
     * 新增企业
     * @param params
     * @return
     */
    @PostMapping("/addCompany")
    @SaCheckPermission("sysCompany:add")
    public Result<Void> addCompany(@RequestBody @Validated SysCompanyAddParams params) {
        return sysCompanyService.addSysCompany(params);
    }

    /**
     * 修改企业
     * @param params
     * @return
     */
    @PostMapping("/updateCompany")
    @SaCheckPermission("sysCompany:edit")
    public Result<Void> updateCompany(@RequestBody @Validated SysCompanySaveParams params) {
        return sysCompanyService.updateSysCompany(params);
    }

    /**
     * 查询当前企业信息
     * @return
     */
    @GetMapping("/getCompanyById/{id}")
    @SaCheckPermission(value = {"sysCompany:detail", "sysCompany:edit"}, mode = SaMode.OR)
    public Result<SysCompanyDetailResult> getCompanyInfo(@PathVariable("id") Long id) {
        return sysCompanyService.getCompanyById(id);
    }

    /**
     * 下拉列表数据
     */
    @GetMapping("/companySelectData/{enableStatus}")
    public Result<List<SelectDataDto>> companySelectData(@PathVariable("enableStatus") Integer enableStatus) {
        SysCompanyQueryParams queryParams = new SysCompanyQueryParams();
        queryParams.setEnableStatus(enableStatus);
        return sysCompanyService.companySelectData(queryParams);
    }

    /**
     * 查询银行信息
     * @return
     */
    @GetMapping("/getShopSettleSetting")
    @SaCheckPermission("shopSettleSetting:update")
    public Result<GetShopSettleSettingResult> getShopSettleSetting() {
        return sysCompanyService.getShopSettleSetting();
    }

    /**
     * 保存银行信息
     * @return
     */
    @PostMapping("/saveShopSettleSetting")
    @SaCheckPermission("shopSettleSetting:update")
    public Result<Void> saveShopSettleSetting(@RequestBody @Validated SaveShopSettleSettingParams params) {
        return sysCompanyService.saveShopSettleSetting(params);
    }


    /**
     * 删除企业
     * @param params
     * @return
     */
    @PostMapping("/deleteById")
    @SaCheckPermission("sysCompany:delete")
    public Result<Void> deleteById(@RequestBody @Validated SysCompanyIdParams params) {
        return sysCompanyService.deleteById(params);
    }

    /**
     * 下拉列表数据
     */
    @GetMapping("/queryCompanyByMarket")
    public Result<List<SelectDataDto>> queryCompanyByMarket(@Validated SysCompanyQueryByMarketIdParams params) {
        return sysCompanyService.queryCompanyByMarket(params);
    }

    /**
     * 导出列表
     *
     * @param params
     * @param response
     * @return
     */
    @PostMapping(value = "/exportCompany", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void export(@Validated @RequestBody SysCompanyExportParams params, HttpServletResponse response) {
        sysCompanyService.export(params, response);
    }
}
