package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.busShopBill.BusShopBillDetailParams;
import cc.buyhoo.tax.result.busShopBill.BusShopBillDetailPageResult;
import cc.buyhoo.tax.service.BusShopBillDetailService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商账单结算明细管理
 *
 * @ClassName BusShopBillDetailController
 * <AUTHOR>
 * @Date 2023/7/27 18:19
 **/
@RestController
@RequestMapping("/shopBillDetail")
public class BusShopBillDetailController extends BaseController {

    @Autowired
    private BusShopBillDetailService busShopBillDetailService;

    /**
     * 查询供应商结算明细分页列表
     *
     * @param busShopBillDetailParams
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busShopBill:settledDetail")
    public Result<BusShopBillDetailPageResult> pageList(@Validated BusShopBillDetailParams busShopBillDetailParams) {
        return busShopBillDetailService.pageList(busShopBillDetailParams);
    }

}
