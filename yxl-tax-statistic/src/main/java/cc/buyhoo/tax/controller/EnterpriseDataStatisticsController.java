package cc.buyhoo.tax.controller;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShop.ShopListExportParams;
import cc.buyhoo.tax.result.enterpriseDataStatistics.TreeVo;
import cc.buyhoo.tax.service.EnterpriseDataStatisticsServer;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName EnterpriseDataStatisticsController
 * @Description 企业数据统计功能
 * <AUTHOR>
 * @Date 2025/6/16 上午11:04
 * @Version 1.0
 */
@RestController
@RequestMapping("/EnterpriseDataStatistics")
public class EnterpriseDataStatisticsController {

    @Autowired
    private EnterpriseDataStatisticsServer enterpriseDataStatisticsServer;
    /**
     * 企业数据统计功能
     *
     * @param params
     * @param
     * @return
     */
    @PostMapping(value = "/dataStatistics")
    public Result<TreeVo> dataStatistics(@RequestBody ShopListExportParams params) {
        //判断前端是否传了区县、市、省、县
        if(StrUtil.isAllBlank(params.getTownCode(), params.getCityCode(),params.getCountyCode(),params.getProvinceCode())){
            //如果没有传省、市、县、区县，则请求第一个接口,返回所有数据
            return enterpriseDataStatisticsServer.dataStatistics(params);
        } else {
            //如果说前端传了省、市、县、区县，则请求第二个接口，返回指定数据
            return enterpriseDataStatisticsServer.getStatisticsByArea(params);
        }
    }

}