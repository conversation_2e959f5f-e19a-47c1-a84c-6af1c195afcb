package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.returnBatch.ReturnBatchListParams;
import cc.buyhoo.tax.result.returnBatch.ReturnBatchListResult;
import cc.buyhoo.tax.service.BusReturnBatchService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 退货单批次
 */
@RestController
@RequestMapping("busReturnBatch")
public class BusReturnBatchController {

    @Autowired
    private BusReturnBatchService busReturnBatchService;

    /**
     * 退货单批次列表
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busShopBill:return")
    public Result<ReturnBatchListResult> pageList(@Validated ReturnBatchListParams params) {
        return busReturnBatchService.pageList(params);
    }

}
