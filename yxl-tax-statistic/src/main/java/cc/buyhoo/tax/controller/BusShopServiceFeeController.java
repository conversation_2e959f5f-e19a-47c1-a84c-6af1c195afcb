package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShopServiceFee.ShopServiceFeeListParams;
import cc.buyhoo.tax.result.busShopServiceFee.ShopServiceFeeListResult;
import cc.buyhoo.tax.service.BusShopServiceFeeService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/busShopServiceFee")
public class BusShopServiceFeeController {

    @Autowired
    private BusShopServiceFeeService busShopServiceFeeService;

    /**
     * 服务费列表
     * @param params
     * @return
     */
    @GetMapping("/list")
    @SaCheckPermission("busShopServiceFee:list")
    public Result<ShopServiceFeeListResult> list(@Validated ShopServiceFeeListParams params) {
        return busShopServiceFeeService.list(params);
    }

}
