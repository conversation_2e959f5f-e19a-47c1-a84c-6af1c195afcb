package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysMenu.SysMenuAddParams;
import cc.buyhoo.tax.params.sysMenu.DeleteMenuParams;
import cc.buyhoo.tax.params.sysMenu.MenuListParams;
import cc.buyhoo.tax.params.sysMenu.UpdateMenuAddParams;
import cc.buyhoo.tax.result.sysMenu.MenuListResult;
import cc.buyhoo.tax.result.sysMenu.RoleMenuListResult;
import cc.buyhoo.tax.result.sysMenu.RouterMenuListResult;
import cc.buyhoo.tax.service.SysMenuService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统菜单
 */
@RestController
@RequestMapping("/sysMenu")
public class SysMenuController {

    @Autowired
    private SysMenuService sysMenuService;

    /**
     * 路由菜单列表
     * @return
     */
    @GetMapping("/routerList")
    public Result<RouterMenuListResult> list() {
        return sysMenuService.routerList();
    }

    /**
     * 菜单管理
     * @return
     */
    @GetMapping("/menuList")
    @SaCheckPermission("sysMenu:list")
    public Result<MenuListResult> menuList(@Validated MenuListParams params) {
        return sysMenuService.menuList(params);
    }

    /**
     * 角色管理选择菜单
     * @return
     */
    @GetMapping("/roleMenuList")
    @SaCheckPermission("sysRole:list")
    public Result<RoleMenuListResult> roleMenuList() {
        return sysMenuService.roleMenuList();
    }

    /**
     * 新增菜单
     */
    @PostMapping("/addMenu")
    @SaCheckPermission("sysMenu:list")
    public Result<Void> addMenu(@Validated @RequestBody SysMenuAddParams params) {
        return sysMenuService.addMenu(params);
    }

    /**
     * 修改菜单
     */
    @PostMapping("/updateMenu")
    @SaCheckPermission("sysMenu:edit")
    public Result<Void> updateMenu(@Validated @RequestBody UpdateMenuAddParams params) {
        return sysMenuService.updateMenu(params);
    }

    /**
     * 菜单新增/修改-查询上级菜单
     */
    @GetMapping("/queryPreMenu")
    @SaCheckPermission("sysMenu:list")
    public Result<RoleMenuListResult> queryPreMenu() {
        return sysMenuService.queryPreMenu();
    }

    /**
     * 删除菜单
     * @param params
     * @return
     */
    @PostMapping("/deleteMenu")
    @SaCheckPermission("sysMenu:delete")
    public Result<Void> deleteMenu(@Validated @RequestBody DeleteMenuParams params) {
        return sysMenuService.deleteMenu(params);
    }

}
