package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.busTaxRecord.BusTaxRecordParams;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busTaxRecord.BusTaxRecordResult;
import cc.buyhoo.tax.service.BusTaxRecordService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 申报记录
 * @ClassName BusTaxRecordController
 * <AUTHOR>
 * @Date 2023/8/11 11:25
 **/
@RestController
@RequestMapping("/busTaxRecord")
public class BusTaxRecordController extends BaseController {

    @Autowired
    private BusTaxRecordService busTaxRecordService;

    /**
     * 申报记录分页查询
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busTaxRecord:list")
    public Result<BusTaxRecordResult> pageList(@Validated BusTaxRecordParams params) {
        return busTaxRecordService.pageList(params);
    }

    /**
     * 现在/更新 申报记录
     * @param params
     * @return
     */
    @PostMapping("/save")
    @SaCheckPermission("busTaxRecord:update")
    public Result<Void> save(@Validated @RequestBody BusTaxRecordParams params) {
        return busTaxRecordService.save(params);
    }

    /**
     * 删除申报记录
     * @param idsParams
     * @return
     */
    @PostMapping("/delete")
    @SaCheckPermission("busTaxRecord:delete")
    public Result<Void> delete(@Validated @RequestBody DeleteIdsParams idsParams) {
        return busTaxRecordService.delete(idsParams);
    }

    /**
     * 完成申报
     * @param params
     * @return
     */
    @PostMapping("/success")
    @SaCheckPermission("busTaxRecord:update")
    public Result<Void> updateSuccess(@Validated @RequestBody BusTaxRecordParams params) {
        return busTaxRecordService.updateSuccess(params);
    }
}
