package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.service.CashUpgradeService;
import cc.buyhoo.upgrade.params.cashUpgrade.*;
import cc.buyhoo.upgrade.result.cashUpgrade.GetMaxVersionResult;
import cc.buyhoo.upgrade.result.cashUpgrade.QueryShopsResult;
import cc.buyhoo.upgrade.result.cashUpgrade.QueryUpgradeShopResult;
import cc.buyhoo.upgrade.result.cashUpgrade.UpgradeListResult;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 升级管理
 */
@RestController
@RequestMapping("/cashUpgrade")
public class CashUpgradeController {

    @Autowired
    private CashUpgradeService cashUpgradeService;

    /**
     * 版本列表
     * @param params
     * @return
     */
    @GetMapping("/upgradeList")
    @SaCheckPermission("cashUpgrade:list")
    public Result<UpgradeListResult> upgradeList(@Validated UpgradeListParams params) {
        return cashUpgradeService.upgradeList(params);
    }

    /**
     * 查询当前最高版本
     * @param params
     * @return
     */
    @GetMapping("/getMaxVersion")
    @SaCheckPermission("cashUpgrade:list")
    public Result<GetMaxVersionResult> getMaxVersion(@Validated GetMaxVersionParams params) {
        return cashUpgradeService.getMaxVersion(params);
    }

    /**
     * 新增版本
     * @param params
     * @return
     */
    @PostMapping("/addUpgrade")
    @SaCheckPermission("cashUpgrade:list")
    public Result<Void> addUpgrade(@RequestBody @Validated AddUpgradeParams params) {
        return cashUpgradeService.addUpgrade(params);
    }

    /**
     * 修改版本
     * @param params
     * @return
     */
    @PostMapping("/updateUpgrade")
    @SaCheckPermission("cashUpgrade:list")
    public Result<Void> updateUpgrade(@RequestBody @Validated UpdateUpgradeParams params) {
        return cashUpgradeService.updateUpgrade(params);
    }

    /**
     * 删除版本
     * @param params
     * @return
     */
    @PostMapping("/deleteUpgrade")
    @SaCheckPermission("cashUpgrade:list")
    public Result<Void> deleteUpgrade(@RequestBody @Validated DeleteUpgradeParams params) {
        return cashUpgradeService.deleteUpgrade(params);
    }

    /**
     * 刷新版本
     * @param params
     * @return
     */
    @PostMapping("/refreshUpgrade")
    @SaCheckPermission("cashUpgrade:list")
    public Result<Void> refreshUpgrade(@RequestBody @Validated RefreshUpgradeParams params) {
        return cashUpgradeService.refreshUpgrade(params);
    }

    /**
     * 版本绑定店铺
     * @param params
     * @return
     */
    @PostMapping("/bindUpgradeShop")
    @SaCheckPermission("cashUpgrade:list")
    public Result<Void> bindUpgradeShop(@RequestBody @Validated BindUpgradeShopParams params) {
        return cashUpgradeService.bindUpgradeShop(params);
    }

    /**
     * 店铺查询
     * @param params
     * @return
     */
    @GetMapping("/queryShops")
    @SaCheckPermission("cashUpgrade:list")
    public Result<QueryShopsResult> queryShops(@Validated QueryShopsParams params) {
        return cashUpgradeService.queryShops(params);
    }

    /**
     * 升级绑定店铺查询
     * @param params
     * @return
     */
    @GetMapping("queryUpgradeShop")
    @SaCheckPermission("cashUpgrade:list")
    public Result<QueryUpgradeShopResult> queryUpgradeShop(@Validated QueryUpgradeShopParams params) {
        return cashUpgradeService.queryUpgradeShop(params);
    }
}
