package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusShopInvoiceSettingEntity;
import cc.buyhoo.tax.enums.PeriodNumberEnum;
import cc.buyhoo.tax.result.busShopInvoiceSetting.GetInvoiceSettingResult;
import cc.buyhoo.tax.result.invoice.InvoicePeriodsDto;
import cc.buyhoo.tax.result.invoice.InvoicePeriodsResult;
import cc.buyhoo.tax.service.BusShopInvoiceSettingService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 财务管理
 */
@RestController
@RequestMapping("/busShopInvoiceSetting")
public class BusShopInvoiceSettingController {



    @Autowired
    private BusShopInvoiceSettingService busShopInvoiceSettingService;

    /**
     * 查询开票期数
     * @return
     */
    @GetMapping("/queryInvoicePeriodNumber")
    public Result<InvoicePeriodsResult> queryInvoicePeriodNumber() {
        PeriodNumberEnum[] periodNumberEnum = PeriodNumberEnum.values();

        InvoicePeriodsResult invoicePeriodsResult = new InvoicePeriodsResult();
        List<InvoicePeriodsDto> list = new ArrayList<>();
        for (PeriodNumberEnum periodNumber : periodNumberEnum) {
            InvoicePeriodsDto invoicePeriodsDto = new InvoicePeriodsDto();
            invoicePeriodsDto.setPeriodsName(periodNumber.getPeriodsName());
            invoicePeriodsDto.setPeriodsLevel(periodNumber.getPeriodsLevel());
            list.add(invoicePeriodsDto);
        }
        invoicePeriodsResult.setList(list);

        return Result.ok(invoicePeriodsResult);
    }
    /**
     * 开票设置
     * @return
     */
    @GetMapping("/getInvoiceSetting")
    public Result<GetInvoiceSettingResult> getInvoiceSetting() {
        return busShopInvoiceSettingService.getInvoiceSetting();
    }

    /**
     * 保存开票设置
     * @param entity
     * @return
     */
    @PostMapping("/saveInvoiceSetting")
    @SaCheckPermission("shopSettleSetting:update")
    public Result<Void> saveInvoiceSetting(@Validated @RequestBody BusShopInvoiceSettingEntity entity) {
        return busShopInvoiceSettingService.saveInvoiceSetting(entity);
    }
}
