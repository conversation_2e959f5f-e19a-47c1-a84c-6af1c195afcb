package cc.buyhoo.tax.controller;

import cc.buyhoo.tax.entity.SysCompanyConfigEntity;
import cc.buyhoo.tax.params.sysCompanyConfig.UpdateSysCompanyConfigParams;
import cc.buyhoo.tax.service.SysCompanyConfigService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import cc.buyhoo.common.standard.Result;

import javax.annotation.Resource;

/**
 * 企业信息配置
 */
@Controller
@RequestMapping("/sysCompanyConfig")
public class SysCompanyConfigController {

    @Resource
    private SysCompanyConfigService sysCompanyConfigService;



    /**
     * 查询本店关于拆单的设置
     * @return
     */
    @GetMapping("/getCompanyConfig")
    @ResponseBody
    public Result<SysCompanyConfigEntity> getCompanyConfig() {
        return sysCompanyConfigService.getCompanyConfig();
    }

    /**
     * 更新本店关于拆单的设置
     * @param updateSysCompanyConfigParams
     * @return
     */
    @PostMapping("/updateCompanyConfig")
    @ResponseBody
    public Result<Void> updateCompanyConfig(@RequestBody UpdateSysCompanyConfigParams updateSysCompanyConfigParams) {
        return sysCompanyConfigService.updateCompanyConfig(updateSysCompanyConfigParams);
    }
}
