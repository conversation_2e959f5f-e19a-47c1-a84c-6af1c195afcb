package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.sysBank.SysBankBranchPageParam;
import cc.buyhoo.tax.params.sysCompany.*;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysBank.SysBankBranchPageResult;
import cc.buyhoo.tax.result.sysCompany.GetShopSettleSettingResult;
import cc.buyhoo.tax.result.sysCompany.SysCompanyDetailResult;
import cc.buyhoo.tax.result.sysCompany.SysCompanyPageResult;
import cc.buyhoo.tax.service.SysBankListService;
import cc.buyhoo.tax.service.SysCompanyService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 银行类型管理
 * @ClassName SysBankListController
 * <AUTHOR>
 * @Date 2023/7/26 16:22
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/sysBank")
public class SysBankListController extends BaseController {

    private final SysBankListService sysBankListService;

    /**
     * 下拉列表数据
     */
    @GetMapping("/bankSelectData")
    public Result<List<SelectDataDto>> bankSelectData() {
        return sysBankListService.bankSelectData();
    }

    /**
     * 支行分页列表
     * @param pageParam
     * @return
     */
    @GetMapping("/bankBranchPageList")
    public Result<SysBankBranchPageResult> bankBranchPageList(@Validated SysBankBranchPageParam pageParam) {
        return sysBankListService.bankBranchPageList(pageParam);
    }


}
