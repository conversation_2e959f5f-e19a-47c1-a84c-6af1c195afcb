package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.userArea.AreaDictQueryParams;
import cc.buyhoo.tax.result.userArea.AreaDictQueryList;
import cc.buyhoo.tax.result.userArea.DataLevelQueryResult;
import cc.buyhoo.tax.service.UserAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户行政区
 * @ClassName UserAreaController
 * <AUTHOR>
 * @Date 2024/11/18 16:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/userArea")
public class UserAreaController {
    @Resource
    private UserAreaService userAreaService;
    /**
     * 维度列表
     *
     * @return
     */
    @GetMapping("/queryDataLevel")
    public Result<DataLevelQueryResult> queryDataLevel() {
        return userAreaService.queryDataLevel();
    }

    /**
     * 行政区列表
     *
     * @return
     */
    @GetMapping("/queryAreaDict")
    public Result<AreaDictQueryList> queryAreaDict() {
        return userAreaService.queryAreaDict();
    }
}
