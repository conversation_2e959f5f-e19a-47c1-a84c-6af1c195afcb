package cc.buyhoo.tax.controller;


import cc.buyhoo.common.bankpay.CmbPayHelper;
import cc.buyhoo.common.bankpay.enums.CmbBusCodEnum;
import cc.buyhoo.common.bankpay.enums.MsgtypEnums;
import cc.buyhoo.common.bankpay.enums.NottypEnums;
import cc.buyhoo.common.bankpay.params.CmbPayConfigParams;
import cc.buyhoo.common.bankpay.params.bb6bthhl.*;
import cc.buyhoo.common.bankpay.params.ntqabinf.NtqabinfDetailParams;
import cc.buyhoo.common.bankpay.params.ntqabinf.NtqabinfParams;
import cc.buyhoo.common.bankpay.params.ntqacinfx.NtqacinfDetailParams;
import cc.buyhoo.common.bankpay.params.ntqacinfx.NtqacinfParams;
import cc.buyhoo.common.bankpay.params.yqn36110.Yqn36110BhntrrcvEntity;
import cc.buyhoo.common.bankpay.result.bb6bthhl.*;
import cc.buyhoo.common.bankpay.result.dclismod.NtqmdlstzResult;
import cc.buyhoo.common.bankpay.result.ntqabinf.NtqabinfResult;
import cc.buyhoo.common.bankpay.result.ntqadinf.NtqacinfResult;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.config.cmb.CmbConfig;
import cc.buyhoo.tax.constant.Constants;
import cc.buyhoo.tax.entity.SysCompanyBankAccountEntity;
import cc.buyhoo.tax.entity.cmb.*;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListExportParams;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListQueryParams;
import cc.buyhoo.tax.params.cmb.CreateNewInventoryParams;
import cc.buyhoo.tax.params.cmb.CreateNewOrderParams;
import cc.buyhoo.tax.result.busTradeRecord.TradeRecordListQueryResult;
import cc.buyhoo.tax.service.BusShopBillService;
import cc.buyhoo.tax.service.CmbService;
import cc.buyhoo.tax.service.TradeRecordService;
import cc.buyhoo.tax.util.CmbVerifySig;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.icbc.api.internal.apache.http.impl.cookie.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 招商银行/招商银行业务
 */
@Slf4j
@RequestMapping("/cmb")
@Controller
public class CmbController {
    @Autowired
    private CmbPayHelper cmbPayHelper;
    @Autowired
    private CmbService cmbService;
    @Autowired
    private BusShopBillService busShopBillService;
    @Autowired
    private TradeRecordService tradeRecordService;
    @Autowired
    private CmbConfig cmbConfig;
    @Autowired
    private RedisCache redisCache;


    /**
     * 手动生成随机订单
     * @param request
     * @return
     */
    @RequestMapping("/cmbNoteRecHand")
    @ResponseBody
    public Result<Void> cmbNoteRecHand(HttpServletRequest request) {

        String body = "";
        try (BufferedReader reader = request.getReader()) {
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            body = stringBuilder.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("招商银行支付回调通知{}",body);
        CMBNoteHand cmbNoteHand = JSONObject.parseObject(body, CMBNoteHand.class);

        //不校验参数
        List<CMBNoteHandData> data = cmbNoteHand.getDataList();
        CMBNoteHandBase base = cmbNoteHand.getBase();

        String notdat = cmbNoteHand.getNotdat();
        CMBNoteData msg = JSONObject.parseObject(notdat, CMBNoteData.class);

        String msgData = msg.getMsgdat();
        CMBNoteDataDetail cmbNoteDataDetail = JSONObject.parseObject(msgData, CMBNoteDataDetail.class);

        //订单总金额
        BigDecimal totalAmount = cmbNoteDataDetail.getC_trsamt();

        BigDecimal balance = totalAmount;

        log.info("本次回调生成的余额{}" , balance);

        //需要随机生成订单号，防止重复
        String tradeNO = cmbNoteDataDetail.getRefnbr();
        Integer index = 1;


        while (balance.compareTo(BigDecimal.ZERO) > 0) {

            if (cmbNoteDataDetail.getAccnbr().equals("803041401421002321") && balance.compareTo(new BigDecimal(20)) <= 0) {
                break;
            }

            Integer goodsCount = getGoodsCount(data);
            BigDecimal orderMoney = base.getBasePrice().multiply(new BigDecimal(goodsCount)).multiply(new BigDecimal(base.getBaseCount()));

            if (balance.compareTo(orderMoney) < 0) {
                orderMoney = balance;
                balance = BigDecimal.ZERO;
            } else {
                balance = balance.subtract(orderMoney);
            }

            log.info("本次交易后，剩余待生成金额{}", balance);

            String orderNO = tradeNO + (index + 1000);
            index ++;

            CreateNewOrderParams createNewOrderParams = new CreateNewOrderParams();
            createNewOrderParams.setBankId(4);
            createNewOrderParams.setBankTrscode(cmbNoteDataDetail.getTrscod());
            createNewOrderParams.setAccnbr(cmbNoteDataDetail.getAccnbr());
            createNewOrderParams.setTradeNo(orderNO);
            createNewOrderParams.setPayAmount(orderMoney);
            createNewOrderParams.setFrmcod(cmbNoteDataDetail.getRpyacc());
            createNewOrderParams.setCreateTime(getDay(cmbNoteDataDetail.getTrsdat(), cmbNoteDataDetail.getTrstim()));

            log.info("当前创建定的信息为{}" , createNewOrderParams);
            log.info("当前创建订单的详情为{}" , cmbNoteDataDetail);

            Result<BigDecimal> obj = cmbService.createNewOrder(createNewOrderParams,cmbNoteDataDetail);
            if (obj.getStatus() == 1) {
                BigDecimal balanceSurplus = obj.getData() == null ? BigDecimal.ZERO : obj.getData();
                balance = balanceSurplus.add(balance);
            } else {
                log.error("创建订单失败，原因为{}", obj);
                return Result.fail();
            }
        }

        return Result.ok();
    }

    /**
     * 查询交易流水列表
     * @param params
     * @return
     */
    @GetMapping("/queryTradeRecordList")
    @ResponseBody
    @SaCheckPermission("busDealRecord:list")
    public Result<TradeRecordListQueryResult> queryTradeRecordList(@Validated TradeRecordListQueryParams params){
        return tradeRecordService.queryTradeRecordList(params);
    }

    /**
     * 导出交易流水列表
     *
     * @param params
     * @param response
     * @return
     */
    @PostMapping(value = "/exportTradeRecord", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @SaCheckPermission("busDealRecord:export")
    public void export(@Validated @RequestBody TradeRecordListExportParams params, HttpServletResponse response) {
        tradeRecordService.exportTradeRecord(params, response);
    }

    /**
     * 获取指定时间前的随机日期
     * @param orderDay 日期
     * @param orderTime 时间
     * @return
     */
    public static String getDay(String orderDay, String orderTime) {
        String day = getRandomDay(DateUtil.parse(orderDay));
        String dayToday = DateUtil.format(DateUtil.parse(orderDay), "yyyy-MM-dd");
        String dayTime = null;
        String startTime = "07:00:00";
        String endTime = "21:00:00";
        if (day.equals(dayToday)) {
            //如果是当天，需要比较时间，如果当前时间大于等于7点，则开始时间为当日的7点，结束时间为当日的当前时间，否则开始时间为当日的7点，结束时间为当日的21点
            if (DateUtil.parse(orderDay + orderTime).isBeforeOrEquals(DateUtil.parse(dayToday + " " + startTime))) {
                return getDay(orderDay, orderTime);
            } else if (DateUtil.parse(orderDay + orderTime).isAfterOrEquals(DateUtil.parse(dayToday+ " "  + endTime))) {
                dayTime = getRandomTime(startTime, endTime);
            } else if (DateUtil.parse(orderDay + orderTime).isBefore(DateUtil.parse(dayToday+ " "  + endTime))) {
                dayTime = getRandomTime(startTime, DateUtil.format(DateUtil.parse(orderDay + orderTime), "HH:mm:ss"));
            } else {
                //这个应该是不存在的
                dayTime = getRandomTime(startTime, endTime);
            }
        } else {
            //如果是非当日，则开始时间为当日的7点，结束时间为当日的21点
            dayTime = getRandomTime(startTime, endTime);
        }
        return day + " " + dayTime;
    }

    /**
     * 获取一个本月初到当前日期之间的随机时间，格式yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getRandomDay(Date endDay) {
        //返回的当前的日期在本月的天数，例如：2024年8月31日返回31
        Integer day = DateUtil.dayOfMonth(endDay);

        Random random = new Random();

        Integer randomDay = random.nextInt(day) ;

        String dayStr = DateUtil.format(DateUtil.offsetDay(endDay, -randomDay), "yyyy-MM-dd");

        return dayStr;
    }

    /**
     * 获取一个随机时间，
     * @param start 限制的开始时间
     * @param end 限制的结束时间
     * @return
     */
    public static String getRandomTime(String start, String end) {
        Date startDate = DateUtil.parse(start);
        Date endDate = DateUtil.parse(end);

        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        if (startTime > endTime) {
            return null;
        }

        long randomTime = startTime + (long) (Math.random() * (endTime - startTime));

        Date randomDate = new Date(randomTime);

        String randomDateStr = DateUtil.format(randomDate, "HH:mm:ss");

        return randomDateStr;
    }

    /**
     * 随机获取商品数量
     * @param data
     * @return
     */
    public Integer getGoodsCount(List<CMBNoteHandData> data) {
        Integer goodsCount = 0;
        BigDecimal random = new BigDecimal(Math.random());
        for (CMBNoteHandData cmbNoteHandData : data) {
            if (random.compareTo(cmbNoteHandData.getMin()) >= 0 && random.compareTo(cmbNoteHandData.getMax()) <= 0) {
                goodsCount = cmbNoteHandData.getGoodsCount();
                break;
            }
        }
        return goodsCount;
    }

    /**
     * 招商银行回调通知接收
     *
     * @param request 请求request
     * @return 返回空
     */
    @RequestMapping("/cmbNoteRec")
    @ResponseBody
    public Result<Void> cmbNoteRec(HttpServletRequest request) {

        String body = "";
        try (BufferedReader reader = request.getReader()) {
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            body = stringBuilder.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("招商银行支付回调通知{}",body);
        CMBNote cmbNote = JSONObject.parseObject(body, CMBNote.class);
        boolean flag = CmbVerifySig.cmbCheckSign(cmbNote,cmbConfig.bankPubkey,cmbConfig.signStr,cmbConfig.USERID);
        log.info("招商银行字符回调通知验签结果{}",flag);
        synchronized (this){
            String redisId = Constants.CMB_CALLBACK_NOTE + cmbNote.getNotdat();

            if (StrUtil.isNotBlank(redisCache.getCacheObject(redisId))) {
                return Result.fail();
            }
            redisCache.setCacheObject(redisId,"1",Constants.CMB_CALLBACK_NOTE_TIMEOUT, TimeUnit.SECONDS);
        }

        if (!flag) {
            //验签不通过
            return Result.fail();
        }



        //记录交易流水信息
        try {
            /**
             * 此处只记录账户变化通知的内容，不做其他处理
             * 如果有其他需求，请在下面的业务里实现
             * 请不要变动此处业务
             */

            if (cmbNote.getNottyp().equals(NottypEnums.YQN01010.getMode())) {
//                //是账务变动通知
                String notdat = cmbNote.getNotdat();
                CMBNoteData msg = JSONObject.parseObject(notdat, CMBNoteData.class);
                tradeRecordService.saveTradeRecord(msg);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        //收到的信息为转账通知
        if (cmbNote.getNottyp().equals(NottypEnums.YQN36110.getMode())) {
            String notdat = cmbNote.getNotdat();
            CMBNoteData msg = JSONObject.parseObject(notdat, CMBNoteData.class);

            //收到的信息为入账通知
            if (msg.getMsgtyp().equals(MsgtypEnums.BHNTRRCV.getMode())) {
                Yqn36110BhntrrcvEntity yqn36110BhntrrcvEntity = JSONObject.parseObject(msg.getMsgdat().toString(), Yqn36110BhntrrcvEntity.class);
                //只有收到转账通知才生成订单，且生成销售订单
                yqn36110Bhntrrcv(yqn36110BhntrrcvEntity);
            }

            //收到的信息为出账通知
            if (msg.getMsgtyp().equals(MsgtypEnums.NCDBTTRS.getMode())) {
                Yqn36110BhntrrcvEntity yqn36110BhntrrcvEntity = JSONObject.parseObject(msg.getMsgdat().toString(), Yqn36110BhntrrcvEntity.class);
                CreateNewInventoryParams createNewInventoryParams = new CreateNewInventoryParams();
                createNewInventoryParams.setTradingAccount(yqn36110BhntrrcvEntity.getRPYACC());
                createNewInventoryParams.setBankId(4);
                createNewInventoryParams.setBankTrscode(yqn36110BhntrrcvEntity.getTRXMOD());
                createNewInventoryParams.setAccnbr(yqn36110BhntrrcvEntity.getACCNBR());
                createNewInventoryParams.setTradeNo(yqn36110BhntrrcvEntity.getMSGNBR());
                cmbService.createNewInventory(createNewInventoryParams);
            }

        } else if (cmbNote.getNottyp().equals(NottypEnums.YQN02030.getMode())) {
            String notdat = cmbNote.getNotdat();
            CMBNoteData msg = JSONObject.parseObject(notdat, CMBNoteData.class);
            if (msg.getMsgtyp().equals(MsgtypEnums.FINS.getMode())) {
                //这里的paySuccMsg只是只请求成功，不代表支付成功，需要判断rtnFlg，如果值未：S代表成功，其他记录为失败，并记录失败原因
                PaySuccMsgDataEntity paySuccMsgDataEntity = JSONObject.parseObject(msg.getMsgdat().toString(), PaySuccMsgDataEntity.class);
                TrsInfoEntity trsInfo = paySuccMsgDataEntity.getTrsInfo();

                String rtnFlg = trsInfo.getRtnFlg();
                if (rtnFlg.equals("S")) {
                    //支付成功
                    return cmbService.resetFinSuccShopBill(trsInfo);
                } else {
                    //支付失败
                    return cmbService.resetFinBackShopBill(trsInfo);
                }

            } else if (msg.getMsgtyp().equals(MsgtypEnums.FINB.getMode())) {
                PayBackMsgDataEntity payBackMsgDataEntity = JSONObject.parseObject(msg.getMsgdat().toString(), PayBackMsgDataEntity.class);
                if (ObjectUtil.isNotEmpty(payBackMsgDataEntity) && ObjectUtil.isNotEmpty(payBackMsgDataEntity.getBackInfo())) {
                    BackInfoEntity backInfo = payBackMsgDataEntity.getBackInfo();
                    return cmbService.resetFinBackShopBill(backInfo);
                }
            }
        } else if (cmbNote.getNottyp().equals(NottypEnums.YQN01010.getMode())) {
            log.info("这是一个账务变动通知{}", cmbNote);
            String notdat = cmbNote.getNotdat();
            log.info("这个账务变动的通知内容为{}", notdat);
            CMBNoteData msg = JSONObject.parseObject(notdat, CMBNoteData.class);
            log.info("这个账务变动的通知内容为{}", msg);
            log.info("这个账务变动的通知内容为{}", msg.getMsgtyp());
            //如果是入账（入账不一定是转账记录，这个暂时不处理）
            CMBNoteDataDetail cmbNoteDataDetail = JSONObject.parseObject(msg.getMsgdat(), CMBNoteDataDetail.class);
            if (msg.getMsgtyp().equals(MsgtypEnums.NCCRTTRS.getMode())) {
                CreateNewOrderParams createNewOrderParams = new CreateNewOrderParams();
                createNewOrderParams.setBankId(4);
                createNewOrderParams.setBankTrscode(cmbNoteDataDetail.getTrscod());
                createNewOrderParams.setAccnbr(cmbNoteDataDetail.getAccnbr());
                createNewOrderParams.setTradeNo(cmbNoteDataDetail.getRefnbr());
                createNewOrderParams.setPayAmount(cmbNoteDataDetail.getC_trsamt());
                createNewOrderParams.setFrmcod(cmbNoteDataDetail.getRpyacc());
                cmbService.createNewOrder(createNewOrderParams,cmbNoteDataDetail);
            }
            if (msg.getMsgtyp().equals(MsgtypEnums.NCDBTTRS.getMode())) {
                log.info("这是出账通知{}", cmbNoteDataDetail);
                //判断为出账，查询出账类型，判断是否要增加采购订单
                //生成采购订单？？？
                CreateNewInventoryParams createNewInventoryParams = new CreateNewInventoryParams();
                createNewInventoryParams.setTradingAccount(cmbNoteDataDetail.getRpyacc());
                createNewInventoryParams.setBankId(4);
                createNewInventoryParams.setBankTrscode(cmbNoteDataDetail.getTrscod());
                createNewInventoryParams.setAccnbr(cmbNoteDataDetail.getAccnbr());
                createNewInventoryParams.setTradeNo(cmbNoteDataDetail.getRefnbr());
                //此处，由于出账为负数，所以要转换成整数
                createNewInventoryParams.setPayAmount(cmbNoteDataDetail.getC_trsamt().abs());
                cmbService.createNewInventory(createNewInventoryParams);
            }
        }
        return Result.ok();
    }

    /**
     * 查询账户历史余额信息
     * @param ntqabinfDetailParams
     * @return
     */
    @RequestMapping("/ntqabinf")
    @ResponseBody
    public Result<NtqabinfResult> ntqabinf(@RequestBody NtqabinfDetailParams ntqabinfDetailParams) {
        System.out.println("ntqabinfParams ===" + ntqabinfDetailParams);
        if (ObjectUtil.isNull(ntqabinfDetailParams.getCompanyId())) {
            return null;
        }
        NtqabinfParams ntqabinfParams = new NtqabinfParams();
        ntqabinfParams.setNtqabinfy(ntqabinfDetailParams);
        NtqabinfResult ntqabinfResult = cmbPayHelper.ntqabinf(ntqabinfParams,getConfigParams(ntqabinfDetailParams.getCompanyId()));
        return Result.ok(ntqabinfResult);
    }


    /**
     * 交易通知-收款通知处理
     * 当其他企业向该企业转账时产生该类型通知
     * 根据通知内容，创建对应的saleList订单
     */
    public void yqn36110Bhntrrcv(Yqn36110BhntrrcvEntity note) {
        /**
         * yqn36110Bhntrrcv
         * 收到入账后可能得操作
         * 要添加的是进货订单，此处作废
         * 1、打款（这个可以查账后操作）
         * 2、创建对应的供应商订单信息（超市信息）
         */
        CreateNewOrderParams createNewOrderParams = new CreateNewOrderParams();
        createNewOrderParams.setAccnbr(note.getACCNBR());
        createNewOrderParams.setFrmcod(note.getRPYACC());
        createNewOrderParams.setTradeNo(note.getMSGNBR());
        createNewOrderParams.setPayAmount(new BigDecimal(note.getTRXAMT()));

        CMBNoteDataDetail cmbNoteDataDetail = new CMBNoteDataDetail();
        cmbNoteDataDetail.setAccnbr(note.getACCNBR());
        cmbNoteDataDetail.setAccnam(note.getACCNAM());
        cmbNoteDataDetail.setTrscod(note.getTRXMOD());
        cmbNoteDataDetail.setTrstim(note.getLGRDAT());
        cmbNoteDataDetail.setC_ccynbr(note.getCCYTYP());
        cmbNoteDataDetail.setC_trsamt(new BigDecimal(note.getTRXAMT()));
        cmbNoteDataDetail.setRpyacc(note.getRPYACC());
        cmbNoteDataDetail.setRpynam(note.getRPYNAM());
        //此处无法获取到余额信息，暂时记为0

        cmbService.createNewOrder(createNewOrderParams, cmbNoteDataDetail);

        //调用一键打款功能
        busShopBillService.transferAccounts();
    }
    /**
     * 测试，招商银卡课支持业务查询
     *
     * @return
     */
    @RequestMapping("/cmbDclismod")
    @ResponseBody
    public Result<List<NtqmdlstzResult>> cmbDclismod() {

        List<NtqmdlstzResult> list = cmbPayHelper.dclismod(CmbBusCodEnum.N02030.getMode(),getConfigParams());

        return Result.ok(list);
    }


    /**
     * 查询可代发的业务
     * @return
     */
    @RequestMapping("/bb6agtqy")
    @ResponseBody
    public Result<Bb6agtqyResult> bb6agtqy() {
        CmbPayConfigParams configParams = getConfigParams();
        Bb6agtqyParams params = new Bb6agtqyParams();
        List<Bb6agtqyParamsBb6typqyx1> list = new ArrayList<>();
        Bb6agtqyParamsBb6typqyx1 bb6typqyx1 = new Bb6agtqyParamsBb6typqyx1();
        bb6typqyx1.setAccnbr("***************");
        bb6typqyx1.setBuscod("N03010");
        list.add(bb6typqyx1);
        params.setBb6typqyx1(list);

        Bb6agtqyResult resList = cmbPayHelper.bb6agtqy(params,getConfigParams());
        return Result.ok(resList);
    }

    /**
     * 查询账户余额信息
     *
     * @param p p.bbknbr 分行号
     *          p.accnbr 银行账号
     *          p.ccynbr 币种
     * @return
     */
    @RequestMapping("/cmbNtqacinf")
    @ResponseBody
    public Result<NtqacinfResult> cmbNtqacinf(@RequestBody NtqacinfDetailParams p) {
        NtqacinfParams params = new NtqacinfParams();
        List<NtqacinfDetailParams> list = new ArrayList<>();
        list.add(p);
        params.setNtqacinfx(list);
       NtqacinfResult resList = cmbPayHelper.ntqacinf(params,getConfigParams());

        return Result.ok(resList);
    }

    /**
     * 校验银行卡是否标准银联卡
     *
     * @param params
     * @return
     */
    @RequestMapping("/cdcMatchCardBin")
    @ResponseBody
    public Result<CdcMatchCardBinZ1Result> cdcMatchCardBin(@RequestBody CdcMatchCardBinParams params) {
        System.out.println("请求的参数信息" + params);
        System.out.println("===========================");
        CdcMatchCardBinZ1Result cdcMatchCardBinX1 = cmbPayHelper.cdcMatchCardBin(params, getConfigParams());
        return Result.ok(cdcMatchCardBinX1);
    }

    /**
     * 待扣代发功能
     *
     * @param params
     * @return
     */
    @RequestMapping("/bb6bthhl")
    @ResponseBody
    public Result<Bb6cdcbhz1Result> bb6bthhl(@RequestBody Bb6bthhlParams params) {
        System.out.println("请求的参数信息" + params);
        System.out.println("+++++++++++++++++++++++++++++++++++++++");
        Bb6cdcbhz1Result result = cmbPayHelper.bb6bthhl(params, getConfigParams());
        return Result.ok(result);
    }

    /**
     * 批次与明细查询
     *
     * @param params
     * @return
     */
    @RequestMapping("/bb6bpdqy")
    @ResponseBody
    public Result<Bb6bpdqyResult> bb6bpdqy(@RequestBody Bb6bpdqyParams params) {
        System.out.println("请求的参数信息" + params);
        System.out.println("+++++++++++++++++++++++++++++++++++++++");
        Bb6bpdqyResult result = cmbPayHelper.bb6bpdqy(params, getConfigParams());
        return Result.ok(result);
    }

    /**
     * 代发批次查询
     *
     * @param params
     * @return
     */
    @RequestMapping("/bb6bthqy")
    @ResponseBody
    public Result<Bb6bthqyResult> bb6bthqy(@RequestBody Bb6bthqyParams params) {
        System.out.println("请求的参数信息" + params);
        System.out.println("+++++++++++++++++++++++++++++++++++++++");
        Bb6bthqyResult result = cmbPayHelper.bb6bthqy(params, getConfigParams());
        return Result.ok(result);
    }

    /**
     * 代发明细查询
     *
     * @param params
     * @return
     */
    @RequestMapping("/bb6dtlqy")
    @ResponseBody
    public Result<Bb6dtlqyResult> bb6dtlqy(@RequestBody Bb6dtlqyParams params) {
        System.out.println("请求的参数信息" + params);
        System.out.println("+++++++++++++++++++++++++++++++++++++++");
        Bb6dtlqyResult result = cmbPayHelper.bb6dtlqy(params, getConfigParams());
        return Result.ok(result);
    }

    /**
     * 根据上传的企业ID获取对应的支付信息，如果未上传，使用默认的信息
     * @param companyId
     * @return
     */
    private CmbPayConfigParams getConfigParams(Long companyId) {

        if (ObjectUtil.isNull(companyId)) {
            return getConfigParams();
        }
        CmbPayConfigParams configParams = new CmbPayConfigParams();
        SysCompanyBankAccountEntity bankAccount = cmbService.getCompanyBankAccount(companyId);
        configParams.setURL(bankAccount.getUrl());
        configParams.setBUSMOD(bankAccount.getBusmod());
        configParams.setUID(bankAccount.getUid());
        configParams.setPRIVATE_KEY(bankAccount.getPrivateKey());
        configParams.setPUBLIC_KEY(bankAccount.getPublicKey());
        configParams.setSYM_KEY(bankAccount.getSymmetricKey());

        return configParams;
    }

    private CmbPayConfigParams getConfigParams() {
        CmbPayConfigParams configParams = new CmbPayConfigParams();
        configParams.setURL("https://cdc.cmbchina.com/cdcserver/api/v2");
        configParams.setUID("U029632865");
        configParams.setPRIVATE_KEY("TvqNdh8w16Jo/nf+u0WzPAoCOpVJMrZs6iiETOt7LWc=");
        configParams.setPUBLIC_KEY("BEynMEZOjNpwZIiD9jXtZSGr3Ecpwn7r+m+wtafXHb6VIZTnugfuxhcKASq3hX+KX9JlHODDl9/RDKQv4XLOFak=");
        configParams.setSYM_KEY("dPikaDrKDUNeW2kQ");

        return configParams;
    }
}
