package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.params.busShop.*;
import cc.buyhoo.tax.params.busTradeRecord.TradeRecordListExportParams;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busShop.BusShopListResult;
import cc.buyhoo.tax.result.busShop.BusShopMigrateInCompanyListQueryResult;
import cc.buyhoo.tax.result.busShop.BusShopSelectDto;
import cc.buyhoo.tax.result.busShop.GetBankInfoResult;
import cc.buyhoo.tax.result.subAccount.CreateSubAccountResult;
import cc.buyhoo.tax.service.BusShopService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商家管理
 * @ClassName BusShopController
 * <AUTHOR>
 * @Date 2023/7/26 12:01
 **/
@RestController
@RequestMapping("/busShop")
public class BusShopController extends BaseController {

    @Autowired
    private BusShopService busShopService;

    /**
     * 同步商家数据
     * @param shopEntity
     * @return
     */
    @PostMapping("/syncShopData")
    public Result<Void> syncShopData(@RequestBody BusShopEntity shopEntity) {
        return busShopService.syncShopData(shopEntity);
    }

    /**
     * 商家列表
     */
    @GetMapping("/list")
    @SaCheckPermission("busShop:list")
    public Result<BusShopListResult> pageList(@Validated ShopListParams shopListParams) {
        return busShopService.pageList(shopListParams);
    }

    /**
     * 保存商户
     */
    @PostMapping("/saveShop")
    @SaCheckPermission("busShop:save")
    public Result<BusShopListResult> saveShop(@Validated @RequestBody ShopParams shopParams) {
        return busShopService.saveShop(shopParams);
    }

    /**
     * 删除商户
     */
    @PostMapping("/deleteShop")
    @SaCheckPermission("busShop:delete")
    public Result<BusShopListResult> deleteShop(@Validated @RequestBody DeleteIdsParams deleteIdsParams) {
        return busShopService.deleteByIds(deleteIdsParams.getIds());
    }

    /**
     * 商家下拉框 列表
     */
    @GetMapping("/selectList")
    public Result<List<BusShopSelectDto>> selectList(@Validated ShopListParams shopListParams) {
        return busShopService.selectList(shopListParams);
    }

    /**
     * 获取银行相关信息
     */
    @GetMapping("/getBankInfo")
    public Result<GetBankInfoResult> getBankInfo(@Validated GetBankInfoParams params) {
        return busShopService.getBankInfo(params);
    }


    /**
     * 批量修改结算费率
     */
    @PostMapping("/saveServiceRateBatch")
    @SaCheckPermission("busShop:updateServiceRate")
    public Result<BusShopListResult> saveServiceRateBatch(@Validated @RequestBody ShopServiceRateParams params) {
        return busShopService.saveServiceRateBatch(params);
    }

    /**
     * 导出交易流水列表
     *
     * @param params
     * @param response
     * @return
     */
    @PostMapping(value = "/exportShop", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @SaCheckPermission("busShop:export")
    public void export(@Validated @RequestBody ShopListExportParams params, HttpServletResponse response) {
        busShopService.exportShop(params, response);
    }

    /**
     * 迁入企业下拉框 列表
     */
    @GetMapping("/queryMigrateInCompanyList")
    public Result<BusShopMigrateInCompanyListQueryResult> queryMigrateInCompanyList(@Validated BusShopMigrateInCompanyListQueryParams params) {
        return busShopService.queryMigrateInCompanyList(params);
    }

    /**
     * 迁入迁出
     */
    @PostMapping("/companyMigrateIo")
    @SaCheckPermission("busShop:companyMigrateIo")
    Result<Void> companyMigrateIo(@Validated @RequestBody ShopCompanyMigrateIoParams params){
        return busShopService.companyMigrateIo(params);
    }


    /**
     * 在线创建子账簿账号
     */
    @PostMapping("/addsubAccount")
    Result<CreateSubAccountResult> addsubAccount(@Validated @RequestBody AddSybAccountParams params){
        return busShopService.addsubAccount(params);
    }

}
