package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.busSetting.SaveBusSettingParams;
import cc.buyhoo.tax.result.busSetting.BusSettingDto;
import cc.buyhoo.tax.service.SysCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 企业配置
 * @ClassName BusSettingController
 * <AUTHOR>
 * @Date 2023/7/31 8:43
 **/
@RestController
@RequestMapping("/busSetting")
public class BusSettingController extends BaseController {
    @Autowired
    private SysCompanyService sysCompanyService;


    /**
     * 获取企业配置
     * @return
     */
    @GetMapping("/getBusSettings")
    public Result<BusSettingDto> invitationUrl() {
        return sysCompanyService.getBusSettings();
    }

    /**
     * 保存设置
     * @return
     */
    @PostMapping("/saveBusSettings")
    public Result<Void> saveBusSettings(@RequestBody SaveBusSettingParams params) {
        return sysCompanyService.saveBusSettings(params);
    }
}
