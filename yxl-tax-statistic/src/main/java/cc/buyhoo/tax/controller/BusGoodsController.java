package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.busGoods.GoodsCategoryBindParams;
import cc.buyhoo.tax.params.busGoods.GoodsListParams;
import cc.buyhoo.tax.result.busGoods.BusGoodsListResult;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.BusGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
* 商品管理表
* @ClassName BusGoods
* <AUTHOR> 
* @Date 2023-07-29
**/
@RestController
@RequestMapping("/busGoods")
@AllArgsConstructor
public class BusGoodsController extends BaseController {

    @Autowired
    private BusGoodsService busGoodsService;

    /**
     * 商品分页列表
     * @param goodsListParams
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busGoods:list")
    public Result<BusGoodsListResult> pageList(@Validated GoodsListParams goodsListParams) {
        return busGoodsService.pageList(goodsListParams);
    }

    /**
     * 商品绑定分类
     * @param goodsCategoryBindParams
     * @return
     */
    @PostMapping("/updateGoodsCategory")
    @SaCheckPermission("busGoods:list")
    public Result<Void> updateGoodsCategory(@Validated @RequestBody GoodsCategoryBindParams goodsCategoryBindParams) {
        return busGoodsService.bindCategory(goodsCategoryBindParams);
    }

}