package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShopInvoice.ShopInvoiceSaveParams;
import cc.buyhoo.tax.params.external.GetInvoiceMsgParmas;
import cc.buyhoo.tax.params.external.SaveInvoiceMsgParams;
import cc.buyhoo.tax.params.external.SaveInvoiceMsgPublicParams;
import cc.buyhoo.tax.params.external.ShopInvoiceAddParams;
import cc.buyhoo.tax.params.invoice.NotifyInvoiceResultParams;
import cc.buyhoo.tax.result.external.GetInvoiceMsgResult;
import cc.buyhoo.tax.service.BusShopInvoiceService;
import cc.buyhoo.tax.service.ExternalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 外部接口调用
 *
 */
@RestController
@RequestMapping("/external")
public class ExternalController {

    @Autowired
    private BusShopInvoiceService busShopInvoiceService;
    @Autowired
    private ExternalService externalService;
    /**
     * 获取开票企业信息
     * @return
     */
    @PostMapping("/getInvoiceMsg")
    public Result<GetInvoiceMsgResult> getInvoiceMsg(@Validated @RequestBody GetInvoiceMsgParmas getInvoiceMsgParmas){
        return externalService.getInvoiceMsg(getInvoiceMsgParmas);
    }

    /**
     * 存储开票结果
     * @param saveInvoiceMsgParams
     * @return
     */
    @PostMapping("/saveInvoiceMsg")
    public Result<Void> saveInvoiceMsg(@Validated @RequestBody SaveInvoiceMsgParams saveInvoiceMsgParams){
        return externalService.saveInvoiceMsg(saveInvoiceMsgParams);
    }

    /**
     * 申请开票
     * 将开票功能存放到纳统平台，开票完成后，回调到餐饮或其他相关业务系统
     * 客户需要上传回调地址，开票的购买方信息及订单信息
     * @param saveInvoiceMsgPublicParams
     * @return
     */
    @PostMapping("/saveInvoiceMsgPub")
    public Result<Void> saveInvoiceMsgPub(@Validated @RequestBody SaveInvoiceMsgPublicParams saveInvoiceMsgPublicParams){
        return externalService.saveInvoiceMsgPub(saveInvoiceMsgPublicParams);
    }

    /**
     * 保存草稿
     * @param params a
     * @return a
     */
    @PostMapping("/saveShopInvoice")
    public Result<NotifyInvoiceResultParams> saveShopInvoice(@Validated @RequestBody ShopInvoiceAddParams params) {
        return externalService.saveShopInvoice(params);
    }
}
