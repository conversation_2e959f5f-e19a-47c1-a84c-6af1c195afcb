package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.enums.DisassembleStatusEnum;
import cc.buyhoo.tax.params.busShop.ShopListExportParams;
import cc.buyhoo.tax.params.saleList.AutoDisassembleSaleListParams;
import cc.buyhoo.tax.params.saleList.DisassembleSaleListParams;
import cc.buyhoo.tax.params.saleList.SaleListDetailParams;
import cc.buyhoo.tax.params.saleList.SaleListParams;
import cc.buyhoo.tax.result.disassembleList.DisassembleStatusDto;
import cc.buyhoo.tax.result.saleList.SaleListDetailResult;
import cc.buyhoo.tax.result.saleList.SaleListResult;
import cc.buyhoo.tax.service.BusSaleListService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订单管理
 */
@RestController
@RequestMapping("/saleList")
public class SaleListController {

    @Autowired
    private BusSaleListService busSaleListService;

    /**
     * 获取拆单状态列表
     * @return
     */

    @GetMapping("/getDisassembleStatus")
    @ResponseBody
    public Result<List<DisassembleStatusDto>> getDisassembleStatusList() {
        List<DisassembleStatusDto> list = DisassembleStatusEnum.getDisassembleStatusList();
        return Result.ok(list);
    }

    /**
     * 拆解订单
     * @param params
     * @return
     */
    @PostMapping("/disassembleSaleList")
    @SaCheckPermission("saleList:list")
    public Result<Void> disassembleSaleList(@Validated @RequestBody DisassembleSaleListParams params) {
        return busSaleListService.disassembleSaleList(params);
    }

    /**
     * 自动执行菜单功能
     * @param params
     * @return
     */
    @PostMapping("/autoDisassembleSaleList")
    public Result<Void> autoDisassembleSaleList(@Validated @RequestBody AutoDisassembleSaleListParams params) {
        return busSaleListService.autoDisassembleSaleList(params);
    }

    /**
     * 订单列表
     * @param params
     * @return
     */
    @GetMapping("/saleList")
    @SaCheckPermission("saleList:list")
    public Result<SaleListResult> saleList(@Validated SaleListParams params) {
        return busSaleListService.saleList(params);
    }

    /**
     * 订单详情
     * @param params
     * @return
     */
    @GetMapping("/saleListDetail")
    @SaCheckPermission("saleList:list")
    public Result<SaleListDetailResult> saleListDetail(@Validated SaleListDetailParams params) {
        return busSaleListService.saleListDetail(params);
    }


    /**
     * 导出交易流水列表
     *
     * @param params
     * @param response
     * @return
     */
    @PostMapping(value = "/exportSaleList", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void exportSaleList(@Validated @RequestBody SaleListParams params, HttpServletResponse response) {
        busSaleListService.exportSaleList(params, response);
    }

}
