package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busCompanyProfitRule.BusCompanyProfitRuleParams;
import cc.buyhoo.tax.result.busCompanyProfitRule.BusCompanyProfitRuleDto;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.BusCompanyProfitRuleEntityService;
import org.springframework.web.bind.annotation.*;

/**
* 企业利润规则
* @ClassName BusCompanyProfitRuleEntity
* <AUTHOR> 
* @Date 2024-06-25
**/
@RestController
@RequestMapping("/companyProfitRule")
@AllArgsConstructor
public class BusCompanyProfitRuleEntityController {
    private final BusCompanyProfitRuleEntityService busCompanyProfitRuleEntityService;

    /**
     * 查询企业利润规则
     * @return
     */
    @GetMapping("/queryCompanyProfitRule")
//    @SaCheckPermission("bus:profitRule:query")
    public Result<BusCompanyProfitRuleDto> queryCompanyProfitRule(){
        return busCompanyProfitRuleEntityService.queryCompanyProfitRule();
    }

    /**
     * 保存企业利润规则配置
     * @param params
     * @return
     */
    @PostMapping("/saveCompanyProfitRule")
//    @SaCheckPermission("bus:profitRule:save")
    public Result save(@RequestBody BusCompanyProfitRuleParams params){
        return busCompanyProfitRuleEntityService.saveCompanyProfitRule(params);
    }
}