package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.service.CashUpgradeForbidShopService;
import cc.buyhoo.upgrade.params.cashUpgradeForbidShop.BindUpgradeForbidShopParams;
import cc.buyhoo.upgrade.result.cashUpgradeForbidShop.QueryUpgradeForbidShopResult;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 禁止升级管理
 */
@RestController
@RequestMapping("/cashUpgradeForbidShop")
public class CashUpgradeForbidShopController {

    @Autowired
    private CashUpgradeForbidShopService forbidShopService;

    /**
     * 禁止升级店铺查询
     * @return
     */
    @GetMapping("queryUpgradeForbidShop")
    @SaCheckPermission("cashUpgrade:list")
    public Result<QueryUpgradeForbidShopResult> queryUpgradeForbidShop(){
        return forbidShopService.queryUpgradeForbidShop();
    }

    /**
     * 绑定禁止升级店铺
     * @param params
     * @return
     */
    @PostMapping("/bindUpgradeForbidShop")
    @SaCheckPermission("cashUpgrade:list")
    public Result<Void> bindUpgradeForbidShop(@RequestBody @Validated BindUpgradeForbidShopParams params){
        return forbidShopService.bindUpgradeForbidShop(params);
    }

}
