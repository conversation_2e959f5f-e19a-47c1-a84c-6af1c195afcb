package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.params.busShopBill.ManualTransferAccountsParams;
import cc.buyhoo.tax.result.busShopBill.BusShopBillPageResult;
import cc.buyhoo.tax.params.busShopBill.BusShopBillParams;
import cc.buyhoo.tax.service.BusShopBillService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 供应商账单管理
 *
 * @ClassName BusShopBillController
 * <AUTHOR>
 * @Date 2023/7/27 18:19
 **/
@RestController
@RequestMapping("/shopBill")
public class BusShopBillController extends BaseController {

    @Autowired
    private BusShopBillService busShopBillService;

    /**
     * 查询供应商账单分页列表
     *
     * @param busShopBillParams
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busShopBill:list")
    public Result<BusShopBillPageResult> pageList(@Validated BusShopBillParams busShopBillParams) {
        return busShopBillService.pageList(busShopBillParams);
    }

    /**
     * 导出企业未结算账单
     *
     * @param busShopBillParams
     * @param response
     */
    @PostMapping(value = "/export", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @SaCheckPermission("busShopBill:export")
    public void export(@Validated @RequestBody BusShopBillParams busShopBillParams, HttpServletResponse response) {
        busShopBillService.export(busShopBillParams, response);
    }

    /**
     * 导入企业结算明细
     * @param file
     */
    @PostMapping(value = "/import")
    @SaCheckPermission("busShopBill:import")
    public Result<Void> importExcel(@RequestPart(value = "file") MultipartFile file) {
        return busShopBillService.importExcel(file);
    }

    /**
     * 一键打款
     * @return
     */
    @PostMapping("/transferAccounts")
    @SaCheckPermission("busShopBill:transferAccounts")
    public Result<Void> transferAccounts() {
        return busShopBillService.transferAccounts();
    }

    /**
     * 手动转账
     * @return
     */
    @PostMapping("/manualTransferAccounts")
    @SaCheckPermission("busShopBill:transferAccounts")
    public Result<Void> manualTransferAccounts(@RequestBody @Validated ManualTransferAccountsParams params) {
        return busShopBillService.manualTransferAccounts(params);
    }
}
