package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.common.AreaEntity;
import cc.buyhoo.tax.entity.common.EnumEntity;
import cc.buyhoo.tax.enums.ShopNatureEnum;
import cc.buyhoo.tax.params.common.GetAreaListSubParam;
import cc.buyhoo.tax.result.busShopInvoice.AnalysisInvoiceDto;
import cc.buyhoo.tax.result.common.GetAreaListResult;
import cc.buyhoo.tax.result.common.GetAreaListSubResult;
import cc.buyhoo.tax.result.common.GetBusinessNatureListResult;
import cc.buyhoo.tax.result.common.UploadFileDto;
import cc.buyhoo.tax.service.UploadService;
import cc.buyhoo.tax.service.CommonService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 公共请求
 * @ClassName CommonController
 * <AUTHOR>
 * @Date 2024/7/15 14:45
 **/
@RequiredArgsConstructor
@RestController
@RequestMapping("/common")
public class CommonController {

    private final UploadService uploadService;

    private final CommonService commonService;

//    @PostMapping("/file/uploadFile")
//    public Result<UploadFileDto> uploadFile(HttpServletRequest request){
//        return uploadService.uploadFile(request);
//    }

    @PostMapping("/file/uploadFile")
    public Result<List<UploadFileDto>> uploadFileList(HttpServletRequest request){
        return uploadService.uploadFileList(request);
    }


    /**
     * OCR解析发票
     * @param request
     * @return
     */
    @PostMapping("/file/analysisInvoice")
    public Result<AnalysisInvoiceDto> analysisInvoice(HttpServletRequest request) {
        return uploadService.analysisInvoice(request);
    }

    /**
     * 获取企业性质列表
     * @return
     */
    @GetMapping("/enums/getBusinessNatureList")
    public Result<List<EnumEntity>> getBusinessNatureList() {
        return Result.ok(ShopNatureEnum.natureList());
    }

    /**
     * 获取地区列表
     * @return
     */
    @GetMapping("/area/getAreaList")
    public Result< List<AreaEntity>> getAreaList() {
        return commonService.getAreaListResult();
    }


    /**
     * 获取下级地区列表
     * @return
     */
    @PostMapping("/area/getAreaListSub")
    public Result<List<AreaEntity>> getAreaListSub(@RequestBody GetAreaListSubParam getAreaListSubParam) {
        return commonService.getAreaListSub(getAreaListSubParam);
    }
}
