package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busContract.*;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.busContract.BusContractDto;
import cc.buyhoo.tax.result.busContract.BusContractListResult;
import cc.buyhoo.tax.result.busContract.BusContractNoDto;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.AllArgsConstructor;

import cc.buyhoo.tax.service.BusContractService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

/**
 * 合同管理
 *
 * <AUTHOR>
 * @since 1.0.0 2024-07-13
 */
@RestController
@RequestMapping("/busContract")
@AllArgsConstructor
public class BusContractController {
    private final BusContractService busContractService;

    /**
     * 合同列表
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busContract:list")
    public Result<BusContractListResult> pageList(@Validated BusContractListParams params) {
        return busContractService.pageList(params);
    }

    /**
     * 合同新增
     *
     * @param addParams
     * @return
     */
    @PostMapping("/addContract")
    @SaCheckPermission("busContract:add")
    public Result<Void> addContract(@Validated @RequestBody BusContractAddParams addParams) {
        return busContractService.addContract(addParams);
    }

    /**
     * 合同编辑
     *
     * @param updateParams
     * @return
     */
    @PostMapping("/updateContract")
    @SaCheckPermission("busContract:update")
    public Result<Void> updateContract(@Validated @RequestBody BusContractUpdateParams updateParams) {
        return busContractService.updateContract(updateParams);
    }

    /**
     * 合同删除
     *
     * @param idsParams
     * @return
     */
    @PostMapping("/deleteContract")
    @SaCheckPermission("busContract:delete")
    public Result<Void> deleteContract(@Validated @RequestBody DeleteIdsParams idsParams) {
        return busContractService.deleteContract(idsParams);
    }

    /**
     * 生成合同编码与名称
     *
     * @param params
     * @return
     */
    @GetMapping("/selectContractNoAndName")
    public Result<BusContractNoDto> selectContractNoAndName(@Validated BusContractNoParams params) {
        return busContractService.createContractNoAndName(params);
    }

    /**
     * 校验订单号
     *
     * @param checkParams
     * @return
     */
    @PostMapping("/checkOrderNo")
    public Result<BigDecimal> checkOrderNo(@Validated @RequestBody BusContractCheckParams checkParams) {
        return busContractService.checkOrderNo(checkParams);
    }

    /**
     * 查询合同详细,通过合同编码
     * @param contractNo
     * @return
     */
    @GetMapping("/selectByContractNo/{contractNo}")
    public Result<BusContractDto> selectByContractNo(@PathVariable String contractNo) {
        return busContractService.selectByContractNo(contractNo);
    }

    /**
     * 导出合同列表
     *
     * @param params
     * @param response
     * @return
     */
    @PostMapping(value = "/export", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @SaCheckPermission("busContract:export")
    public void export(@Validated @RequestBody BusContractListParams params, HttpServletResponse response) {
        busContractService.export(params, response);
    }
}