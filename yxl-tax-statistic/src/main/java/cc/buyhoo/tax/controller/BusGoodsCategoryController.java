package cc.buyhoo.tax.controller;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.entity.BusGoodsCategoryEntity;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.goodsCategory.BusGoodsCategoryParams;
import cc.buyhoo.tax.params.goodsCategory.EdutGoodsCategoryListParams;
import cc.buyhoo.tax.result.goodsCateogry.BusGoodsCategoryDto;
import cc.buyhoo.tax.service.BusGoodsCategoryService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品分类管理
 * @Description
 * @ClassName BusGoodsCategoryController
 * <AUTHOR>
 * @Date 2023/7/26 18:06
 **/
@RestController
@RequestMapping("/goodsCategory")
public class BusGoodsCategoryController extends BaseEntity {

    @Autowired
    private BusGoodsCategoryService busGoodsCategoryService;

    /**
     * 查询分类类别
     *
     * @param busGoodsCategoryParams
     * @return
     */
    @GetMapping("/list")
    @SaCheckPermission("busGoodsCategory:list")
    public Result<List<BusGoodsCategoryDto>> list(@Validated BusGoodsCategoryParams busGoodsCategoryParams) {
        return busGoodsCategoryService.list(busGoodsCategoryParams);
    }

    /**
     * 批量修改商品分类税务信息
     * @param params
     * @return
     */
    @PostMapping("/edutGoodsCategoryList")
    public Result<Void> edutGoodsCategoryList(@Validated @RequestBody EdutGoodsCategoryListParams params) {
        return busGoodsCategoryService.edutGoodsCategoryList(params);
    }

    /**
     * 添加分类
     *
     * @param busGoodsCategoryParams
     * @return
     */
    @PostMapping("/addCategory")
    @SaCheckPermission("busGoodsCategory:add")
    public Result<Void> addCategory(@Validated @RequestBody BusGoodsCategoryParams busGoodsCategoryParams) {
        return busGoodsCategoryService.save(busGoodsCategoryParams);
    }

    /**
     * 添加/修改 分类
     *
     * @param busGoodsCategoryParams
     * @return
     */
    @PostMapping("/updateCategory")
    @SaCheckPermission("busGoodsCategory:edit")
    public Result<Void> updateCategory(@Validated @RequestBody BusGoodsCategoryParams busGoodsCategoryParams) {
        return busGoodsCategoryService.save(busGoodsCategoryParams);
    }

    /**
     * 删除 分类
     *
     * @param
     * @return
     */
    @PostMapping("/deleteCategory")
    @SaCheckPermission("busGoodsCategory:delete")
    public Result<Void> delete(@Validated @RequestBody DeleteIdsParams deleteIdsParams) {
        return busGoodsCategoryService.deleteByIds(deleteIdsParams.getIds());
    }

    /**
     * 查询分类级别
     *
     * @param level 分类级别：0-所有级别分类，1-一级分类，2-二级分类
     * @return
     */
    @GetMapping("/categoryList/{level}")
    public Result<List<BusGoodsCategoryEntity>> categoryList(@PathVariable("level") String level, @Validated BusGoodsCategoryParams busGoodsCategoryParams) {
        return busGoodsCategoryService.categoryList(level, busGoodsCategoryParams);
    }
    /**
     * 查询子分类级别
     * @param busGoodsCategoryParams
     * @return
     */
    @GetMapping("/categoryListByParentId")
    public Result<List<BusGoodsCategoryEntity>> getGoodsCategoryByParentId(@Validated BusGoodsCategoryParams busGoodsCategoryParams) {
        return busGoodsCategoryService.categoryListByParentId(busGoodsCategoryParams);
    }
}
