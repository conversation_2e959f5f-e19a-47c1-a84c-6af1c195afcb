package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.web.controller.BaseController;
import cc.buyhoo.tax.entity.BusInventoryBatchEntity;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.inventoryBatch.InventoryBatchAddParams;
import cc.buyhoo.tax.params.inventoryBatch.InventoryBatchParams;
import cc.buyhoo.tax.result.inventoryBatch.InventoryBatchListResult;
import cc.buyhoo.tax.service.BusInventoryBatchService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 入库单批次
 * @ClassName BusInventoryBatchController
 * <AUTHOR>
 * @Date 2023/7/31 15:36
 **/
@RestController
@RequestMapping("/inventoryBatch")
public class BusInventoryBatchController extends BaseController {

    @Autowired
    private BusInventoryBatchService busInventoryBatchService;

    /**
     * 入库批次分页查询
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("busShopBill:supplyDetail")
    public Result<InventoryBatchListResult> pageList(@Validated InventoryBatchParams params) {
        return busInventoryBatchService.pageList(params);
    }

    /**
     * 添加入库批次
     * @param addParams
     * @return
     */
    @PostMapping("/save")
    @SaCheckPermission("inventoryBatch:add")
    public Result<Void> saveInventoryBatch(@Validated @RequestBody InventoryBatchAddParams addParams) {
        return busInventoryBatchService.saveInventoryBatch(addParams);
    }

    /**
     * 获取订单对应店铺编码、批次ID
     */
    @GetMapping("/getShopUniqueAndBatchIdByOrderId/{orderId}")
    public Result<List<BusInventoryBatchEntity>> getShopUniqueAndBatchIdByOrderId(@PathVariable("orderId") Long orderId) {
        return busInventoryBatchService.getShopUniqueAndBatchIdByOrderId(orderId);
    }

    /**
     * 删除入库批次
     */
    @PostMapping("/delete")
    public Result<Void> delete(@Validated @RequestBody DeleteIdsParams idsParams) {
        return busInventoryBatchService.deleteByIds(idsParams.getIds());
    }
}
