package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.params.sysMarket.SysMarketExportParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateAddParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateAuditParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateExportParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigratePageParams;
import cc.buyhoo.tax.result.sysMigrate.SysCompanyQueryDto;
import cc.buyhoo.tax.result.sysMigrate.SysMigrateDetailResult;
import cc.buyhoo.tax.result.sysMigrate.SysMigratePageResult;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.service.SysMigrateService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* 平台端/迁入迁出管理
*
* <AUTHOR> 
* @since 1.0.0 2024-08-29
*/
@RestController
@RequestMapping("/sysMigrate")
@AllArgsConstructor
public class SysMigrateController {
    private final SysMigrateService sysMigrateService;

    /**
     * 分页查询迁入迁出列表
     * @param pageParams
     * @return
     */
    @GetMapping("pageList")
    @SaCheckPermission("sysMigrate:list")
    public Result<SysMigratePageResult> pageList(@Validated SysMigratePageParams pageParams){
        return sysMigrateService.selectPageList(pageParams);
    }

    /**
     * 迁入迁出详情
     * @param id
     * @return
     */
    @GetMapping("/selectById/{id}")
    @SaCheckPermission("sysMigrate:detail")
    public Result<SysMigrateDetailResult> selectById(@PathVariable("id") Long id){
        return sysMigrateService.selectById(id);
    }

    /**
     * 新增迁入迁出记录
     * @param addParams
     * @return
     */
    @PostMapping("/addSysMigrate")
    @SaCheckPermission(value = {"sysMigrate:detail", "sysMigrate:edit"}, mode = SaMode.OR)
    public Result<String> addSysMigrate(@RequestBody SysMigrateAddParams addParams){
        return sysMigrateService.addSysMigrate(addParams);
    }

    /**
     * 迁入迁出记录审核
     * @param auditParams
     * @return
     */
    @PostMapping("/auditSysMigrate")
    @SaCheckPermission("sysMigrate:audit")
    public Result<String> auditSysMigrate(@RequestBody SysMigrateAuditParams auditParams){
        return sysMigrateService.auditSysMigrate(auditParams);
    }

    /**
     * 删除迁入迁出记录
     * @param idsParams
     * @return
     */
    @PostMapping("/deleteByIds")
    @SaCheckPermission("sysMigrate:delete")
    public Result<String> deleteByIds(@RequestBody DeleteIdsParams idsParams){
        return sysMigrateService.deleteByIds(idsParams);
    }

    /**
     * 导出列表
     *
     * @param params
     * @param response
     * @return
     */
    @PostMapping(value = "/exportMigrate", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void export(@Validated @RequestBody SysMigrateExportParams params, HttpServletResponse response) {
        sysMigrateService.export(params, response);
    }

    @GetMapping("/queryCompanyList")
    public Result<List<SysCompanyQueryDto>> queryCompanyList(){
        return sysMigrateService.queryCompanyList();
    }
}