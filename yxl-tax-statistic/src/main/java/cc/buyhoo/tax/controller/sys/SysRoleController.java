package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysRole.*;
import cc.buyhoo.tax.result.sysRole.RoleDetailResult;
import cc.buyhoo.tax.result.sysRole.SysRolePageResult;
import cc.buyhoo.tax.service.SysRoleService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统角色
 */
@RestController
@RequestMapping("/sysRole")
public class SysRoleController {

    @Autowired
    private SysRoleService sysRoleService;

    /**
     * 角色列表
     * @param params
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("sysRole:list")
    public Result<SysRolePageResult> pageList(@Validated SysRolePageParams params) {
        return sysRoleService.pageList(params);
    }

    /**
     * 新增角色
     * @param params
     * @return
     */
    @PostMapping("/addRole")
    @SaCheckPermission("sysRole:add")
    public Result<Void> addRole(@RequestBody @Validated SysRoleAddParams params) {
        return sysRoleService.addRole(params);
    }

    /**
     * 修改角色
     * @param params
     * @return
     */
    @PostMapping("/updateRole")
    @SaCheckPermission("sysRole:edit")
    public Result<Void> updateRole(@RequestBody @Validated SysRoleEditParams params) {
        return sysRoleService.updateRole(params);
    }

    /**
     * 角色详情
     * @param id
     * @return
     */
    @GetMapping("/selectById/{id}")
    @SaCheckPermission(value = {"sysRole:detail", "sysRole:edit"}, mode = SaMode.OR)
    public Result<RoleDetailResult> selectById(@PathVariable("id") Long id) {
        return sysRoleService.selectById(id);
    }

    /**
     * 删除角色
     * @param params
     * @return
     */
    @PostMapping("/deleteRole")
    @SaCheckPermission("sysRole:delete")
    public Result<Void> deleteRole(@RequestBody @Validated DeleteRoleParams params) {
        return sysRoleService.deleteRole(params);
    }

}
