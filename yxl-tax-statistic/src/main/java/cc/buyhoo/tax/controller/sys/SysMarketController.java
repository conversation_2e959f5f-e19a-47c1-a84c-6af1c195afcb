package cc.buyhoo.tax.controller.sys;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.sysCompany.SysCompanyExportParams;
import cc.buyhoo.tax.params.sysIndustry.SysIndustryQueryParams;
import cc.buyhoo.tax.params.sysMarket.*;
import cc.buyhoo.tax.params.common.DeleteIdsParams;
import cc.buyhoo.tax.result.common.SelectDataDto;
import cc.buyhoo.tax.result.sysMarket.MarketSelectDataDto;
import cc.buyhoo.tax.result.sysMarket.SysMarketDto;
import cc.buyhoo.tax.result.sysMarket.SysMarketPageResult;
import cc.buyhoo.tax.service.SysMarketService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* 平台端/市场管理
*
* <AUTHOR> 
* @since 1.0.0 2024-08-23
*/
@RestController
@RequestMapping("/sysMarket")
@RequiredArgsConstructor
public class SysMarketController {
    private final SysMarketService sysMarketService;

    /**
     * 分页查询市场列表
     * @param pageParams
     * @return
     */
    @GetMapping("/pageList")
    @SaCheckPermission("sysMarket:list")
    public Result<SysMarketPageResult> pageList(@Validated SysMarketPageParams pageParams) {
        return sysMarketService.pageList(pageParams);
    }

    /**
     * 市场详情
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission(value = {"sysMarket:detail", "sysMarket:edit"}, mode = SaMode.OR)
    public Result<SysMarketDto> selectById(@PathVariable("id") Long id) {
        return sysMarketService.selectById(id);
    }

    /**
     * 新增市场
     * @param addParams
     * @return
     */
    @PostMapping("/addMarket")
    @SaCheckPermission("sysMarket:add")
    public Result<Void> addMarket(@Validated @RequestBody SysMarketAddParams addParams) {
        return sysMarketService.addMarket(addParams);
    }

    /**
     * 修改市场
     * @param updateParams
     * @return
     */
    @PostMapping("/editMarket")
    @SaCheckPermission("sysMarket:edit")
    public Result<Void> editMarket(@Validated @RequestBody SysMarketEditParams updateParams) {
        return sysMarketService.editMarket(updateParams);
    }

    /**
     * 删除市场
     * @param idsParams
     * @return
     */
    @PostMapping("deleteByIds")
    @SaCheckPermission("sysMarket:delete")
    public Result<Void> deleteByIds(@Validated @RequestBody DeleteIdsParams idsParams) {
        return sysMarketService.deleteByIds(idsParams);
    }

    /**
     * 市场下拉列表
     * @param enableStatus
     * @return
     */
    @GetMapping("/marketSelectData/{enableStatus}")
    public Result<List<MarketSelectDataDto>> marketSelectData(@PathVariable("enableStatus") Integer enableStatus) {
        SysMarketQueryParams queryParams = new SysMarketQueryParams();
        queryParams.setEnableStatus(enableStatus);
        return sysMarketService.marketSelectData(queryParams);
    }

    /**
     * 导出列表
     *
     * @param params
     * @param response
     * @return
     */
    @PostMapping(value = "/exportMarket", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void export(@Validated @RequestBody SysMarketExportParams params, HttpServletResponse response) {
        sysMarketService.export(params, response);
    }
}