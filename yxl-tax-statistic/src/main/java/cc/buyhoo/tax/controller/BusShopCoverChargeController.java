package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.busShopCoverCharge.ShopCoverChargeListParams;
import cc.buyhoo.tax.result.busShopCoverCharge.ShopCoverChargeListResult;
import cc.buyhoo.tax.service.BusShopCoverChargeService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务费统计
 */
@RestController
@RequestMapping("/busShopCoverCharge")
public class BusShopCoverChargeController {

    @Autowired
    private BusShopCoverChargeService busShopCoverChargeService;

    /**
     * 服务费列表
     * @param params
     * @return
     */
    @GetMapping("/list")
    @SaCheckPermission("busShopCoverCharge:list")
    public Result<ShopCoverChargeListResult> list(@Validated ShopCoverChargeListParams params) {
        return busShopCoverChargeService.list(params);
    }

}
