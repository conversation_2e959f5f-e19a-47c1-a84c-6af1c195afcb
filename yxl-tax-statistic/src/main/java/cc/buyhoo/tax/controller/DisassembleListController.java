package cc.buyhoo.tax.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.params.disassembleList.QueryDisassembleDetailListParams;
import cc.buyhoo.tax.params.disassembleList.QueryDisassembleListParams;
import cc.buyhoo.tax.params.disassembleList.RetryDisassembleParams;
import cc.buyhoo.tax.result.disassembleList.QueryDisassembleDetailListResult;
import cc.buyhoo.tax.result.disassembleList.QueryDisassembleListResult;
import cc.buyhoo.tax.service.DisassembleListService;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 拆单任务列表
 */
@Controller
@RequestMapping("/disassembleList")
public class DisassembleListController {

    @Resource
    private DisassembleListService disassembleListService;

    /**
     * 查询拆单任务列表
     * @param params
     * @return
     */
    @GetMapping("/queryDisassembleList")
    @ResponseBody
    public Result<QueryDisassembleListResult> queryDisassembleList(@Validated QueryDisassembleListParams params) {
        return disassembleListService.queryDisassembleList(params);
    }

    /**
     * 查询拆单任务详情列表
     * @param params
     * @return
     */
    @GetMapping("/queryDisassembleDetailList")
    @ResponseBody
    public Result<QueryDisassembleDetailListResult> queryDisassembleDetailList(@Validated QueryDisassembleDetailListParams params) {
        return disassembleListService.queryDisassembleDetailList(params);
    }

    /**
     * 重新拆单
     * @param params
     * @return
     */
    @PostMapping("/retryDisassemble")
    @ResponseBody
    public Result<Void> retryDisassemble(@RequestBody RetryDisassembleParams params) {
        return disassembleListService.retryDisassemble(params);
    }
}
