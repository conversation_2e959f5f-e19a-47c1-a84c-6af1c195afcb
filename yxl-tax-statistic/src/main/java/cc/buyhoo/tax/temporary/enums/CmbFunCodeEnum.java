package cc.buyhoo.tax.temporary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CmbFunCodeEnum {
    DCLISMOD("DCLISMOD","可经办业务模式查询"),
    BB1PAYOP("BB1PAYOP","企银支付单笔经办"),
    BB1PAYBH("BB1PAYBH","企银支付批量经办"),
    BB1QRYBT("BB1QRYBT","企银批量支付批次查询"),
    BB1QRYBD("BB1QRYBD","企银批量支付明细查询"),
    BB1PAYQR("BB1PAYQR","企银支付业务查询");
    private String name;
    private String desc;
}
