package cc.buyhoo.tax.temporary.params;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CmbcTransferParams
 * @Description 批量转账入参
 * <AUTHOR>
 * @Date 2025/4/2 17:24
 * @Version 1.0
 */
@Data
public class CmbcQueryTransferParams implements Serializable {

    /**
     * 银行标识
     */
    private String bankKey;

    /**
     * 客户业务流水号，代表每一次业务请求的唯一
     */
    private String insId;


    /**
     * 商户d对应的银行编号
     */
    private String mchBankId;
    /**
     * 商户编号
     */
    private String mchId;

}