package cc.buyhoo.tax.temporary.result;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName PayCenterBaseResp
 * @Description 支付中心基础数据结构
 * <AUTHOR>
 * @Date 2025/3/20 16:51
 * @Version 1.0
 */
@Data
public class PayCenterBaseResp implements Serializable {

    private static final long serialVersionUID = -500128977883975027L;

    /**
     * 消息状态码
     * 200-成功（仅表示接口请求）
     */
    private Integer code;

    /**
     * 消息内容
     */
    private String msg;

    /**
     * 数据对象
     */
    private String data;

}