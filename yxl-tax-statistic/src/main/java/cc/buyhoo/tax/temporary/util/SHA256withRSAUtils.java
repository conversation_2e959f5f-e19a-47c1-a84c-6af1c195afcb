package cc.buyhoo.tax.temporary.util;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

@Slf4j
public class SHA256withRSAUtils {
    private static final String ENCODING = "UTF-8";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    private static final String ENCODING_RSA = "RSA";

    /**
     * 获取签名字符串
     * 按参数名按照ASCII码从小到大排序
     * 返回格式：A=a&B=b&C=c
     * @param map 参数集合
     * @param excludeKeyList 排除在外的参数
     * @return
     */
    public static String getParamsSign(Map<String, Object> map, List<String> excludeKeyList) {
        List<String> keys = new ArrayList<>(map.keySet());
        Collections.sort(keys);
        List<String> list = new ArrayList<>();
        for (String key : keys) {
            if (null != excludeKeyList && excludeKeyList.contains(key)) {
                continue;
            }
            if (ObjectUtil.isNotEmpty(map.get(key))) {
                list.add(key + "=" + map.get(key));
            }
        }
        return String.join("&", list);
    }

    /**
     * SHA256WithRSA签名
     *
     * @param data
     * @param privateKey
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws InvalidKeyException
     * @throws SignatureException
     * @throws UnsupportedEncodingException
     */
    private static byte[] sign256(String data, PrivateKey privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException,
            SignatureException, UnsupportedEncodingException {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateKey);

        signature.update(data.getBytes(ENCODING));

        return signature.sign();
    }

    private static boolean verify256(String data, byte[] sign, PublicKey publicKey) {
        if (data == null || sign == null || publicKey == null) {
            return false;
        }
        try {
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(data.getBytes(ENCODING));
            return signature.verify(sign);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 256签名
     *
     * @param data
     * @param privateKey
     * @return
     */
    public static String sign256(String data, String privateKey) {
        try {
            PrivateKey pkey = restorePrivateKey(privateKey);
            byte[] signDatas = sign256(data, pkey);
            return Base64.getEncoder().encodeToString(signDatas);
        } catch (Exception e) {
            log.error("SHA256WithRSA签名失败", e);
        }
        return null;
    }

    /**
     * 校验256
     *
     * @param data
     * @param publicKeyStr
     * @param sign
     * @return
     */
    public static boolean verify256(String data, String publicKeyStr, String sign) {
        byte[] signed = Base64.getDecoder().decode(sign.getBytes());
        PublicKey publicKey = restorePublicKey(publicKeyStr);
        return verify256(data, signed, publicKey);
    }

    /**
     * 还原公钥
     *
     * @param publickeyStr
     * @return
     */
    public static PublicKey restorePublicKey(String publickeyStr) {
        byte[] encodedKey = Base64.getDecoder().decode(publickeyStr.getBytes());
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(encodedKey);
        try {
            KeyFactory factory = KeyFactory.getInstance(ENCODING_RSA);
            PublicKey publicKey = factory.generatePublic(x509EncodedKeySpec);
            return publicKey;
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            log.error("还原公钥失败", e);
        }
        return null;
    }

    /**
     * 还原私钥
     *
     * @param privateKey
     * @return
     */
    public static PrivateKey restorePrivateKey(String privateKey) {
        byte[] encodedKey = Base64.getDecoder().decode(privateKey.getBytes());
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(encodedKey);
        try {
            KeyFactory factory = KeyFactory.getInstance(ENCODING_RSA);
            return factory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (Exception e) {
            log.error("还原私钥失败", e);
        }
        return null;
    }

    public static void main(String[] args) {
        // 明文
        String plainText = "auth_code=131100259954889512&body=百事可乐330ml&charset=UTF-8&goods_detail=百事可乐330ml&mch_create_ip=*************&mch_id=1836658038891249666&nonce_str=b8fe07739c504e108c7faf68ce0f128f&out_trade_no=1836959682652737536&service=unified.trade.micropay&sign_type=RSA_1_256&terminal_info={terminal_type=02, terminal_id=1836658038891249666, app_version=1.0}&time_expire=20240920104952&time_start=20240920104452&total_fee=1&version=2.0";

        String privateK = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCM9ne7iEjYniuXCSj5tTEyqkWK3+9bbwZBjk4bFN7Dw2U/veXdw0OqeVe5ECmjs4htbsjiGHVGOvkkvrTWMm4sKqj4lVnyTPJ4bbTjt8ERi2Wv2H61Gz+hvsciZGfEZ5RFO45BkR90tttxjTA9oPInlEGz6e5zmNfhLrBPjd1zBAzPImTnO3pj2e2HerKRr9EkWmVjpTutiXTb3rzHprfANlzK/clmTG1Q8MIpxy//OCEsikbmlpGx8+q5GoR20I1UI0MwGCE7QILloXBvXLqtjXiB+y9BWylT0STyHJiOcuRL/hJTBzVhM0/4crUnUQo47E9v1VFF5aj4xqz+59i9AgMBAAECggEAQCk3Gm7P6XklEhwjQHWOrSb/fFStWArS6CfjuUPomWZowXioFiZ4uDF/EkcfDNn88QKKAQ9s4ciZ+nWrTguqRgQs4OjqBfWsL9CPk07iXB/86us50SlT70Us0qSs7qLOxCx1dbaegLtyat7+5C5LymQa+88wsAET0fU0XyGNeHni42cPIKlIbOXxfy3nAn64Fz8qqxkZOpaFjeuiIvGPMdxax2JvGWnO8V91SA8SwYaIwDy6DlKd7woH4K+o5SLGnj6vFCwa08zHUSEg4jckJImsCuYHNPkDa63KgLM9Bmd9rVQL3RAFEbN43OeHMv+NVJmJSSF/78+FRtntUVRnQQKBgQDZ4GSk5GjErPYJ4wvDMzeXR8fz7L08q84FrBk+4q2H5LFrgiccN+KBU1tKYcyeFdyqlpxjDZC8wu6VFd+gJuALfiEY6dN1ylGCtGVcG1IXOVYL2t4HxZkAAfaPXdSvgngyfZpZ28ZznDCYZZPJy0H76BQzYxuplL/PqsEAkwTIpQKBgQCloMh5Ey1B5ALQs2wv601PqPvQNQQS0Q6yAIJLZKiRGJwOjR+aFEPq976jd67RjN3w6AdgPi5jT7kEDSKtWLaL9DSvM6cCq0b00DzM2J2TaJBUHuIXnYkAjKoLC4D3MWRoEBzSxMQprrFfVKpUBU5NW4wKPreNSFl8W+Faaya8OQKBgQC2Qujup8SdArOwAadNXAMdIoc6nsiPgImQZS+ZU7cm/0EMVoyxJm7tEPZFexrZBCDc87FPi4GQ5uD/BpVUPDEgbBrz5GdpGh6l4JMKQjCiDOzWbflfNwWO5BWGSVAqolI6qqf8k8Wn4PkmA5NmNAq22KZNdpMlgbIT3xwlhtdxoQKBgHWlUiCmHXR7GADUIVmcQqr9tff7YGxZMJ4BPIt+8Tk3CMpRNYxFLsT8ut30CinwbElNFsgnAt4p55pYFjSZGfkpOi7kepGrxVfpffZnOu5UzIfj8+agUIsiXzo/Lpjm7ZH5N/ACMfAFlRnI0r+hkS/jggJY8BobtOxfex+1kqsJAoGAcQYRNyezpZOzVGOJa/2pXKUG8OP6HIfKTNQq8EIybjVpRrXnvlauMHBNwh1ppPLHEudIzhmIJ22ef0KIZnGddUYFu+jGeWl8KCyFrxXI1Ob9XWX7pyIs0X2Yz9kfqN6uhuFgjF2a/TSrHMybfa3y+9RZDdzfjsDw6U90BOHkOts=";
        String sign = sign256(plainText, privateK);
        //私钥签名
        System.out.println("签名2："+ sign);
        System.out.println("base64:"+ Base64.getEncoder().encodeToString(sign.getBytes()));
    }
}
