package cc.buyhoo.tax.temporary.result;

import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
public class TableDataInfoResp<T> implements Serializable {

    private static final long serialVersionUID = 5828941881902760708L;
    /**
     * 页码
     */
    private long pageNum;

    /**
     * 每页显示条数
     */
    private Long pageSize;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 列表数据
     */
    private List<T> rows;

    /**
     * 消息状态码
     */
    private int code;

    /**
     * 消息内容
     */
    private String msg;

    /**
     * 分页
     *
     * @param list  列表数据
     * @param total 总记录数
     */
    public TableDataInfoResp(List<T> list, long total) {
        this.rows = list;
        this.total = total;
    }

    public TableDataInfoResp(List<T> list, long total, long pageNum, long pageSize) {
        this.rows = list;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public static <T> TableDataInfoResp<T> build(IPage<T> page) {
        TableDataInfoResp<T> rspData = new TableDataInfoResp<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getRecords());
        rspData.setTotal(page.getTotal());
        return rspData;
    }

    public static <T> TableDataInfoResp<T> build(List<T> list) {
        TableDataInfoResp<T> rspData = new TableDataInfoResp<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(list.size());
        return rspData;
    }

    public static <T> TableDataInfoResp<T> build() {
        TableDataInfoResp<T> rspData = new TableDataInfoResp<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setMsg("查询成功");
        return rspData;
    }

}
