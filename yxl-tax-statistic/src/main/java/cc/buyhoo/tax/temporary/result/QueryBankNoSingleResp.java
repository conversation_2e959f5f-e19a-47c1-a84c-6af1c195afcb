package cc.buyhoo.tax.temporary.result;

import lombok.Data;

import java.util.List;

/**
 * @Description    查询开户行行号结果
 * @ClassName TransferQueryResponse
 * <AUTHOR>
 * @Date 2024/10/25 15:03
 **/
@Data
public class QueryBankNoSingleResp {
    private static final long serialVersionUID = 5544286338985213729L;

    /**
     * 原上送行名
     */
    private String bankName;
    /**
     * 查询类型 1:精确查询 2：模糊查询
     */
    private String matchType;

    /**
     * 返回结果列表
     */
    private ResultList resultList;

    @Data
    public static class ResultList {

        private List<Item> items;

        @Data
        public static class Item {
            /**
             * 查询出的行名
             */
            private String matchBankName;
            /**
             * 大小额汇路行号
             */
            private String basBankNo;

            /**
             * 网银互联汇路行号
             */
            private String intBankNo;
        }
    }

}
