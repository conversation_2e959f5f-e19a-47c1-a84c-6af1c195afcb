package cc.buyhoo.tax.temporary.config;

public class CdcConstant {
    //招商银行自动打款
    public static final int BOUND_START = 1000000;
    //用于批量发起支付经办，一个报文交互支持1000笔，大于1000笔需要进行续传
    public static final int MAX_NUMBER = 1000;
    public static final int BOUND_END = 9000000;
    //返回状态码-正确
    public static final String SUS_CODE = "SUC0000";
    //业务代码-支付
    public static final String ZHIFU = "N02030";
    //币种名称-人民币
    public static final String RMB = "10";
    // 处理状态-完成
    public static final String STATUS_FIN = "FIN";
    // 返回结果-银行支付成功
    public static final String STATUS_RTNFLG_SUCC = "S";
    // 返回结果-银行支付失败
    public static final String STATUS_RTNFLG_FAIL = "F,B,R,D,C,";

}
