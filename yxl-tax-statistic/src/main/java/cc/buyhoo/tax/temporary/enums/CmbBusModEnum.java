package cc.buyhoo.tax.temporary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CmbBusModEnum {
    BUSMOD00001("00001","1"),
    BUSMOD00002("00002","无审批"),
    BUSMODS2003("S2003","有审批支付-1"),
    BUSMODS2002("S2002","无审批支付-1"),
    BUSMODS200B("S200B","无审批支付-2"),
    BUSMODS200C("S200C","有审批支付-2"),
    BUSMODS4008("S4008","有审批支付"),
    BUSMODS400C("S400C","无审批支付")
    ;
    private String mode;
    private String desc;
}
