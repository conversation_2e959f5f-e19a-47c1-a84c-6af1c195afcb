package cc.buyhoo.tax.temporary.util;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName PayCenterUtil
 * @Description 支付中心工具
 * <AUTHOR>
 * @Date 2025/3/20 16:38
 * @Version 1.0
 */
@Slf4j
public class PayCenterUtil {


    /**
     * 构建请求头
     */
    public static  Map<String,String> buildHeader( Map<String, Object> paramMap , String mch_key){
        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("yxl-timestamp", DateUtil.currentSeconds() + "");
        headerMap.put("yxl-nonce", RandomUtil.randomString(32));
        List<String> excludeKeyList = new ArrayList<>();
        excludeKeyList.add("sign");
        excludeKeyList.add("yxl-sign");
        excludeKeyList.add("customJson");
        excludeKeyList.add("thirdMsg");
        paramMap.put("yxl-timestamp", headerMap.get("yxl-timestamp"));
        paramMap.put("yxl-nonce", headerMap.get("yxl-nonce"));

        String parStr = SHA256withRSAUtils.getParamsSign(paramMap, excludeKeyList);
        log.info("支付中心签名参数：{}", parStr);
        String sign = SHA256withRSAUtils.sign256(parStr, mch_key);
        log.info("支付中心签名：{}", sign);
        headerMap.put("yxl-sign", sign);
        return headerMap;
    }
}