package cc.buyhoo.tax.temporary.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName CdcPayConfig
 * <AUTHOR>
 * @Date 2024/1/9 13:44
 */
@Component
@Data
@ConfigurationProperties(prefix = "bankpay.cdc")
public class CdcPayConfig {
    /**
     * 银行公钥
     */
    private String publicKey;
    /**
     * 客户私钥
     */
    private String privateKey;
    /**
     * 对称密钥
     */
    private String symKey;
    /**
     * 测试地址
     */
    private String url;
    /**
     * 企业网银用户号
     */
    private String uid;
}
