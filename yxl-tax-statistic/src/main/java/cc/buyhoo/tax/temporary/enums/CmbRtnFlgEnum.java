package cc.buyhoo.tax.temporary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 支付方式
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CmbRtnFlgEnum {

    S("S", "银行支付成功","成功"),
    F("F", "银行支付失败","失败"),
    B("B", "银行支付被退票","退票"),
    R("R", "企业审批否决","否决"),
    D("D", "企业过期不审批","过期"),
    C("C", "企业撤销", "撤消");
    private String status;
    private String desc;
    private String value;

}
