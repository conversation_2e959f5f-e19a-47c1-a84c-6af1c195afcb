package cc.buyhoo.tax.temporary.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 支行信息
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-22 16:19
 **/
@Data
public class BankBranchResp implements Serializable {

    private static final long serialVersionUID = -1646623998179782394L;
    /**
     * 总行行号
     */
    private String bankCode;
    /**
     * 总行名称
     */
    private String bankName;
    /**
     * 分行行号
     */
    private String branchCode;
    /**
     * 分行名称
     */
    private String branchName;
    /**
     * 地区代码
     */
    private String areaCode;

}
