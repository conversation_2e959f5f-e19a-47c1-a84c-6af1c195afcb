package cc.buyhoo.tax.temporary.params;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CmbcTransferParams
 * @Description 单笔转账入参
 * <AUTHOR>
 * @Date 2025/4/2 17:24
 * @Version 1.0
 */
@Data
public class CmbcTransferParams implements Serializable {

    /**
     * 银行标识
     */
    private String bankKey;

    /**
     * 付款账号
     */
    private String acntNo;

    /**
     * 收款账号
     */
    private String acntToNo;
    /**
     * 收款人名称
     */
    private String acntToName;

    /**
     * 转账金额
     */
    private String amount;

    /**
     * 商户d对应的银行编号
     */
    private String mchBankId;
    /**
     * 商户编号
     */
    private String mchId;
    /**
     * 跨行标识 0：本行 1：跨行
     */
    private String externBank="0";
    /**
     * 汇路 0：本行转账，<externBank>需输 0
     * 汇路 2：跨行转账-小额支付系统，<externB
     * ank>需输 1
     * 汇路 3：跨行转账-大额支付系统，<externB
     * ank>需输 1
     * 汇路 5：跨行转账-网银互联，<externBan
     * k>需输 1
     * 汇路 9：跨行转账-自动计算汇路，<externB
     * ank>需输 1
     * 注意： 必须提供完整的行名行号； 汇路 2、
     * 5 最多支持 100 万元，7*24 小时运行。 汇
     * 路 3 金额无上限，工作时间：工作日的前一日
     * 20:30 至工作日当日的 17:15
     * 当选择汇路 9，如 bankCode 字段上送收款
     * 行总行行号，则无法使用大额、小额汇路，仅
     * 会尝试使用网银互联汇路；如 bankCode 字
     * 段上送收款行支行行号，则会在大额、小额、
     * 网银互联三个汇路中选中当前时间、金额可达
     * 的最优汇路。
     */
    private String localFlag="0";
    /**
     *   收款人账户类型：1:对公；2:对私；
     */
    private String rcvCustType="2";
    /**
     * 收款人开户行行号 是否跨行标志<externBa
     * nk>选择 1 时必填
     */
    private String bankCode;
    /**
     * 收款人开户行名称 大/小额、网银互联汇路此
     * 项必填
     */
    private String bankName;

    /**
     * 备注
     */
    private String remark;
}