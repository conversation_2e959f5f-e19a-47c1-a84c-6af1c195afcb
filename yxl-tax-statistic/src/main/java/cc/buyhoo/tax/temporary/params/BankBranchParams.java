package cc.buyhoo.tax.temporary.params;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 支付接口参数
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 14:03
 **/
@Data
public class BankBranchParams implements Serializable {
    private static final long serialVersionUID = -1794155160672727853L;

    /**
     * 商户编号
     */
    private String mchId;
    /**
     * 客户IP
     */
    private String clientIp;
    /**
     * 支行名称
     */
    private String branchName;
    /**
     * 支行行号
     */
    private String branchCode;
    /**
     * 总行行号
     */
    private String bankCode;
    /**
     * 分页条数
     */
    private Long pageSize;
    /**
     * 页码
     */
    private Long pageNum;
}
