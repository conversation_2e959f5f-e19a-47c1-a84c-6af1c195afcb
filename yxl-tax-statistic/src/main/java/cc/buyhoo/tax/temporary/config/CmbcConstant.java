package cc.buyhoo.tax.temporary.config;

/**
 * @ClassName CmbcConstant
 * @Description 民生银行常量
 * <AUTHOR>
 * @Date 2025/4/2 17:37
 * @Version 1.0
 */
public class CmbcConstant {

    /**
     * 单笔转账
     */
    public static final String TRANSFER = "/openApi/v1/bankBusiness/transfer";
    /**
     * 批量转账
     */
    public static final String BATCHTRANSFER = "/openApi/v1/bankBusiness/batchTransfer";
    /**
     * 查询转账结果
     */
    public static final String QUERY_TRANSFER = "/openApi/v1/bankBusiness/queryTransfer";
    /**
     * 查询批量转账结果
     */
    public static final String QUERY_BATCH_TRANSFER = "/openApi/v1/bankBusiness/queryBatchTransfer";

    /**
     * 开户行行号
     */
    public static final String QUERY_B2E_BANK_NO_SINGLE = "/openApi/v1/bankBusiness/queryB2eBankNoSingle";

    /**
     * 支行信息查询
     */
    public static final String QUERY_BANK_BRANCH_IFNO = "/openApi/v1/bankBusiness/bankBranchInfo";
}