package cc.buyhoo.tax.temporary.params;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CmbcTransferParams
 * @Description 批量转账入参
 * <AUTHOR>
 * @Date 2025/4/2 17:24
 * @Version 1.0
 */
@Data
public class CmbcBatchTransferParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 银行标识
     */
    private String bankKey;

    /**
     * 付款账号
     */
    private String payerAcNo;

    /**
     * 总记录数（★）正常数据
     */
    private String totalRow;

    /**
     * 商户d对应的银行编号
     */
    private String mchBankId;
    /**
     * 商户编号
     */
    private String mchId;


    /**
     * 总金额数（★）
     */

    private String totalAmt;


    /**
     * 竖线“|”分割数据元素，以尖号“^”为数据行分割符，
     * 具体格式定义与付款类型有关（★）
     * 交易参考号|收款账号|收款账户名 |用途|备注|本他行标志|汇路|收款行行名|收款行行号|
     * 企业自制凭证号|备用字段 1|备用字段 2|备用字段 3|金额^交易参考号|收款账号|收款账户名 |用途|备注|本他行标志|汇路|收款行行名|收款行行号|
     * 企业自制凭证号|备用字段 1|备用字段 2|备用字段 3|金额
     */
    private String billInfos;


    /**
     * 订单信息
     */
    @Data
    public static class BillInfo implements Serializable {

        private static final long serialVersionUID = 3179963189514571874L;
        /**
         * 交易参考号
         */
        private String transactionReferenceNumber;
        /**
         * 收款账号
         */
        private String receivingAccountNumber;
        /**
         * 收款账户名
         */
        private String receivingAccountName;
        /**
         * 用途
         */
        private String purpose;
        /**
         * 备注
         */
        private String remarks;
        /**
         * 本他行标志
         */
        private String bankFlag = "0";
        /**
         * 汇路
         */
        private String remittanceRoute;
        /**
         * 收款行行名
         */
        private String receivingBankName;
        /**
         * 收款行行号
         */
        private String receivingBankCode;
        /**
         * 企业自制凭证号
         */
        private String enterpriseSelfMadeVoucherNumber;
        /**
         * 备用字段 1
         */
        private String spareField1;
        /**
         * 备用字段 2
         */
        private String spareField2;
        /**
         * 备用字段 3
         */
        private String spareField3;
        /**
         * 金额
         */
        private String amount;
    }
}