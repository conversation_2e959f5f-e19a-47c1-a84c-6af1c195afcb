package cc.buyhoo.tax.temporary;


import cc.buyhoo.common.dingdingtalk.utils.SendDingDingTalkUtils;
import cc.buyhoo.tax.temporary.config.CmbcConstant;
import cc.buyhoo.tax.temporary.params.*;
import cc.buyhoo.tax.temporary.result.PayCenterBaseResp;
import cc.buyhoo.tax.temporary.result.TableDataInfoResp;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static cc.buyhoo.tax.temporary.util.PayCenterUtil.buildHeader;


/**
 * 民生银行支付
 */
@Component
@Slf4j
public class PayCenterPayHelper {


    @Resource
    private SendDingDingTalkUtils sendDingDingTalkUtils;

    /**
     * 单笔转账
     */
    public PayCenterBaseResp transferPay(String url,CmbcTransferParams params, String payCenterSecretKey){
        Map<String, Object> map = BeanUtil.beanToMap(params);
        Map<String, String> headerMap =  buildHeader(map,payCenterSecretKey);
        String jsonStr = JSONUtil.toJsonStr(params);
        url = url+ CmbcConstant.TRANSFER;
        log.info("民生银行单笔转账入参：{}----------------headerMap:{}------------------key:{}",jsonStr, JSONUtil.toJsonStr(headerMap), payCenterSecretKey);
        try {
            HttpResponse response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
            log.info("民生银行单笔转账返回结果：{}", response.body());
            PayCenterBaseResp bean = JSONUtil.toBean(response.body(), PayCenterBaseResp.class);
            if (bean.getCode()!= HttpStatus.HTTP_OK) {
                log.info("民生银行单笔转账-支付中心-错误信息：{}", bean.getMsg());
               // sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行单笔转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心返回异常"+ bean.getMsg(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行单笔转账警告",null);
            }
            return bean;
        }catch (Exception e){
            log.error("民生银行单笔转账异常：{}",e.getMessage());
           // sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行单笔转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心异常"+e.getMessage(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行单笔转账警告",null);
            PayCenterBaseResp bean = new PayCenterBaseResp();
            bean.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
            bean.setMsg("调用支付中心异常"+e.getMessage());
            return bean;
        }


    }

    /**
     * 批量转账
     */
    public PayCenterBaseResp batchTransferPay(String url,CmbcBatchTransferParams params, String payCenterSecretKey){
        Map<String, Object> map = BeanUtil.beanToMap(params);
        Map<String, String> headerMap =  buildHeader(map,payCenterSecretKey);
        String jsonStr = JSONUtil.toJsonStr(params);
        url = url+ CmbcConstant.BATCHTRANSFER;
        log.info("民生银行批量转账入参：{}------------------headerMap:{}-------------------key:{}",jsonStr, JSONUtil.toJsonStr(headerMap), payCenterSecretKey);
        try {
            HttpResponse response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
            log.info("民生银行批量转账返回结果：{}", response.body());
            PayCenterBaseResp bean = JSONUtil.toBean(response.body(), PayCenterBaseResp.class);
            if (bean.getCode()!= HttpStatus.HTTP_OK) {
                log.info("民生银行批量转账-支付中心-错误信息：{}", bean.getMsg());
                //sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行批量转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心返回异常"+ bean.getMsg(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行批量转账警告",null);
            }
            return bean;
        }catch (Exception e){
            log.error("民生银行批量转账异常：{}",e.getMessage());
           // sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行批量转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心异常"+e.getMessage(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行批量转账警告",null);
            PayCenterBaseResp bean = new PayCenterBaseResp();
            bean.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
            bean.setMsg("调用支付中心异常"+e.getMessage());
            return bean;
        }
    }

    /**
     * 查询单笔转账结果
     */
    public PayCenterBaseResp queryTransfer(String url,CmbcQueryTransferParams params, String payCenterSecretKey){
        Map<String, Object> map = BeanUtil.beanToMap(params);
        Map<String, String> headerMap =  buildHeader(map,payCenterSecretKey);
        String jsonStr = JSONUtil.toJsonStr(params);
        url = url+ CmbcConstant.QUERY_TRANSFER;
        log.info("民生银行查询单笔转账入参：{},headerMap:{},key:{}",jsonStr,jsonStr,payCenterSecretKey);
        try {
            HttpResponse response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
            log.info("民生银行查询单笔转账返回结果：{}", response.body());
            PayCenterBaseResp bean = JSONUtil.toBean(response.body(), PayCenterBaseResp.class);
            if (bean.getCode()!= HttpStatus.HTTP_OK) {
                log.info("民生银行查询单笔转账-支付中心-错误信息：{}", bean.getMsg());
               // sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行查询单笔转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心返回异常"+ bean.getMsg(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行查询单笔转账警告",null);
            }
            return bean;
        }catch (Exception e){
            log.error("民生银行查询单笔转账账异常：{}",e.getMessage());
           // sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行查询单笔转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心异常"+e.getMessage(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行查询单笔转账警告",null);
            PayCenterBaseResp bean = new PayCenterBaseResp();
            bean.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
            bean.setMsg("调用支付中心异常"+e.getMessage());
            return bean;
        }

    }


    /**
     * 查询批量转账结果
     */
    public PayCenterBaseResp queryBatchTransfer(String url,CmbcQueryBatchTransferParams params, String payCenterSecretKey){
        Map<String, Object> map = BeanUtil.beanToMap(params);
        Map<String, String> headerMap =  buildHeader(map,payCenterSecretKey);
        String jsonStr = JSONUtil.toJsonStr(params);
        url = url+ CmbcConstant.QUERY_BATCH_TRANSFER;
        log.info("民生银行查询批量转账入参：{},headerMap:{},key:{}",jsonStr,jsonStr,payCenterSecretKey);
        try {
            HttpResponse response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
            log.info("民生银行查询批量转账返回结果：{}", response.body());
            PayCenterBaseResp bean = JSONUtil.toBean(response.body(), PayCenterBaseResp.class);
            if (bean.getCode()!= HttpStatus.HTTP_OK) {
                log.info("民生银行查询批量转账-支付中心-错误信息：{}", bean.getMsg());
              //  sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行查询批量转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心返回异常"+ bean.getMsg(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行查询批量转账警告",null);
            }
            return bean;
        }catch (Exception e){
            log.error("民生银行查询批量转账异常：{}",e.getMessage());
           // sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行查询批量转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心异常"+e.getMessage(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行查询批量转账警告",null);
            PayCenterBaseResp bean = new PayCenterBaseResp();
            bean.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
            bean.setMsg("调用支付中心异常"+e.getMessage());
            return bean;
        }

    }

    /**
     * 查询开户行行号
     */
    public PayCenterBaseResp queryBankNoSingleQry(String url, CmbcQueryB2eBankNoSingleParams params, String payCenterSecretKey){
        Map<String, Object> map = BeanUtil.beanToMap(params);
        Map<String, String> headerMap =  buildHeader(map,payCenterSecretKey);
        String jsonStr = JSONUtil.toJsonStr(params);
        url = url+ CmbcConstant.QUERY_B2E_BANK_NO_SINGLE;
        log.info("民生银行查询开户行行号入参：{},headerMap:{},key:{}",jsonStr,JSONUtil.toJsonStr(headerMap),payCenterSecretKey);
        try {
            HttpResponse response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
            log.info("民生银行查询开户行行号返回结果：{}", response.body());
            PayCenterBaseResp bean = JSONUtil.toBean(response.body(), PayCenterBaseResp.class);
            if (bean.getCode()!= HttpStatus.HTTP_OK) {
                log.info("民生银行查询开户行行号-支付中心-错误信息：{}", bean.getMsg());
                //  sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行查询批量转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心返回异常"+ bean.getMsg(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行查询批量转账警告",null);
            }
            return bean;
        }catch (Exception e){
            log.error("民生银行查询开户行行号异常：{}",e.getMessage());
            // sendDingDingTalkUtils.sendDingDingTalkMsg(null, "民生银行查询批量转账警告通知", CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "调用支付中心异常"+e.getMessage(), JSONUtil.toJsonStr(params), CmbFunCodeEnum.valueOf(CmbFunCodeEnum.BB1PAYBH.getName()).getDesc(), "民生银行查询批量转账警告",null);
            PayCenterBaseResp bean = new PayCenterBaseResp();
            bean.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
            bean.setMsg("调用支付中心异常"+e.getMessage());
            return bean;
        }

    }

    /**
     * 查询支行信息
     * @param url
     * @param params
     * @param payCenterSecretKey
     * @return
     */
    public PayCenterBaseResp queryBankBranch(String url, BankBranchParams params, String payCenterSecretKey) {
        Map<String, Object> map = BeanUtil.beanToMap(params);
        Map<String, String> headerMap = buildHeader(map, payCenterSecretKey);
        String jsonStr = JSONUtil.toJsonStr(params);
        url = url + CmbcConstant.QUERY_BANK_BRANCH_IFNO;
        log.info("支行信息查询入参：{},headerMap:{},key:{}", jsonStr, jsonStr, payCenterSecretKey);
        try {
            HttpResponse response = HttpRequest.post(url).headerMap(headerMap, false).body(jsonStr).execute();
            log.info("支行信息查询返回结果：{}", response.body());
            PayCenterBaseResp bean = JSONUtil.toBean(response.body(), PayCenterBaseResp.class);
            if (bean.getCode() != HttpStatus.HTTP_OK) {
                log.info("支行信息查询 错误信息：{}", bean.getMsg());
            }
            return bean;
        } catch (Exception e) {
            log.error("支行信息查询异常：{}", e.getMessage());
            PayCenterBaseResp bean = new PayCenterBaseResp();
            bean.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
            bean.setMsg("调用支付中心异常" + e.getMessage());
            return bean;
        }
    }

}
