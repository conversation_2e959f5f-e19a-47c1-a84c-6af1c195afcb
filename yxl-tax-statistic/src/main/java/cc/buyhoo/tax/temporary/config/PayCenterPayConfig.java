package cc.buyhoo.tax.temporary.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName CdcPayConfig
 * <AUTHOR>
 * @Date 2024/1/9 13:44
 */
@Component
@Data
@ConfigurationProperties(prefix = "paycenter")
public class PayCenterPayConfig {

    /**
     * 支付中心转账url
     */
   private String transferUrl;

}
