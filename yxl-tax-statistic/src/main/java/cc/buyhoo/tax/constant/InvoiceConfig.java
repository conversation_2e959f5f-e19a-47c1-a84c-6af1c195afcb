package cc.buyhoo.tax.constant;

/**
 * 设置开票所需的配置
 */
public class InvoiceConfig {

    //四期开票域名信息
    private static final String INVOICE_URL = "https://union.fyyk.net/logistics";
    //全电登录接口
    public static final String INVOICE_LOGIN_URL = INVOICE_URL + "/electricInv/eleUserLogin";

    //开具发票
    public static final String INVOICE_INVOICE_URL = INVOICE_URL + "/electricInv/generateQdInvoice";

    //获取发票信息
    public static final String INVOICE_INVOICE_INFO_URL = INVOICE_URL + "/electricInv/getQdInvoice";

    //查询发票文件
    public static final String INVOICE_INVOICE_FILE_URL = INVOICE_URL + "/electricInv/querySdInvoiceFile";
    //实名认证
    public static final String RPA_QR_CODE = INVOICE_URL + "/electricInv/rpaQrCode";

    //查询实名认证结果
    public static final String PRA_AUTH_STATUS = INVOICE_URL + "/electricInv/rpaAuthStatus";

    //apiKey
    public static final String APIKEY = "AEEED8EA0963FE44";
    //apiSecret
    public static final String APISECRET = "fe4b00d691d74f22b81418546ca036fc";
    //交互字符格式
    public static final String CHARTSET = "UTF-8";
}
