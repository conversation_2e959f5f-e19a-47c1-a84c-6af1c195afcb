package cc.buyhoo.tax.constant;

public class Constants {

    //登陆失败次数
    public static final String PWD_ERR_CNT_KEY = "taxStatistic:pwd_err_cnt:";

    //登录用户
    public static final String LOGIN_USER = "loginUser";

    //创建新进货订单的
    public static final String CREATE_NEW_INVENTORY_REDIS_KEY = "MARK_CREATE_INVENTORY";

    //创建新订单的标记
    public static final String CREATE_NEW_ORDER_REDIS_KEY = "MARK_CREATE_ORDER";
    // 创建新的交易记录
    public static final String CREATE_NEW_TRADE_RECORD_KEY = "CREATE_NEW_TRADE_RECORD_KEY";
    //招商银行回调限制标识，防止重复处理相同回调信息
    public static final String CMB_CALLBACK_NOTE = "CMB_CALLBACK_NOTE";
    //招商银行回调redis标识缓存时间
    public static final Integer CMB_CALLBACK_NOTE_TIMEOUT = 10;
    //最小价格
    public static final String MIN_CANYIN_PRICE = "MIN_CANYIN_PRICE";
}
