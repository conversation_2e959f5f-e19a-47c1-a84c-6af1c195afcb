package cc.buyhoo.tax.constant;

import cn.hutool.core.util.ObjectUtil;

/**
 * 店铺类型
 */

public enum ShopTypeEnum {

    EASY_SHOP("便利店" ,1 ),
    FRUIT_SHOP("水果店" , 2),
    INFANT_SHOP("母婴店" , 3),
    YINONG_CENTER_SHOP("益农中心站" ,4),
    YINONG_SHOP("益农标准站" , 5),
    NINGYU_CENTER_SHOP("宁宇管理员", 6),
    NINGYU_SHOP("宁宇分店", 7),
    HARDWARE_SHOP("五金店", 8),
    SCHOOL_SHOP("学校类型", 9),
    GOVERNMENT_SHOP("机关食堂", 10),
    OIL_SHOP("加油站", 11),
    RESTAURANT_SHOP("餐饮店", 12),
    GROUP_SHOP("拼团店", 13)

    ;
    private String label;
    private Integer value;

    ShopTypeEnum(String label, Integer value) {
        this.label = label;
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据value值获取枚举类
     * @param value
     * @return
     */
    public static ShopTypeEnum getEnumByValue(Integer value){
        ShopTypeEnum[] list =ShopTypeEnum.values();

        for(ShopTypeEnum typeEnum : list){
            if(ObjectUtil.equals(typeEnum.getValue(), value)){
                return typeEnum;
            }
        }

        return null;
    }
}
