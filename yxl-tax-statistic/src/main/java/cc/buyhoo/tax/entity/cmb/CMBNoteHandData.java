package cc.buyhoo.tax.entity.cmb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CMBNoteHandData implements Serializable {
    //取值
    private Integer goodsCount;
    //随机数范围最小值
    private BigDecimal min;
    //随机数范围最大值
    private BigDecimal max;
}
