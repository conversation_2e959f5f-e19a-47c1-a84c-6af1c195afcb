package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "bus_invoice_staff")
public class BusInvoiceStaffEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * sys_company.id
     */
    private Long companyId;

    /**
     * 员工名称
     */
    private String staffName;

    /**
     * 全电4期登录账户
     */
    private String staffAccount;

    /**
     * 全电4期密码
     */
    private String staffPwd;

    /**
     * 责任人类型：01、法人；02、财务负责人；03、办税员；05、管理员；08、社保经办人；09、开票员；10、销售人员
     */
    private String dutyType;

    /**
     * 1、已认证成功；0、未认证；
     */
    private Integer authStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 1、可用；0、不可用
     */
    private Integer validType;

    /**
     * 最后一次更新时间
     */
    private Date modifyTime;
    /**
     * 1、已登录；0、未登录
     */
    private Integer isLogin;
    /**
     * 最后一次登录时间
     */
    private String lastLoginTime;
    /**
     * 认证ID，申请认证信息时，需要记录，后期查询成功时，根据ID修改认证状态
     */
    private String rzId;
}
