package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 入库单批次
*/
@Data
@TableName(value = "bus_inventory_batch")
public class BusInventoryBatchEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 店铺编号
    */
    private Long shopUnique;
    /**
    * 批次单号
    */
    private String batchNo;
    /**
    * 总金额
    */
    private BigDecimal totalMoney;
    /**
     * 成本金额
     */
    private BigDecimal costMoney;
    /**
     * 利润金额
     */
    private BigDecimal profitMoney;
    /**
    * 入库单ID
    */
    private Long orderId;
    /**
    * 实际订单时间
    */
    private String batchDate;

    /**
     * 入库单类型:1收银订单同步2餐饮订单同步(仅统计数据使用)3手动录入
     */
    private Integer inventoryType;

}