package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@TableName("bus_shop_invoice_detail")
@Data
public class BusShopInvoiceDetailEntity extends BaseEntity {
    /**
     * bus_shop_invoice.id
     */
    private Long invoiceId;

    /**
     * 商品税收分类编码，由税务机构提供
     */
    private String taxClassificationCode;

    /**
     * 销售商品名称
     */
    private String goodsName;

    /**
     * 商品税目，税种具体化，决定税率
     */
    private String goodsSm;

    /**
     * 商品规格型号（选填）
     */
    private String spec;

    /**
     * 商品计量单位（选填）
     */
    private String unit;

    /**
     * 销售单价（含税）
     */
    private BigDecimal price;

    /**
     * 销售数量，整数
     */
    private BigDecimal quantity;

    /**
     * 销售小计（不能为空）
     */
    private BigDecimal amount;

    /**
     * 税率：0.06代表6个点的税率
     */
    private BigDecimal taxRate;

    /**
     * 税额，由单个商品税额*销量计算得出，不能为空
     */
    private BigDecimal taxAmount;

    /**
     * 商品税收分类名称
     */
    private String taxClassificationName;
}
