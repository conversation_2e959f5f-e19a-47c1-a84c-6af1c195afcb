package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 店铺服务费
*/
@Data
@TableName(value = "bus_shop_service_fee")
public class BusShopServiceFeeEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 店铺编号
    */
    private Long shopUnique;
    /**
    * 服务费
    */
    private BigDecimal serviceFee;

}