package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 退款单-商品分类统计
*/
@Data
@TableName(value = "bus_return_order_category")
public class BusReturnOrderCategoryEntity extends BaseEntity {

    /**
    * 退货单id
    */
    private Long returnOrderId;
    /**
    * 一级分类
    */
    private Long categoryId;
    /**
    * 二级分类
    */
    private Long categoryTwoId;
    /**
    * 批次数量总和
    */
    private BigDecimal totalGoodsCount;
    /**
    * 商品金额
    */
    private BigDecimal totalMoney;
    /**
    * 公司编号
    */
    private Long companyId;

}