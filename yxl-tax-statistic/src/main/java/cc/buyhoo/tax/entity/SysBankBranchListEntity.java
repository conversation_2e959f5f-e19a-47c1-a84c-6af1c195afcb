package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("sys_bank_branch_list")
@EqualsAndHashCode(callSuper=false)
public class SysBankBranchListEntity extends BaseEntity {
    /**
     * 所属银行ID
     */
    private Long bankId;
    /**
     * 支行在银行中的编号
     */
    private String branchId;
    /**
     * 支行名称
     */
    private String branchName;
    /**
     * 删除标记：1、未删除；0、已删除
     */
    private Integer delFlag;
}
