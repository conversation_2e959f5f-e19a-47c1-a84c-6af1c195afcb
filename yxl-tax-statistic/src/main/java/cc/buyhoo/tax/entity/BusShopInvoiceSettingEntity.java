package cc.buyhoo.tax.entity;

import cc.buyhoo.tax.enums.PeriodNumberEnum;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
* 企业开票信息
*/
@Data
@TableName(value = "bus_shop_invoice_setting")
public class BusShopInvoiceSettingEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 开票企业
    */
    private String companyName;
    /**
    * 开票企业税号
    */
    private String companyTaxNo;
    /**
    * 开票企业地址
    */
    private String companyAddress;
    /**
    * 开票企业电话
    */
    private String companyPhone;
    /**
    * 开票人
    */
    private String invoiceMan;
    /**
    * 复核人
    */
    private String checker;
    /**
    * 收款人
    */
    private String payee;
    /**
    * 开票企业银行
    */
    private String invoiceBankName;
    /**
    * 开票企业银行账号
    */
    private String invoiceBankCard;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;
    /**
     * 期数
     */
    private Integer periodsLevel = PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel();

}