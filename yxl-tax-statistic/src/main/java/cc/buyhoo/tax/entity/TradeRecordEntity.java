package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("trade_record")
public class TradeRecordEntity extends BaseEntity {
    //企业ID
    private Long companyId;
    //企业银行账户名称
    private String companyBankName;
    //企业银行账户账号
    private String companyBankAccount;
    //交易日期
    private String tradeDate;
    //交易时间
    private String tradeTime;
    //交易金额
    private BigDecimal tradeAmount;
    //交易类型：1、入账；2、出账；
    private Integer tradeWay;
    //交易摘要
    private String tradeAbstract;
    //交易另一方账户
    private String tradeOtherAccount;
    //交易另一方账户名称
    private String tradeOtherName;
    //交易后账户余额
    private BigDecimal companyBalance;
    //银行交易流水号
    private String serialNumber;
    //交易类型，由于不同银行交易类型不一致，此处直接记录银行交易原文内容
    private String tradeType;
    //交易借贷标记
    private Integer creditMark;
    //业务参考号，银行内部业务编号
    private String businessNum;
    //交易另一方所属银行内部支行号
    private String bankBranchId;
    //交易另一方所在银行的行号
    private String fromerBankCode;
    //交易另一方所在银行的银行名称
    private String fromerBankName;
    //交易对方所在银行的银行地址
    private String fromerBankAddress;
    //创建人
    private String createUser;
    //最后修改人
    private String modifyUser;
}
