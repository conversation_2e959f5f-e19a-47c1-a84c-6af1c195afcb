package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper=false)
@Data
@TableName("sys_company_bank_account")
public class SysCompanyBankAccountEntity extends BaseEntity {
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 是否默认账户：1、默认；0、非默认
     */
    private Integer isDefault;
    /**
     * 所属银行ID，银行信息见bank_list表
     */
    private Integer bankId;
    /**
     * 银行账号
     */
    private String bankCard;
    /**
     * 银行账户名称，企业名称（公户），人名（私户）
     */
    private String bankName;
    /**
     * 联行号
     */
    private String bankLhh;
    /**
     * 所在银行的支行号，见bank_branch_list表
     */
    private String bankBranch;
    /**
     * 银行分配的对称秘钥
     */
    private String symmetricKey;
    /**
     * 银行分配的私钥
     */
    private String privateKey;
    /**
     * 银行分配的公钥
     */
    private String publicKey;
    /**
     * 银行分配的员工号
     */
    private String uid;
    /**
     * 银行内部的请求路径url
     */
    private String url;
    /**
     * 银行分配的业务ID，私户不应该出现在这里
     */
    private String busmod;
    /**
     * 转账方式：1、转账；2、代发工资；
     */
    private Integer billType;

}
