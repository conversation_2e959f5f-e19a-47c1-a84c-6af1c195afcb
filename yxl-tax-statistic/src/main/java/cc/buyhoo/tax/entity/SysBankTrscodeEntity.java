package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("sys_bank_trscode_list")
@EqualsAndHashCode(callSuper = true)
public class SysBankTrscodeEntity extends BaseEntity {
    //交易类型：1、进账；2、出账
    private Integer transactionType;
    //银行类型，见表sys_bank_list
    private Long bankId;
    //银行内部交易类型码
    private String bankTrscode;
    //是否生成交易订单（sale_list）：1、是；0、否
    private Integer isGenerateSaleOrder;
    //是否生成进货订单（bus_inventory_batch）：1、是；0、否
    private Integer isGenerateInventoryOrder;
    //针对次交易码的说明信息
    private String remark;
}
