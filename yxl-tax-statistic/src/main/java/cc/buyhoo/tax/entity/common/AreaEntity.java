package cc.buyhoo.tax.entity.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaEntity {
    //区域名称
    private String name;
    //区域值
    private Long code;
    //区域登记,1、全国；2、省市自治区；3、地级市；4、区县；5、城镇
    private Integer level;
    //子区域列表
    private List<AreaEntity> children;
}
