package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
* 系统用户表
*/
@Data
@TableName(value = "sys_user")
public class SysUserEntity extends BaseEntity {

    /**
    * sys_company.id
    */
    private Long companyId;
    /**
    * 用户名
    */
    private String username;
    /**
    * 密码
    */
    private String password;
    /**
     * 用户类型：1超级管理员2普通管理员
     */
    private Integer userType;
    /**
    * 手机号
    */
    private String mobile;
    /**
    * 邮箱
    */
    private String email;
    /**
    * 用户头像
    */
    private String avatar;
    /**
    * 有效状态:1正常0无效
    */
    private Integer enableStatus;
    /**
    * 删除标记:0未删除1已删除
    */
    private Integer delFlag;
    /**
    * 备注
    */
    private String remark;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}