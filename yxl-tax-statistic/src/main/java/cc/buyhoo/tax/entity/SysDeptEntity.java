package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
* 部门
*/
@Data
@TableName(value = "sys_dept")
public class SysDeptEntity extends BaseEntity {

    /**
    * sys_company.id
    */
    private Long companyId;
    /**
    * 父部门id
    */
    private Long parentId;
    /**
    * 部门名称
    */
    private String deptName;
    /**
    * 排序
    */
    private Integer sort;
    /**
    * 负责人
    */
    private String leader;
    /**
    * 邮箱
    */
    private String email;
    /**
    * 手机号
    */
    private String mobile;
    /**
    * 删除标记:0未删除1已删除
    */
    private Integer delFlag;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}