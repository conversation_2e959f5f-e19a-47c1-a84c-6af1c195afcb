package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;

/**
 * 企业利润规则
 * <AUTHOR> 
 * @ClassName BusCompanyProfitRuleEntity
 * @Date 2024-06-25
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_company_profit_rule")
public class BusCompanyProfitRuleEntity extends BaseEntity {

	/**
	 * sys_company.id
	 */
	private Long companyId;

	/**
	 * 规则类型：1-按订单支付比例，2-按金额数量
	 */
	private Integer ruleType;

	/**
	 * 按订单比例方式，金额
	 */
	private BigDecimal amount;

	/**
	 * 按订单：小于等于金额，利润百分比
	 */
	private BigDecimal firstProfitRate;

	/**
	 * 按订单：大于金额，利润百分比
	 */
	private BigDecimal secondProfitRate;

	/**
	 * 按订单：每月最大利润百分比
	 */
	private BigDecimal monthProfitRate;

	/**
	 * 按金额数量：固定利润百分比
	 */
	private BigDecimal profitRate;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 有效状态:1正常0无效
	 */
	private Integer enableStatus;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;

}