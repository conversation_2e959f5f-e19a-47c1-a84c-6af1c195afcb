package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 入库单批次
*/
@Data
@TableName(value = "bus_return_batch")
public class BusReturnBatchEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 店铺编号
    */
    private Long shopUnique;
    /**
    * 批次单号
    */
    private String batchNo;
    /**
    * 总金额
    */
    private BigDecimal totalMoney;
    /**
    * 在线支付金额
    */
    private BigDecimal onlineMoney;
    /**
    * 现金支付金额
    */
    private BigDecimal cashMoney;
    /**
    * 入库单ID
    */
    private Long orderId;
    /**
    * 订单实际时间
    */
    private String batchDate;

}