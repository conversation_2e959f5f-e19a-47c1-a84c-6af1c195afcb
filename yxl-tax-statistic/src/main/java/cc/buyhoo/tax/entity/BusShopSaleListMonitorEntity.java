package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

/**
* 订单监控表
*/
@Data
@TableName(value = "bus_shop_sale_list_monitor")
public class BusShopSaleListMonitorEntity extends BaseEntity {

    /**
    * 企业ID
    */
    private Long companyId;
    /**
    * 供货商唯一ID
    */
    private Long shopUnique;
    /**
    * 统计日期
    */
    private String statDate;
    /**
    * 零售订单数
    */
    private Long buyhooSaleCount;
    /**
    * 餐饮订单数
    */
    private Long canyinSaleCount;
    /**
    * 纳统订单数
    */
    private Long taxSaleCount;
    /**
     * 零售订单数
     */
    private Long buyhooReturnCount;
    /**
     * 餐饮订单数
     */
    private Long canyinReturnCount;
    /**
     * 纳统订单数
     */
    private Long taxReturnCount;
    /**
    * 零售结算金额
    */
    private BigDecimal buyhooAmount;
    /**
    * 餐饮结算金额
    */
    private BigDecimal canyinAmount;
    /**
    * 纳统结算金额
    */
    private BigDecimal taxAmount;
    /**
    * 打款状态，0-待结算；1-结算中；2-结算成功；3-结算失败
    */
    private Integer settledStatus;
    /**
    * 备注
    */
    private String remark;
    /**
    * 打款编号
    */
    private String billNo;
    /**
    * 删除标记:0未删除1已删除
    */
    private Integer delFlag;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}