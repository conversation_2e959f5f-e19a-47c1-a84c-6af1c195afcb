package cc.buyhoo.tax.entity.cmb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.lang.reflect.Field;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CMBNote implements Serializable {

    private static final long serialVersionUID = 1L;
    //签名时间，格式为年月日时分秒
    private String sigtim;
    //签名内容
    private String sigdat;
    //通知内容
    private String notdat;
    //通知键值
    private String notkey;
    //用户编号
    private String usrnbr;
    //通知编号
    private String notnbr;
    //通知类型
    private String nottyp;


    /**
     * 根据字段值，获取其对应的值
     * @param param
     * @return
     */
    public Object getFiled(String param) {
        try {
            Field field = this.getClass().getDeclaredField(param);
            field.setAccessible(true);
            return field.get(this);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
