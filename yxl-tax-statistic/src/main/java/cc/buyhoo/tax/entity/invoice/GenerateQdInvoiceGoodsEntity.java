package cc.buyhoo.tax.entity.invoice;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class GenerateQdInvoiceGoodsEntity {
    //行号（必选）
    private String rowNumbe;
    //原商品行号（冲红用，可选）
    private String originalRowNumber;
    //行类型：0、正常行；1、折扣行；2、被折扣行
    private String rowKind;
    //商品名称（必选）
    private String goodsName;
    //规格型号（可选）
    private String standard;
    //包装单位
    private String unit;
    //数量（必选）
    private String number;
    //单价，单位元,最多8位小数（必选）
    private String price;
    //金额（单价×数量），单位元，必选保持两位小数（必选）
    private String amount;
    //含税标识，1、含税；0、不含税（必选）
    private String priceKind;
    //税率，必选保持两位小数（必选）：示例：0.06
    private String taxRate;
    //税额，单位元，必选保持两位小数（必选）
    private String taxAmount;
    //税收分类编码，(可选）
    private String goodsTaxNo;
    //优惠政策标识，0、不使用；1、使用；（必选）
    private String taxPre;
    //享受优惠政策内容，使用优惠政策必传
    private String taxPreCon;
    //零税率标识，空：非零税率；1、免税；2、不征税；3、普通零税率
    private String zeroTax;
    //企业自编码（可选）
    private String cropGoodsNo;
    //批准文号（可选）
    private String approvalNo;
    //批号（可选）
    private String batchCode;
    //生产厂家（可选）
    private String manufacturer;
    //商品id（可选）
    private String goodsId;
}
