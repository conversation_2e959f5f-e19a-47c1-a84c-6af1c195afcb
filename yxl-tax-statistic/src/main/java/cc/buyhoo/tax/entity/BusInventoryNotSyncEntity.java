package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
* 未同步的订单数据
*/
@Data
@TableName(value = "bus_inventory_not_sync")
public class BusInventoryNotSyncEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 同步类型:1商品未同步2库存未同步
    */
    private Integer syncType;
    /**
    * 销售单唯一标识
    */
    private String saleListUnique;
    /**
    * 百货详情id
    */
    private Long saleListDetailId;
    /**
    * 商品编号
    */
    private Long goodsId;
    /**
    * 库存同步状态:1待同步2已同步
    */
    private Integer status;

}