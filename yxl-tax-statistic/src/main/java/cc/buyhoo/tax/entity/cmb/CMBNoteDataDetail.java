package cc.buyhoo.tax.entity.cmb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CMBNoteDataDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    //账号
    private String accnbr;
    //户名
    private String accnam;
    //交易日期
    private String trsdat;
    //交易时间
    private String trstim;
    //币种
    private String c_ccynbr;
    //交易金额
    private BigDecimal c_trsamt;
    //账号（收款时，未付款方账号，付款时，为收款方账号）
    private String rpyacc;
    //户名
    private String rpynam;
    //余额
    private BigDecimal blvamt;
    //流水号
    private String refnbr;
    //起息日
    private String vltdat;
    //交易类型
    private String trscod;
    //摘要
    private String naryur;
    //借贷标记：C（贷）；D（借）
    private String amtcdr;
    //流程示例号
    private String reqnbr;
    //业务名称
    private String busnam;
    //用途
    private String nusage;
    //银行分行号
    private String rpybbk;
    //交易方开户行行号
    private String rpybbn;
    //交易方开户行名称
    private String rpybnk;
    //分行号
    private String gsbbbk;
    //交易方地址
    private String rpyadr;
    //业务参考号
    private String yurref;
    //企业识别码
    private String frmcod;

}
