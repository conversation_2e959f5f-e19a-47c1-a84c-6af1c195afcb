package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 *民生银行转账记录
 * <AUTHOR>
 * @ClassName BusShop
 * @Date 2023-07-26
 **/

@Data
@TableName("cmbc_tranfer_record")
public class CmbcTranferRecordEntity extends BaseEntity {


	/**
	 * 客户端交易的唯一标志
	 */
	private String trnId;
	/**
	 * 客户业务流水号，代表每一次业务请求的唯一 用于查询转账信息
	 */
	private String insId;

	/**
	 * 状态 0已发送转账请求待验证 1转账成功 2转账请求失败 3 转账失败
	 */
	private Integer status;

	/**
	 * 转账结果
	 */
	private String result;
/**
	 *是否批量 0单条 1 批量
	 */
	private Integer multipleFlg;
	/**
	 *转账金额 单笔转账才有值
	 */
	private BigDecimal tranferAmount;

	/**
	 *企业id
	 */
	private Long companyId;
	/**
	 * 付款账号账单唯一标志
	 */
	private String yurRef;


}