package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

/**
 * <AUTHOR> 
 * @Description 城市表
 * @ClassName CityInfo
 * @Date 2024-08-23
 **/

@Data
@TableName("sys_city_info")
public class SysCityInfoEntity {
	/**
	* ID
	*/
	@TableId
	private Long id;

	/**
	* PID
	*/
	private Long pid;

	/**
	* 级别（1：省份；2：城市；3：区县；4：街道；）
	*/
	private Integer grade;

	/**
	* 名称
	*/
	private String name;

	/**
	* 缩写（如：名称是北京市，缩写是北京）
	*/
	private String abbr;

	/**
	* 首字母
	*/
	private String initial;

	/**
	* 全称（如：河北省石家庄市新华区）
	*/
	private String full;

	/**
	* 热门（0：否；1：是；）
	*/
	private Integer hot;

}