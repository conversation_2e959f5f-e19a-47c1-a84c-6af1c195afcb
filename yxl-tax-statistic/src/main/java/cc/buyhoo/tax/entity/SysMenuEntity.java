package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
* 系统菜单
*/
@Data
@TableName(value = "sys_menu")
public class SysMenuEntity extends BaseEntity {

    private static final long serialVersionUID = 7602249128985769618L;
    /**
    * 父id
    */
    private Long parentId;
    /**
    * 菜单名称
    */
    private String menuName;
    /**
    * 菜单权限
    */
    private String permission;
    /**
    * 菜单类型:1目录2菜单3按钮
    */
    private Integer type;
    /**
    * 路由地址
    */
    private String path;
    /**
    * 组件地址
    */
    private String component;
    /**
    * 组件名称
    */
    private String componentName;
    /**
    * 排序
    */
    private Integer sort;
    /**
    * 组件图标
    */
    private String icon;
    /**
    * 1隐藏2显示
    */
    private Integer hidden;
    /**
     * 菜单级别:0-普通,1-平台,2-内置
     */
    private Integer menuLevel;
    /**
     * 是否系统内置：0-否，1-是
     */
    private Integer builtInSystem;
    /**
     * 创建人
     */
    private Long createUser;
    /**
     * 修改人
     */
    private Long modifyUser;

    /**
     * 删除标记:0未删除1已删除
     */
    private Integer delFlag;

}