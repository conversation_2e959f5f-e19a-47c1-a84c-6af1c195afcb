package cc.buyhoo.tax.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.math.BigDecimal;

import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
 * 供货商账单明细表
 * <AUTHOR>
 * @ClassName BusShopBillDetail
 * @Date 2023-07-27
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_shop_bill_detail")
public class BusShopBillDetailEntity extends BaseEntity {

	/**
	* 供应商账单表ID
	*/
	private Long billId;

	/**
	* 企业唯一ID
	*/
	private Long companyId;

	/**
	* 供货商唯一ID
	*/
	private Long shopUnique;

	/**
	* 打款单号
	*/
	private String billNo;

	/**
	* 银行名称
	*/
	private String bankName;

	/**
	* 银行卡号
	*/
	private String bankCard;

	/**
	* 结算金额
	*/
	private BigDecimal settledAmount;

	/**
	* 结算方式，0-手动结算，1-自动结算
	*/
	private String settledType;

	/**
	 * 打款状态，YTJ(自定义状态:已提交)、AUT(等待审批)、NTE(终审完毕)、BNK(银行处理中)、FINS(银行支付成功)、FINF(银行支付失败)、FINB(银行支付被退票)、
	 * FINR(企业审批否决)、FIND(企业过期不审批)、FINC(企业撤销)、FINU(银行挂账)、OPR(数据接收中)、APW(银行人工审批)、
	 * WRF(属于银行处理中状态，可疑，表示状态未知，需要人工介入处理)
	*/
	private String settledStatus;

	/**
	 * 转账失败原因
	 */
	private String failReason;

	/**
	* 进货单号
	*/
	private String purchaseNo;

	/**
	* 企业开户行
	*/
	private String companyBankName;

	/**
	* 企业对公账户
	*/
	private String companyBankCard;

	/**
	* 备注
	*/
	private String remark;

	/**
	 * 招行打款批次编号
	 */
	private String bthNbr;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;
	/**
	 * 操作类型：1、转账；2、代扣
	 */
	private Integer billType;

}