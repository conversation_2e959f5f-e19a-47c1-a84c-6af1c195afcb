package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper=false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_company_config")
public class SysCompanyConfigEntity extends BaseEntity {
    public final static Long serialVersionUID = 1L;
    //企业ID
    private Long companyId;
    //创建人ID
    private Long createUser;
    //修改人ID
    private Long modifyUser;
    //是否开启拆解功能:1、开启；0、关闭，如果未设置，默认关闭
    private Integer disassembleStatus;
    //拆解订单设置的最小订单金额
    private BigDecimal disassembleMinMoney;
    //拆解订单设置的最大订单金额
    private BigDecimal disassembleMaxMoney;
    //拆解订单的起拆金额
    private BigDecimal disassembleStartMoney;
}

