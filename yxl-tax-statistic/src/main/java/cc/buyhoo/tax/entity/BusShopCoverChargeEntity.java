package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

/**
* 店铺日服务费
*/
@Data
@TableName(value = "bus_shop_cover_charge")
public class BusShopCoverChargeEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 店铺编号
    */
    private Long shopUnique;
    /**
    * 统计日期
    */
    private String statDate;
    /**
    * 服务费
    */
    private BigDecimal coverCharge;

}