package cc.buyhoo.tax.entity.cmb;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName TrsInfo
 * <AUTHOR>
 * @Date 2024/6/20 17:23
 */
@Data
public class TrsInfoEntity implements Serializable {
    private String reqNbr; //流程实例号
    private String busCod; //业务编码
    private String busMod; //业务模式
    private String dbtBbk; //转出分行号
    private String dbtAcc; //付方帐号
    private String dmaNbr; //付方记账子单元编号
    private String dbtNam; //付方帐户名
    private String crtBbk; //收方分行号
    private String crtAcc; //收方帐号
    private String crtNam; //收方名称
    private String crtBnk; //收方行名称
    private String crtAdr; //收方行地址
    private String ccyNbr; //币种
    private String trsAmt; //交易金额
    private String eptDat; //期望日
    private String eptTim; //期望时间
    private String bnkFlg; //系统内外标志
    private String stlChn; //结算通路
    private String nusAge; //用途
    private String ntfCh1; //通知方式一
    private String ntfCh2; //通知方式二
    private String oprDat; //经办日期
    private String yurRef; //参考号
    private String busNar; //业务摘要
    private String reqSts; //请求状态
    private String rtnFlg; //业务处理结果
    private String oprSqn; //待处理操作序列
    private String oprAls; //操作别名
    private String lgnNam; //用户名
    private String usrNam; //用户姓名
    private String rtnNar; //失败原因
    private String athFlg; //是否有附件信息
    private String rcvBrd; //收方大额行号
    private String trsTyp; //业务种类
    private String trxSet; //账务套号
    private String trxSeq; //账务流水
}
