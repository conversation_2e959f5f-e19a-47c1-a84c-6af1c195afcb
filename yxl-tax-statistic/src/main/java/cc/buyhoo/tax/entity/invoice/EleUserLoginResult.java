package cc.buyhoo.tax.entity.invoice;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class EleUserLoginResult {
    //状态码
    private Integer code;
    //状态信息
    private String msg;
    //返回数据
    private EleUserLoginData data;

    EleUserLoginResult(){};
    EleUserLoginResult(Integer code,String msg){
        this.code = code;
        this.msg = msg;
    }

    /**
     * 成功，返回数据
     * @param data
     * @return
     */
    public static EleUserLoginResult success(EleUserLoginData data){
        EleUserLoginResult ele = new EleUserLoginResult(1,"请求成功");
        ele.setData(data);
        return ele;
    }

    /**
     * 失败，返回失败原因
     * @param msg
     * @return
     */
    public static EleUserLoginResult fail(String msg){
        EleUserLoginResult ele = new EleUserLoginResult(0,msg);
        return ele;
    }
}
