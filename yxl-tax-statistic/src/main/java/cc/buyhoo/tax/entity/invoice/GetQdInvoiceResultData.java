package cc.buyhoo.tax.entity.invoice;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class GetQdInvoiceResultData {
    //订单流水号
    private String billNumber;
    //订单状态
    private String statusCode;
    //订单状态描述
    private String statusMessage;
    //订单金额
    private String amount;
    //订单税额
    private String taxAmount;
    //开票日期
    private String date;
    //发票校验码
    private String checkCode;
    //发票密文
    private String antiCipher;
    //机器编号
    private String deviceNo;
    //发票代码
    private String invoiceCode;
    //发票号码
    private String invoiceNumber;
    //开票类型
    private String invoiceType;
    //销售方地址
    private String selesAddress;
    //购货方名称
    private String clientName;
    //购货方税号
    private String clientTaxCode;
    //购货方银行名称
    private String clientBankName;
    //购货方银行账号
    private String clientBankAccountNumber;
    //购货方地址
    private String clientAddress;
    //销售方名称
    private String sellerName;
    //销售方税号
    private String sellerTaxCode;
    //销售方银行名称
    private String sellerBankName;
    //销售方银行账号
    private String sellerBankAccountNumber;
    //销售方电话
    private String sellerPhone;
    //编码表版本号
    private String codeVersion;
    //税率
    private String taxRate;
    //备注
    private String notes;
    //开票人
    private String invoicer;
    //复核人
    private String checker;
    //收款人
    private String cashier;
    //清单标识
    private String goodsListFlag;
    //发票类型
    private String invoiceKind;
    //原发票代码（红字用）
    private String originalInvoiceCode;
    //原发票号码（红字用）
    private String originalInvoiceNumber;
    //原发票日期（红字用）
    private String originalInvoiceDate;
    //原发票类型（红字用）
    private String originalInvoiceKind;
    //红冲原因
    private String flushRedtReason;
    //特殊票种标识
    private String specialInvoiceType;
    //价税合计
    private String totalAmount;
    //base64文件流
    private String fileContent;
    //文件类型
    private String fileType;
    //商品明细
    private List<GetQdInvoiceGoodsResult> invoiceDetails;
}
