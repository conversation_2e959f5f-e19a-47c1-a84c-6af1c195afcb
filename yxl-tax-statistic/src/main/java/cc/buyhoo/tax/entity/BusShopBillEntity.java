package cc.buyhoo.tax.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.math.BigDecimal;

import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
 * <AUTHOR> 
 * @Description 供货商账单表
 * @ClassName BusShopBill
 * @Date 2023-07-27
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_shop_bill")
public class BusShopBillEntity extends BaseEntity {

	/**
	 * 公司编号
	 */
	private Long companyId;

	/**
	* 商家唯一标识
	*/
	private Long shopUnique;

	/**
	* 已结算金额
	*/
	private BigDecimal settledAmount;

	/**
	* 未结算金额
	*/
	private BigDecimal unsettledAmount;

	/**
	 * 结算中金额
	 */
	private BigDecimal settledingAmount;

	/**
	* 结算方式，0-手动结算，1-自动结算
	*/
	private String settledType;

	/**
	* 备注
	*/
	private String remark;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;


}