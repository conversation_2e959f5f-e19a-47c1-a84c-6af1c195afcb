package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "disassemble_list")
public class DisassembleListEntity extends BaseEntity {

    //企业ID
    private Long companyId;
    //创建人
    private Long createUser;
    //修改人
    private Long modifyUser;
    //拆解前原订单号
    private String saleListUnique;
    //拆解出来的订单数量
    private Integer disassembleCount;
    //拆解订单的进度
    private Integer disassembleStatus;
    //拆解订单，设置的最小拆单金额
    private BigDecimal minAmountSet;
    //拆解订单，设置的最大拆单金额
    private BigDecimal maxAmountSet;
    //实际的拆单最小金额
    private BigDecimal minAmountActual;
    //实际的拆单最大金额
    private BigDecimal maxAmountActual;
    //备注信息
    private String disassmebleRemarks;
    //订单总金额
    private BigDecimal orderAmount;
}
