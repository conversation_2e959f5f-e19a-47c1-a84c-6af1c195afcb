package cc.buyhoo.tax.entity.invoice;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import cc.buyhoo.tax.util.MD5Util;

@Setter
@Getter
@ToString
public class RequestParams {
    //调用企业方税号
    private String nsrsbh;
    //分配企业的apiKey
    private String apiKey;
    //业务参数，base64(json字符串)
    private String requestData;
    //签名
    private String sign;

    public String getPublicSign(String data) {
        String newSign = MD5Util.MD5Encode(data, "UTF-8");
        return newSign;
    }
}
