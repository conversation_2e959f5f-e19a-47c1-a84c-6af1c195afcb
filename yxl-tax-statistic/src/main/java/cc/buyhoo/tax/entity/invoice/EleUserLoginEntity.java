package cc.buyhoo.tax.entity.invoice;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EleUserLoginEntity {
    //全电账号
    private String electricAccount;
    //全电密码
    private String electricPassword;
    //销售方税号
    private String nsrsbh;
    //调用的方法，参见MethodNameEnum：1、首次登陆；2、获取验证码；3、选择责任人；4、短信登录
    private String methodName;
    //责任人类型：参见DutyTypeEnum：01、法人；02、财务；03、办税员；05、管理员；08、社保负责人；09：开票员；10、销售
    private String dutyType;
    //短信验证码
    private String smsCode;
}
