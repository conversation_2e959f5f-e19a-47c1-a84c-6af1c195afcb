package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
* 系统角色
*/
@Data
@TableName(value = "sys_role")
public class SysRoleEntity extends BaseEntity {

    /**
    * sys_company.id
    */
    private Long companyId;
    /**
    * 角色类型:1超级管理员2普通管理员
    */
    private Integer roleType;
    /**
    * 角色名称
    */
    private String roleName;
    /**
    * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
    */
    private Integer dataScope;
    /**
    * 数据范围(指定部门数组)
    */
    private String dataScopeDeptIds;
    /**
    * 备注
    */
    private String remark;
    /**
     * 删除标记:0未删除1已删除
     */
    private Integer delFlag;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}