package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * 订单同步记录
 * <AUTHOR> 
 * @ClassName BusSyncRecordEntity
 * @Date 2024-06-13
 **/

@Data
@TableName("bus_sync_record")
public class BusSyncRecordEntity extends BaseEntity {

	/**
	* sys_company.id
	*/
	private Long companyId;

	/**
	* 同步类型，0-订单
	*/
	private Integer syncType;

	/**
	* 开始时间
	*/
	private Date startTime;

	/**
	* 结束时间
	*/
	private Date endTime;

	/**
	* 同步数量
	*/
	private Long saleListCount;

	/**
	* 同步状态，0-成功，1-失败
	*/
	private Integer status;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;

}