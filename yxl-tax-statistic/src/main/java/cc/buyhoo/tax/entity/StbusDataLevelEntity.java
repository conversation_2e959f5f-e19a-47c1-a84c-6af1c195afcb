package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* 
*/
@Data
@TableName(value = "stbus_data_level")
public class StbusDataLevelEntity extends BaseEntity {

    /**
    * 维度（1-全国、2-省、3-市、4-区县、5-市场、6-企业）
    */
    private Integer level;
    /**
    * 名称
    */
    private String name;
    /**
    * 上级维度
    */
    private Long parentId;
    /**
    * 删除标记:1未删除2已删除
    */
    private Integer delFlag;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 修改人
    */
    private Long modifyBy;

}