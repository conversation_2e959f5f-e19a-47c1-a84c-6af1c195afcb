package cc.buyhoo.tax.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.math.BigDecimal;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
 * 商品管理表
 * <AUTHOR> 
 * @ClassName BusGoods
 * @Date 2023-07-29
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_goods")
public class BusGoodsEntity extends BaseEntity {

	/**
	 * 百货端商品ID
	 */
	private Long goodsId;

	/**
	* 供应商编码
	*/
	private Long shopUnique;

	/**
	* 商品编码
	*/
	private String goodsBarcode;

	/**
	* 商品名称
	*/
	private String goodsName;

	/**
	* 商品别名
	*/
	private String goodsAlias;

	/**
	* 商品进价
	*/
	private BigDecimal goodsInPrice;

	/**
	* 商品售价
	*/
	private BigDecimal goodsSalePrice;

	/**
	* 商品线上售价
	*/
	private BigDecimal goodsWebSalePrice;

	/**
	* 商品保质天数
	*/
	private Integer goodsLife;

	/**
	* 商品规格
	*/
	private String goodsStandard;

	/**
	* 商品计价单位
	*/
	private String goodsUnit;

	/**
	* 一级分类ID
	*/
	private Long categoryId;

	/**
	* 二级分类ID
	*/
	private Long categoryTwoId;

	/**
	* 所属企业ID
	*/
	private Long companyId;

	/**
	* 备注
	*/
	private String remark;

	/**
	* 创建人ID
	*/
	private Long createUser;


	/**
	* 更新人ID
	*/
	private Long modifyUser;


	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

}