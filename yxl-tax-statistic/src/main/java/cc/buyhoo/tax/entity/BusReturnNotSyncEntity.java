package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

/**
* 未同步的订单数据
*/
@Data
@TableName(value = "bus_return_not_sync")
public class BusReturnNotSyncEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 同步类型:1商品未同步2库存未同步
    */
    private Integer syncType;
    /**
    * 退款单号
    */
    private String retListUnique;
    /**
    * 百货return_list_detail.id
    */
    private Integer retListDetailId;
    /**
    * 商品编号
    */
    private Long goodsId;
    /**
    * 库存同步状态:1待同步2已同步
    */
    private Integer status;

}