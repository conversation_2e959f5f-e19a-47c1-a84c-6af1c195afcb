package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
* 纳统申报记录
*/
@Data
@TableName(value = "bus_tax_record")
public class BusTaxRecordEntity extends BaseEntity {

    /**
    * 企业ID
    */
    private Long companyId;
    /**
     * 纳统类型,1-按月,2-按季度,2-按年
     */
    private String taxType;
    /**
    * 申报金额
    */
    private BigDecimal targetAmount;
    /**
    * 营业金额
    */
    private BigDecimal taxAmount;
    /**
    * 申报日期
    */
    private String taxDate;
    /**
    * 申报状态：0-未完成，1-已完成
    */
    private String taxStatus;
    /**
    * 备注
    */
    private String remark;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;
    /**
     * 删除标记:0未删除1已删除
     */
    private Integer delFlag;

}