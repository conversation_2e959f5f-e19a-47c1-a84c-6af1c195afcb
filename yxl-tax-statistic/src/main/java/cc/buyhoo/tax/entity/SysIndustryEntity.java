package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 
 * @Description 行业管理表
 * @ClassName SysIndustryEntity
 * @Date 2024-08-23
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("sys_industry")
public class SysIndustryEntity extends BaseEntity {

	/**
	* 行业名称
	*/
	private String industryName;

	/**
	* 备注
	*/
	private String remark;

	/**
	* 有效状态:1正常0无效
	*/
	private Integer enableStatus;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

}