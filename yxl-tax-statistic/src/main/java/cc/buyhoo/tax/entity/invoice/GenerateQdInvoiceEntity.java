package cc.buyhoo.tax.entity.invoice;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class GenerateQdInvoiceEntity {
    /*
    全电账号,针对发票平台开票企业维护了多个电子税局账号时，可以使用指定账号进行业务处理（必选）
     */
    private String electricAccount;
    //开票类型0:正数发票开具；1:负数发票开具(红冲)（必选）
    private String invoiceType;
    //农产品标识农产品销售发票，若赋值为“2”表示农产品收购发票，其 它表示增值税普通发票（可选）
    private String sellerAddress;
    //购买方名称（必选）
    private String clientName;
    //购买方税号（企业用户必须上传）
    private String clientTaxCode;
    //购买方银行名称（企业用户必须上传）
    private String clientBankName;
    //购买方银行账户（企业用户必须上传）
    private String clientBankAccountNumber;
    //购买方地址（企业用户必须上传）
    private String clientAddress;
    //购买方电话（可选）
    private String clientPhone;
    //销货方名称（必选）
    private String sellerName;
    //销货方税号（必选）
    private String sellerTaxCode;
    //销货方银行名称（必选）
    private String sellerBankName;
    //销货方银行账户（必选）
    private String sellerBankAccountNumber;
    //销货方电话（必选）
    private String sellerPhone;
    //销货方地址（必选）
    private String selesAddress;
    //编码版本（可选）
    private String codeVersion;
    //税率（可选）
    private String taxRate;
    //备注（可选）
    private String notes;
    //开票人（必选）
    private String invoicer;
    //复核人（必选）
    private String checker;
    //收款人（必选）
    private String cashier;
    //是否有清单：1、有清单；0、无清单
    private String goodsListFlag;
    //请求流水号
    private String billNumber;
    //发票种类：21、全电专票；22、全电普票
    private String invoiceKind;
    //原发票代码（红冲必填）
    private String originalInvoiceCode;
    //原发票号码（红冲必填）
    private String originalInvoiceNumber;
    //原开票时间（红冲必填）
    private String originalInvoiceDate;
    //原发票类型（红冲必填）
    private String originalInvoiceKind;
    //红冲原因（红冲必填）
    private String flushRedtReason;
    //红字确认单编号（红冲必填）
    private String redConfCode;
    //特殊标识（可选）
    private String specialInvoiceType;
    //订单金额（价税合计金额），保留两位小数，单位元，不能为0（必填）
    private String totalAmount;
    //不含税金额（合计金额），含税价开票时，填0，由发票系统做价税分离重新赋值，冲红时填负数（可选）
    private String amount;
    //税额,含税价开票时，填0，由发票系统做价税分离重新赋值，冲红时填负数（可选）
    private String taxAmount;
    //商品明细（必选）
    private List<GenerateQdInvoiceGoodsEntity> invoiceDetails;

}
