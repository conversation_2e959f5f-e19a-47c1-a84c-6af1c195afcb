package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
* 退货表，管理客户退货信息
*/
@Data
@TableName(value = "return_list")
public class BusReturnListEntity extends BaseEntity {

    /**
     * 公司编号
     */
    private Long companyId;
    /**
    * 销售订单编号
    */
    private String saleListUnique;
    /**
    * 退款的店铺编号
    */
    private Long shopUnique;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
    * 退货日期
    */
    private Date retListDatetime;
    /**
    * 收银退款状态:1-未退款，2-已退款
     * 餐饮退款状态：11待处理退款，12已同意退款，13已完成退款，14退款失败，15拒绝用户退款
    */
    private String retListState;
    /**
    * 退货申请受理状态：1未处理，2-已受理，3受理完毕，4、驳回
    */
    private String retListHandlestate;
    /**
    * 退款方式：1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡  6 易通（原路退回）;7、赊账（原订单赊账）
    */
    private Integer retMoneyType;
    /**
    * 实际退款金额
    */
    private BigDecimal retListTotalMoney;
    /**
    * 退款到帐时间
    */
    private Date retBackDatetime;
    /**
    * 退款申请单号
    */
    private String retListUnique;
    /**
    * 申请退款原因
    */
    private String retListReason;
    /**
    * 退还的配送费
    */
    private BigDecimal retListDelfee;
    /**
     * 退还的服务费
     */
    private BigDecimal returnSaleListServiceFee;
    /**
     * 结算状态
     */
    private Integer settledStatus;

}