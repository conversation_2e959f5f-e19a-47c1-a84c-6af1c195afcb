package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 退货单
*/
@Data
@TableName(value = "bus_return_order")
public class BusReturnOrderEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 退款单号
    */
    private String orderNo;
    /**
    * 退款金额
    */
    private BigDecimal totalMoney;
    /**
    * 在线支付退款
    */
    private BigDecimal onlineMoney;
    /**
    * 现金支付退款
    */
    private BigDecimal cashMoney;

}