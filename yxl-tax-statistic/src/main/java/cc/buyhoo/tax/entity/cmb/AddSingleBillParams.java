package cc.buyhoo.tax.entity.cmb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class AddSingleBillParams implements Serializable {
    //版本号，填写2.0（传）
    private String version = "2.0";
    //308开头商户号（传）
    private String merchId;
    //商户自定义订单号（传）
    private String orderId;
    //应缴金额，单位为元 ，精度为分（传）
    private BigDecimal feeAmt;
    //收款账户
    private String rcvAccNo;
    //客户邮箱
    private String custMail;
    //客户编号
    private String payNo;
    //客户名称
    private String custName;
    //客户手机号
    private String custPhone;
    //备注
    private String memo;
    //聚合支付收银员
    private String userId;
    //虚拟子账号
    private String vraccno;
    //商户保留域(此字段会上送聚合),回调通知中的note
    private String note;
    //商品描述信息
    private String body;
    //交易通分账平台号，分账时必填
    private String platformNo;
    //支付类型列表
    private List payTypeList;
    //支付完成跳转到商户方界面地址
    private String returnUrl;


}
