package cc.buyhoo.tax.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
* 
*/
@Data
@TableName(value = "sale_list")
public class BusSaleListEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 店铺编号
    */
    private Long shopUnique;
    /**
    * 店铺名称
    */
    private String shopName;
    /**
     * 收银订单类型（0，实体店销售；1，APP订单;2,微信商城小程序;3,网页订单;4,美团外卖订单 5,饿了么外卖\r\n\r\n订单,6移动端收银）7:收银端平台会员结算 8:积分兑换）
     * 餐饮订单状态(21堂食订单22打包订单23外卖订单)
     */
    private Integer saleType;
    /**
    * 订单编号
    */
    private String saleListUnique;
    /**
     * 消费者唯一编号
     */
    private String cusUnique;
    /**
    * 收货人姓名
    */
    private String saleListName;
    /**
    * 应收金额
    */
    private BigDecimal saleListTotal;
    /**
    * 实收金额
    */
    private BigDecimal saleListActuallyReceived;
    /**
     * 服务费
     */
    private BigDecimal saleListServiceFee;
    /**
    * 交易流水号
    */
    private String tradeNo;
    /**
    * 支付时间
    */
    private Date payTime;
    /**
     * 交易手续费
     */
    private BigDecimal payFee;
    /**
    * 销售单日期
    */
    private Date saleListDatetime;
    /**
     * 收银付款状态(1货到付款未付款，2网上订单未付款，3已付款 ,4赊账 ，5申请退款 ，6同意退款 7拒\r\n\r\n绝退款 8自助收银未付款)
     * 餐饮付款状态(21、未付款；22、已付款；23、支付中)
     */
    private Integer saleListState;
    /**
    * 收银支付方式：（1-现金，2-支付宝，3-微信，4-银行卡 ，5-储值卡 ,6-美团外卖,7-饿了么外卖，\r\n\r\n8-混合支付，9-免密支付,10-积分兑换 ,11-百货豆 12拉卡拉\r\n13 易通付款码支付   14金圈聚合码）
     * 餐饮支付方式(31现金，32支付宝，33微信，34银行卡，35储值卡，36美团，37饿了么，38混合支付，39金圈支付，40积分兑换，41百货豆，42金圈支付，43金圈支付，44金圈聚合码)
    */
    private Integer saleListPayment;

    /**
     * 计算结算状态：0-待计算，1-已计算
     */
    private Integer settledStatus;

    /**
     * 计算成本状态：0-待计算，1-已计算
     */
    private Integer profitStatus;

    /**
     * 计算成本金额
     */
    private BigDecimal profitTotal;

    /**
     * 合同编码
     */
    private String contractNo;

    /**
     * 订单类型：1-代收代付，2-联营订单
     */
    private String orderType;

    /**
     * 已拆解的订单的父订单编号，默认为不是拆解的订单
     */
    private String parentListUnique;

}