package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 开票服务
*/
@Data
@TableName(value = "bus_shop_invoice_goods_service")
public class BusShopInvoiceGoodsServiceEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 税收分类编码
    */
    private String categoryNo;
    /**
    * 商品名称
    */
    private String goodsName;
    /**
    * 商品税目
    */
    private String taxItem;
    /**
    * 商品规格
    */
    private String goodsSpec;
    /**
    * 商品单位
    */
    private String goodsUnit;
    /**
    * 税率
    */
    private String taxRate;
    /**
    * 有效状态:1正常0无效
    */
    private Integer enableStatus;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}