package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 入库单
*/
@Data
@TableName(value = "bus_inventory_order")
public class BusInventoryOrderEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 入库单号
    */
    private String orderNo;
    /**
     * 三方交易单号，如果是根据三方交易生成的单号，需要记录三方交易单号
     */
    private String thirdTradeNo;
    /**
    * 入库金额
    */
    private BigDecimal totalMoney;
    /**
     * 成本金额
     */
    private BigDecimal costMoney;
    /**
     * 利润金额
     */
    private BigDecimal profitMoney;
    /**
     * 入库单类型:1收银订单同步2餐饮订单同步(仅统计数据使用)3手动录入
     */
    private Integer inventoryType;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}