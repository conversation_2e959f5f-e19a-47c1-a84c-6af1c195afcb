package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 银行
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 09:06
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_bank_list")
public class SysBankListEntity extends BaseEntity {
    private static final long serialVersionUID = -4717429214313293527L;

    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行联行号
     */
    private String interbankNumber;

    /**
     * 银行logo
     */
    private String bankLogo;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 删除标识(0:未删除,1:已删除)
     */
    private Integer delFlag;
}
