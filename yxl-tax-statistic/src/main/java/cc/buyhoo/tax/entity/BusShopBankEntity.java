package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* 商家银行卡信息
*/
@Data
@TableName(value = "bus_shop_bank")
public class BusShopBankEntity extends BaseEntity {

    /**
    * 商家唯一标识
    */
    private Long shopUnique;
    /**
    * 有效状态:1正常0无效
    */
    private Integer enableStatus;
    /**
    * 企业ID
    */
    private Long companyId;
    /**
    * 银行名称
    */
    private String bankName;
    /**
     * 开户行行号
     */
    private String bankCode;
    /**
    * 开户行所在地（非银联卡必传）
    */
    private String bankCity;
    /**
    * 银行卡号
    */
    private String bankCard;
    /**
    * 所属银行ID，见表bank_list
    */
    private Long bankId;
    /**
     * 银行类型编码
     */
    @TableField(exist = false)
    private String bankNo;
    /**
    * 法人姓名
    */
    private String legalPerson;
    /**
    * 法人手机号
    */
    private String legalPhone;
    /**
    * 账户类型：1:对公；2:对私；
    */
    private Integer rcvCustType;
    /**
    * 删除标记:0未删除1已删除
    */
    private Integer delFlag;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}