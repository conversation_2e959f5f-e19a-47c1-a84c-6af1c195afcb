package cc.buyhoo.tax.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 
 * @Description 合同管理
 * @ClassName BusContract
 * @Date 2024-07-13
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_contract")
public class BusContractEntity extends BaseEntity {

	/**
	* 企业ID
	*/
	private Long companyId;

	/**
	* 合同编码
	*/
	private String contractNo;

	/**
	* 商户编码
	*/
	private Long shopUnique;

	/**
	* 合同名称
	*/
	private String contractName;

	/**
	* 合同类型：1-销售合同，2-采购合同，3-联营合同，4-其它
	*/
	private Integer contractType;

	/**
	* 合同金额（元）
	*/
	private BigDecimal totalAmount;

	/**
	* 合同状态：0-草拟，1-待审，2-已签署，3-执行中，4-已完成，5-终止
	*/
	private Integer contractStatus;

	/**
	* 合同开始时间
	*/
	private Date startTime;

	/**
	* 合同结束时间
	*/
	private Date endTime;

	/**
	* 合同签订日期
	*/
	private String signTime;

	/**
	* 关联单号,多个逗号(,)分割
	*/
	private String orderNo;

	/**
	* 附件地址
	*/
	private String fileUrl;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;

}