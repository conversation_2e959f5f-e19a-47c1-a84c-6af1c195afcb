package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 订单-商品分类统计
*/
@Data
@TableName(value = "bus_inventory_order_category")
public class BusInventoryOrderCategoryEntity extends BaseEntity {

    /**
     * 入库单ID
     */
    private Long orderId;
    /**
    * 一级分类
    */
    private Long categoryId;
    /**
    * 二级分类
    */
    private Long categoryTwoId;
    /**
    * 批次数量总和
    */
    private BigDecimal totalGoodsCount;
    /**
     * 商品金额
     */
    private BigDecimal totalMoney;
    /**
     * 公司编号
     */
    private Long companyId;

}