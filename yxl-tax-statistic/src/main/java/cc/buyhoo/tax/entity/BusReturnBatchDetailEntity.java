package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 批次详情
*/
@Data
@TableName(value = "bus_return_batch_detail")
public class BusReturnBatchDetailEntity extends BaseEntity {

    /**
    * bus_return_batch.id
    */
    private Long batchId;
    /**
     * 退货单id
     */
    private Long orderId;
    /**
    * 一级分类
    */
    private Long categoryId;
    /**
    * 二级分类
    */
    private Long categoryTwoId;
    /**
    * 商家端goods.id
    */
    private Long goodsId;
    /**
    * 商品名称
    */
    private String goodsName;
    /**
    * 商品条码
    */
    private String goodsBarcode;
    /**
    * 批次数量
    */
    private BigDecimal goodsCount;
    /**
    * 商品总金额
    */
    private BigDecimal totalMoney;
    /**
    * 企业ID
    */
    private Long companyId;

}