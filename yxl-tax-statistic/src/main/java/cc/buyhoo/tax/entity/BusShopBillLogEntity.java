package cc.buyhoo.tax.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cc.buyhoo.common.datasource.entity.BaseEntity;

import java.math.BigDecimal;

/**
* 供货商账单变动记录表
*/
@Data
@TableName(value = "bus_shop_bill_log")
public class BusShopBillLogEntity extends BaseEntity {

    /**
    * 店铺标识
    */
    private Long shopUnique;
    /**
    * 企业id
    */
    private Long companyId;
    /**
    * 支付类型：1订单结算
    */
    private Integer payType;
    /**
    * 对应业务编号：pay_type=1对应bus_inventory_batch.id
    */
    private Long busId;
    /**
    * 变动前金额
    */
    private BigDecimal changeBefore;
    /**
    * 变动金额
    */
    private BigDecimal changeMoney;

}