package cc.buyhoo.tax.util;

import java.io.*;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PCUpdateUtil {
	 /**
     * 给出一个路径，将该文件夹下的所有文件信息的MD5值及其路径保存到数据库 file_pcupdate
     */
	public static List<Map<String,Object>> getMd5FileForUpdateNew(String filePath){

		List<Map<String,Object>> data=new ArrayList<Map<String,Object>>();
		File file=new File(filePath);
		try{
			Map<String,Object> map=new HashMap<String, Object>();


			if(file.isFile()){

				String fileName = file.getName();

				//判断是否为安装包文件
				if(fileName.contains("BUYHOO-")) {
					//安装包文件
					map.put("fileType", "3");
				}else {
					map.put("fileType", 1);
				}
				map.put("filePath", file.getPath());
				map.put("fileMd5", PCUpdateUtil.getMd5ByFile(file));
				map.put("fileName", file.getName());
				map.put("fileSize", file.length());
				data.add(map);

			}else if(file.isDirectory()){
				System.out.println("当前文件路径" + file.getPath());
				map.put("filePath", file.getPath());//文件的MD5值
				map.put("fileMd5", PCUpdateUtil.string2MD5(filePath));//获取文件的值
				map.put("fileName", "");//文件夹不需要传名字
				map.put("fileSize", 0);
				map.put("fileType", 2);
				data.add(map);//将文件的路径放到
				//获取文件的字文件内容
				File[] files=file.listFiles();
				for(int i=0;i<files.length;i++){
					data.addAll(PCUpdateUtil.getMd5FileForUpdateNew(files[i].getPath()));
				}
			}
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
		return data;
	}
    /**
     * 将文件转换为MD5值
     * @param file
     * @return
     * @throws FileNotFoundException
     */
    public static String getMd5ByFile(File file) throws FileNotFoundException {  
		String value = null;  
		FileInputStream in = new FileInputStream(file);  
		try {  
		    MappedByteBuffer byteBuffer = in.getChannel().map(FileChannel.MapMode.READ_ONLY, 0, file.length());  
		    MessageDigest md5 = MessageDigest.getInstance("MD5");  
	        md5.update(byteBuffer);  
	        
	        byte[] msg = md5.digest();
	        StringBuffer sb = new StringBuffer();
	        for(byte b : msg) {
	        	sb.append(String.format("%02x",b & 0xff));
	        }
	        value = sb.toString();
	    } catch (Exception e) {  
	        e.printStackTrace();  
	    } finally {  
            if(null != in) {  
                try {  
                in.close();  
                } catch (IOException e) {  
                	e.printStackTrace();  
                }  
            }  
		}  
		return value;  
	}     /**
     * MD5加码 生成32位md5码 
     */  
    public static String string2MD5(String inStr){  
        MessageDigest md5 = null;  
        try{  
            md5 = MessageDigest.getInstance("MD5");  
        }catch (Exception e){  
            System.out.println(e);
            e.printStackTrace();  
            return "";  
        }  
        char[] charArray = inStr.toCharArray();  
        byte[] byteArray = new byte[charArray.length];  
  
        for (int i = 0; i < charArray.length; i++)  
            byteArray[i] = (byte) charArray[i];  
        byte[] md5Bytes = md5.digest(byteArray);  
        StringBuffer hexValue = new StringBuffer();  
        for (int i = 0; i < md5Bytes.length; i++){  
            int val = ((int) md5Bytes[i]) & 0xff;  
            if (val < 16)  
                hexValue.append("0");  
            hexValue.append(Integer.toHexString(val));  
        }  
        return hexValue.toString().trim();  
    }
    
    /**
     * 获取exe文件的版本号信息
     * @param file
     * @return
     */
    public static String getVersion(File file) {
		RandomAccessFile raf = null;
		byte[] buffer;
		String str;
		try {
			raf = new RandomAccessFile(file, "r");
			buffer = new byte[64];
			raf.read(buffer);
			str = "" + (char) buffer[0] + (char) buffer[1];
			if (!"MZ".equals(str)) {
				return null;
			}

			int peOffset = unpack(new byte[] { buffer[60], buffer[61],
					buffer[62], buffer[63] });
			if (peOffset < 64) {
				return null;
			}

			raf.seek(peOffset);
			buffer = new byte[24];
			raf.read(buffer);
			str = "" + (char) buffer[0] + (char) buffer[1];
			if (!"PE".equals(str)) {
				return null;
			}
			int machine = unpack(new byte[] { buffer[4], buffer[5] });
			if (machine != 332) {
				return null;
			}

			int noSections = unpack(new byte[] { buffer[6], buffer[7] });
			int optHdrSize = unpack(new byte[] { buffer[20], buffer[21] });
			raf.seek(raf.getFilePointer() + optHdrSize);
			boolean resFound = false;
			for (int i = 0; i < noSections; i++) {
				buffer = new byte[40];
				raf.read(buffer);
				str = "" + (char) buffer[0] + (char) buffer[1]
						+ (char) buffer[2] + (char) buffer[3]
						+ (char) buffer[4];
				if (".rsrc".equals(str)) {
					resFound = true;
					break;
				}
			}
			if (!resFound) {
				return null;
			}

			int infoVirt = unpack(new byte[] { buffer[12], buffer[13],
					buffer[14], buffer[15] });
			int infoSize = unpack(new byte[] { buffer[16], buffer[17],
					buffer[18], buffer[19] });
			int infoOff = unpack(new byte[] { buffer[20], buffer[21],
					buffer[22], buffer[23] });
			raf.seek(infoOff);
			buffer = new byte[infoSize];
			raf.read(buffer);
			int nameEntries = unpack(new byte[] { buffer[12], buffer[13] });
			int idEntries = unpack(new byte[] { buffer[14], buffer[15] });
			boolean infoFound = false;
			int subOff = 0;
			for (int i = 0; i < (nameEntries + idEntries); i++) {
				int type = unpack(new byte[] { buffer[i * 8 + 16],
						buffer[i * 8 + 17], buffer[i * 8 + 18],
						buffer[i * 8 + 19] });
				if (type == 16) { // FILEINFO resource
					infoFound = true;
					subOff = unpack(new byte[] { buffer[i * 8 + 20],
							buffer[i * 8 + 21], buffer[i * 8 + 22],
							buffer[i * 8 + 23] });
					break;
				}
			}
			if (!infoFound) {
				return null;
			}

			subOff = subOff & 0x7fffffff;
			infoOff = unpack(new byte[] { buffer[subOff + 20],
					buffer[subOff + 21], buffer[subOff + 22],
					buffer[subOff + 23] }); // offset of first FILEINFO
			infoOff = infoOff & 0x7fffffff;
			infoOff = unpack(new byte[] { buffer[infoOff + 20],
					buffer[infoOff + 21], buffer[infoOff + 22],
					buffer[infoOff + 23] }); // offset to data
			int dataOff = unpack(new byte[] { buffer[infoOff],
					buffer[infoOff + 1], buffer[infoOff + 2],
					buffer[infoOff + 3] });
			dataOff = dataOff - infoVirt;

			int version1 = unpack(new byte[] { buffer[dataOff + 48],
					buffer[dataOff + 48 + 1] });
			int version2 = unpack(new byte[] { buffer[dataOff + 48 + 2],
					buffer[dataOff + 48 + 3] });
			int version3 = unpack(new byte[] { buffer[dataOff + 48 + 4],
					buffer[dataOff + 48 + 5] });
			int version4 = unpack(new byte[] { buffer[dataOff + 48 + 6],
					buffer[dataOff + 48 + 7] });
			return version2 + "." + version1 + "." + version4 + "." + version3;
		} catch (Exception e) {
			return null;
		} finally {
			if (raf != null) {
				try {
					raf.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	public static int unpack(byte[] b) {
		int num = 0;
		for (int i = 0; i < b.length; i++) {
			num = 256 * num + (b[b.length - 1 - i] & 0xff);
		}
		return num;
	}
}
