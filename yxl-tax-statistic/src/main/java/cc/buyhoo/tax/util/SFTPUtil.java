package cc.buyhoo.tax.util;

import com.jcraft.jsch.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.util.*;

import cc.buyhoo.tax.config.FTP.FTPConfig;

/** 
 *  
 * @ClassName: SFTPUtil 
 * @Description: sftp连接工具类 
 * @date 2017年5月22日 下午11:17:21 
 * @version 1.0.0 
 */   
public class SFTPUtil {   
    private final transient Logger log = LoggerFactory.getLogger(this.getClass());
         
    private ChannelSftp sftp;   
           
    private Session session;   
    /** FTP 登录用户名*/     
    private String username;   
    /** FTP 登录密码*/     
    private String password;   
    /** 私钥 */     
    private String privateKey;   
    /** FTP 服务器地址IP地址*/     
    private String host;   
    /** FTP 端口*/   
    private int port;   
           
    /**  
     * 构造基于密码认证的sftp对象  
     * @param username
     * @param password  
     * @param host  
     * @param port  
     */     
    public SFTPUtil(String username, String password, String host, int port) {   
        this.username = username;   
        this.password = password;   
        this.host = host;   
        this.port = port;   
    }   
       
    /**  
     * 构造基于秘钥认证的sftp对象 
     * @param username
     * @param host 
     * @param port 
     * @param privateKey 
     */   
    public SFTPUtil(String username, String host, int port, String privateKey) {   
        this.username = username;   
        this.host = host;   
        this.port = port;   
        this.privateKey = privateKey;   
    }   
       
    public SFTPUtil(){}   
       
    /** 
     * 连接sftp服务器
     * @throws Exception  
     */   
    public void login(){   
        try {   
            JSch jsch = new JSch();   
            if (privateKey != null) {   
                jsch.addIdentity(privateKey);// 设置私钥   
                log.info("sftp connect,path of private key file：{}" , privateKey);   
            }   
            log.info("sftp connect by host:{} username:{}",host,username);   
       
            session = jsch.getSession(username, host, port);   
            log.info("Session is build");   
            if (password != null) {   
                session.setPassword(password);     
            }   
            Properties config = new Properties();   
            config.put("StrictHostKeyChecking", "no");   
                   
            session.setConfig(config);   
            session.connect();   
            log.info("Session is connected");   
                 
            Channel channel = session.openChannel("sftp");   
            channel.connect();   
            log.info("channel is connected");   
       
            sftp = (ChannelSftp) channel;   
            log.info(String.format("sftp server host:[%s] port:[%s] is connect successfull", host, port));   
        } catch (JSchException e) {   
            log.error("Cannot connect to specified sftp server : {}:{} \n Exception message is: {}", host, port, e.getMessage());
        }   
    }
    /**
     * 遍历指定目录，并得到这个路径的集合list.
     * create by pushkin on 20220822.
     *ls() 得到指定目录下的文件列表
     * @param directory 路径
     * @throws SftpException SftpException
     */
    public  void getSftpPathList(String directory, List<Map<String,Object>> result,String mainPath) throws SftpException, IOException {
        System.out.println(directory);
        Vector files = sftp.ls(directory);

        if (files.isEmpty()) {
            return;
        }
        for (Object object : files) {
            Map<String,Object> map=new HashMap<String, Object>();
            ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) object;
            String filename = entry.getFilename();
            if (".".equals(filename) || "..".equals(filename)) {
                continue;
            }

            String subDirectory = directory + "/" + filename;
            map.put("realPath",directory + "/" + filename);
            map.put("filePath",subDirectory.substring(mainPath.length()));
            SftpATTRS attrs = entry.getAttrs();
            if (attrs.isDir()) {
                map.put("fileName", "");
                map.put("fileMd5", PCUpdateUtil.string2MD5(subDirectory));//获取文件的值
                getSftpPathList(subDirectory, result,mainPath);
                map.put("fileType", 2);
                map.put("fileSize", 0);
            }else
            {

                map.put("fileSize", getFileSize(subDirectory));
                map.put("fileName", filename);
                map.put("fileType", 1);
               if(getFileSize(subDirectory)<1024*1024)//1MB
                {
                    map.put("fileMd5",  FileMd5Util.getMD5StringByInputStream(sftp.get(subDirectory)));
                }
            }
            result.add(map);

        }
    }


    public  String getSftpPathMd5(String directory) throws SftpException, IOException {
        return  FileMd5Util.getMD5StringByInputStream(sftp.get(directory));
    }


    /** 
     * 关闭连接 server  
     */   
    public void logout(){   
        if (sftp != null) {   
            if (sftp.isConnected()) {   
                sftp.disconnect();   
                log.info("sftp is closed already");   
            }   
        }   
        if (session != null) {   
            if (session.isConnected()) {   
                session.disconnect();   
                log.info("sshSession is closed already");   
            }   
        }   
    }   
    
    /**
     * 上传文件夹，本功能只会将本地文件夹下的文件上传到远程文件夹，不会在远处文件夹下先创建于本地同名文件夹，所以上传时，需要确定好文件夹路径
     * 例如：本地文件夹C://excel/ 有一个文件a.exe
     * 远程文件夹/mnt/file/
     * 上传后，远程文件/mnt/file/a.exe,并不是/mnt/file/excel/a.exe
     * 远程文件夹必须在基础文件夹 FTPConfig.base_path 的基础上创建，否则会失败
     * @param sftpFileDirectory 远程文件夹
     */
    public void uploadFilePath(File localFile, String sftpFileDirectory) {
        try {

            /*
    		 * 分为两种情况
    		 * 1、本地是文件；
    		 * 2、本地是文件夹
    		 */
            if(localFile.isFile()) {
                upload(sftpFileDirectory, localFile.getAbsolutePath());
            }
            if(localFile.isDirectory()) {

                //先创建远程文件夹
                createFilePath(sftpFileDirectory, localFile.getName());

                File[] files = localFile.listFiles();
                for(Integer i = 0; i < files.length ; i++) {
                    uploadFilePath(files[i], sftpFileDirectory);
                }
            }

        }catch (Exception e) {
            e.printStackTrace();
        }
    }
       
    /**  
     * 将输入流的数据上传到sftp作为文件
     * @param directory 上传到该目录  
     * @param sftpFileName sftp端文件名  
     * @param input 输入流
     * @throws SftpException   
     * @throws Exception  
     */     
    public boolean upload(String directory, String sftpFileName, InputStream input) throws SftpException{   
        try {
            //提前先测试文件路径是否存在
            SftpATTRS pathHave = null;
            try {
                pathHave = sftp.stat(directory);
            }catch (Exception e) {

            }
            if(null == pathHave) {

                createFilePath(FTPConfig.base_path, directory.substring(FTPConfig.base_path.length() + 1));
            }
            sftp.cd(directory);   
        } catch (SftpException e) {   
            log.warn("directory is not exist");   
            sftp.mkdir(directory);   
            sftp.cd(directory);   
        }   
        sftp.put(input, sftpFileName);   
        log.info("file:{} is upload successful" , sftpFileName);   
        return true;
    }

    /**
     * 获取文件大小
     * @param srcSftpFilePath
     * @return
     */
    public long getFileSize(String srcSftpFilePath) {
        long fileSize;//文件大于等于0则存在
        try {
            SftpATTRS sftpATTRS = sftp.lstat(srcSftpFilePath);
            fileSize = sftpATTRS.getSize();
        } catch (Exception e) {
            fileSize = -1;//获取文件大小异常
            if (e.getMessage().equalsIgnoreCase("no such file")) {
                fileSize = -2;//文件不存在
            }
        }
        return fileSize;
    }

    /**  
     * 上传单个文件
     * @param directory 上传到sftp目录  
     * @param uploadFile 要上传的文件,包括路径  
     * @throws FileNotFoundException 
     * @throws SftpException 
     * @throws Exception 
     */   
    public void upload(String directory, String uploadFile) throws FileNotFoundException, SftpException{   
        File file = new File(uploadFile);   
        upload(directory, file.getName(), new FileInputStream(file));   
    }   
       
    /** 
     * 将byte[]上传到sftp，作为文件。注意:从String生成byte[]是，要指定字符集。
     * @param directory 上传到sftp目录 
     * @param sftpFileName 文件在sftp端的命名 
     * @param byteArr 要上传的字节数组 
     * @throws SftpException 
     * @throws Exception 
     */   
    public void upload(String directory, String sftpFileName, byte[] byteArr) throws SftpException{   
        upload(directory, sftpFileName, new ByteArrayInputStream(byteArr));   
    }   
       
    /**  
     * 将字符串按照指定的字符编码上传到sftp
     * @param directory 上传到sftp目录 
     * @param sftpFileName 文件在sftp端的命名 
     * @param dataStr 待上传的数据 
     * @param charsetName sftp上的文件，按该字符编码保存 
     * @throws UnsupportedEncodingException 
     * @throws SftpException 
     * @throws Exception 
     */   
    public void upload(String directory, String sftpFileName, String dataStr, String charsetName) throws UnsupportedEncodingException, SftpException{     
        upload(directory, sftpFileName, new ByteArrayInputStream(dataStr.getBytes(charsetName)));     
    }


    public boolean uploadByProgress( String directory,String sftpFileName,  MultipartFile file) {
        InputStream in = null;
        OutputStream outputStream = null;
        FileProgressMonitor fileProgressMonitor = null;

        try {
            //提前先测试文件路径是否存在
            SftpATTRS pathHave = null;
            try {
                pathHave = sftp.stat(directory);
            }catch (Exception e) {

            }
            if(null == pathHave) {

                createFilePath(FTPConfig.base_path, directory.substring(FTPConfig.base_path.length() + 1));
            }
            sftp.cd(directory);
            in =  file.getInputStream();

            fileProgressMonitor = new FileProgressMonitor(file.getSize());
            sftp.put( in, sftpFileName, fileProgressMonitor, ChannelSftp.OVERWRITE);
        } catch (Exception var47) {
            var47.printStackTrace();
        } finally {
            if (in != null) {
                try {

                    in.close();
                    if(outputStream != null){
                        outputStream.close();
                    }
                } catch (IOException var45) {
                    var45.printStackTrace();
                } finally {
                    in = null;
                }
            }

        }
        return true;
    }

    /** 
     * 下载文件  
     * @param directory 下载目录  
     * @param downloadFile 下载的文件 
     * @param saveFile 存在本地的路径 
     * @throws SftpException 
     * @throws FileNotFoundException 
     * @throws Exception 
     */     
    public void download(String directory, String downloadFile, String saveFile) throws SftpException, FileNotFoundException{   
        if (directory != null && !"".equals(directory)) {   
            sftp.cd(directory);   
        }   
        File file = new File(saveFile);   
        sftp.get(downloadFile, new FileOutputStream(file));   
        log.info("file:{} is download successful" , downloadFile);   
    }  
       
    /**  
     * 下载文件 
     * @param directory 下载目录 
     * @param downloadFile 下载的文件名 
     * @return 字节数组 
     * @throws SftpException 
     * @throws IOException 
     * @throws Exception 
     */   
    public byte[] download(String directory, String downloadFile) throws SftpException, IOException{   
        if (directory != null && !"".equals(directory)) {   
            sftp.cd(directory);   
        }   
        InputStream is = sftp.get(downloadFile); 
        byte[] fileData = IOUtils.toByteArray(is); 
        log.info("file:{} is download successful" , downloadFile);   
        return fileData;   
    }   
       
    /** 
     * 删除文件 
     * @param directory 要删除文件所在目录 
     * @param deleteFile 要删除的文件 
     * @throws SftpException 
     * @throws Exception 
     */   
    public void delete(String directory, String deleteFile) throws SftpException{   
        sftp.cd(directory);   
        sftp.rm(deleteFile);   
    }   
       
    /** 
     * 列出目录下的文件
     * @param directory 要列出的目录 
     * @return
     * @throws SftpException 
     */   
    public Vector<?> listFiles(String directory) throws SftpException {   
        return sftp.ls(directory);   
    }   
         
    public static void main(String[] args) throws SftpException, IOException {
        long beginTime = System.currentTimeMillis();
        System.out.println("======开始时间======="+beginTime);
        //byte[] buff = sftp.download("/opt", "start.sh");   
        //System.out.println(Arrays.toString(buff));   
//        File file = new File("E:\\");
//        System.out.println(file.isDirectory());
//        System.out.println(file.getName());
        SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
        sftp.login();
        List<Map<String,Object>> data=new ArrayList<>();//pc 更新详情
        sftp.getSftpPathList("/mnt/myData/tomcat/tomcat1/webapps/file/shouyinji/**********/pos_version2",data,"/mnt/myData/tomcat/tomcat1/webapps/file/shouyinji/**********/pos_version2");
        System.out.println(data);

       long endTime = System.currentTimeMillis();
        System.out.println("==消耗时间=="+(endTime-beginTime)+"ms");
    }   
    
    //由于SFTP工具不能创建多级目录，所以需要依次向下创建子目录
    public boolean createFilePath(String directory,String subDirectory) {
        boolean flag = true;
        try {
            SftpATTRS pathHave = null;
            try {
                pathHave = sftp.stat(directory);
            }catch (Exception e) {

            }
            if(null == pathHave) {
                sftp.mkdir(directory);
            }

            if(null != subDirectory && !subDirectory.equals("")) {
                String[] subDirs = subDirectory.split("/");
                if(subDirs.length == 1) {
                    return createFilePath(directory + "/" + subDirs[0], null);
                }else {
                    return createFilePath(directory + "/" + subDirs[0], subDirectory.substring(subDirs[0].length() + 1));
                }

            }
        }catch (Exception e) {
            e.printStackTrace();
            flag = false;
        }
        return flag;
    }
}