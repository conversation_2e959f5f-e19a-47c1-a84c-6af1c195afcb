package cc.buyhoo.tax.util;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName IpUtil
 * @Description 获取IP地址
 * <AUTHOR>
 * @Date 2025/3/24 16:50
 * @Version 1.0
 */
public class IpUtil {

    public static String getIpAddr() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getRemoteAddr();
        }
        return "";
    }
}