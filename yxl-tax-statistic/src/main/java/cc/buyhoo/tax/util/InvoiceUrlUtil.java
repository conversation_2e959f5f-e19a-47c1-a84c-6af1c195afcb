package cc.buyhoo.tax.util;

import cc.buyhoo.tax.config.InvoiceNotifyConfig;
import cc.buyhoo.tax.constant.ShopTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class InvoiceUrlUtil {

    @Resource
    private InvoiceNotifyConfig invoiceNotifyConfig;

    public String buildUrl(Integer type) { // 直接使用枚举更安全
        ShopTypeEnum shopType = ShopTypeEnum.getEnumByValue(type);
        return shopType == ShopTypeEnum.RESTAURANT_SHOP ?
                invoiceNotifyConfig.urlRes : invoiceNotifyConfig.url;
    }


}
