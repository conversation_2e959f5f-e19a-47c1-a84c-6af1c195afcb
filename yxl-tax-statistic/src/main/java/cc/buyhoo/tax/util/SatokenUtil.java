package cc.buyhoo.tax.util;

import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.tax.constant.Constants;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.enums.SysUserErrorEnum;
import cc.buyhoo.tax.enums.sys.CompanyTypeEnum;
import cc.buyhoo.tax.enums.SysCompanyErrorEnum;
import cc.buyhoo.tax.enums.sys.UserTypeEnum;
import cc.buyhoo.tax.result.common.LoginUser;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;

public class SatokenUtil {

    /**
     * 获取当前登录的用户
     * @return
     */
    public static LoginUser getLoginUser() {
        LoginUser loginUser = (LoginUser) StpUtil.getTokenSession().get(Constants.LOGIN_USER);
        return loginUser;
    }

    /**
     * 获取当前登录的用户id
     * @return
     */
    public static Long getLoginUserId() {
        LoginUser loginUser = (LoginUser) StpUtil.getTokenSession().get(Constants.LOGIN_USER);
        return loginUser.getId();
    }

    /**
     * 获取当前登录的用户的companyId
     * @return
     */
    public static Long getLoginUserCompanyId() {
        LoginUser loginUser = (LoginUser) StpUtil.getTokenSession().get(Constants.LOGIN_USER);
        return loginUser.getCompanyId();
    }

    /**
     * 判断是否是平台
     * @return
     */
    public static boolean isPlatform(Integer companyType) {
        if (ObjectUtil.isNull(companyType)) throw new BusinessException(SysCompanyErrorEnum.ID_NULL_ERROR);
        return ObjectUtil.equal(CompanyTypeEnum.PLATFORM.getValue(), companyType);
    }

    /**
     * 判断是否超管
     * @return
     */
    public static boolean isSuperAdmin(Integer userType) {
        if (ObjectUtil.isNull(userType)) throw new BusinessException(SysUserErrorEnum.USER_NO_EXIST);
        return ObjectUtil.equal(UserTypeEnum.SUPER_ADMIN.getValue(), userType);
    }

}
