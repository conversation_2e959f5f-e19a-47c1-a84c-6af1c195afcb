package cc.buyhoo.tax.util;

import com.jcraft.jsch.SftpProgressMonitor;

import java.text.DecimalFormat;
import java.util.TimerTask;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class FileProgressMonitor extends TimerTask implements SftpProgressMonitor {
    // 默认间隔时间为5秒
    private final long progressInterval = 5 * 1000;

    private boolean isEnd = false; // 记录传输是否结束

    private long transfered; // 记录已传输的数据总大小

    private final long fileSize; // 记录文件总大小

    private ScheduledThreadPoolExecutor scheduled; // 定时器对象

    private boolean isScheduled = false; // 记录是否已启动timer记时器
    //自定义的进度单例类(前端查询进度时，返回该对象)
    //有其他好的方法欢迎留言
    ScheduleSingLeTon insteans = ScheduleSingLeTon.getInsteans();



    public FileProgressMonitor(long fileSize) {
        this.fileSize = fileSize;
        insteans.setFileSize(fileSize);

    }

    @Override
    public void run() {
        if (!isEnd()) { // 判断传输是否已结束
            System.out.println("判断传输是否已结束...........");
            long transfered = getTransfered();

            if (transfered != fileSize) { // 判断当前已传输数据大小是否等于文件总大小
                System.out.println("Current transfered: " + transfered + " bytes");
                sendProgressMessage(transfered);
            } else {
                System.out.println("上传完成...........");
                setEnd(true); // 如果当前已传输数据大小等于文件总大小，说明已完成，设置end
            }
        } else {
            System.out.println("Transfering done. Cancel timer.");
            stop(); // 如果传输结束，停止timer记时器
        }
    }

    public void stop() {
        System.out.println("Try to stop progress monitor.");
        if (scheduled != null) {
            scheduled.shutdownNow();
            scheduled = null;
            isScheduled = false;
        }
        System.out.println("Progress monitor stoped.");
    }

    public void start() {
        System.out.println("Try to start progress monitor.");
        if (scheduled == null) {
            scheduled = new ScheduledThreadPoolExecutor(1);
        }
        scheduled.scheduleAtFixedRate(this,0,1000, TimeUnit.MILLISECONDS);
        insteans.setTransfered(0L);
        insteans.setPlan("0.00");
        isScheduled = true;
        System.out.println("Progress monitor started.");
    }

    /**
     * 打印progress信息
     * @param transfered
     */
    private void sendProgressMessage(long transfered) {
        if (fileSize != 0) {
            double d = ((double)transfered * 100)/(double)fileSize;
            DecimalFormat df = new DecimalFormat( "#.##");
            System.out.println("Sending progress message: " + df.format(d) + "%");

            insteans.setPlan(df.format(d));
        } else {
            System.out.println("Sending progress message: " + transfered);
        }
    }

    /**
     * 实现了SftpProgressMonitor接口的count方法
     */
    @Override
    public boolean count(long count) {
        if (isEnd()){
            return false;
        }
        if (!isScheduled) {
            start();
        }
        add(count);
        return true;
    }

    /**
     * 实现了SftpProgressMonitor接口的end方法
     */
    @Override
    public void end() {
        setEnd(true);
        insteans.setFileSize(0L);
        insteans.setTransfered(0L);
        insteans.setPlan("0.00");
        System.out.println("transfering end.");
    }

    private synchronized void add(long count) {
        transfered = transfered + count;
        insteans.setTransfered(transfered);
    }

    private synchronized long getTransfered() {
        return transfered;
    }

    public synchronized void setTransfered(long transfered) {
        this.transfered = transfered;
    }

    private synchronized void setEnd(boolean isEnd) {
        this.isEnd = isEnd;
    }

    private synchronized boolean isEnd() {
        return isEnd;
    }

    @Override
    public void init(int op, String src, String dest, long max) {
        // Not used for putting InputStream
    }
}

