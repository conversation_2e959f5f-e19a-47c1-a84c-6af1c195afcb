package cc.buyhoo.tax.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;

/**
 * @Description 序列工具类
 * @ClassName SequenceUtils
 * <AUTHOR>
 * @Date 2024/7/12 15:13
 **/
public class SequenceUtils {


    private static final String CMBC_MER_SERIAL_NO_PREFIX = "CMBC";

    /**
     * 支付订单号生成
     * @return
     */

    /**
     * 民生银行外部操作流水号 (固定32位)
     * @return
     */
    public static String cmbcMerSerialNo() {
        StringBuilder sb = new StringBuilder(CMBC_MER_SERIAL_NO_PREFIX);
        sb.append(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_PATTERN));
        sb.append(String.format("%06d", RandomUtil.randomInt(10000)));
        // 补充 5 个随机字符
        sb.append(RandomUtil.randomString(5));
        return sb.toString();
    }
}
