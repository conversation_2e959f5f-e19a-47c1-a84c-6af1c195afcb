package cc.buyhoo.tax.util;

import cc.buyhoo.tax.config.cmb.CmbConfig;
import cc.buyhoo.tax.entity.cmb.CMBNote;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithID;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Base64;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 招商银行回调通知验签
 */
public class CmbVerifySig {

    public static String getCheckStr(CMBNote cmbNote) {
        List<String> list = new ArrayList<>();
        list.add("sigtim");
        list.add("sigdat");
        list.add("notdat");
        list.add("notkey");
        list.add("usrnbr");
        list.add("notnbr");
        list.add("nottyp");

        String jsonstr = "{";
        for (String key : list) {
            if (null != cmbNote.getFiled(key)) {
                jsonstr += "\"" + key + "\":" + JSON.toJSONString(cmbNote.getFiled(key)) + ",";
            }
        }
        jsonstr = jsonstr.substring(0,jsonstr.length() - 1);
        jsonstr += "}";
        return jsonstr;
    }

    public static boolean cmbCheckSign(CMBNote cmbNote,String bankPubKey,String signStr,String userId) {
        boolean flag = false;
        try {
            String resSign = cmbNote.getSigdat();
            cmbNote.setSigdat(signStr);
            String resSignSource = getCheckStr(cmbNote);
            return cmbSM2Verify(bankPubKey, resSignSource, resSign,userId);
        } catch (Exception e) {
            return flag;
        }
    }

    /**
     * 使用SM2公钥进行验证签名
     * @return
     */
    public static boolean cmbSM2Verify(String sPubKey, String strToSign, String strSign, String userId){
        System.out.println("当前公钥信息" + sPubKey);
        System.out.println("当前待验证信息" +strToSign);
        System.out.println("当前加密字符串" + strSign);
        byte[] USER_ID = userId.getBytes();
        try {
            byte[] pubKeys = Base64.decode(sPubKey);
            byte[] byteBuffer = strToSign.getBytes(StandardCharsets.UTF_8);
            byte[] signature = Base64.decode(strSign);
            ECPublicKeyParameters publicKey = encodePublicKey(pubKeys);
            SM2Signer signer = new SM2Signer();
            ParametersWithID parameters = new ParametersWithID(publicKey, USER_ID);
            signer.init(false, parameters);
            signer.update(byteBuffer, 0, byteBuffer.length);
//            if (!signer.verifySignature(encodeDERSignature(signature))) {
//                throw new Exception("请求签名校验不通过");
//            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return false;
        }
        return true;
    }


    private static byte[] encodeDERSignature(byte[] signature) throws Exception {
        byte[] r = new byte[32];
        byte[] s = new byte[32];
        System.arraycopy(signature, 0, r, 0, 32);
        System.arraycopy(signature, 32, s, 0, 32);
        ASN1EncodableVector vector = new ASN1EncodableVector();
        vector.add(new ASN1Integer(new BigInteger(1, r)));
        vector.add(new ASN1Integer(new BigInteger(1, s)));


        try {
            return (new DERSequence(vector)).getEncoded();
        } catch (Exception e) {
            throw new Exception("签名数据不正常");
        }
    }
    private static ECPublicKeyParameters encodePublicKey(byte[] value) {
        byte[] x = new byte[32];
        byte[] y = new byte[32];
        System.arraycopy(value, 1, x, 0, 32);
        System.arraycopy(value, 33, y, 0, 32);
        BigInteger iX = new BigInteger(1, x);
        BigInteger iY = new BigInteger(1, y);
        ECPoint ecQ = getSM2Curve().createPoint(iX, iY,true);
        return new ECPublicKeyParameters(ecQ, getECDomainParameters());
    }


    private static ECCurve getSM2Curve() {
        ECParameterSpec spec = ECNamedCurveTable.getParameterSpec("sm2p256v1");
        return spec.getCurve();
    }
    private static ECDomainParameters getECDomainParameters() {
        ECParameterSpec spec = ECNamedCurveTable.getParameterSpec("sm2p256v1");
        return new ECDomainParameters(spec.getCurve(), spec.getG(), spec.getN(), spec.getH(), spec.getSeed());
    }
}
