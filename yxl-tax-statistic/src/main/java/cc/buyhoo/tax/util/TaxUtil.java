package cc.buyhoo.tax.util;

import cc.buyhoo.tax.entity.invoice.TaxCountEntity;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 税率计算
 */
public class TaxUtil {

    /**
     * 输入商品金额（含税）和税率，计算商品的不含税价格
     * @param total（含税总金额）
     * @param taxRate（税率，6表示税率6%）
     * @return {taxAmount（不含税金额）, price（含税金额）}
     */
    public static TaxCountEntity getTaxAmount(BigDecimal total, BigDecimal taxRate) {
        TaxCountEntity taxCountEntity = new TaxCountEntity();
        taxCountEntity.setTotal(total);
        taxCountEntity.setTaxRate(taxRate);

        BigDecimal taxAmount = total.divide(BigDecimal.ONE.add(taxRate.divide(new BigDecimal(100),2 , RoundingMode.HALF_UP)),2 , RoundingMode.HALF_UP);
        BigDecimal price = total.subtract(taxAmount).setScale(2, RoundingMode.HALF_UP);

        taxCountEntity.setTaxAmount(taxAmount);
        taxCountEntity.setPrice(price);

        return taxCountEntity;
    }
}
