package cc.buyhoo.tax.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SystemUtil {
    private static final String PHONE_NUMBER_REGEX = "^1[3456789]\\d{9}$";
    private static final Pattern pattern = Pattern.compile(PHONE_NUMBER_REGEX);
    private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$";

    /**
     * 校验一个手机号码是否合法
     * @param phoneNumber
     * @return
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        return pattern.matcher(phoneNumber).matches();
    }

    /**
     * 将日期转换为指定格式
     * @param originalFormat
     * @param targetFormat
     * @param time
     * @return
     */
    public static String convertTimeFormat(String originalFormat, String targetFormat,String time) {
        SimpleDateFormat sourceDateFormat = new SimpleDateFormat(originalFormat);
        SimpleDateFormat targetDateFormat = new SimpleDateFormat(targetFormat);
        try {
            return targetDateFormat.format(sourceDateFormat.parse(time));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 校验是否是邮箱
     * @param email
     * @return
     */
    public static boolean isValidEmail(String email) {
        Pattern pattern = Pattern.compile(EMAIL_REGEX);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }

    /**
     * 将文件转换为字节数组
     * @param file
     * @return
     */
    public static byte[] convertFileToBytes(File file) {
        FileInputStream fis = null;
        byte[] fileBytes = null;
        try {
            fis = new FileInputStream(file);
            fileBytes = new byte[(int) file.length()];
            fis.read(fileBytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return fileBytes;
    }
}
