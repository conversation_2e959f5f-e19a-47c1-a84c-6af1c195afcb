package cc.buyhoo.tax.util;

import cc.buyhoo.common.bankpay.params.CmbPayConfigParams;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.tax.entity.SysCompanyBankAccountEntity;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;

public class CommonUtil {
    public static final String CREATE_ORDER = "CREATE_ORDER";
    public static final String CMBYURREF = "CMBYURREF";
    public static final String dateFormat = "yyMMdd";


    /**
     * 将数据库查询出的结果转换成所需的支付方式
     * @param sysCompanyBankAccountEntity
     * @return
     */
    public static CmbPayConfigParams convertCmbPayInfo(SysCompanyBankAccountEntity sysCompanyBankAccountEntity) {
        return new CmbPayConfigParams(
                sysCompanyBankAccountEntity.getUrl(),
                sysCompanyBankAccountEntity.getUid(),
                sysCompanyBankAccountEntity.getPublicKey(),
                sysCompanyBankAccountEntity.getPrivateKey(),
                sysCompanyBankAccountEntity.getSymmetricKey(),
                sysCompanyBankAccountEntity.getBusmod()
        );
    }
    /**
     * 创建订单号
     * @param redisCache
     * @return
     */
    public static String createOrder(RedisCache redisCache) {
        return createIndexWithDay(redisCache, CREATE_ORDER);
    }

    /**
     * 生成招商银行业务参考号，要求全局唯一，不同的业务之间也不能相同
     * @return
     */
    public static String createCmbYurref(RedisCache redisCache) {
        return createIndexWithDay(redisCache, CMBYURREF);
    }

    /**
     * 生成交易流水号
     * @param redisCache 缓存redis
     * @param yurref 关键key值
     * @return
     */
    public static String createTrxseq(RedisCache redisCache,String yurref) {
        return createIndexWithOutDate(redisCache, yurref);
    }

    /**
     * 生成流水号,不包含日期信息
     * @param redisCache 缓存redis
     * @param key 关键key值
     * @return
     */
    public static String createIndexWithOutDate(RedisCache redisCache, String key) {
        return createIndex(redisCache, key, "");
    }

    /**
     * 生成流水号,包含日期信息
     * @param redisCache
     * @param key
     * @return
     */
    public static String createIndexWithDay(RedisCache redisCache, String key) {
        String dayStr = getDayStr(dateFormat);
        return createIndex(redisCache, key, dayStr);
    }

    /**
     * 生成流水号
     * @param redisCache
     * @param key
     * @param dayStr
     * @return
     */
    public static String createIndex(RedisCache redisCache, String key,String dayStr) {

        String redisKey = key + dayStr;
        Integer index = 10000000;
        if (ObjectUtil.isNull(redisCache.getCacheObject(redisKey))) {
            index = index + 1;
        } else {
            index = Integer.parseInt(redisCache.getCacheObject(redisKey).toString());
            index = index + 1;
        }
        redisCache.setCacheObject(redisKey, index, 24 * 3600 , TimeUnit.SECONDS);

        //高并发下会重现重复，添加4位随机数
        Random random = new Random();
        return dayStr + index + random.nextInt(9999);
    }

    public static String getDayStr(String dateFormat) {
        Date date = DateUtil.date();
        String dateStr = DateUtil.format(date, "yyMMdd");
        return dateStr;
    }

}
