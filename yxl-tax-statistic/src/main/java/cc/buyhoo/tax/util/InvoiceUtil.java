package cc.buyhoo.tax.util;

import cc.buyhoo.tax.constant.InvoiceConfig;
import cc.buyhoo.tax.entity.invoice.EleUserLoginEntity;
import cc.buyhoo.tax.enums.InvoiceStaffDutyTypeEnum;
import cc.buyhoo.tax.enums.MethodNameEnum;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.lowagie.text.pdf.PdfReader;
import org.apache.logging.log4j.util.Base64Util;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

import cc.buyhoo.tax.entity.invoice.*;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

public class InvoiceUtil<T> {

    //用户登录
    public static EleUserLoginResult eleUserLogin(EleUserLoginEntity entity) {
        //验证通过，调用接口
        //引用hututil
        HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.INVOICE_LOGIN_URL);

        postRequest.header("Content-Type","application/json;charset=UTF-8");

        RequestParams requestParams = new RequestParams();
        requestParams.setNsrsbh(entity.getNsrsbh());
        requestParams.setApiKey(InvoiceConfig.APIKEY);
        String encodeString = Base64Util.encode(JSONUtil.toJsonStr(entity));
        requestParams.setRequestData(encodeString);
        requestParams.setSign(MD5Util.MD5Encode(JSONUtil.toJsonStr(entity) + InvoiceConfig.APISECRET,InvoiceConfig.CHARTSET));

        System.out.println("请求参数" + requestParams);
        postRequest.body(JSONUtil.toJsonStr(requestParams));

        String res = postRequest.execute().body();
        System.out.println("调用接口返回数据" + res);
        //将请求结果转换为json
        Gson gson = new Gson();

        EleUserLoginResult eleUserLoginResult = gson.fromJson(res, EleUserLoginResult.class);

        return eleUserLoginResult;
    }

    public static Map<String, Object> objectToMap(Object obj) {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                map.put(field.getName(), field.get(obj));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return map;
    }

    public static GenerateQdInvoiceResult generateQdInvoice(GenerateQdInvoiceEntity entity){
        System.out.println("当前请求的开票信息为" + entity);
        //引用hututil
        HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.INVOICE_INVOICE_URL);
        //设置头格式
        postRequest.header("Content-Type","application/json;charset=UTF-8");
        //上传参数信息
        RequestParams requestParams = new RequestParams();
        requestParams.setNsrsbh(entity.getSellerTaxCode());
        requestParams.setApiKey(InvoiceConfig.APIKEY);
        String encodeString = Base64Util.encode(JSONUtil.toJsonStr(entity));
        requestParams.setRequestData(encodeString);
        requestParams.setSign(MD5Util.MD5Encode(JSONUtil.toJsonStr(entity) + InvoiceConfig.APISECRET,InvoiceConfig.CHARTSET));

        System.out.println("请求参数" + requestParams);
        postRequest.body(JSONUtil.toJsonStr(requestParams));

        String res = postRequest.execute().body();
        System.out.println("调用接口返回数据" + res);
        //将请求结果转换为json
        Gson gson = new Gson();
        //将结果转换为实体类
        GenerateQdInvoiceResult result = gson.fromJson(res, GenerateQdInvoiceResult.class);
        return result;
    }

    public static void login(){
        //登录部分
        EleUserLoginEntity entity = new EleUserLoginEntity();
        entity.setNsrsbh("913701003072184269");
        entity.setDutyType(InvoiceStaffDutyTypeEnum.INVOICER.getValue());
        entity.setElectricAccount("***********");
        entity.setElectricPassword("wang1987!!@@");
        entity.setMethodName(MethodNameEnum.SMS_LOGIN.getValue());
        entity.setSmsCode("188643");

        eleUserLogin(entity);
    }

    //申请开票
    public static void invoiceApply(){

//        //申请开票部分
//        GenerateQdInvoiceEntity e1 = new GenerateQdInvoiceEntity();
//        e1.setElectricAccount("***********");
//        e1.setInvoiceType(InvoiceTypeEnum.NORMAT.getValue());
//        e1.setClientTaxCode("91371302674540097K");
//        e1.setClientName("临沂博林信息技术有限公司");
//        e1.setClientPhone("***********");
//        e1.setClientBankName("中国建设银行股份有限公司");
//        e1.setClientBankAccountNumber("37050182870100000121");
//        e1.setClientAddress("临沂市兰山区海关路东段星河晶都现代城2号楼314室");
//
//        e1.setSellerTaxCode("913701003072184269");
//        e1.setSellerName("山东影响力智能科技有限公司");
//        e1.setSellerBankName("临商银行股份有限公司金泰支行");
//        e1.setSellerBankAccountNumber("818210101421219117");
//        e1.setSellerPhone("0539-8602606");
//        e1.setSelesAddress("山东省临沂市临港经济开发区坪上镇中兴商务企业发展中心605室");
//        e1.setInvoicer("禚昌英");
//        e1.setChecker("石运华");
//        e1.setCashier("王庆旭");
//        e1.setGoodsListFlag(GoodsListFlagEnum.HAVE.getValue());
//        e1.setBillNumber("" + System.currentTimeMillis());
//        e1.setInvoiceKind(InvoiceKindEnum.NormalTicket.getValue());
//        e1.setTotalAmount("102.00");
//        e1.setAmount("0.00");
//        e1.setTaxAmount("0.00");
//
//        GenerateQdInvoiceGoodsEntity goods = new GenerateQdInvoiceGoodsEntity();
//        goods.setRowNumbe("1");
//        goods.setRowKind(RowKindEnym.Normal.getValue());
//        goods.setGoodsName("智能网关");
//        goods.setUnit("个");
//        goods.setNumber("1.0");
//        goods.setPrice("90.27");
//        goods.setAmount("102.00");
//        goods.setGoodsTaxNo("*********");
//        goods.setPriceKind(PriceKindEnum.CONTAIN.getValue());
//        goods.setTaxRate("0.13");
//        goods.setTaxPre(TaxPreEnum.DOTUSE.getValue());
//
//        List<GenerateQdInvoiceGoodsEntity> list = new ArrayList<>();
//        list.add(goods);
//        System.out.println("商品信息" + goods);
//        e1.setInvoiceDetails(list);
//        System.out.println("开票请求信息" + e1);
//        //发送开票请求
//        generateQdInvoice(e1);
    }

    public static void auth() {
        //获取认证二维码
        RpaQrCodeEntity entity = new RpaQrCodeEntity();
        entity.setNsrsbh("913701003072184269");
        entity.setElectricAccount("***********");

        rpaQrCode(entity);
    }

    public static void checkKp() {
        //获取开票结果
        GetQdInvoiceEntity entity = new GetQdInvoiceEntity();
        entity.setNsrsbh("913701003072184269");
        entity.setBillNumber("*************");

        getQdInvoice(entity);
    }
    public static void main(String[] args) {
        //登录
//        login();

//          //查看开票结果
        checkKp();

        //用户认证
//      auth();
//
//        //获取认证状态
//        RpaAuthStatusEntity entity1 = new RpaAuthStatusEntity();
//        entity1.setNsrsbh("913701003072184269");
//        entity1.setElectricAccount("***********");
//        entity1.setRzId("a815e6cc35c246ea9c8ec2022c644b9f");
//        rpaAuthStatus(entity1);

//        base64ToFile(stringBuilder.toString(),url);
//        String path = "/mnt/myData/file/fapiao";
//        File file = new File(path);
//        System.out.println(file.getAbsolutePath());


    }

    //查询开票结果
    public static GetQdInvoiceResult getQdInvoice(GetQdInvoiceEntity entity){
        //引用hututil
        HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.INVOICE_INVOICE_INFO_URL);
        //设置头格式
        postRequest.header("Content-Type","application/json;charset=UTF-8");
        //上传参数信息
        RequestParams requestParams = new RequestParams();
        requestParams.setNsrsbh(entity.getNsrsbh());
        requestParams.setApiKey(InvoiceConfig.APIKEY);
        String encodeString = Base64Util.encode(JSONUtil.toJsonStr(entity));
        requestParams.setRequestData(encodeString);
        requestParams.setSign(MD5Util.MD5Encode(JSONUtil.toJsonStr(entity) + InvoiceConfig.APISECRET,InvoiceConfig.CHARTSET));

        System.out.println("发票查询接口请求参数" + requestParams);
        postRequest.body(JSONUtil.toJsonStr(requestParams));

        String res = postRequest.execute().body();
        System.out.println("调用发票查询接口返回数据" + res);
        //将请求结果转换为json
        Gson gson = new Gson();
        GetQdInvoiceResult result = gson.fromJson(res, GetQdInvoiceResult.class);
        return result;
    }

    //查询开票结果
    public static QuerySdInvoiceFileResult querySdInvoiceFile(QuerySdInvoiceFileEntity entity){
        //引用hututil
        HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.INVOICE_INVOICE_FILE_URL);
        //设置头格式
        postRequest.header("Content-Type","application/json;charset=UTF-8");
        //上传参数信息
        RequestParams requestParams = new RequestParams();
        requestParams.setNsrsbh(entity.getNsrsbh());
        requestParams.setApiKey(InvoiceConfig.APIKEY);
        String encodeString = Base64Util.encode(JSONUtil.toJsonStr(entity));
        requestParams.setRequestData(encodeString);
        requestParams.setSign(MD5Util.MD5Encode(JSONUtil.toJsonStr(entity) + InvoiceConfig.APISECRET,InvoiceConfig.CHARTSET));

        System.out.println("查询发票文件请求参数" + requestParams);
        postRequest.body(JSONUtil.toJsonStr(requestParams));

        String res = postRequest.execute().body();
        System.out.println("调用查询发票文件接口返回数据" + res);
        //将请求结果转换为json
        Gson gson = new Gson();
        QuerySdInvoiceFileResult result = gson.fromJson(res, QuerySdInvoiceFileResult.class);
        return result;
    }

    //获取实名认证二维码
    public static RpaQrCodeResult rpaQrCode(RpaQrCodeEntity entity) {
        //引用hututil
        HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.RPA_QR_CODE);
        //设置头格式
        postRequest.header("Content-Type","application/json;charset=UTF-8");
        //上传参数信息
        RequestParams requestParams = new RequestParams();
        requestParams.setNsrsbh(entity.getNsrsbh());
        requestParams.setApiKey(InvoiceConfig.APIKEY);
        String encodeString = Base64Util.encode(JSONUtil.toJsonStr(entity));
        requestParams.setRequestData(encodeString);
        requestParams.setSign(MD5Util.MD5Encode(JSONUtil.toJsonStr(entity) + InvoiceConfig.APISECRET,InvoiceConfig.CHARTSET));

        System.out.println("请求参数" + requestParams);
        postRequest.body(JSONUtil.toJsonStr(requestParams));

        String res = postRequest.execute().body();
        System.out.println("调用接口返回数据" + res);

        //将请求结果转换为json
        Gson gson = new Gson();
        RpaQrCodeResult result = gson.fromJson(res, RpaQrCodeResult.class);
        return result;
    }

    /**
     * 获取认证结果
     * @param entity
     * @return
     */
    public static RpaAuthStatusResult rpaAuthStatus(RpaAuthStatusEntity entity) {
        //引用hututil
        HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.PRA_AUTH_STATUS);
        //设置头格式
        postRequest.header("Content-Type","application/json;charset=UTF-8");
        //上传参数信息
        RequestParams requestParams = new RequestParams();
        requestParams.setNsrsbh(entity.getNsrsbh());
        requestParams.setApiKey(InvoiceConfig.APIKEY);
        String encodeString = Base64Util.encode(JSONUtil.toJsonStr(entity));
        requestParams.setRequestData(encodeString);
        requestParams.setSign(MD5Util.MD5Encode(JSONUtil.toJsonStr(entity) + InvoiceConfig.APISECRET,InvoiceConfig.CHARTSET));

        System.out.println("请求参数" + requestParams);
        postRequest.body(JSONUtil.toJsonStr(requestParams));

        String res = postRequest.execute().body();
        System.out.println("调用接口返回数据" + res);

        Gson gson = new Gson();
        RpaAuthStatusResult result = gson.fromJson(res, RpaAuthStatusResult.class);
        return result;
    }

    public static File base64ToFile(String base64, String filePath) {
        File file = new File(filePath);
        byte[] buffer;
        try {
            buffer = Base64.getDecoder().decode(base64);
            FileOutputStream out = new FileOutputStream(filePath);
            out.write(buffer);
            out.close();
        } catch (Exception e) {
        }
        return file;
    }

    /**
     * 将PDF转换为图片
     * @param PdfFilePath pdf文件路径
     * @param dstImgFolder 修改后文件保存路径，文件名不变，格式png
     */
    public static void pdfToImage(String PdfFilePath, String dstImgFolder) {
            /* dpi越大转换后越清晰，相对转换速度越慢 */
            int dpi = 300;
            File file = new File(PdfFilePath);
            PDDocument pdDocument = new PDDocument(); // 创建PDF文档
            try {
                String imgPDFPath = file.getParent();
                int dot = file.getName().lastIndexOf('.');
                String imagePDFName = file.getName().substring(0, dot); // 获取图片文件名
                String imgFolderPath = null;
                if (dstImgFolder.equals("")) {
                    imgFolderPath = imgPDFPath + File.separator;// 获取图片存放的文件夹路径
                } else {
                    imgFolderPath = dstImgFolder + File.separator;
                }
                if (createDirectory(imgFolderPath)) {
                    pdDocument = PDDocument.load(file);
                    PDFRenderer renderer = new PDFRenderer(pdDocument);
                    PdfReader reader = new PdfReader(PdfFilePath);
                    int pages = reader.getNumberOfPages();
                    StringBuffer imgFilePath = null;
                    BufferedImage[] bufferedImages = new BufferedImage[pages];
                    for (int i = 0; i < pages; i++) {
                        String imgFilePathPrefix = imgFolderPath + File.separator;
                        imgFilePath = new StringBuffer();
                        imgFilePath.append(imgFilePathPrefix);
                        imgFilePath.append("_");
                        imgFilePath.append(i + 1);
                        imgFilePath.append(".png");
                        // File dstFile = new File(imgFilePath.toString());
                        BufferedImage image = renderer.renderImageWithDPI(i, dpi);
                        bufferedImages[i] = image;
                        // ImageIO.write(image, "png", dstFile);
                    }
                    dstImgFolder = dstImgFolder + imagePDFName + ".png";
                    // PDF文件全部页数转PNG图片，若多张展示注释即可 工具类贴在下面
                    ImageMergeUtil.mergeImage(bufferedImages, 2, dstImgFolder);
                    System.out.println("PDF文档转PNG图片成功！");
                    reader.close();
                } else {
                    System.out.println("PDF文档转PNG图片失败：" + "创建" + imgFolderPath + "失败");
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    pdDocument.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
    }

    /**
     * 测试文件夹是否存在，如果不存在，创建指定目录
     * @param folder
     * @return
     */
    private static boolean createDirectory(String folder) {
        File dir = new File(folder);
        if (dir.exists()) {
            return true;
        } else {
            return dir.mkdirs();
        }
    }
}
