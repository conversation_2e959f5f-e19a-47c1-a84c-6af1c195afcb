package cc.buyhoo.tax.util;

import cc.buyhoo.common.baidu.result.VatInvoiceFormalResult;
import cc.buyhoo.common.baidu.result.VatInvoiceResult;
import cc.buyhoo.common.baidu.util.VatInvoiceUtil;

public class BadiDuUtil {
    //百度图像识别accessToken,后期由定时任务更新到redis，方便不同项目之间调用
    public static String accessToken = "24.1d510a82b94333ebfe64a0f4c232dab4.2592000.1724291747.282335-96707964";

    public static void main(String[] args) {

    }
}
