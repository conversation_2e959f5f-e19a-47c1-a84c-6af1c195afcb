package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusShopServiceFeeMapper;
import cc.buyhoo.tax.entity.BusShopServiceFeeEntity;
import cc.buyhoo.tax.facade.BusShopServiceFeeFacade;
import cc.buyhoo.tax.facade.params.busShopServiceFee.UpdateShopServiceFeeParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@DubboService
@Service
public class BusShopServiceFeeFacadeImpl implements BusShopServiceFeeFacade {

    @Autowired
    private BusShopServiceFeeMapper busShopServiceFeeMapper;

    /**
     * 更新店铺服务费
     * @param params
     */
    @Override
    public void updateShopServiceFee(UpdateShopServiceFeeParams params) {

        //查询服务费是否存在
        LambdaQueryWrapper<BusShopServiceFeeEntity> serviceFeeWrapper = new LambdaQueryWrapper<>();
        serviceFeeWrapper.eq(BusShopServiceFeeEntity::getCompanyId,params.getCompanyId());
        serviceFeeWrapper.eq(BusShopServiceFeeEntity::getShopUnique,params.getShopUnique());
        BusShopServiceFeeEntity feeEntity = busShopServiceFeeMapper.selectOne(serviceFeeWrapper);

        if (ObjectUtil.isEmpty(feeEntity)) { //新增
            saveServiceFee(params);
        }else { //修改
            updateServiceFee(params,feeEntity);
        }
    }

    /**
     * 保存服务费
     * @param params
     */
    private void saveServiceFee(UpdateShopServiceFeeParams params) {
        BusShopServiceFeeEntity entity = new BusShopServiceFeeEntity();
        BeanUtil.copyProperties(params,entity);
        busShopServiceFeeMapper.insert(entity);
    }

    /**
     * 修改服务费
     * @param params
     * @param oldData
     */
    private void updateServiceFee(UpdateShopServiceFeeParams params,BusShopServiceFeeEntity oldData) {
        BusShopServiceFeeEntity upd = new BusShopServiceFeeEntity();
        upd.setId(oldData.getId());
        upd.setServiceFee(NumberUtil.add(oldData.getServiceFee(),params.getServiceFee()));
        busShopServiceFeeMapper.updateById(upd);
    }

}
