package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusInventoryOrderCategoryMapper;
import cc.buyhoo.tax.entity.BusInventoryOrderCategoryEntity;
import cc.buyhoo.tax.facade.BusInventoryOrderCategoryFacade;
import cc.buyhoo.tax.facade.params.busInventoryOrderCategory.OrderCategoryRemote;
import cc.buyhoo.tax.facade.params.busInventoryOrderCategory.SaveInventoryOrderCategoryRemoteParams;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@DubboService
@Service
public class BusInventoryOrderCategoryFacadeImpl implements BusInventoryOrderCategoryFacade {

    @Autowired
    private BusInventoryOrderCategoryMapper orderCategoryMapper;

    /**
     * 保存订单-商品分类统计
     * @param params
     */
    @Override
    public void saveInventoryOrderCategory(SaveInventoryOrderCategoryRemoteParams params) {
        List<OrderCategoryRemote> paramsOrderCategoryList = params.getOrderCategoryList();
        List<BusInventoryOrderCategoryEntity> categoryEntityList = BeanUtils.copyList(paramsOrderCategoryList, BusInventoryOrderCategoryEntity.class);
        orderCategoryMapper.insertBatch(categoryEntityList);
    }
}
