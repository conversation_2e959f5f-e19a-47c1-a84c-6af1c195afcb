package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusInventoryOrderMapper;
import cc.buyhoo.tax.entity.BusInventoryOrderEntity;
import cc.buyhoo.tax.facade.BusInventoryOrderFacade;
import cc.buyhoo.tax.facade.params.busInventoryOrder.SaveInventoryOrderRemoteParams;
import cc.buyhoo.tax.facade.params.busInventoryOrder.UpdateInventoryOrderRemoteParams;
import cn.hutool.core.util.NumberUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@DubboService
@Service
public class BusInventoryOrderFacadeImpl implements BusInventoryOrderFacade {

    @Autowired
    private BusInventoryOrderMapper busInventoryOrderMapper;

    /**
     * 保存入库单
     * @param params
     * @return
     */
    @Override
    public Long saveInventoryOrder(SaveInventoryOrderRemoteParams params) {
        BusInventoryOrderEntity entity = new BusInventoryOrderEntity();
        BeanUtils.copy(params,entity);
        busInventoryOrderMapper.insert(entity);
        return entity.getId();
    }

    /**
     * 修改入库单金额
     * @param params
     */
    @Override
    public void updateInventoryOrderMoney(UpdateInventoryOrderRemoteParams params) {
        if (params.getCashMoney().compareTo(BigDecimal.ZERO) == 0 && params.getOnlineMoney().compareTo(BigDecimal.ZERO) == 0) { //删除入库单
            busInventoryOrderMapper.deleteById(params.getOrderId());
        }else { //修改金额
            BusInventoryOrderEntity upd = new BusInventoryOrderEntity();
            upd.setId(params.getOrderId());
//            upd.setCashMoney(params.getCashMoney());
//            upd.setOnlineMoney(params.getOnlineMoney());
            upd.setTotalMoney(NumberUtil.add(params.getCashMoney(),params.getOnlineMoney()));
            busInventoryOrderMapper.updateById(upd);
        }
    }
}
