package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.facade.ReturnListFacade;
import cc.buyhoo.tax.facade.params.returnList.*;
import cc.buyhoo.tax.facade.params.saleList.SaveSaleList;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@DubboService
@Service
@Slf4j
public class ReturnListFacadeImpl implements ReturnListFacade {

    @Autowired
    private BusReturnListMapper busReturnListMapper;

    @Autowired
    private BusReturnListDetailMapper busReturnListDetailMapper;

    @Autowired
    private BusReturnListPaydetailMapper busReturnListPaydetailMapper;

    @Autowired
    private BusReturnBatchMapper busReturnBatchMapper;

    @Autowired
    private BusReturnBatchDetailMapper busReturnBatchDetailMapper;

    @Autowired
    private BusReturnNotSyncMapper busReturnNotSyncMapper;

    @Autowired
    private BusReturnOrderMapper busReturnOrderMapper;

    @Autowired
    private BusReturnOrderCategoryMapper busReturnOrderCategoryMapper;

    @Autowired
    private BusGoodsMapper busGoodsMapper;


    /**
     * 保存退款单
     * @param params
     */
    @Override
    public void syncReturnList(SyncReturnListParams params) {
        List<SyncReturnList> returnList = params.getReturnList();
        if (ObjectUtil.isEmpty(returnList)) return;
        /*List<SyncReturnList> newReturnList = new ArrayList<>();
        for (SyncReturnList srl : returnList) {
            LambdaQueryWrapper<BusReturnListEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusReturnListEntity::getSaleListUnique, srl.getReturnList().getSaleListUnique());
            List<BusReturnListEntity> busSaleListEntities = busReturnListMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(busSaleListEntities)) {
                log.error("------[退单定时任务同步]----零售退单数据已存在,退单编号={}----------", srl.getReturnList().getSaleListUnique());
                continue;
            }
            newReturnList.add(srl);
        }*/
        //请求参数
        List<ReturnListRemote> returnListRemoteList = returnList.stream().map(SyncReturnList::getReturnList).collect(Collectors.toList());
        List<ReturnListDetailRemote> returnDetailRemoteList = returnList.stream().map(SyncReturnList::getReturnListDetailList).flatMap(d -> d.stream()).collect(Collectors.toList());
        List<ReturnListPaydetailRemote> returnPayRemoteList = returnList.stream().map(SyncReturnList::getPaydetailList).flatMap(p -> p.stream()).collect(Collectors.toList());

        /*保存退款单相关*/
        if (ObjectUtil.isNotEmpty(returnListRemoteList)) {
            busReturnListMapper.insertBatch(BeanUtils.copyList(returnListRemoteList, BusReturnListEntity.class));
        }
        if (ObjectUtil.isNotEmpty(returnDetailRemoteList)) {
            busReturnListDetailMapper.insertBatch(BeanUtils.copyList(returnDetailRemoteList, BusReturnListDetailEntity.class));
        }
        if (ObjectUtil.isNotEmpty(returnPayRemoteList)) {
            busReturnListPaydetailMapper.insertBatch(BeanUtils.copyList(returnPayRemoteList, BusReturnListPaydetailEntity.class));
        }

        //收银相关同步商品分类
        if (!"cash".equals(params.getBusinessType())) return;

        /*保存退货单相关*/
        //查询所有的商品对应分类
        List<Long> goodsIdList = returnDetailRemoteList.stream().map(ReturnListDetailRemote::getGoodsId).collect(Collectors.toList());
        LambdaQueryWrapper<BusGoodsEntity> busGoodsWrapper = new LambdaQueryWrapper<>();
        busGoodsWrapper.in(BusGoodsEntity::getGoodsId,goodsIdList);
        List<BusGoodsEntity> busGoodsList = busGoodsMapper.selectList(busGoodsWrapper);

        //保存退款单
        Long orderId = saveReturnOrder(params.getCompanyId());
        //总在线支付
        BigDecimal totalOnlineMoney = BigDecimal.ZERO;
        //总现金支付
        BigDecimal totalCashMoney = BigDecimal.ZERO;
        //总的批次数据
        List<BusReturnBatchDetailEntity> totalBatchDetail = new ArrayList<>();
        Map<Long, List<ReturnListRemote>> groupByShopUnique = returnListRemoteList.parallelStream().collect(Collectors.groupingBy(ReturnListRemote::getShopUnique));
        Iterator<Map.Entry<Long, List<ReturnListRemote>>> iter = groupByShopUnique.entrySet().iterator();

        while (iter.hasNext()) {
            Map.Entry<Long, List<ReturnListRemote>> entry = iter.next();
            Long shopUnique = entry.getKey();
            List<ReturnListRemote> rlr = entry.getValue();
            List<String> retListUniqueList = rlr.stream().map(ReturnListRemote::getRetListUnique).collect(Collectors.toList());
            List<ReturnListPaydetailRemote> payList = returnPayRemoteList.stream().filter(p -> retListUniqueList.contains(p.getRetListUnique())).collect(Collectors.toList());
            List<ReturnListDetailRemote> detailList = returnDetailRemoteList.stream().filter(d -> retListUniqueList.contains(d.getRetListUnique())).collect(Collectors.toList());

            //现金退款
            BigDecimal cashMoney = payList.stream().filter(p -> p.getServiceType() == 1).map(ReturnListPaydetailRemote::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            //线上退款
            BigDecimal onlineMoney = payList.stream().filter(p -> p.getServiceType() != 1).map(ReturnListPaydetailRemote::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalOnlineMoney = NumberUtil.add(totalOnlineMoney,onlineMoney);
            totalCashMoney = NumberUtil.add(totalCashMoney,cashMoney);

            //保存批次单
            BusReturnBatchEntity batch = new BusReturnBatchEntity();
            batch.setCompanyId(params.getCompanyId());
            batch.setShopUnique(shopUnique);
            batch.setBatchNo(StringUtils.join(DateUtil.format(new Date(), "yyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
            batch.setOnlineMoney(onlineMoney);
            batch.setCashMoney(cashMoney);
            batch.setTotalMoney(NumberUtil.add(onlineMoney,cashMoney));
            batch.setOrderId(orderId);
            batch.setBatchDate(DateUtil.format(DateUtil.offsetDay(new Date(), -1), DatePattern.NORM_DATE_PATTERN));
            busReturnBatchMapper.insert(batch);

            //保存批次详情
            for (ReturnListDetailRemote d : detailList) {
                BusGoodsEntity goods = busGoodsList.stream().filter(b -> d.getGoodsId().equals(b.getGoodsId())).findAny().orElse(null);
                if (ObjectUtil.isEmpty(goods) || ObjectUtil.isEmpty(goods.getCategoryId()) || ObjectUtil.isEmpty(goods.getCategoryTwoId())) {
                    BusReturnNotSyncEntity notSync = new BusReturnNotSyncEntity();
                    notSync.setCompanyId(params.getCompanyId());
                    notSync.setSyncType(1);
                    notSync.setRetListUnique(d.getRetListUnique());
                    notSync.setRetListDetailId(d.getRetListDetailId());
                    notSync.setGoodsId(d.getGoodsId());
                    notSync.setStatus(1);
                    busReturnNotSyncMapper.insert(notSync);
                }else {
                    BusReturnBatchDetailEntity bd = new BusReturnBatchDetailEntity();
                    BeanUtils.copy(d,bd);
                    bd.setBatchId(batch.getId());
                    bd.setCategoryId(goods.getCategoryId());
                    bd.setCategoryTwoId(goods.getCategoryTwoId());
                    bd.setGoodsCount(d.getRetListDetailCount());
                    bd.setTotalMoney(NumberUtil.mul(d.getRetListDetailCount(),d.getRetListDetailPrice()));
                    bd.setCompanyId(params.getCompanyId());
                    bd.setOrderId(batch.getOrderId());
                    totalBatchDetail.add(bd);
                }
            }
        }
        //保存所有的批次详情
        busReturnBatchDetailMapper.insertBatch(totalBatchDetail);

        //保存退款单-商品分类统计
        Map<Long, List<BusReturnBatchDetailEntity>> byCategoryTwoId = totalBatchDetail.parallelStream().collect(Collectors.groupingBy(BusReturnBatchDetailEntity::getCategoryTwoId));
        Iterator<Map.Entry<Long, List<BusReturnBatchDetailEntity>>> cateIter = byCategoryTwoId.entrySet().iterator();
        List<BusReturnOrderCategoryEntity> cateList = new ArrayList<>();
        while (cateIter.hasNext()) {
            Map.Entry<Long, List<BusReturnBatchDetailEntity>> entry = cateIter.next();
            Long towId = entry.getKey();
            List<BusReturnBatchDetailEntity> dList = entry.getValue();
            BigDecimal totalCount = dList.stream().map(BusReturnBatchDetailEntity::getGoodsCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalMoney = dList.stream().map(BusReturnBatchDetailEntity::getTotalMoney).reduce(BigDecimal.ZERO, BigDecimal::add);

            //分类
            BusReturnOrderCategoryEntity cate = new BusReturnOrderCategoryEntity();
            cate.setReturnOrderId(orderId);
            cate.setCategoryId(dList.get(0).getCategoryId());
            cate.setCategoryTwoId(towId);
            cate.setTotalGoodsCount(totalCount);
            cate.setTotalMoney(totalMoney);
            cate.setCompanyId(params.getCompanyId());
            cateList.add(cate);
        }
        busReturnOrderCategoryMapper.insertBatch(cateList);

        //更新退款单金额
        updateReturnOrder(orderId,totalOnlineMoney,totalCashMoney);

    }

    /**
     * 保存退货单
     * @return
     */
    private Long saveReturnOrder(Long companyId) {
        BusReturnOrderEntity entity = new BusReturnOrderEntity();
        entity.setCompanyId(companyId);
        entity.setOrderNo(StringUtils.join("TH", DateUtil.format(new Date(), "yyyMMddHHmmss"), RandomUtil.randomNumbers(4)));
        entity.setTotalMoney(BigDecimal.ZERO);
        entity.setOnlineMoney(BigDecimal.ZERO);
        entity.setCashMoney(BigDecimal.ZERO);
        busReturnOrderMapper.insert(entity);

        return entity.getId();
    }

    /**
     * 更新退款单
     * @param id
     * @param onlineMoney
     * @param cashMoney
     */
    private void updateReturnOrder(Long id,BigDecimal onlineMoney,BigDecimal cashMoney) {
        BusReturnOrderEntity upd = new BusReturnOrderEntity();
        upd.setId(id);
        upd.setOnlineMoney(onlineMoney);
        upd.setCashMoney(cashMoney);
        upd.setTotalMoney(NumberUtil.add(onlineMoney,cashMoney));
        busReturnOrderMapper.updateById(upd);
    }
}
