package cc.buyhoo.tax.dubbo;

import cc.buyhoo.tax.dao.BusSaleListDetailMapper;
import cc.buyhoo.tax.dao.BusSaleListMapper;
import cc.buyhoo.tax.dao.BusSaleListPayDetailMapper;
import cc.buyhoo.tax.entity.BusSaleListDetailEntity;
import cc.buyhoo.tax.entity.BusSaleListEntity;
import cc.buyhoo.tax.entity.BusSaleListPayDetailEntity;
import cc.buyhoo.tax.facade.SaleListFacade;
import cc.buyhoo.tax.facade.params.saleList.SaveSaleList;
import cc.buyhoo.tax.facade.params.saleList.SaveSaleListParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@DubboService
@Service
@Slf4j
public class SaleListFacadeImpl implements SaleListFacade {

    @Resource
    private BusSaleListMapper busSaleListMapper;

    @Resource
    private BusSaleListDetailMapper busSaleListDetailMapper;

    @Resource
    private BusSaleListPayDetailMapper busSaleListPayDetailMapper;

    /**
     * 保存订单数据
     */
    @Override
    public void saveSaleList(SaveSaleListParams params){

        List<BusSaleListEntity> busSaleListEntityList = new ArrayList<>();
        List<BusSaleListDetailEntity> busSaleListDetailEntityList = new ArrayList<>();
        List<BusSaleListPayDetailEntity> busSaleListPayDetailEntityList = new ArrayList<>();
        for (SaveSaleList ssl : params.getSaveList()) {
            /*LambdaQueryWrapper<BusSaleListEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusSaleListEntity::getSaleListUnique, ssl.getSaleList().getSaleListUnique());
            queryWrapper.eq(BusSaleListEntity::getSaleListDatetime, ssl.getSaleList().getSaleListDatetime());
            List<BusSaleListEntity> busSaleListEntities = busSaleListMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(busSaleListEntities)) {
                log.error("------[订单定时任务同步]----零售订单数据已存在,订单编号={}----------", ssl.getSaleList().getSaleListUnique());
                continue;
            }*/
            //sale_list
            BusSaleListEntity busSaleListEntity = new BusSaleListEntity();
            BeanUtil.copyProperties(ssl.getSaleList(), busSaleListEntity);
            busSaleListEntityList.add(busSaleListEntity);

            //sale_list_detail
            busSaleListDetailEntityList.addAll(BeanUtil.copyToList(ssl.getSaleListDetailList(), BusSaleListDetailEntity.class));
            busSaleListDetailEntityList.stream().forEach(v -> v.setCompanyId(busSaleListEntity.getCompanyId()));

            //sale_list_pay_detail
            List<BusSaleListPayDetailEntity> payDetailList = BeanUtil.copyToList(ssl.getSaleListPayDetailList(), BusSaleListPayDetailEntity.class);
            payDetailList.stream().forEach(v -> v.setCompanyId(busSaleListEntity.getCompanyId()));
            busSaleListPayDetailEntityList.addAll(payDetailList);

        }
        if (ObjectUtil.isNotEmpty(busSaleListEntityList)) {
            busSaleListMapper.insertBatch(busSaleListEntityList);
        }
        if (ObjectUtil.isNotEmpty(busSaleListDetailEntityList)) {
            busSaleListDetailMapper.insertBatch(busSaleListDetailEntityList);
        }
        if (ObjectUtil.isNotEmpty(busSaleListPayDetailEntityList)) {
            busSaleListPayDetailMapper.insertBatch(busSaleListPayDetailEntityList);
        }

    }
}
