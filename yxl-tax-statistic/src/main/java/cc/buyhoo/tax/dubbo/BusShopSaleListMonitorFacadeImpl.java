package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.dingdingtalk.utils.SendDingDingTalkUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.SaleListMonitorSettledStatusEnum;
import cc.buyhoo.tax.enums.SettledStatusEnum;
import cc.buyhoo.tax.enums.sys.CompanyTypeEnum;
import cc.buyhoo.tax.facade.BusShopSaleListMonitorFacade;
import cc.buyhoo.tax.facade.params.busShopSaleListMonitor.*;
import cc.buyhoo.tax.params.busShopSaleListMonitor.*;
import cc.buyhoo.tax.result.busShopCoverCharge.ShopCoverChargeListDto;
import cc.buyhoo.tax.result.returnList.ReturnListSyncDto;
import cc.buyhoo.tax.result.saleList.SaleListSyncDto;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName BusShopSaleListMonitorFacadeImpl
 * <AUTHOR>
 * @Date 2024/10/31 9:08
 */
@Slf4j
@DubboService
@Service
public class BusShopSaleListMonitorFacadeImpl implements BusShopSaleListMonitorFacade {
    @Resource
    private BusShopSaleListMonitorMapper busShopSaleListMonitorMapper;

    @Resource
    private BusSaleListMapper busSaleListMapper;

    @Resource
    private BusReturnListMapper busReturnListMapper;

    @Resource
    private BusShopBillMapper busShopBillMapper;

    @Resource
    private BusShopMapper busShopMapper;

    @Resource
    private SysCompanyMapper sysCompanyMapper;

    @Resource
    private SendDingDingTalkUtils sendDingDingTalkUtils;

    @Override
    public void insertSaleListMonitor(BusSaleListMonitorInsertParams params) {
        if (ObjectUtil.isNotEmpty(params) && ObjectUtil.isNotEmpty(params.getList())) {
            List<BusShopSaleListMonitorEntity> list = new ArrayList<>();
            for (BusSaleListMonitorDetailInsertParams detail : params.getList()) {
                if (ObjectUtil.isNotNull(detail.getTaxAmount()) && detail.getTaxAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BusShopSaleListMonitorEntity entity = new BusShopSaleListMonitorEntity();
                    entity.setShopUnique(detail.getShopUnique());
                    entity.setTaxAmount(detail.getTaxAmount());
                    entity.setCompanyId(detail.getCompanyId());
                    entity.setStatDate(detail.getStatDate());
                    entity.setCreateTime(DateUtil.date());
                    entity.setModifyTime(DateUtil.date());
                    list.add(entity);
                }
            }
            busShopSaleListMonitorMapper.insertBatch(list);
        }
    }

    @Override
    public void updateSaleListMonitor(BusSaleListMonitorUpdateListParams params) {
        Date endTime = DateUtil.endOfDay(DateUtil.yesterday());
        Date startTime = DateUtil.beginOfDay(DateUtil.yesterday());
        StringBuffer str = new StringBuffer();

        //查询数据
        LambdaQueryWrapper<SysCompanyEntity> companyWrapper = new LambdaQueryWrapper<>();
        companyWrapper.ne(SysCompanyEntity::getCompanyType, CompanyTypeEnum.PLATFORM.getValue());
        companyWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        companyWrapper.eq(SysCompanyEntity::getStatisticsStatus, 1);
        List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyWrapper);

        LambdaQueryWrapper<BusShopEntity> shopQueryWrapper = new LambdaQueryWrapper<>();
        shopQueryWrapper.select(BusShopEntity::getShopUnique, BusShopEntity::getShopName);
        List<BusShopEntity> shopList = busShopMapper.selectList(shopQueryWrapper);
        shopList = shopList.stream().map(v -> {
            if (ObjectUtil.isNotEmpty(companyList) && companyList.stream().anyMatch(v1 -> v1.getId().equals(v.getCompanyId()))) {
                return v;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 查询已统计结算金额的订单监控信息
        LambdaQueryWrapper<BusShopSaleListMonitorEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopSaleListMonitorEntity::getStatDate, params.getStatDate());
        queryWrapper.eq(BusShopSaleListMonitorEntity::getSettledStatus, SaleListMonitorSettledStatusEnum.UNSETTLED.getValue());
        List<BusShopSaleListMonitorEntity> list = busShopSaleListMonitorMapper.selectList(queryWrapper);
        list = list.stream().map(v -> {
            if (ObjectUtil.isNotEmpty(companyList) && companyList.stream().anyMatch(v1 -> v1.getId().equals(v.getCompanyId()))) {
                return v;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        LambdaQueryWrapper<BusShopSaleListMonitorEntity> saleListMonitorEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        saleListMonitorEntityLambdaQueryWrapper.in(BusShopSaleListMonitorEntity::getSettledStatus, SaleListMonitorSettledStatusEnum.SETTLED_FAIL.getValue(), SaleListMonitorSettledStatusEnum.UNSETTLED.getValue());
        List<BusShopSaleListMonitorEntity> saleListMonitorList = busShopSaleListMonitorMapper.selectList(saleListMonitorEntityLambdaQueryWrapper);
        saleListMonitorList = saleListMonitorList.stream().map(v -> {
            if (ObjectUtil.isNotEmpty(companyList) && companyList.stream().anyMatch(v1 -> v1.getId().equals(v.getCompanyId()))) {
                return v;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        List<BusShopSaleListMonitorEntity> agoList = saleListMonitorList.stream().filter(v -> DateUtil.parse(v.getStatDate(), "yyyy-MM-dd")
                .compareTo(DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.date(), -2))) <= 0).collect(Collectors.toList());
        List<SaleListMonitorTotalParams> saleListMonitorTotalParamsList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(agoList)){
            saleListMonitorTotalParamsList = agoList.stream().collect(Collectors.groupingBy(
                            BusShopSaleListMonitorEntity::getShopUnique,
                            Collectors.reducing(BigDecimal.ZERO,
                                    BusShopSaleListMonitorEntity::getTaxAmount,
                                    BigDecimal::add)
                    )).entrySet().stream().sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        SaleListMonitorTotalParams dto = new SaleListMonitorTotalParams();
                        dto.setShopUnique(entry.getKey());
                        dto.setTaxAmount(entry.getValue());
                        return dto;
                    }).collect(Collectors.toList());
        }

        LambdaQueryWrapper<BusShopBillEntity> billListQueryWrapper = new LambdaQueryWrapper<>();
        billListQueryWrapper.eq(BusShopBillEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<BusShopBillEntity> billList = busShopBillMapper.selectList(billListQueryWrapper);
        billList = billList.stream().map(v -> {
            if (ObjectUtil.isNotEmpty(companyList) && companyList.stream().anyMatch(v1 -> v1.getId().equals(v.getCompanyId()))) {
                return v;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        List<BusShopBillEntity> unSettledBillList = billList.stream().filter(v -> v.getUnsettledAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        BigDecimal unSettledAmountBillTotal = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(billList)) {
            unSettledAmountBillTotal = billList.stream().map(BusShopBillEntity::getUnsettledAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        if (ObjectUtil.isNotEmpty(params) && ObjectUtil.isNotEmpty(params.getList()) && ObjectUtil.isNotEmpty(params.getSyncList())) {

            Long taxSyncCount = 0L;
            BigDecimal taxSyncAmount = BigDecimal.ZERO;
            // 查询已同步的订单信息
            List<SaleListSyncDto> saleListSyncList = busSaleListMapper.saleListSyncInfo(params.getStartSyncTime(), params.getEndSyncTime());
            if (ObjectUtil.isNotEmpty(saleListSyncList)) {
                taxSyncCount = Long.valueOf(saleListSyncList.size());
                taxSyncAmount = saleListSyncList.stream().map(SaleListSyncDto::getSaleListActuallyReceived).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            str.append("  \n    ①订单同步对账");
            str.append("  \n          零售订单-数量：").append(params.getSyncBuyhooCount()).append("，金额：").append(params.getSyncBuyhooAmount());
            str.append("  \n          餐饮订单-数量：").append(params.getSyncCanyinCount()).append("，金额：").append(params.getSyncCanyinAmount());
            str.append("  \n          纳统订单-数量：").append(taxSyncCount).append("，金额：").append(taxSyncAmount);
            if (taxSyncCount.compareTo(params.getSyncBuyhooCount() + params.getSyncCanyinCount()) != 0 || taxSyncAmount.compareTo(params.getSyncBuyhooAmount().add(params.getSyncCanyinAmount())) != 0) {
                str.append("  \n          **对比结果：不一致**");
            } else {
                str.append("  \n          对比结果：一致");
            }

            // 查询未同步或同步失败的订单
            List<SyncSaleListAddParams> addList = new ArrayList<>();
            // 查询多余同步订单
            List<SyncSaleListSubtractParams> subtractList = new ArrayList<>();
            compareSyncList(addList,subtractList,params.getSyncList(),saleListSyncList);

            // 查询未同步或同步失败的订单
            List<SyncReturnListAddParams> addReturnList = new ArrayList<>();
            // 查询多余同步订单
            List<SyncReturnListSubtractParams> subtractReturnList = new ArrayList<>();
            List<ReturnListSyncDto> returnListSyncDtoList = busReturnListMapper.returnListSyncInfo(params.getStartSyncTime(), params.getEndSyncTime());
            compareSyncReturnList(addReturnList, subtractReturnList, params.getSyncList(), returnListSyncDtoList);
            compareSyncBuffer(addList, subtractList, addReturnList, subtractReturnList, str);


            str.append("  \n    ②结算金额对账");
            BigDecimal buyhooSettledAmount = BigDecimal.ZERO;
            BigDecimal canyinSettledAmount = BigDecimal.ZERO;
            Long buyhooSettledCount = 0L;
            Long buyhooReturnCount = 0L;
            Long canyinSettledCount = 0L;
            Long canyinReturnCount = 0L;
            Long taxSettledCount = 0L;
            Long taxReturnCount = 0L;
            BigDecimal taxSettledAmount = BigDecimal.ZERO;
            Set<Long> shopUniqueUnsettledList = new HashSet<>();

            List<Map<String, Object>> map = busSaleListMapper.selectTaxCountGroup(startTime, endTime);
            List<Map<String, Object>> returnMap = busReturnListMapper.selectTaxCountGroup(startTime, endTime);

            LambdaQueryWrapper<BusSaleListEntity> saleListSettledQueryWrapper = new LambdaQueryWrapper<>();
            saleListSettledQueryWrapper.eq(BusSaleListEntity::getSettledStatus, SettledStatusEnum.SETTLED.getValue());
            saleListSettledQueryWrapper.between(BusSaleListEntity::getSaleListDatetime, startTime, endTime);
            saleListSettledQueryWrapper.select(BusSaleListEntity::getShopUnique,BusSaleListEntity::getShopName, BusSaleListEntity::getSaleListUnique);
            saleListSettledQueryWrapper.in(BusSaleListEntity::getCompanyId,companyList.stream().map(SysCompanyEntity::getId).collect(Collectors.toList()));
            List<BusSaleListEntity> settledSaleList = busSaleListMapper.selectList(saleListSettledQueryWrapper);

            List<SettledSaleListAddParams> settledSaleListAddParamsList = new ArrayList<>();
            List<SettledSaleListSubtractParams> settledSaleListSubtractParamsList = new ArrayList<>();
            compareSettledSaleList(settledSaleListAddParamsList,settledSaleListSubtractParamsList,params.getSyncList(),settledSaleList);

            LambdaQueryWrapper<BusReturnListEntity> returnListSettledQueryWrapper = new LambdaQueryWrapper<>();
            returnListSettledQueryWrapper.eq(BusReturnListEntity::getSettledStatus, SettledStatusEnum.SETTLED.getValue());
            returnListSettledQueryWrapper.between(BusReturnListEntity::getRetListDatetime, startTime, endTime);
            returnListSettledQueryWrapper.select(BusReturnListEntity::getShopUnique,BusReturnListEntity::getShopName, BusReturnListEntity::getSaleListUnique);
            returnListSettledQueryWrapper.in(BusReturnListEntity::getCompanyId,companyList.stream().map(SysCompanyEntity::getId).collect(Collectors.toList()));
            List<BusReturnListEntity> settledreturnList = busReturnListMapper.selectList(returnListSettledQueryWrapper);


            List<SettledReturnListAddParams> settledReturnListAddParamsList = new ArrayList<>();
            List<SettledReturnListSubtractParams> settledReturnListSubtractParamsList = new ArrayList<>();
            compareSettledReturnList(settledReturnListAddParamsList,settledReturnListSubtractParamsList,params.getSyncList(),settledreturnList);

            List<BusShopSaleListMonitorEntity> updateList = new ArrayList<>();
            for (BusSaleListMonitorUpdateParams detail : params.getList()) {
                if (ObjectUtil.isNotNull(detail.getShopUnique())) {

                    if (ObjectUtil.isNotEmpty(saleListMonitorTotalParamsList)) {
                        if (saleListMonitorTotalParamsList.stream().anyMatch(v -> ObjectUtil.equals(v.getShopUnique(), detail.getShopUnique()))) {
                            saleListMonitorTotalParamsList.forEach(v -> {
                                if (ObjectUtil.equals(v.getShopUnique(), detail.getShopUnique())) {
                                    v.setTaxAmount(v.getTaxAmount().add(detail.getBuyhooAmount().add(detail.getCanyinAmount())));
                                }
                            });
                        } else {
                            SaleListMonitorTotalParams saleListMonitorTotalParams = new SaleListMonitorTotalParams();
                            saleListMonitorTotalParams.setShopUnique(detail.getShopUnique());
                            saleListMonitorTotalParams.setTaxAmount(detail.getBuyhooAmount().add(detail.getCanyinAmount()));
                            saleListMonitorTotalParamsList.add(saleListMonitorTotalParams);
                        }
                    } else {
                        SaleListMonitorTotalParams saleListMonitorTotalParams = new SaleListMonitorTotalParams();
                        saleListMonitorTotalParams.setShopUnique(detail.getShopUnique());
                        saleListMonitorTotalParams.setTaxAmount(detail.getBuyhooAmount().add(detail.getCanyinAmount()));
                        saleListMonitorTotalParamsList.add(saleListMonitorTotalParams);
                    }

                    if (ObjectUtil.isNotEmpty(map) && map.stream().anyMatch(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))) {
                        taxSettledCount += Long.valueOf(String.valueOf(map.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                .findFirst().get().get("count")));
                        taxSettledAmount = taxSettledAmount.add(new BigDecimal(String.valueOf(map.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                .findFirst().get().get("amount"))).subtract(new BigDecimal(String.valueOf(map.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                .findFirst().get().get("fee")))));
                    }
                    if (ObjectUtil.isNotEmpty(returnMap) && returnMap.stream().anyMatch(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))) {
                        taxReturnCount += Long.valueOf(String.valueOf(returnMap.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                .findFirst().get().get("count")));
                        taxSettledAmount = taxSettledAmount.subtract(new BigDecimal(String.valueOf(returnMap.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                .findFirst().get().get("ret_total_money"))).subtract(new BigDecimal(String.valueOf(returnMap.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                .findFirst().get().get("ret_delfee")))).subtract(new BigDecimal(String.valueOf(returnMap.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                .findFirst().get().get("ret_service_fee")))));
                    }
                    buyhooSettledCount += detail.getBuyhooSaleCount();
                    buyhooReturnCount += detail.getBuyhooReturnCount();
                    buyhooSettledAmount = buyhooSettledAmount.add(detail.getBuyhooAmount());
                    canyinSettledCount += detail.getCanyinSaleCount();
                    canyinReturnCount += detail.getCanyinReturnCount();
                    canyinSettledAmount = canyinSettledAmount.add(detail.getCanyinAmount());

                    if (ObjectUtil.isNotEmpty(list)) {
                        if (list.stream().anyMatch(item -> item.getShopUnique().equals(detail.getShopUnique()))) {
                            BusShopSaleListMonitorEntity entity = list.stream().filter(item -> item.getShopUnique().equals(detail.getShopUnique())).findFirst().get();
                            if (ObjectUtil.isNotEmpty(map) && map.stream().anyMatch(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))) {
                                entity.setTaxSaleCount(Long.valueOf(String.valueOf(map.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                        .findFirst().get().get("count"))));
                            } else {
                                entity.setTaxSaleCount(0L);
                            }
                            if (ObjectUtil.isNotEmpty(returnMap) && returnMap.stream().anyMatch(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))) {
                                entity.setTaxReturnCount(Long.valueOf(String.valueOf(returnMap.stream().filter(item -> Objects.equals(detail.getShopUnique(), Long.valueOf(String.valueOf(item.get("shopUnique")))))
                                        .findFirst().get().get("count"))));
                            } else {
                                entity.setTaxReturnCount(0L);
                            }
                            entity.setBuyhooSaleCount(detail.getBuyhooSaleCount());
                            entity.setBuyhooReturnCount(detail.getBuyhooReturnCount());
                            entity.setBuyhooAmount(detail.getBuyhooAmount());
                            entity.setCanyinSaleCount(detail.getCanyinSaleCount());
                            entity.setCanyinReturnCount(detail.getCanyinReturnCount());
                            entity.setCanyinAmount(detail.getCanyinAmount());
                            entity.setModifyTime(DateUtil.date());
                            updateList.add(entity);
                        } else {
                            if (ObjectUtil.isNotNull(detail.getBuyhooSaleCount()) && detail.getBuyhooSaleCount().compareTo(0L) > 0) {
                                str.append("  \n          商户【").append(detail.getShopUnique()).append("】未生成订单监控信息。");
                            }
                        }
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(updateList)) {
                busShopSaleListMonitorMapper.updateBatchById(updateList);
            }

            compareSettledAmount(saleListMonitorTotalParamsList, unSettledBillList, shopUniqueUnsettledList);

            str.append("  \n          零售-订单数量：").append(buyhooSettledCount).append("，退单数量：").append(buyhooReturnCount).append("，金额：").append(buyhooSettledAmount);
            str.append("  \n          餐饮-订单数量：").append(canyinSettledCount).append("，退单数量：").append(canyinReturnCount).append("，金额：").append(canyinSettledAmount);
            str.append("  \n          纳统-订单数量：").append(taxSettledCount).append("，退单数量：").append(taxReturnCount).append("，金额：").append(taxSettledAmount);

            if (buyhooSettledCount + canyinSettledCount != taxSettledCount
                    || buyhooReturnCount + canyinReturnCount != taxReturnCount
                    || buyhooSettledAmount.add(canyinSettledAmount).compareTo(taxSettledAmount) != 0) {
                str.append("  \n          **对比结果：不一致**");
                compareSettledBuffer(settledSaleListAddParamsList, settledSaleListSubtractParamsList, settledReturnListAddParamsList, settledReturnListSubtractParamsList, str);
            } else {
                str.append("  \n          对比结果：一致");
            }

            str.append("  \n    ③待打款金额对账");
            if (ObjectUtil.isEmpty(list)) {
                str.append("  \n          结算日期【").append(params.getStatDate()).append("】未生成订单监控信息。");
            }
            BigDecimal unSettledAmountTotal = ObjectUtil.isNotEmpty(agoList) ? agoList.stream()
                    .map(BusShopSaleListMonitorEntity::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add).add(buyhooSettledAmount).add(canyinSettledAmount)
                    : BigDecimal.ZERO.add(buyhooSettledAmount).add(canyinSettledAmount);
            str.append("  \n          监控-金额：").append(unSettledAmountTotal);
            str.append("  \n          待结算-金额：").append(unSettledAmountBillTotal);
            if (unSettledAmountTotal.compareTo(unSettledAmountBillTotal) != 0){
                str.append("  \n          **对比结果：不一致**");
            } else {
                str.append("  \n          对比结果：一致");
            }
            if (ObjectUtil.isNotEmpty(shopUniqueUnsettledList)) {
                str.append("  \n          问题商户：");
                List<BusShopEntity> finalShopList = shopList;
                shopUniqueUnsettledList.stream().forEach(shopUnique -> {
                    if (finalShopList.stream().anyMatch(item -> item.getShopUnique().equals(shopUnique))) {
                        String shopName = finalShopList.stream().filter(item -> item.getShopUnique().equals(shopUnique)).findFirst().get().getShopName();
                        str.append(shopName).append("（").append(shopUnique).append("）、");
                    } else {
                        str.append(shopUnique).append("、");
                    }
                });
                str.deleteCharAt(str.length() - 1);
                str.append("；");
            }

            String body = str.toString();
            sendDingDingTalkUtils.sendDingDingTalkMsgText(null, DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd"), body);
        }
    }

    /**
     * 同步订单差异
     * @param addList
     * @param subtractList
     * @param str
     */
    private void compareSyncBuffer(List<SyncSaleListAddParams> addList,List<SyncSaleListSubtractParams> subtractList,List<SyncReturnListAddParams> addReturnList,List<SyncReturnListSubtractParams> subtractReturnList,StringBuffer str){
        if (ObjectUtil.isNotEmpty(addList) || ObjectUtil.isNotEmpty(subtractList) || ObjectUtil.isNotEmpty(addReturnList) || ObjectUtil.isNotEmpty(subtractReturnList)) {
            str.append("  \n          问题商户：");
            final int[] i = {1};
            if (ObjectUtil.isNotEmpty(addList)) {
                addList.forEach(addParams -> {
                    if (ObjectUtil.isNotEmpty(addParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(addParams.getShopName()).append("（").append(addParams.getShopUnique()).append("）。原因：金圈订单未同步。订单编号：");
                        addParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
            if (ObjectUtil.isNotEmpty(subtractList)) {
                subtractList.forEach(subtractParams -> {
                    if (ObjectUtil.isNotEmpty(subtractParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(subtractParams.getShopName()).append("（").append(subtractParams.getShopUnique()).append("）。原因：纳统订单多余同步。订单编号：");
                        subtractParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
            if (ObjectUtil.isNotEmpty(addReturnList)) {
                addReturnList.forEach(addParams -> {
                    if (ObjectUtil.isNotEmpty(addParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(addParams.getShopName()).append("（").append(addParams.getShopUnique()).append("）。原因：金圈退单未同步。订单编号：");
                        addParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
            if (ObjectUtil.isNotEmpty(subtractReturnList)) {
                subtractReturnList.forEach(subtractParams -> {
                    if (ObjectUtil.isNotEmpty(subtractParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(subtractParams.getShopName()).append("（").append(subtractParams.getShopUnique()).append("）。原因：纳统退单多余同步。订单编号：");
                        subtractParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
        }
    }

    /**
     * 结算订单差异
     * @param addList
     * @param subtractList
     * @param str
     */
    private void compareSettledBuffer(List<SettledSaleListAddParams> addList,List<SettledSaleListSubtractParams> subtractList,List<SettledReturnListAddParams> addReturnList,List<SettledReturnListSubtractParams> subtractReturnList,StringBuffer str){
        if (ObjectUtil.isNotEmpty(addList) || ObjectUtil.isNotEmpty(subtractList) || ObjectUtil.isNotEmpty(addReturnList) || ObjectUtil.isNotEmpty(subtractReturnList)) {
            str.append("  \n          问题商户：");
            final int[] i = {1};
            if (ObjectUtil.isNotEmpty(addList)) {
                addList.forEach(addParams -> {
                    if (ObjectUtil.isNotEmpty(addParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(addParams.getShopName()).append("（").append(addParams.getShopUnique()).append("）。原因：金圈订单未结算。订单编号：");
                        addParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
            if (ObjectUtil.isNotEmpty(subtractList)) {
                subtractList.forEach(subtractParams -> {
                    if (ObjectUtil.isNotEmpty(subtractParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(subtractParams.getShopName()).append("（").append(subtractParams.getShopUnique()).append("）。原因：纳统订单多余结算。订单编号：");
                        subtractParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
            if (ObjectUtil.isNotEmpty(addReturnList)) {
                addReturnList.forEach(addParams -> {
                    if (ObjectUtil.isNotEmpty(addParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(addParams.getShopName()).append("（").append(addParams.getShopUnique()).append("）。原因：金圈退单未结算。订单编号：");
                        addParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
            if (ObjectUtil.isNotEmpty(subtractReturnList)) {
                subtractReturnList.forEach(subtractParams -> {
                    if (ObjectUtil.isNotEmpty(subtractParams.getSaleListUniqueList())) {
                        str.append("  \n              （").append(i[0]).append("）、").append(subtractParams.getShopName()).append("（").append(subtractParams.getShopUnique()).append("）。原因：纳统退单多余结算。订单编号：");
                        subtractParams.getSaleListUniqueList().stream().forEach(saleListUnique -> str.append(saleListUnique).append("、"));
                    }
                    str.deleteCharAt(str.length() - 1);
                    str.append("；");
                    i[0]++;
                });
            }
        }
    }

    /**
     * 比较同步订单差异
     * @param addList
     * @param subtractList
     * @param syncList
     * @param saleListSyncList
     */
    private static void compareSyncList(List<SyncSaleListAddParams> addList,List<SyncSaleListSubtractParams> subtractList,List<BusSaleListMonitorSyncParams> syncList,List<SaleListSyncDto> saleListSyncList){
        if (ObjectUtil.isNotEmpty(syncList)) {
            for (BusSaleListMonitorSyncParams syncParams : syncList) {
                SyncSaleListAddParams addParams = new SyncSaleListAddParams();
                addParams.setShopUnique(syncParams.getShopUnique());
                addParams.setShopName(syncParams.getShopName());
                List<String> addSaleListUniqueList = new ArrayList<>();
                boolean addFlag = false;
                if (ObjectUtil.isNotEmpty(saleListSyncList)) {
                    if (saleListSyncList.stream().anyMatch(s -> s.getShopUnique().equals(syncParams.getShopUnique()))) {
                        List<String> salelist = saleListSyncList.stream().filter(s -> s.getShopUnique().equals(syncParams.getShopUnique())).map(SaleListSyncDto::getSaleListUnique).collect(Collectors.toList());
                        for (BusSaleListMonitorSettledParams busSaleListMonitorSettledParams : syncParams.getSaleListUniqueList()) {
                            if (salelist.stream().noneMatch(busSaleListMonitorSettledParams.getSaleListUnique()::equals)) {
                                addFlag = true;
                                addSaleListUniqueList.add(busSaleListMonitorSettledParams.getSaleListUnique());
                            }
                        }
                    } else {
                        for (BusSaleListMonitorSettledParams busSaleListMonitorSettledParams : syncParams.getSaleListUniqueList()) {
                            addFlag = true;
                            addSaleListUniqueList.add(busSaleListMonitorSettledParams.getSaleListUnique());
                        }
                    }
                } else {
                    for (BusSaleListMonitorSettledParams busSaleListMonitorSettledParams : syncParams.getSaleListUniqueList()) {
                        addFlag = true;
                        addSaleListUniqueList.add(busSaleListMonitorSettledParams.getSaleListUnique());
                    }
                }
                if (addFlag) {
                    addParams.setSaleListUniqueList(addSaleListUniqueList);
                    addList.add(addParams);
                }
            }
        }
        for (SaleListSyncDto saleListSyncDto: saleListSyncList) {
            boolean subtractFlag = false;
            if (subtractList.stream().anyMatch(s -> s.getShopUnique().equals(saleListSyncDto.getShopUnique()))) {
                SyncSaleListSubtractParams subtractParams = subtractList.stream().filter(s -> s.getShopUnique().equals(saleListSyncDto.getShopUnique())).findFirst().get();
                List<String> saleListUniqueList = subtractParams.getSaleListUniqueList();
                if (ObjectUtil.isEmpty(syncList)) {
                    saleListUniqueList.add(saleListSyncDto.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(saleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getSaleListUniqueList().stream().map(BusSaleListMonitorSettledParams::getSaleListUnique).collect(Collectors.toList()).contains(saleListSyncDto.getSaleListUnique()))) {
                        saleListUniqueList.add(saleListSyncDto.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(saleListUniqueList);
                    }
                }
            } else {
                SyncSaleListSubtractParams subtractParams = new SyncSaleListSubtractParams();
                subtractParams.setShopUnique(saleListSyncDto.getShopUnique());
                subtractParams.setShopName(saleListSyncDto.getShopName());
                subtractParams.setSaleListUniqueList(Collections.EMPTY_LIST);
                List<String> subtractSaleListUniqueList = new ArrayList<>();
                if (ObjectUtil.isEmpty(syncList)) {
                    subtractFlag = true;
                    subtractSaleListUniqueList.add(saleListSyncDto.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getSaleListUniqueList().stream().map(BusSaleListMonitorSettledParams::getSaleListUnique).collect(Collectors.toList()).contains(saleListSyncDto.getSaleListUnique()))) {
                        subtractFlag = true;
                        subtractSaleListUniqueList.add(saleListSyncDto.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                    }
                }
                if (subtractFlag) {
                    subtractList.add(subtractParams);
                }
            }
        }
    }

    /**
     * 比较同步退单差异
     * @param addList
     * @param subtractList
     * @param syncList
     * @param returnListSyncList
     */
    private static void compareSyncReturnList(List<SyncReturnListAddParams> addList,List<SyncReturnListSubtractParams> subtractList,List<BusSaleListMonitorSyncParams> syncList,List<ReturnListSyncDto> returnListSyncList){
        if (ObjectUtil.isNotEmpty(syncList)) {
            for (BusSaleListMonitorSyncParams syncParams : syncList) {
                SyncReturnListAddParams addParams = new SyncReturnListAddParams();
                addParams.setShopUnique(syncParams.getShopUnique());
                addParams.setShopName(syncParams.getShopName());
                List<String> addReturnListUniqueList = new ArrayList<>();
                boolean addFlag = false;
                if (ObjectUtil.isNotEmpty(returnListSyncList)) {
                    if (returnListSyncList.stream().anyMatch(s -> s.getShopUnique().equals(syncParams.getShopUnique()))) {
                        List<String> salelist = returnListSyncList.stream().filter(s -> s.getShopUnique().equals(syncParams.getShopUnique())).map(ReturnListSyncDto::getSaleListUnique).collect(Collectors.toList());
                        for (BusSaleListMonitorSettledReturnParams busSaleListMonitorSettledReturnParams : syncParams.getReturnListUniqueList()) {
                            if (salelist.stream().noneMatch(busSaleListMonitorSettledReturnParams.getSaleListUnique()::equals)) {
                                addFlag = true;
                                addReturnListUniqueList.add(busSaleListMonitorSettledReturnParams.getSaleListUnique());
                            }
                        }
                    } else {
                        for (BusSaleListMonitorSettledReturnParams busSaleListMonitorSettledReturnParams : syncParams.getReturnListUniqueList()) {
                            addFlag = true;
                            addReturnListUniqueList.add(busSaleListMonitorSettledReturnParams.getSaleListUnique());
                        }
                    }
                } else {
                    for (BusSaleListMonitorSettledReturnParams busSaleListMonitorSettledReturnParams : syncParams.getReturnListUniqueList()) {
                        addFlag = true;
                        addReturnListUniqueList.add(busSaleListMonitorSettledReturnParams.getSaleListUnique());
                    }
                }
                if (addFlag) {
                    addParams.setSaleListUniqueList(addReturnListUniqueList);
                    addList.add(addParams);
                }
            }
        }
        for (ReturnListSyncDto returnListSyncDto: returnListSyncList) {
            boolean subtractFlag = false;
            if (subtractList.stream().anyMatch(s -> s.getShopUnique().equals(returnListSyncDto.getShopUnique()))) {
                SyncReturnListSubtractParams subtractParams = subtractList.stream().filter(s -> s.getShopUnique().equals(returnListSyncDto.getShopUnique())).findFirst().get();
                List<String> saleListUniqueList = subtractParams.getSaleListUniqueList();
                if (ObjectUtil.isEmpty(syncList)) {
                    saleListUniqueList.add(returnListSyncDto.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(saleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getReturnListUniqueList().stream().map(BusSaleListMonitorSettledReturnParams::getSaleListUnique).collect(Collectors.toList()).contains(returnListSyncDto.getSaleListUnique()))) {
                        saleListUniqueList.add(returnListSyncDto.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(saleListUniqueList);
                    }
                }
            } else {
                SyncReturnListSubtractParams subtractParams = new SyncReturnListSubtractParams();
                subtractParams.setShopUnique(returnListSyncDto.getShopUnique());
                subtractParams.setShopName(returnListSyncDto.getShopName());
                subtractParams.setSaleListUniqueList(Collections.EMPTY_LIST);
                List<String> subtractSaleListUniqueList = new ArrayList<>();
                if (ObjectUtil.isEmpty(syncList)) {
                    subtractFlag = true;
                    subtractSaleListUniqueList.add(returnListSyncDto.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getReturnListUniqueList().stream().map(BusSaleListMonitorSettledReturnParams::getSaleListUnique).collect(Collectors.toList()).contains(returnListSyncDto.getSaleListUnique()))) {
                        subtractFlag = true;
                        subtractSaleListUniqueList.add(returnListSyncDto.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                    }
                }
                if (subtractFlag) {
                    subtractList.add(subtractParams);
                }
            }
        }
    }

    /**
     * 比较结算订单差异
     * @param addList
     * @param subtractList
     * @param syncList
     * @param settledSaleList
     */
    private void compareSettledSaleList(List<SettledSaleListAddParams> addList,List<SettledSaleListSubtractParams> subtractList,List<BusSaleListMonitorSyncParams> syncList, List<BusSaleListEntity> settledSaleList) {
        if (ObjectUtil.isNotEmpty(syncList)) {
            for (BusSaleListMonitorSyncParams syncParams : syncList) {
                SettledSaleListAddParams addParams = new SettledSaleListAddParams();
                addParams.setShopUnique(syncParams.getShopUnique());
                addParams.setShopName(syncParams.getShopName());
                List<String> addSaleListUniqueList = new ArrayList<>();
                boolean addFlag = false;
                if (settledSaleList.stream().anyMatch(s -> s.getShopUnique().equals(syncParams.getShopUnique()))) {
                    List<String> salelist = settledSaleList.stream().filter(s -> s.getShopUnique().equals(syncParams.getShopUnique())).map(BusSaleListEntity::getSaleListUnique).collect(Collectors.toList());
                    for (BusSaleListMonitorSettledParams busSaleListMonitorSettledParams : syncParams.getSaleListUniqueList()) {
                        if (busSaleListMonitorSettledParams.isSettledFlag()) {
                            if (salelist.stream().noneMatch(busSaleListMonitorSettledParams.getSaleListUnique()::equals)) {
                                addFlag = true;
                                addSaleListUniqueList.add(busSaleListMonitorSettledParams.getSaleListUnique());
                            }
                        }
                    }
                } else {
                    for (BusSaleListMonitorSettledParams busSaleListMonitorSettledParams : syncParams.getSaleListUniqueList()) {
                        if (busSaleListMonitorSettledParams.isSettledFlag()) {
                            addFlag = true;
                            addSaleListUniqueList.add(busSaleListMonitorSettledParams.getSaleListUnique());
                        }
                    }
                }
                if (addFlag) {
                    addParams.setSaleListUniqueList(addSaleListUniqueList);
                    addList.add(addParams);
                }
            }
        }

        // 查询多余同步订单
        for (BusSaleListEntity busSaleListEntity : settledSaleList) {
            boolean subtractFlag = false;
            if (subtractList.stream().anyMatch(s -> s.getShopUnique().equals(busSaleListEntity.getShopUnique()))) {
                SettledSaleListSubtractParams subtractParams = subtractList.stream().filter(s -> s.getShopUnique().equals(busSaleListEntity.getShopUnique())).findFirst().get();
                List<String> saleListUniqueList = subtractParams.getSaleListUniqueList();
                if (ObjectUtil.isEmpty(syncList)) {
                    saleListUniqueList.add(busSaleListEntity.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(saleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getSaleListUniqueList().stream().map(BusSaleListMonitorSettledParams::getSaleListUnique).collect(Collectors.toList()).contains(busSaleListEntity.getSaleListUnique()))) {
                        saleListUniqueList.add(busSaleListEntity.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(saleListUniqueList);
                    } else {
                        BusSaleListMonitorSyncParams busSaleListMonitorSyncParams = syncList.stream().filter(s -> s.getShopUnique().equals(busSaleListEntity.getShopUnique())).findFirst().get();
                        if (!busSaleListMonitorSyncParams.getSaleListUniqueList().stream().filter(v -> v.getSaleListUnique().equals(busSaleListEntity.getSaleListUnique())).findFirst().get().isSettledFlag()) {
                            saleListUniqueList.add(busSaleListEntity.getSaleListUnique());
                            subtractParams.setSaleListUniqueList(saleListUniqueList);
                        }
                    }
                }
            } else {
                SettledSaleListSubtractParams subtractParams = new SettledSaleListSubtractParams();
                subtractParams.setShopUnique(busSaleListEntity.getShopUnique());
                subtractParams.setShopName(busSaleListEntity.getShopName());
                subtractParams.setSaleListUniqueList(Collections.EMPTY_LIST);
                List<String> subtractSaleListUniqueList = new ArrayList<>();
                if (ObjectUtil.isEmpty(syncList)) {
                    subtractFlag = true;
                    subtractSaleListUniqueList.add(busSaleListEntity.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getSaleListUniqueList().stream().map(BusSaleListMonitorSettledParams::getSaleListUnique).collect(Collectors.toList()).contains(busSaleListEntity.getSaleListUnique()))) {
                        subtractFlag = true;
                        subtractSaleListUniqueList.add(busSaleListEntity.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                    } else {
                        BusSaleListMonitorSyncParams busSaleListMonitorSyncParams = syncList.stream().filter(s -> s.getShopUnique().equals(busSaleListEntity.getShopUnique())).findFirst().get();
                        if (!busSaleListMonitorSyncParams.getSaleListUniqueList().stream().filter(v -> v.getSaleListUnique().equals(busSaleListEntity.getSaleListUnique())).findFirst().get().isSettledFlag()) {
                            subtractFlag = true;
                            subtractSaleListUniqueList.add(busSaleListEntity.getSaleListUnique());
                            subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                        }
                    }
                }
                if (subtractFlag) {
                    subtractList.add(subtractParams);
                }
            }
        }
    }

    /**
     * 比较结算退单差异
     * @param addList
     * @param subtractList
     * @param syncList
     * @param settledReturnList
     */
    private void compareSettledReturnList(List<SettledReturnListAddParams> addList,List<SettledReturnListSubtractParams> subtractList,List<BusSaleListMonitorSyncParams> syncList, List<BusReturnListEntity> settledReturnList) {
        if (ObjectUtil.isNotEmpty(syncList)) {
            for (BusSaleListMonitorSyncParams syncParams : syncList) {
                SettledReturnListAddParams addParams = new SettledReturnListAddParams();
                addParams.setShopUnique(syncParams.getShopUnique());
                addParams.setShopName(syncParams.getShopName());
                List<String> addSaleListUniqueList = new ArrayList<>();
                boolean addFlag = false;
                if (settledReturnList.stream().anyMatch(s -> s.getShopUnique().equals(syncParams.getShopUnique()))) {
                    List<String> salelist = settledReturnList.stream().filter(s -> s.getShopUnique().equals(syncParams.getShopUnique())).map(BusReturnListEntity::getSaleListUnique).collect(Collectors.toList());
                    for (BusSaleListMonitorSettledReturnParams busSaleListMonitorSettledReturnParams : syncParams.getReturnListUniqueList()) {
                        if (busSaleListMonitorSettledReturnParams.isSettledFlag()) {
                            if (salelist.stream().noneMatch(busSaleListMonitorSettledReturnParams.getSaleListUnique()::equals)) {
                                addFlag = true;
                                addSaleListUniqueList.add(busSaleListMonitorSettledReturnParams.getSaleListUnique());
                            }
                        }
                    }
                } else {
                    for (BusSaleListMonitorSettledReturnParams busSaleListMonitorSettledReturnParams : syncParams.getReturnListUniqueList()) {
                        if (busSaleListMonitorSettledReturnParams.isSettledFlag()) {
                            addFlag = true;
                            addSaleListUniqueList.add(busSaleListMonitorSettledReturnParams.getSaleListUnique());
                        }
                    }
                }
                if (addFlag) {
                    addParams.setSaleListUniqueList(addSaleListUniqueList);
                    addList.add(addParams);
                }
            }
        }

        // 查询多余同步订单
        for (BusReturnListEntity busReturnListEntity : settledReturnList) {
            boolean subtractFlag = false;
            if (subtractList.stream().anyMatch(s -> s.getShopUnique().equals(busReturnListEntity.getShopUnique()))) {
                SettledReturnListSubtractParams subtractParams = subtractList.stream().filter(s -> s.getShopUnique().equals(busReturnListEntity.getShopUnique())).findFirst().get();
                List<String> saleListUniqueList = subtractParams.getSaleListUniqueList();
                if (ObjectUtil.isEmpty(syncList)) {
                    saleListUniqueList.add(busReturnListEntity.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(saleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getReturnListUniqueList().stream().map(BusSaleListMonitorSettledReturnParams::getSaleListUnique).collect(Collectors.toList()).contains(busReturnListEntity.getSaleListUnique()))) {
                        saleListUniqueList.add(busReturnListEntity.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(saleListUniqueList);
                    } else {
                        BusSaleListMonitorSyncParams busSaleListMonitorSyncParams = syncList.stream().filter(s -> s.getShopUnique().equals(busReturnListEntity.getShopUnique())).findFirst().get();
                        if (!busSaleListMonitorSyncParams.getReturnListUniqueList().stream().filter(v -> v.getSaleListUnique().equals(busReturnListEntity.getSaleListUnique())).findFirst().get().isSettledFlag()) {
                            saleListUniqueList.add(busReturnListEntity.getSaleListUnique());
                            subtractParams.setSaleListUniqueList(saleListUniqueList);
                        }
                    }
                }
            } else {
                SettledReturnListSubtractParams subtractParams = new SettledReturnListSubtractParams();
                subtractParams.setShopUnique(busReturnListEntity.getShopUnique());
                subtractParams.setShopName(busReturnListEntity.getShopName());
                subtractParams.setSaleListUniqueList(Collections.EMPTY_LIST);
                List<String> subtractSaleListUniqueList = new ArrayList<>();
                if (ObjectUtil.isEmpty(syncList)) {
                    subtractFlag = true;
                    subtractSaleListUniqueList.add(busReturnListEntity.getSaleListUnique());
                    subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                } else {
                    if (syncList.stream().noneMatch(s -> s.getReturnListUniqueList().stream().map(BusSaleListMonitorSettledReturnParams::getSaleListUnique).collect(Collectors.toList()).contains(busReturnListEntity.getSaleListUnique()))) {
                        subtractFlag = true;
                        subtractSaleListUniqueList.add(busReturnListEntity.getSaleListUnique());
                        subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                    } else {
                        BusSaleListMonitorSyncParams busSaleListMonitorSyncParams = syncList.stream().filter(s -> s.getShopUnique().equals(busReturnListEntity.getShopUnique())).findFirst().get();
                        if (!busSaleListMonitorSyncParams.getReturnListUniqueList().stream().filter(v -> v.getSaleListUnique().equals(busReturnListEntity.getSaleListUnique())).findFirst().get().isSettledFlag()) {
                            subtractFlag = true;
                            subtractSaleListUniqueList.add(busReturnListEntity.getSaleListUnique());
                            subtractParams.setSaleListUniqueList(subtractSaleListUniqueList);
                        }
                    }
                }
                if (subtractFlag) {
                    subtractList.add(subtractParams);
                }
            }
        }
    }

    /**
     * 比较待结算
     * @param saleListMonitorTotalParamsList
     * @param billList
     * @param shopUniqueUnsettledList
     */
    private void compareSettledAmount(List<SaleListMonitorTotalParams> saleListMonitorTotalParamsList, List<BusShopBillEntity> billList, Set<Long> shopUniqueUnsettledList) {
        if (ObjectUtil.isNotEmpty(saleListMonitorTotalParamsList)) {
            for (SaleListMonitorTotalParams saleListMonitorTotalParams : saleListMonitorTotalParamsList) {
                if (ObjectUtil.isNotNull(saleListMonitorTotalParams.getTaxAmount()) && saleListMonitorTotalParams.getTaxAmount().compareTo(BigDecimal.ZERO) > 0) {
                    if (ObjectUtil.isEmpty(billList)) {
                        shopUniqueUnsettledList.add(saleListMonitorTotalParams.getShopUnique());
                    } else if (billList.stream().noneMatch(v -> ObjectUtil.equals(v.getShopUnique(), saleListMonitorTotalParams.getShopUnique()))) {
                        shopUniqueUnsettledList.add(saleListMonitorTotalParams.getShopUnique());
                    } else {
                        BigDecimal unsettledAmount = billList.stream().filter(v -> ObjectUtil.equals(v.getShopUnique(), saleListMonitorTotalParams.getShopUnique())).findFirst().get().getUnsettledAmount();
                        BigDecimal settledAmount = saleListMonitorTotalParams.getTaxAmount();
                        if (unsettledAmount.compareTo(settledAmount) != 0) {
                            shopUniqueUnsettledList.add(saleListMonitorTotalParams.getShopUnique());
                        }
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(billList)) {
            for (BusShopBillEntity busShopBillEntity : billList) {
                if (ObjectUtil.isEmpty(saleListMonitorTotalParamsList)) {
                    shopUniqueUnsettledList.add(busShopBillEntity.getShopUnique());
                } else if (saleListMonitorTotalParamsList.stream().noneMatch(v -> ObjectUtil.equals(v.getShopUnique(), busShopBillEntity.getShopUnique()))) {
                    shopUniqueUnsettledList.add(busShopBillEntity.getShopUnique());
                } else {
                    BigDecimal unsettledAmount = busShopBillEntity.getUnsettledAmount();
                    BigDecimal settledAmount = saleListMonitorTotalParamsList.stream().filter(v -> ObjectUtil.equals(v.getShopUnique(), busShopBillEntity.getShopUnique())).findFirst().get().getTaxAmount();
                    if (unsettledAmount.compareTo(settledAmount) != 0) {
                        shopUniqueUnsettledList.add(busShopBillEntity.getShopUnique());
                    }
                }
            }
        }
    }
}
