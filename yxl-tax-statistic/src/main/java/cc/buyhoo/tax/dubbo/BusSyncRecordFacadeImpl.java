package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusSyncRecordMapper;
import cc.buyhoo.tax.entity.BusSyncRecordEntity;
import cc.buyhoo.tax.enums.BusSyncStatusEnum;
import cc.buyhoo.tax.facade.BusSyncRecordFacade;
import cc.buyhoo.tax.facade.result.busSyncRecord.BusSyncRecordDto;
import cc.buyhoo.tax.facade.result.busSyncRecord.QueryBusSyncRecordResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description 同步记录
 * @ClassName BusSyncRecordFacadeImpl
 * <AUTHOR>
 * @Date 2024/6/13 16:25
 **/
@Service
@DubboService
@RequiredArgsConstructor
public class BusSyncRecordFacadeImpl implements BusSyncRecordFacade {

    private final BusSyncRecordMapper busSyncRecordMapper;

    @Override
    public Result<QueryBusSyncRecordResult> queryLastOne(Long companyId, Integer syncType) {
        LambdaQueryWrapper<BusSyncRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusSyncRecordEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusSyncRecordEntity::getCompanyId, companyId);
        queryWrapper.eq(BusSyncRecordEntity::getSyncType, syncType);
        queryWrapper.orderByDesc(BusSyncRecordEntity::getId);
        queryWrapper.last("limit 1");
        queryWrapper.select(BusSyncRecordEntity::getCompanyId, BusSyncRecordEntity::getEndTime);
        BusSyncRecordEntity entity = busSyncRecordMapper.selectOne(queryWrapper);
        QueryBusSyncRecordResult result = BeanUtil.toBean(entity, QueryBusSyncRecordResult.class);
        return Result.ok(result);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<BusSyncRecordDto> insertBusSyncRecord(BusSyncRecordDto dto) {
        BusSyncRecordEntity entity = new BusSyncRecordEntity();
        BeanUtil.copyProperties(dto, entity);
        entity.setStatus(BusSyncStatusEnum.FAIL.getCode());
        entity.setCreateTime(DateUtil.date());
        int n = busSyncRecordMapper.insert(entity);
        dto.setId(entity.getId());
        return n > 0 ? Result.ok(dto) : Result.fail();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result updateBusSyncRecord(BusSyncRecordDto dto) {
        BusSyncRecordEntity entity = new BusSyncRecordEntity();
        entity.setId(dto.getId());
        entity.setSaleListCount(dto.getSaleListCount());
        entity.setStatus(BusSyncStatusEnum.SUCCESS.getCode());
        int n = busSyncRecordMapper.updateById(entity);
        return n > 0 ? Result.ok() : Result.fail();
    }
}
