package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.facade.SysCompanyFacade;
import cc.buyhoo.tax.dao.SysCompanyMapper;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.enums.sys.CompanyTypeEnum;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyDto;
import cc.buyhoo.tax.facade.result.sysCompany.QueryBranchCompanyResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@DubboService
@Service
public class SysCompanyFacadeImpl implements SysCompanyFacade {

    @Autowired
    private SysCompanyMapper sysCompanyMapper;

    /**
     * 查询分公司
     * @return
     */
    @Override
    public Result<QueryBranchCompanyResult> queryBranchCompany() {
        //查询数据
        LambdaQueryWrapper<SysCompanyEntity> companyWrapper = new LambdaQueryWrapper<>();
        companyWrapper.ne(SysCompanyEntity::getCompanyType, CompanyTypeEnum.PLATFORM.getValue());
        companyWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyWrapper);

        //返回构建
        List<QueryBranchCompanyDto> dtoList = new ArrayList<>();
        for (SysCompanyEntity c : companyList) {
            QueryBranchCompanyDto dto = new QueryBranchCompanyDto();
            dto.setId(c.getId());
            dto.setCompanyName(c.getCompanyName());
            dto.setPayFeeRate(c.getPayFeeRate());
            dto.setStatisticsStatus(c.getStatisticsStatus());
            dtoList.add(dto);
        }
        QueryBranchCompanyResult result = new QueryBranchCompanyResult();
        result.setCompanyList(dtoList);

        return Result.ok(result);
    }
}
