package cc.buyhoo.tax.dubbo;

import cc.buyhoo.tax.dao.TruncateOrderDataMapper;
import cc.buyhoo.tax.facade.TruncateOrderDataFacade;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@DubboService
public class TruncateOrderDataFacadeImpl implements TruncateOrderDataFacade {

    @Autowired
    private TruncateOrderDataMapper truncateOrderDataMapper;

    /**
     * 清空订单数据
     */
    @Override
    public void truncateOrdeData() {

        truncateOrderDataMapper.truncate1();
        truncateOrderDataMapper.truncate2();
        truncateOrderDataMapper.truncate3();
        truncateOrderDataMapper.truncate4();
        truncateOrderDataMapper.truncate5();
        truncateOrderDataMapper.truncate6();
        truncateOrderDataMapper.truncate7();
        truncateOrderDataMapper.truncate8();
        truncateOrderDataMapper.truncate9();
        truncateOrderDataMapper.truncate10();
        truncateOrderDataMapper.truncate11();
        truncateOrderDataMapper.truncate12();
        truncateOrderDataMapper.truncate13();
        truncateOrderDataMapper.truncate14();
        truncateOrderDataMapper.truncate15();
        truncateOrderDataMapper.truncate16();
        truncateOrderDataMapper.truncate17();
        truncateOrderDataMapper.truncate18();
        truncateOrderDataMapper.truncate19();
        truncateOrderDataMapper.truncate20();
        truncateOrderDataMapper.truncate21();

    }
}
