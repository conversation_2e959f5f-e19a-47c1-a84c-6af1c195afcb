package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusInventoryBatchMapper;
import cc.buyhoo.tax.dao.BusReturnListMapper;
import cc.buyhoo.tax.dao.BusSaleListMapper;
import cc.buyhoo.tax.entity.BusShopBillEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import cc.buyhoo.tax.facade.BusShopBillFacade;
import cc.buyhoo.tax.facade.BusShopSaleListMonitorFacade;
import cc.buyhoo.tax.facade.BusShopServiceFeeFacade;
import cc.buyhoo.tax.facade.params.busShopBill.CmbcTransferParams;
import cc.buyhoo.tax.facade.params.busShopBill.UpdateUnsettledAmountRemoteParams;
import cc.buyhoo.tax.facade.params.busShopSaleListMonitor.BusSaleListMonitorDetailInsertParams;
import cc.buyhoo.tax.facade.params.busShopSaleListMonitor.BusSaleListMonitorInsertParams;
import cc.buyhoo.tax.facade.params.busShopServiceFee.UpdateShopServiceFeeParams;
import cc.buyhoo.tax.params.busShopBill.UpdateShopUnsettledAmountParams;
import cc.buyhoo.tax.service.BusShopBillService;
import cc.buyhoo.tax.service.BusShopService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@Service
public class BusShopBillFacadeImpl implements BusShopBillFacade {

    @Autowired
    private BusShopBillService busShopBillService;
    @Resource
    private BusSaleListMapper busSaleListMapper;
    @Resource
    private BusReturnListMapper busReturnListMapper;
    @Resource
    private BusInventoryBatchMapper busInventoryBatchMapper;
    @Resource
    private BusShopService busShopService;
    @DubboReference
    private BusShopServiceFeeFacade busShopServiceFeeFacade;
    @DubboReference
    private BusShopSaleListMonitorFacade busShopSaleListMonitorFacade;

    /**
     * 实现转账的功能
     * @param params
     */
    @Override
    public void cmbcTransfer(CmbcTransferParams params) {
        List<BusShopBillEntity> cmbcBill = new ArrayList<>();
        SysCompanyEntity sysCompanyEntity = new SysCompanyEntity();
        log.info("传过来的参数信息{}", params.getCmbcBill());
        BeanUtil.copyProperties(params.getCmbcBill(), cmbcBill);
        log.info("复制后的参数信息{}",cmbcBill);
        List<cc.buyhoo.tax.facade.params.busShopBill.BusShopBillEntity> bills = params.getCmbcBill();
        for (cc.buyhoo.tax.facade.params.busShopBill.BusShopBillEntity b: bills) {
            BusShopBillEntity bs = new BusShopBillEntity();
            BeanUtil.copyProperties(b, bs);
            cmbcBill.add(bs);
        }
        log.info("循环处理后的数据为{}", cmbcBill);

        BeanUtil.copyProperties(params.getCompany(), sysCompanyEntity);
        busShopBillService.cmbcTransfer(params.getTransferMoney(), params.getRemark(), params.getUserId(), cmbcBill, sysCompanyEntity);
    }

    /**
     * 更新待结算金额
     * @param params
     */
    @Override
    public void updateUnsettledAmount(UpdateUnsettledAmountRemoteParams params) {
        UpdateShopUnsettledAmountParams updateShopUnsettledAmountParams = new UpdateShopUnsettledAmountParams();
        BeanUtils.copy(params,updateShopUnsettledAmountParams);
        busShopBillService.updateShopUnsettledAmount(updateShopUnsettledAmountParams);
    }

    /**
     * 统计店铺指定时间段内的订单信息
     * @param companyId
     * @param startTime
     * @param endTime
     */
    @Override
    public void statisticBill(Long companyId, Date startTime, Date endTime) {
        if (!(ObjectUtil.isNotEmpty(startTime) && ObjectUtil.isNotEmpty(endTime))) {
            Date yesterdayTime = DateUtil.offsetDay(DateUtil.date(), -1);
            startTime = DateUtil.beginOfDay(yesterdayTime);
            endTime = DateUtil.endOfDay(yesterdayTime);
        }
        List<Map<String, Object>> list = busSaleListMapper.selectOnlineMoney(companyId, startTime, endTime);
        log.info("店铺结算金额：{}", JSONUtil.toJsonStr(list));
        List<Map<String, Object>> retList = busReturnListMapper.selectRetOnlineMoney(companyId, startTime, endTime);
        log.info("店铺退款金额：{}", JSONUtil.toJsonStr(retList));
        /*List<Map<String, Object>> serviceFeeList = busInventoryBatchMapper.selectShopServiceFee(companyId, DateUtil.formatDate(startTime));
        Map<Long, BigDecimal> shopServiceFeeMap = serviceFeeList.stream().collect(Collectors.toMap(k -> (Long) k.get("shop_unique"), v -> (BigDecimal) v.getOrDefault("profit_money", BigDecimal.ZERO)));
        log.info("店铺服务费：{}", shopServiceFeeMap);*/
        List<BusSaleListMonitorDetailInsertParams> busSaleListMonitorDetailInsertParamsList = new ArrayList<>();
        Map<Long, BusShopEntity> busShopEntityMaps = new HashMap<>();
        if (CollectionUtil.isNotEmpty(list)) {
            //涉及到的店铺
            List<String> shopUniques = list.stream().map(item -> String.valueOf(item.get("shop_unique"))).collect(Collectors.toList());
            List<BusShopEntity> busShopEntities =  busShopService.selectBusShopByshopUniques(shopUniques);
            busShopEntityMaps = busShopEntities.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, v -> v));
        }
        //商户分得的利润
        Map<Long,UpdateShopUnsettledAmountParams> inMoney = new HashMap<>();
        for (Map<String, Object> sl : list) {
            Long shopUnique = (Long) sl.get("shop_unique");
            BigDecimal totalMoney = (BigDecimal) sl.getOrDefault("pay_money", BigDecimal.ZERO);
            if (ObjectUtil.isNotEmpty(sl.get("has_service_fee")) && ObjectUtil.equals(0, Integer.valueOf(String.valueOf(sl.get("has_service_fee"))))) {
                totalMoney = (BigDecimal) sl.getOrDefault("profitTotal", BigDecimal.ZERO);
            }
            BigDecimal totalFee = (BigDecimal) sl.getOrDefault("fee", BigDecimal.ZERO);
            totalMoney = NumberUtil.sub(totalMoney, totalFee);
            for (Map<String, Object> ret : retList) {
                Long retShopUnique = (Long) sl.get("shop_unique");
                BigDecimal totalRetMoney = (BigDecimal) ret.getOrDefault("ret_total_money", BigDecimal.ZERO);
                BigDecimal totalRetFee = (BigDecimal) ret.getOrDefault("ret_delfee", BigDecimal.ZERO);
                BigDecimal totalRetServiceFee = (BigDecimal) ret.getOrDefault("ret_service_fee", BigDecimal.ZERO);
                if (ObjectUtil.equals(shopUnique, retShopUnique)) {
                    totalMoney = NumberUtil.sub(totalMoney, totalRetMoney);
                    totalMoney = NumberUtil.add(totalMoney, totalRetFee, totalRetServiceFee);
                    totalFee = NumberUtil.sub(totalFee, totalRetFee, totalRetServiceFee);
                    break;
                }
            }
            /*//减去成本费
            totalMoney = NumberUtil.sub(totalMoney, shopServiceFeeMap.getOrDefault(shopUnique, BigDecimal.ZERO));*/
            BusShopEntity busShopEntity = busShopEntityMaps.get(shopUnique);
            log.info("店铺信息：{}", JSONUtil.toJsonStr(busShopEntity));
            if (ObjectUtil.isNotEmpty(busShopEntity)) {
                //再此计算合作商分润金额 ，例：当前商户配置了合作商，且配置合作商成本占比 10%，
                //   当前的最终利润*10%给合作商，若配置的合作商在此次计算中，则将分得的10%利润加到此次计算中，若此次计算并不包含配置的合作商，则要最终新建账单，
                BigDecimal cooperatorCostProportion = new BigDecimal(0);
                if (ObjectUtil.isNotEmpty(busShopEntity.getCooperatorCostProportion())){
                    cooperatorCostProportion = busShopEntity.getCooperatorCostProportion().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                }
                //分出去的利润
                BigDecimal outMoney = totalMoney.multiply(cooperatorCostProportion);
                totalMoney= totalMoney.subtract(outMoney);
                Long supplierNo = busShopEntity.getSupplierNo();
                //如果存在，则在原来基础上加上这个值，如果不存在则，新建一条数据，主要用于最终的结算
                if (inMoney.containsKey(supplierNo)){
                    inMoney.get(supplierNo).setOnlineMoney(inMoney.get(supplierNo).getOnlineMoney().add(outMoney));
                }else {
                    UpdateShopUnsettledAmountParams updateShopUnsettledAmountParams = new UpdateShopUnsettledAmountParams();
                    updateShopUnsettledAmountParams.setShopUnique(supplierNo);
                    updateShopUnsettledAmountParams.setBusId(0L);
                    updateShopUnsettledAmountParams.setCompanyId(companyId);
                    updateShopUnsettledAmountParams.setPayType(1);
                    updateShopUnsettledAmountParams.setOnlineMoney(outMoney);
                    inMoney.put(supplierNo,updateShopUnsettledAmountParams);
                }
            }
            //更新结算金额
            UpdateShopUnsettledAmountParams updateShopUnsettledAmountParams = new UpdateShopUnsettledAmountParams();
            updateShopUnsettledAmountParams.setShopUnique(shopUnique);
            updateShopUnsettledAmountParams.setBusId(0L);
            updateShopUnsettledAmountParams.setCompanyId(companyId);
            updateShopUnsettledAmountParams.setPayType(1);
            updateShopUnsettledAmountParams.setOnlineMoney(totalMoney);
            busShopBillService.updateShopUnsettledAmount(updateShopUnsettledAmountParams);
            updateServiceFee(companyId, totalFee, shopUnique);

            //将结算金额保存到订单监控表中
            BusSaleListMonitorDetailInsertParams busSaleListMonitorDetailInsertParams = new BusSaleListMonitorDetailInsertParams();
            busSaleListMonitorDetailInsertParams.setShopUnique(updateShopUnsettledAmountParams.getShopUnique());
            busSaleListMonitorDetailInsertParams.setCompanyId(updateShopUnsettledAmountParams.getCompanyId());
            busSaleListMonitorDetailInsertParams.setTaxAmount(updateShopUnsettledAmountParams.getOnlineMoney());
            busSaleListMonitorDetailInsertParams.setStatDate(DateUtil.format(endTime,"yyyy-MM-dd"));
            busSaleListMonitorDetailInsertParamsList.add(busSaleListMonitorDetailInsertParams);
        }
        log.info("inMoney================>{}",JSONUtil.toJsonStr(inMoney));
        if (CollectionUtil.isNotEmpty(inMoney)){
            for (UpdateShopUnsettledAmountParams value : inMoney.values()) {
                busShopBillService.updateShopUnsettledAmount(value);
            }
        }

        if (CollectionUtil.isNotEmpty(list)) {
            busSaleListMapper.updateSettledStatus(companyId, startTime, endTime);
        }
        if (CollectionUtil.isNotEmpty(retList)) {
            busReturnListMapper.updateSettledStatus(companyId, startTime, endTime);
        }
        if (CollectionUtil.isNotEmpty(busSaleListMonitorDetailInsertParamsList)) {
            ThreadUtil.execAsync(() -> insertSaleListMonitor(busSaleListMonitorDetailInsertParamsList));
        }
    }

    /**
     * 更新服务费
     * @param companyId
     * @param totalServiceFee
     * @param shopUnique
     */
    private void updateServiceFee(Long companyId, BigDecimal totalServiceFee, Long shopUnique) {
        UpdateShopServiceFeeParams params = new UpdateShopServiceFeeParams();
        params.setServiceFee(totalServiceFee);
        params.setShopUnique(shopUnique);
        params.setCompanyId(companyId);
        busShopServiceFeeFacade.updateShopServiceFee(params);
    }

    /**
     * 调用远程服务存储结算金额
     * @param busSaleListMonitorDetailInsertParamsList
     */
    private void insertSaleListMonitor(List<BusSaleListMonitorDetailInsertParams> busSaleListMonitorDetailInsertParamsList){
        BusSaleListMonitorInsertParams params = new BusSaleListMonitorInsertParams();
        params.setList(busSaleListMonitorDetailInsertParamsList);
        busShopSaleListMonitorFacade.insertSaleListMonitor(params);
    }
}
