package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.facade.BusShopFacade;
import cc.buyhoo.tax.facade.params.busShop.QueryBindShopParams;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopDto;
import cc.buyhoo.tax.facade.result.busShop.QueryBindShopResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@DubboService
@Service
public class BusShopFacadeImpl implements BusShopFacade {

    @Resource
    private BusShopMapper busShopMapper;

    /**
     * 根据公司查询所有的绑定店铺
     * @return
     */
    @Override
    public Result<QueryBindShopResult> queryBindShop() {
        LambdaQueryWrapper<BusShopEntity> busShopWrapper = new LambdaQueryWrapper<>();
        busShopWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        busShopWrapper.eq(BusShopEntity::getBindStatus,1); //已绑定
        List<BusShopEntity> busShopList = busShopMapper.selectList(busShopWrapper);

        //参数构建
        List<QueryBindShopDto> dtoList = new ArrayList<>();
        for (BusShopEntity bse : busShopList) {
            QueryBindShopDto dto = new QueryBindShopDto();
            BeanUtils.copyProperties(bse,dto);

            dtoList.add(dto);
        }
        QueryBindShopResult result = new QueryBindShopResult();
        result.setShopList(dtoList);

        return Result.ok(result);
    }

    /**
     * 根据公司查询所有的绑定店铺
     * @return
     */
    @Override
    public Result<QueryBindShopResult> queryBindShopByCompany(QueryBindShopParams params) {
        LambdaQueryWrapper<BusShopEntity> busShopWrapper = new LambdaQueryWrapper<>();
        busShopWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        busShopWrapper.eq(BusShopEntity::getBindStatus,1); //已绑定
        busShopWrapper.eq(BusShopEntity::getCompanyId,params.getCompanyId());
        if (ObjectUtil.isNotNull(params.getSyncCanyinData())) {
            busShopWrapper.eq(BusShopEntity::getSyncCanyinData, params.getSyncCanyinData());
        }
        if (ObjectUtil.isNotNull(params.getSyncBuyhooData())) {
            busShopWrapper.eq(BusShopEntity::getSyncBuyhooData, params.getSyncBuyhooData());
        }
        List<BusShopEntity> busShopList = busShopMapper.selectList(busShopWrapper);

        //参数构建
        List<QueryBindShopDto> dtoList = new ArrayList<>();
        for (BusShopEntity bse : busShopList) {
            QueryBindShopDto dto = new QueryBindShopDto();
            BeanUtils.copyProperties(bse,dto);

            dtoList.add(dto);
        }
        QueryBindShopResult result = new QueryBindShopResult();
        result.setShopList(dtoList);

        return Result.ok(result);
    }

    @Override
    public QueryBindShopDto queryByShopUnique(Long shopUnique) {
        LambdaQueryWrapper<BusShopEntity> busShopWrapper = new LambdaQueryWrapper<>();
        busShopWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        busShopWrapper.eq(BusShopEntity::getBindStatus,1); //已绑定
        busShopWrapper.eq(BusShopEntity::getShopUnique, shopUnique);
        busShopWrapper.last("limit 1");
        BusShopEntity busShop = busShopMapper.selectOne(busShopWrapper);
        return BeanUtil.toBean(busShop, QueryBindShopDto.class);
    }
}
