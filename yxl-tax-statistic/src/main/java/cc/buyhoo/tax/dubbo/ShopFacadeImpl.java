package cc.buyhoo.tax.dubbo;

import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.facade.ShopFacade;
import cc.buyhoo.tax.facade.params.shop.Shop;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName GoodsFacadeImpl
 * <AUTHOR>
 * @Date 2023/7/29 14:48
 **/
@Slf4j
@DubboService
@Service
public class ShopFacadeImpl implements ShopFacade {

    @Resource
    private BusShopMapper busShopMapper;

    @Override
    public void syncShop(List<Shop> shopList) {
        if (CollUtil.isNotEmpty(shopList) && shopList.size() > 0) {
            Map<Long, Shop> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getShopUnique, v->v));
            List<BusShopEntity> list = busShopMapper.selectList(null);
            List<BusShopEntity> updateList = new ArrayList<>();
            for (BusShopEntity entity : list) {
                if (ObjectUtil.isNotEmpty(shopMap.get(entity.getShopUnique()))) {
                    Shop shop = shopMap.get(entity.getShopUnique());
                    entity.setShopName(shop.getShopName());
                    entity.setShopPhone(shop.getShopPhone());
                    entity.setShopType(shop.getShopType());
                    entity.setAddress(shop.getShopAddressDetail());
                    updateList.add(entity);
                }
            }
            if (updateList.size() > 0) {
                busShopMapper.updateBatchById(updateList);
            }
        }
    }
}
