package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusGoodsCategoryMapper;
import cc.buyhoo.tax.dao.BusGoodsMapper;
import cc.buyhoo.tax.entity.BusGoodsCategoryEntity;
import cc.buyhoo.tax.entity.BusGoodsEntity;
import cc.buyhoo.tax.facade.GoodsFacade;
import cc.buyhoo.tax.facade.params.goods.QueryByGoodsIdDto;
import cc.buyhoo.tax.facade.params.goods.QueryByGoodsIdResult;
import cc.buyhoo.tax.facade.params.goodsList.Goods;
import cc.buyhoo.tax.facade.result.goods.QueryByGoodsIdParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName GoodsFacadeImpl
 * <AUTHOR>
 * @Date 2023/7/29 14:48
 **/
@Slf4j
@DubboService
@Service
public class GoodsFacadeImpl implements GoodsFacade {

    @Resource
    private BusGoodsMapper busGoodsMapper;

    @Resource
    private BusGoodsCategoryMapper busGoodsCategoryMapper;

    @Override
    @Async("async")
    @Transactional(rollbackFor = {Exception.class})
    public void syncGoods(Long companyId, Long shopUnique, List<Goods> goodsList) {
        List<BusGoodsEntity> list = busGoodsMapper.selectList(new LambdaQueryWrapper<BusGoodsEntity>().select(BusGoodsEntity::getId, BusGoodsEntity::getGoodsId).eq(BusGoodsEntity::getShopUnique, shopUnique).eq(BusGoodsEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
        Map<Long, Long> goodsIdMap = list.stream().collect(Collectors.toMap(BusGoodsEntity::getGoodsId, BusGoodsEntity::getId));
        List<BusGoodsEntity> newGoods = new ArrayList<>(), updateGoods = new ArrayList<>();
        Date current = DateUtil.date();
        List<Long> newGoodsIds = new ArrayList<>();

        //查询默认分类
        LambdaQueryWrapper<BusGoodsCategoryEntity> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(BusGoodsCategoryEntity::getCategoryType,1); //默认分类
        categoryWrapper.eq(BusGoodsCategoryEntity::getCompanyId,companyId);
        List<BusGoodsCategoryEntity> cateList = busGoodsCategoryMapper.selectList(categoryWrapper);
        Long cateId = cateList.stream().filter(c -> c.getParentId() == 0).map(BusGoodsCategoryEntity::getId).findFirst().orElse(-1L);
        Long cateTwoId = cateList.stream().filter(c -> c.getParentId() != 0).map(BusGoodsCategoryEntity::getId).findFirst().orElse(-1L);

        for (Goods g : goodsList) {
            Long id = goodsIdMap.get(g.getGoodsId());
            BusGoodsEntity entity = new BusGoodsEntity();
            BeanUtil.copyProperties(g, entity);
            entity.setCompanyId(companyId);
            if (null == id) {
                entity.setCategoryId(cateId);
                entity.setCategoryTwoId(cateTwoId);
                entity.setCreateTime(current);
                newGoods.add(entity);
            } else {
                entity.setId(id);
                entity.setModifyTime(current);
                updateGoods.add(entity);
            }
            newGoodsIds.add(g.getGoodsId());
        }
        List<Long> oldGoodsIds = new ArrayList<>(goodsIdMap.keySet());
        List<Long> deleteIds = CollectionUtil.subtractToList(oldGoodsIds, newGoodsIds);

        if (CollUtil.isNotEmpty(newGoods)) {
            busGoodsMapper.insertBatch(newGoods);
        }
        if (CollUtil.isNotEmpty(updateGoods)) {
            busGoodsMapper.updateBatchById(updateGoods);
        }
        List<BusGoodsEntity> deleteList = new ArrayList<>();
        if (CollUtil.isNotEmpty(deleteIds)) {
            deleteList = busGoodsMapper.selectList(new LambdaQueryWrapper<BusGoodsEntity>().in(BusGoodsEntity::getGoodsId, deleteIds));
            if (CollUtil.isNotEmpty(deleteList)) {
                deleteList.forEach(v -> {
                    v.setModifyTime(current);
                    v.setDelFlag(DelFlagEnum.DELETED.getCode());
                });
                busGoodsMapper.updateBatchById(deleteList);
            }
        }
        log.info("--------同步百货商家端商品数据详情,供应商:{}----新增数据：{}---------更新数据:{}-----------------删除数据:{}-----------------", shopUnique, newGoods.size(), updateGoods.size(), deleteList.size());
    }

    /**
     * 根据goods_id查询
     * @param params
     * @return
     */
    @Override
    public Result<QueryByGoodsIdResult> queryByGoodsId(QueryByGoodsIdParams params) {
        LambdaQueryWrapper<BusGoodsEntity> goodsWrapper = new LambdaQueryWrapper<>();
        goodsWrapper.in(BusGoodsEntity::getGoodsId,params.getGoodsIdList());
        List<BusGoodsEntity> goodsEList = busGoodsMapper.selectList(goodsWrapper);
        List<QueryByGoodsIdDto> dtoList = BeanUtils.copyList(goodsEList, QueryByGoodsIdDto.class);

        QueryByGoodsIdResult result = new QueryByGoodsIdResult();
        result.setGoodsList(dtoList);

        return Result.ok(result);
    }
}
