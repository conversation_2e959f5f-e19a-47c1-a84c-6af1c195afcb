package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusInventoryBatchDetailMapper;
import cc.buyhoo.tax.dao.BusInventoryBatchMapper;
import cc.buyhoo.tax.entity.BusInventoryBatchDetailEntity;
import cc.buyhoo.tax.entity.BusInventoryBatchEntity;
import cc.buyhoo.tax.enums.InventoryTypeEnum;
import cc.buyhoo.tax.facade.BusInventoryBatchFacade;
import cc.buyhoo.tax.facade.params.busInventoryBatch.InventoryBatchDetailRemote;
import cc.buyhoo.tax.facade.params.busInventoryBatch.InventoryBatchRemote;
import cc.buyhoo.tax.facade.params.busInventoryBatch.SaveInventoryBatchRemoteParams;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@DubboService
@Service
public class BusInventoryBatchFacadeImpl implements BusInventoryBatchFacade {

    @Autowired
    private BusInventoryBatchMapper batchMapper;

    @Autowired
    private BusInventoryBatchDetailMapper batchDetailMapper;


    /**
     * 保存批次单
     * @param params
     */
    @Override
    public void saveInventoryBatch(SaveInventoryBatchRemoteParams params) {
        InventoryBatchRemote batch = params.getBatch();

        //保存批次单
        BusInventoryBatchEntity batchEntity = new BusInventoryBatchEntity();
        BeanUtils.copy(batch,batchEntity);
        batchEntity.setBatchDate(DateUtil.format(DateUtil.offsetDay(new Date(), -1), DatePattern.NORM_DATE_PATTERN));
        batchEntity.setInventoryType(InventoryTypeEnum.SHOP_SALE.getInventoryType());
        batchMapper.insert(batchEntity);

        //保存批次详情
        List<InventoryBatchDetailRemote> baatchDetailList = params.getBaatchDetailList();
        List<BusInventoryBatchDetailEntity> detailList = BeanUtils.copyList(baatchDetailList, BusInventoryBatchDetailEntity.class);
        for (BusInventoryBatchDetailEntity d : detailList) {
            d.setBatchId(batchEntity.getId());
            d.setOrderId(batchEntity.getOrderId());
        }
        batchDetailMapper.insertBatch(detailList);
    }
}
