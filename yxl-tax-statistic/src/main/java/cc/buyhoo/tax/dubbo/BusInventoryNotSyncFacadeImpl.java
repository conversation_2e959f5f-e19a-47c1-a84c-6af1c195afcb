package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.utils.BeanUtils;
import cc.buyhoo.tax.dao.BusInventoryNotSyncMapper;
import cc.buyhoo.tax.entity.BusInventoryNotSyncEntity;
import cc.buyhoo.tax.facade.BusInventoryNotSyncFacade;
import cc.buyhoo.tax.facade.params.busInventoryNotSync.InventoryNotSyncInsertBatchDto;
import cc.buyhoo.tax.facade.params.busInventoryNotSync.InventoryNotSyncInsertBatchParams;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@DubboService
@Service
public class BusInventoryNotSyncFacadeImpl implements BusInventoryNotSyncFacade {

    @Autowired
    private BusInventoryNotSyncMapper inventoryNotSyncMapper;

    /**
     * 批量保存数据
     * @param params
     */
    @Override
    public void insertBatch(InventoryNotSyncInsertBatchParams params) {
        List<InventoryNotSyncInsertBatchDto> insertList = params.getInsertList();
        List<BusInventoryNotSyncEntity> notSyncEntityList = BeanUtils.copyList(insertList, BusInventoryNotSyncEntity.class);
        inventoryNotSyncMapper.insertBatch(notSyncEntityList);
    }
}
