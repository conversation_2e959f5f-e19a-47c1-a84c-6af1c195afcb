package cc.buyhoo.tax.dubbo;

import cc.buyhoo.tax.dao.SysMarketMapper;
import cc.buyhoo.tax.facade.SysMarketFacade;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @ClassName SysMarketFacadeImpl
 * <AUTHOR>
 * @Date 2024/9/26 10:22
 */
@DubboService
@Service
@AllArgsConstructor
public class SysMarketFacadeImpl implements SysMarketFacade {

    private final SysMarketMapper sysMarketMapper;

}
