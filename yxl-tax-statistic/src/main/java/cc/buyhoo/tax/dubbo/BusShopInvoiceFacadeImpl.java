package cc.buyhoo.tax.dubbo;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.InvoiceStatusEnum;
import cc.buyhoo.tax.enums.PeriodNumberEnum;
import cc.buyhoo.tax.facade.BusShopInvoiceFacade;
import cc.buyhoo.tax.facade.params.busShopInvoice.BusShopInvoiceRemoteParams;
import cc.buyhoo.tax.facade.params.saleList.SaleList;
import cc.buyhoo.tax.facade.params.saleList.SaleListDetail;
import cc.buyhoo.tax.facade.params.saleList.SaveSaleList;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName BusShopInvoiceFacadeImpl
 * <AUTHOR>
 * @Date 2024/6/14 15:06
 **/
@Slf4j
@DubboService
@Service
public class BusShopInvoiceFacadeImpl implements BusShopInvoiceFacade {

    @Resource
    BusShopInvoiceMapper busShopInvoiceMapper;
    @Resource
    BusShopInvoiceDetailMapper busShopInvoiceDetailMapper;
    @Resource
    BusShopInvoiceSettingMapper busShopInvoiceSettingMapper;
    @Resource
    BusGoodsCategoryMapper busGoodsCategoryMapper;
    @Resource
    BusGoodsMapper busGoodsMapper;

    @Override
    public void autoInsertShopInvoice(BusShopInvoiceRemoteParams params) {
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(new LambdaQueryWrapper<BusShopInvoiceSettingEntity>().eq(BusShopInvoiceSettingEntity::getCompanyId, params.getCompanyId()).eq(BusShopInvoiceSettingEntity::getPeriodsLevel, PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel()));
        if (CollectionUtil.isEmpty(params.getSaleListList())) {
            return;
        }
        LambdaQueryWrapper<BusGoodsCategoryEntity> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(BusGoodsCategoryEntity::getCompanyId, params.getCompanyId());
        categoryWrapper.eq(BusGoodsCategoryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        categoryWrapper.eq(BusGoodsCategoryEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        categoryWrapper.eq(BusGoodsCategoryEntity::getParentId, 0);
        List<BusGoodsCategoryEntity> goodsCategoryEntityList = busGoodsCategoryMapper.selectList(categoryWrapper);
        Map<Long, BusGoodsCategoryEntity> categoryMap = goodsCategoryEntityList.stream().collect(Collectors.toMap(BusGoodsCategoryEntity::getId, v -> v));

        LambdaQueryWrapper<BusShopInvoiceEntity> shopInvoiceWrapper = new LambdaQueryWrapper<>();
        shopInvoiceWrapper.eq(BusShopInvoiceEntity::getCompanyId, params.getCompanyId());
        shopInvoiceWrapper.isNotNull(BusShopInvoiceEntity::getShopUnique);
        shopInvoiceWrapper.isNotNull(BusShopInvoiceEntity::getSaleListUnique);
        shopInvoiceWrapper.select(BusShopInvoiceEntity::getShopUnique,BusShopInvoiceEntity::getSaleListUnique);
        List<BusShopInvoiceEntity> invoiceEntityList = busShopInvoiceMapper.selectList(shopInvoiceWrapper);

        LambdaQueryWrapper<BusGoodsEntity> goodsWrapper = new LambdaQueryWrapper<>();
        goodsWrapper.select(BusGoodsEntity::getShopUnique, BusGoodsEntity::getGoodsBarcode, BusGoodsEntity::getCategoryId);
        goodsWrapper.eq(BusGoodsEntity::getCompanyId, params.getCompanyId());
        goodsWrapper.eq(BusGoodsEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<BusGoodsEntity> goodsEntityList = busGoodsMapper.selectList(goodsWrapper);
        Map<String, Long> goodsMap = goodsEntityList.stream().collect(Collectors.toMap(k -> StrUtil.concat(true, String.valueOf(k.getShopUnique()), "_", k.getGoodsBarcode()), BusGoodsEntity::getCategoryId));

        for (SaveSaleList saleList : params.getSaleListList()) {
            SaleList sl = saleList.getSaleList();

            /*LambdaQueryWrapper<BusShopInvoiceEntity> shopInvoiceQueryWrapper = new LambdaQueryWrapper<>();
            shopInvoiceQueryWrapper.eq(BusShopInvoiceEntity::getSaleListUnique, sl.getSaleListUnique());
            shopInvoiceQueryWrapper.eq(BusShopInvoiceEntity::getShopUnique, sl.getShopUnique());
            List<BusShopInvoiceEntity> invoiceList = busShopInvoiceMapper.selectList(shopInvoiceQueryWrapper);
            if (ObjectUtil.isNotEmpty(invoiceList)) {
                log.error("------[订单定时任务同步生成发票信息]----零售订单发票数据已存在,订单编号={}----------", sl.getSaleListUnique());
                continue;
            }*/

            List<SaleListDetail> detailList = saleList.getSaleListDetailList();
            BusShopInvoiceEntity invoiceEntity = new BusShopInvoiceEntity();
            invoiceEntity.setCompanyId(sl.getCompanyId());
            invoiceEntity.setSaleListUnique(sl.getSaleListUnique());
            invoiceEntity.setShopUnique(sl.getShopUnique());
            invoiceEntity.setInvoiceType(2); //发票类型:1进项票2销项票
            invoiceEntity.setMediumType(1); //发票介质：1、电子发票；2、纸质发票
            invoiceEntity.setInvoiceKind(1); //发票种类:1：普通发票；2、专用发票
            invoiceEntity.setPurchaseType(1);//采购方类型：1、个人；2、企业
            invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_2.getValue());
            if (ObjectUtil.isNotNull(settingEntity)) {
                invoiceEntity.setSaleName(settingEntity.getCompanyName());
                invoiceEntity.setSaleIdentity(settingEntity.getCompanyTaxNo());
                invoiceEntity.setSaleAddress(settingEntity.getCompanyAddress());
                invoiceEntity.setSalePhone(settingEntity.getCompanyPhone());
                invoiceEntity.setSaleBank(settingEntity.getInvoiceBankName());
                invoiceEntity.setSaleBankNo(settingEntity.getInvoiceBankCard());
                invoiceEntity.setPayee(settingEntity.getPayee());
                invoiceEntity.setChecker(settingEntity.getChecker());
                invoiceEntity.setInvoiceMan(settingEntity.getInvoiceMan());
                invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_2.getValue());
            }
            BigDecimal taxMoney = BigDecimal.ZERO; //合计税额
            BigDecimal orderTaxMoney = BigDecimal.ZERO; //价税合计
            List<BusShopInvoiceDetailEntity> detailEntityList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(detailList)) {
                for (SaleListDetail sld : detailList) {
                    BusShopInvoiceDetailEntity detailEntity = new BusShopInvoiceDetailEntity();
                    detailEntity.setGoodsName(sld.getGoodsName());
                    detailEntity.setPrice(sld.getSaleListDetailPrice());
                    detailEntity.setQuantity(sld.getSaleListDetailCount());
                    detailEntity.setAmount(NumberUtil.mul(detailEntity.getPrice(), detailEntity.getQuantity()).setScale(2, RoundingMode.HALF_DOWN));
                    BigDecimal taxRate = BigDecimal.ZERO;
                    Long categoryId = goodsMap.get(sl.getShopUnique() + "_" + sld.getGoodsBarcode());
                    BusGoodsCategoryEntity categoryEntity = categoryMap.get(categoryId);
                    if (null != categoryEntity) {
                        taxRate = NumberUtil.null2Zero(categoryEntity.getTaxRate());
                        detailEntity.setTaxClassificationCode(categoryEntity.getCategoryNo());
                        detailEntity.setTaxClassificationName(categoryEntity.getGoodsName());
                        detailEntity.setUnit(categoryEntity.getUnit());
                    }
                    if (BigDecimal.ZERO.compareTo(taxRate) == -1) {
                        taxRate = NumberUtil.div(taxRate, 100);
                    }
                    detailEntity.setTaxRate(taxRate);
                    detailEntity.setTaxAmount(NumberUtil.mul(NumberUtil.div(detailEntity.getAmount(), NumberUtil.add(BigDecimal.ONE, taxRate)), taxRate).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    detailEntityList.add(detailEntity);

                    taxMoney = NumberUtil.add(taxMoney, detailEntity.getTaxAmount());
                    orderTaxMoney = NumberUtil.add(orderTaxMoney, detailEntity.getAmount());
                }
                // 处理订单总金额 与 明细总金额不一致问题
                if (sl.getSaleListActuallyReceived().compareTo(orderTaxMoney) == 1) {
                    //订单总金额 大于 明细总金额
                    BigDecimal diffV = NumberUtil.sub(sl.getSaleListActuallyReceived(), orderTaxMoney);
                    BusShopInvoiceDetailEntity detailEntity = detailEntityList.get(detailEntityList.size() - 1);
                    detailEntity.setAmount(NumberUtil.add(detailEntity.getAmount(), diffV));
                    detailEntity.setPrice(NumberUtil.div(detailEntity.getAmount(), detailEntity.getQuantity()).setScale(12, RoundingMode.HALF_UP));
                    detailEntity.setTaxAmount(NumberUtil.mul(NumberUtil.div(detailEntity.getAmount(), NumberUtil.add(BigDecimal.ONE, detailEntity.getTaxRate())), detailEntity.getTaxRate()).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    detailEntityList.remove(detailEntityList.size() - 1);
                    detailEntityList.add(detailEntity);
                } else if (sl.getSaleListActuallyReceived().compareTo(orderTaxMoney) == -1) {
                    //订单总金额 小于 明细总金额
                    BigDecimal diffV = NumberUtil.sub(orderTaxMoney, sl.getSaleListActuallyReceived());
                    BusShopInvoiceDetailEntity detailEntity = detailEntityList.get(detailEntityList.size() - 1);
                    if (diffV.compareTo(detailEntity.getAmount()) == -1) {
                        detailEntity.setAmount(NumberUtil.sub(detailEntity.getAmount(), diffV));
                        detailEntity.setPrice(NumberUtil.div(detailEntity.getAmount(), detailEntity.getQuantity()).setScale(12, RoundingMode.HALF_UP));
                        detailEntity.setTaxAmount(NumberUtil.mul(NumberUtil.div(detailEntity.getAmount(), NumberUtil.add(BigDecimal.ONE, detailEntity.getTaxRate())), detailEntity.getTaxRate()).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                        detailEntityList.remove(detailEntityList.size() - 1);
                        detailEntityList.add(detailEntity);
                    }
                }
            }
            invoiceEntity.setOrderMoney(NumberUtil.sub(sl.getSaleListActuallyReceived(), taxMoney));
            invoiceEntity.setTaxMoney(taxMoney);
            invoiceEntity.setOrderTaxMoney(sl.getSaleListActuallyReceived());
            if (ObjectUtil.isEmpty(invoiceEntityList) || (ObjectUtil.isNotEmpty(invoiceEntityList)
                    && invoiceEntityList.stream().noneMatch(v -> v.getSaleListUnique().equals(sl.getSaleListUnique())
                    && v.getShopUnique().equals(sl.getShopUnique())))) {
                int n = busShopInvoiceMapper.insert(invoiceEntity);
                if (n > 0 && CollectionUtil.isNotEmpty(detailEntityList)) {
                    detailEntityList.stream().forEach(v -> v.setInvoiceId(invoiceEntity.getId()));
                    busShopInvoiceDetailMapper.insertBatch(detailEntityList);
                }
            }
        }
    }
}
