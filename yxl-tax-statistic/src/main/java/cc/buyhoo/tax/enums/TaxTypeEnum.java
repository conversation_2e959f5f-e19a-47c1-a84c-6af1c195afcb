package cc.buyhoo.tax.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 纳统申报方式
 * @ClassName TaxTypeEnum
 * <AUTHOR>
 * @Date 2023/8/11 16:09
 **/
@AllArgsConstructor
@Getter
public enum TaxTypeEnum {

    MONTH("1", "月"),
    SEASON("2", "季度"),
    YEAR("3", "年");

    private String value;
    private String label;

    public static String getLabel(String value) {
        for(TaxTypeEnum e : TaxTypeEnum.values()) {
            if (StrUtil.equals(e.getValue(), value)) {
                return e.getLabel();
            }
        }
        return StrUtil.EMPTY;
    }
}
