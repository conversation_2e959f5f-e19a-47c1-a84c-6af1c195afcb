package cc.buyhoo.tax.enums;

import cc.buyhoo.tax.entity.common.EnumEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Getter
@AllArgsConstructor
public enum ShopNatureEnum {

    GeneralTaxpayer("一般纳税人", 0),
    IndividualBusiness("个体户", 1),
    SmallScale("小规模纳税人", 2),
    Other("其他", 3)

    ;
    private String name;
    private Integer value;

    /**
     * 获取所有 Natura 列表
     * @return
     */
    public static List<EnumEntity> natureList() {
        ShopNatureEnum[] values = ShopNatureEnum.values();
        List<EnumEntity> natureList = new ArrayList<>();
        for (ShopNatureEnum item : values) {
            EnumEntity map = new EnumEntity();
            map.setName(item.getName());
            map.setValue(item.getValue().toString());
            natureList.add(map);
        }
        return natureList;
    }
    public static String getName(Integer value) {
        if (value == null) {
            return "";
        }
        for (ShopNatureEnum item : ShopNatureEnum.values()) {
            if (item.getValue().equals(value)) {
                return item.getName();
            }
        }
        return null;
    }
}
