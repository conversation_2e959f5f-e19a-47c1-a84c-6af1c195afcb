package cc.buyhoo.tax.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SysMigrateAuditStatusEnm {
    AUDIT(0, "待审核"),
    AUDIT_SUCCESS(1, "通过"),
    AUDIT_FAIL(2, "不通过");

    private Integer value;
    private String label;
    public static String getLabel(Integer value) {
        for (SysMigrateAuditStatusEnm item : SysMigrateAuditStatusEnm.values()) {
            if (item.getValue().equals(value)) {
                return item.getLabel();
            }
        }
        return null;
    }
}
