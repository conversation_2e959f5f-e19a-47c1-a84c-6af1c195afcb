package cc.buyhoo.tax.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 合同类型枚举
 * @ClassName BusContractTypeEnum
 * <AUTHOR>
 * @Date 2024/7/15 9:19
 **/
@Getter
@AllArgsConstructor
public enum BusContractTypeEnum {

    XS(1, "XS", "销售合同"),
    CG(2, "CG", "采购合同"),
    LY(3, "LY", "联营合同"),
    QT(4, "QT", "其他合同或协议"),
    ;

    private Integer value;
    private String code;
    private String name;

    public static String getCode(Integer value) {
        for (BusContractTypeEnum e : BusContractTypeEnum.values()) {
            if (ObjectUtil.equals(e.getValue(), value)) {
                return e.getCode();
            }
        }
        return null;
    }
    public static String getName(Integer value) {
        for (BusContractTypeEnum e : BusContractTypeEnum.values()) {
            if (ObjectUtil.equals(e.getValue(), value)) {
                return e.getName();
            }
        }
        return null;
    }

}
