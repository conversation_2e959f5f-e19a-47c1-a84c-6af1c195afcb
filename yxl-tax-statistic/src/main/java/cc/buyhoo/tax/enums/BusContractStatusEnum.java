package cc.buyhoo.tax.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 合同状态枚举
 * @ClassName BusContractStatusEnum
 * <AUTHOR>
 * @Date 2024/7/15 9:19
 **/
@Getter
@AllArgsConstructor
public enum BusContractStatusEnum {
    STATUS_DRAFT(0, "草拟"),
    STATUS_WAIT(1, "待审"),
    STATUS_SIGNED(2, "已签署"),
    STATUS_DOING(3, "执行中"),
    STATUS_FINISH(4, "已完成"),
    STATUS_END(5, "终止"),
    ;

    private Integer code;
    private String name;

    public static String getName(Integer code) {
        for (BusContractStatusEnum e : BusContractStatusEnum.values()) {
            if (ObjectUtil.equals(e.getCode(), code)) {
                return e.getName();
            }
        }
        return null;
    }

}
