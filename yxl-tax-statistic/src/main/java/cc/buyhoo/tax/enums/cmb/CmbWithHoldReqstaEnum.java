package cc.buyhoo.tax.enums.cmb;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
public enum CmbWithHoldReqstaEnum {
    YTJ("YTJ","已提交"), //自定义状态:
    AUT("AUT","等待审批"),
    NTE("NTE","终审完毕"),
    BNK("BNK","银行处理中"),
    FIN("FIN","完成"),
    FINS("FINS","银行支付成功"),
    FINF("FINF","银行支付失败"),
    FINB("FINB","银行支付被退票"),
    FINR("FINR","企业审批否决"),
    FIND("FIND","企业过期不审批"),
    FINC("FINC","企业撤销"),
    FINU("FINU","银行挂账"),
    OPR("OPR","数据接收中"),
    APW("APW","银行人工审批"),
    WRF("WRF","需要人工介入处理"),

    ;

    private final String status;
    private final String desc;
}
