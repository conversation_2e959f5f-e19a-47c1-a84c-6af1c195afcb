package cc.buyhoo.tax.enums.cmb;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum BusmodEnums {

    ACCOUNTSEARCH("业务查询", "DCLISMOD"),
    CdcMatchCardBin("卡BIN匹配识别标准银联卡","CdcMatchCardBin"),
    HANDLEHELP("代发经办","BB6BTHHL"),
    BB6BPDQY("批次与明细查询","BB6BPDQY"),
    BB6INTQY("代发代扣综合查询 ","BB6INTQY"),
    BB6BTHQY("代发批次查询","BB6BTHQY"),
    BB6DTLQY("代发明细查询","BB6DTLQY"),
    BB6RFDQY("代发退票查询","BB6RFDQY"),
    BB6AGTQY("代发类型查询","BB6AGTQY"),
    BB6ACLAK("代扣经办","BB6ACLAK"),
    NTQACINF("账务查询","NTQACINF"),
    NTQADINF("批量查询余额","NTQADINF"),
    NTQABINF("查询账户历史余额","NTQABINF"),
    ;
    private String name;
    private String value;
}
