package cc.buyhoo.tax.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 季度
 * @ClassName SeasonEnum
 * <AUTHOR>
 * @Date 2023/8/11 16:09
 **/
@AllArgsConstructor
@Getter
public enum SeasonEnum {

    FIRST("1", "第一季度"),
    SECOND("2", "第二季度"),
    THIRD("3", "第三季度"),
    FOURTH("4", "第四季度");

    private String value;
    private String label;

    public static String getLabel(String value) {
        for(SeasonEnum e : SeasonEnum.values()) {
            if (StrUtil.equals(e.getValue(), value)) {
                return e.getLabel();
            }
        }
        return StrUtil.EMPTY;
    }
}
