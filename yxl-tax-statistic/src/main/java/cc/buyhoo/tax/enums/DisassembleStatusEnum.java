package cc.buyhoo.tax.enums;

import cc.buyhoo.tax.result.disassembleList.DisassembleStatusDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum DisassembleStatusEnum {

    WAIT_EXAMINE("待审核",1),
    DISASSEMBLE_ING("正在拆单",2),
    EXAMINE_REFUSED("审核拒绝",3),
    DISASSEMBLE_SUCCESS("拆单成功",4),
    DISASSEMBLE_FAIL("拆单失败",5),
    ;
    private String name;
    private Integer value;

    /**
     * 根据状态值获取状态名称
     * @param status
     * @return
     */
    public static String getName(Integer status) {
        for (DisassembleStatusEnum c : DisassembleStatusEnum.values()) {
            if (c.getValue() == status) {
                return c.name;
            }
        }
        return null;
    }

    /**
     * 获取当前所有状态
     * @return
     */

    public static List<DisassembleStatusDto> getDisassembleStatusList() {
        List<DisassembleStatusDto> list = new ArrayList<>();
        for (DisassembleStatusEnum c : DisassembleStatusEnum.values()) {
            DisassembleStatusDto disassembleStatusDto = new DisassembleStatusDto();
            disassembleStatusDto.setName(c.getName());
            disassembleStatusDto.setValue(c.getValue());
            list.add(disassembleStatusDto);
        }
        return list;
    }
}
