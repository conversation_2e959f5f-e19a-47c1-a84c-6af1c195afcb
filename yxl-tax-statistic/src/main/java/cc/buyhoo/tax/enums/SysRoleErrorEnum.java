package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SysRoleErrorEnum implements ErrorEnum {

    ROLENAME_REPEAT_ERROR("role-0001","角色名称重复"),
    EXISTS_USER_ROLE_ERROR("role-0002","该角色已绑定用户"),
    NOT_ALL_DELETE("role-0003","超级管理员角色禁止删除"),
    NOT_EXIST("role-0004","角色不存在"),
    NOT_CONSISTENT("role-0005","角色所属企业不允许变更"),
    NOT_ALLOW_MODIFY("role-0006","超级管理员角色禁止修改"),
    ;

    private final String code;
    private final String desc;

}
