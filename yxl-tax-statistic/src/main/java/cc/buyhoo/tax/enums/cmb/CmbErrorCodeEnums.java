package cc.buyhoo.tax.enums.cmb;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CmbErrorCodeEnums {

    SUCCESS("交易成功","LX11C000"),
    FAIL("失败","LX11CXXX"),
    SYSTEMERROR("系统异常,请稍后重试","LX11C999"),
    NETERROR("网络连接超时,请稍后重试","LX11C998"),
    DATAERROR("数据异常","LX11C997"),
    SYSTEMBUSY("系统繁忙,请稍后重试","LX11C996"),
    SIGNERROR("签名验证失败","LX11C995"),
    SIGNCREATEERROR("生成签名失败","LX11C994"),
    ORDERNOTEXIST("订单不存在","LX11C993"),
    PARAMSERROR("参数异常","LX11C901"),
    ;
    private String name;
    private String value;
}
