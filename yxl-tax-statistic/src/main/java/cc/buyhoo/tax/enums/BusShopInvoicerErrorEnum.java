package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum BusShopInvoicerErrorEnum implements ErrorEnum {

    INVOICE_NUMBER_REPEAT_ERROR("invoice-0001","发票号码重复"),
    ;

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
