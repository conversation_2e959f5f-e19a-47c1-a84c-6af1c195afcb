package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum CommonErrorEnum implements ErrorEnum {

    PARAM_ERROR("common-0001","请求参数异常"),
    ;

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
