package cc.buyhoo.tax.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ManagementModelEnum {
    MANAGEMENT_MODEL_0(0,"企业独营"),
    MANAGEMENT_MODEL_1(1,"企业联营");
    private Integer value;
    private String desc;
    public static String getLabel(Integer value) {
        for(ManagementModelEnum e : ManagementModelEnum.values()) {
            if (e.getValue() == value) {
                return e.getDesc();
            }
        }
        return StrUtil.EMPTY;
    }
}
