package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum BusShopBillErrorEnum implements ErrorEnum {

    EMPTY_ACCOUT_ERROR("bill-0001","没有可以打款的供货商"),
    COMP_SETTING_ERROR("bill-0002","请先配置企业开户行以及银行账号"),
    SHOP_EMPTY_ERROR("bill-0003","未查询到供货商配置"),
    SHOP_SETTING_EMPTY_ERROR("bill-0004","请先在\"供货商管理\"补全供货商打款信息"),
    TRANSFER_REQ_ERROR("bill-0005","发起转账请求失败"),
    TRANSFER_MONEY_ERROR("bill-0006","转账金额需小于等于未结算金额"),
    BANK_CODE_ERROR("bill-0007","请先配置企业银行编号"),
    SHOP_BANK_NAME_ERROR("bill-0008","请先配置供货商银行开户行信息"),
    ;

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
