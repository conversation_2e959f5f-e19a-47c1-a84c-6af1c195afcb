package cc.buyhoo.tax.enums;

import cc.buyhoo.tax.entity.invoice.NameValueEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum InvoiceStatusEnum {

    INVOICE_STATUS_1("已开票", 1),
    INVOICE_STATUS_2("未开票", 2),
    INVOICE_STATUS_3("开票中", 3),
    INVOICE_STATUS_4("开票失败", 4)
    ;
    //标题
    private String label;
    //值
    private Integer value;

    /**
     * 获取开票状态数组
     * @return
     */
    public static List<NameValueEntity> list() {
        InvoiceStatusEnum[] invoiceStatusEnums = InvoiceStatusEnum.values();
        return Arrays.asList(invoiceStatusEnums).stream().map(invoiceStatusEnum -> {
            NameValueEntity nameValueEntity = new NameValueEntity();
            nameValueEntity.setName(invoiceStatusEnum.getLabel());
            nameValueEntity.setValue(invoiceStatusEnum.getValue() + "");
            return nameValueEntity;
        }).collect(Collectors.toList());
    }
}
