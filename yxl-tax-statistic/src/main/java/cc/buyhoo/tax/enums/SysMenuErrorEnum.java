package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SysMenuErrorEnum implements ErrorEnum {

    EXISTS_CHILD_ERROR("menu-0001","存在子菜单，不允许删除"),
    ;

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
