package cc.buyhoo.tax.enums.cmb;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
@AllArgsConstructor
@Getter
public enum CmbPayTypeEnums {

    WX("微信","wx"),
    ZFB("支付宝","zfb"),
    YWTH5("一网通h5","ywth5"),
    YWTAPP("手机银行一网通","ywtapp"),
    YWTIN("一网通内场景","ywtin"),
    VIRTUALACCOUNT("账户识别（虚拟子账户）","7"),
    UNLINETRANSFER("转账码线下转账","10"),
    TOGETHERCODE("聚合码","13"),
    CMBAPP("招行手机银行小程序二维码","14"),
    NUMBERCODE("数币收款码","17"),
    PHONEBANK("手机银行小程序","18"),
    NUMBERAPP("数币APP","19")

    ;
    private String name;
    private String value;

}
