package cc.buyhoo.tax.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结算状态
 * @ClassName SettledStatusEnum
 * <AUTHOR>
 * @Date 2023/7/28 10:00
 **/
@Getter
@AllArgsConstructor
public enum SettledStatusEnum {
    UNSETTLED("0", "未结算"),
    SETTLED("1", "已结算");

    private String value;
    private String label;

    public static String getValueByLabel(String label) {
        for (SettledStatusEnum e : SettledStatusEnum.values()){
            if (StrUtil.equals(e.getLabel(), label)) {
                return e.getValue();
            }
        }
        return StrUtil.EMPTY;
    }

    public static String getLabelByValue(String value) {
        for (SettledStatusEnum e : SettledStatusEnum.values()){
            if (StrUtil.equals(e.getValue(), value)) {
                return e.getLabel();
            }
        }
        return StrUtil.EMPTY;
    }
}
