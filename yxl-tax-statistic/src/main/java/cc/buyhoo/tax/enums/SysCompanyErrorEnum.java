package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SysCompanyErrorEnum implements ErrorEnum {

    ID_NULL_ERROR("company-0001","未获取到企业信息"),
    PERMISSION_ERROR("company-0002","系统权限不足，请与管理员联系"),
    INVITATION_CODE_ERROR("company-0003","邀请码重复"),
    NOT_DELETE("company-0004","非自己创建企业禁止删除"),
    STATISTIC_AMOUNT_ERROR("company-0005","突击金额不能小于0"),
    STATISTIC_AMOUNT_NULL("company-0006","突击金额不能为空"),

    STATISTIC_GRADE_NULL("company-0007","突击顺序不能为空"),
    ;

    private final String code;
    private final String desc;

}
