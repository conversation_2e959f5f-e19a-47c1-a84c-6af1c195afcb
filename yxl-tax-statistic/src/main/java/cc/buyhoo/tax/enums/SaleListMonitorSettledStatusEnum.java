package cc.buyhoo.tax.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 订单监控-结算状态枚举类
 * @ClassName SaleListMonitorSettledStatusEnum
 * <AUTHOR>
 * @Date 2024-11-4 17:18:57
 **/
@Getter
@AllArgsConstructor
public enum SaleListMonitorSettledStatusEnum {

    /**
     * 待结算
     */
    UNSETTLED(0),

    /**
     * 结算中
     */
    SETTLEING(1),
    /**
     * 已结算
     */
    SETTLED_SUCCESS(2),
    /**
     * 结算失败
     */
    SETTLED_FAIL(3);

    private Integer value;
}
