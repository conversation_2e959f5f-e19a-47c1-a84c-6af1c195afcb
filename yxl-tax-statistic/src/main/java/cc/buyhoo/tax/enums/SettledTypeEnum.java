package cc.buyhoo.tax.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结算方式
 * @ClassName SettledTypeEnum
 * <AUTHOR>
 * @Date 2023/7/28 10:00
 **/
@Getter
@AllArgsConstructor
public enum SettledTypeEnum {
    UNSETTLED("0", "手动结算"),
    SETTLED("1", "自动结算");

    private String value;
    private String label;

    public static String getValueByLabel(String label) {
        for (SettledTypeEnum e : SettledTypeEnum.values()){
            if (StrUtil.equals(e.getLabel(), label)) {
                return e.getValue();
            }
        }
        return StrUtil.EMPTY;
    }

    public static String getLabelByValue(String value) {
        for (SettledTypeEnum e : SettledTypeEnum.values()){
            if (StrUtil.equals(e.getValue(), value)) {
                return e.getLabel();
            }
        }
        return StrUtil.EMPTY;
    }
}
