package cc.buyhoo.tax.enums;

import cc.buyhoo.tax.result.invoice.InvoiceStaffDutyTypeDto;
import cc.buyhoo.tax.result.invoice.InvoiceStaffDutyTypeListResult;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum InvoiceStaffDutyTypeEnum {
    LEGAL_PERSON("法人","01"),
    FINANCE_CHIEF("财务负责人","02"),
    TAX_OFFER("办税员","03"),
    ADMINISTRATOR("管理员","05"),
    SOCIAL_SECURITY_AGENT("社保经办人","08"),
    INVOICER("开票员","09"),
    SELLER("销售员","10")

    ;
    private String label;
    private String value;

    /**
     * 获取财务负责人类型列表
     * @return
     */
    public static InvoiceStaffDutyTypeListResult convertList() {
        InvoiceStaffDutyTypeEnum[] values = InvoiceStaffDutyTypeEnum.values();
        List<InvoiceStaffDutyTypeDto> list = new ArrayList<>();
        for (InvoiceStaffDutyTypeEnum value : values) {
            InvoiceStaffDutyTypeDto result = new InvoiceStaffDutyTypeDto();
            result.setDutyType(value.getValue());
            result.setDutyTypeName(value.getLabel());
            list.add(result);
        }
        InvoiceStaffDutyTypeListResult listResult = new InvoiceStaffDutyTypeListResult();
        listResult.setList(list);
        return listResult;
    }
}
