package cc.buyhoo.tax.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName CmbcTranferRecordEnum
 * @Description 民生银行转账记录状态  状态 0已发送转账请求待验证 1转账成功 2转账请求失败 3 转账失败
 * <AUTHOR>
 * @Date 2025/4/2 19:36
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum CmbcTranferRecordEnum {
    BEGIN(0,"已发送转账请求待验证"),
    SUCCESS(1,"转账成功"),
    FAIL(2,"转账请求失败"),
    TRANSFER_FAIL(3,"转账失败"),
    PROCESSING(4,"处理中"),
    ;

    private final int value;
    private final String label;
}