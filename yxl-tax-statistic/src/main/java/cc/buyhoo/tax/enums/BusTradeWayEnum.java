package cc.buyhoo.tax.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 交易类型枚举
 * @ClassName BusTradeWayEnum
 * <AUTHOR>
 * @Date 2024-9-2 09:06:06
 **/
@Getter
@AllArgsConstructor
public enum BusTradeWayEnum {

    IN(1, "1", "入账"),
    OUT(2, "2", "出账"),
    ;

    private Integer value;
    private String code;
    private String name;

    public static String getCode(Integer value) {
        for (BusTradeWayEnum e : BusTradeWayEnum.values()) {
            if (ObjectUtil.equals(e.getValue(), value)) {
                return e.getCode();
            }
        }
        return null;
    }
    public static String getName(Integer value) {
        for (BusTradeWayEnum e : BusTradeWayEnum.values()) {
            if (ObjectUtil.equals(e.getValue(), value)) {
                return e.getName();
            }
        }
        return null;
    }

}
