package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SysUserErrorEnum implements ErrorEnum {

    USERNAME_REPEAT_ERROR("user-0001","用户名重复"),
    EXIST_CANNOT_DEL_ERROR("user-0002","存在不可删除的用户"),
    OLD_PWD_ERROR("user-0003","旧密码错误"),
    USER_ERROR("user-0004","用户异常"),
    USER_NO_PERMISSION("user-0005","无操作权限"),
    USER_NO_EXIST("user-0006","用户不存在"),
    ;

    private final String code;
    private final String desc;

}
