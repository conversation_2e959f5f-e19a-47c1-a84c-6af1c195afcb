package cc.buyhoo.tax.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DateFormatEnums {

    yyyy_MM_dd("yyyy-MM-dd","日期格式"),
    yyyy_mm_dd_HH_mm_ss("yyyy-MM-dd HH:mm:ss","日期时间格式"),
    yyyyMMdd("yyyyMMdd","日期格式"),
    yyyyMMddHHmmss("yyyyMMddHHmmss","日期时间格式"),
    yyyy_mm_dd_BIAS("yyyy/mm/dd","斜线形式的日期"),
    yyyy_mm_dd_HH_mm_ss_BIAS("yyyy/mm/dd HH:mm:ss","斜线形式的日期时间"),
    HHmmss("HHmmss","时分秒格式"),
    HH_mm_ss("HH:mm:ss","时分秒格式"),


    ;
    private String format;
    private String desc;
}
