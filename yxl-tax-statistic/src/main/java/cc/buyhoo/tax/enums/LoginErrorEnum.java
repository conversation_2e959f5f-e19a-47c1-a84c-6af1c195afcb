package cc.buyhoo.tax.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum LoginErrorEnum implements ErrorEnum {

    CAPTCHA_ERROR("login-0001","获取验证码失败"),
    USERNAME_PWD_ERROR("login-0002","用户名或密码错误"),
    ENABLE_ERROR("login-0003","用户已被禁用"),
    LOGIN_LIMIT_EXCEED_ERROR("login-0004","失败次数已达上线"),
    CAPTCHA_EXPIRE_ERROR("login-0005","验证码已失效"),
    CAPTCHA_CODE_ERROR("login-0006","验证码错误"),
    ;

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
