package cc.buyhoo.tax;

import cn.hutool.crypto.SecureUtil;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

import java.security.Security;

@SpringBootApplication
@MapperScan("cc.buyhoo.tax.dao")
@EnableDiscoveryClient
@EnableDubbo
public class YxlTaxStatisticApplication {

    public static void main(String[] args) {
        SpringApplication.run(YxlTaxStatisticApplication.class, args);
        SecureUtil.disableBouncyCastle();
    }

    static {
        // 装载BC库,必须在应用的启动类中调用此函数
        Security.addProvider(new BouncyCastleProvider());
        //不展示pagehelper.banner
        System.setProperty("pagehelper.banner", "false");
        //同一台服务器部署多个环境，dubbo缓存文件重复问题
        System.setProperty("user.home", "dubboCache");
    }

}
