package cc.buyhoo.tax.task;


import cc.buyhoo.common.minio.MinioUploadHelper;
import cc.buyhoo.common.smsg.config.properties.SmsgProperties;
import cc.buyhoo.tax.config.InvoiceNotifyConfig;
import cc.buyhoo.tax.config.properties.InvoiceProperties;
import cc.buyhoo.tax.dao.BusSaleListMapper;
import cc.buyhoo.tax.dao.BusShopInvoiceMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.BusShopInvoiceEntity;
import cc.buyhoo.tax.entity.invoice.GetQdInvoiceEntity;
import cc.buyhoo.tax.entity.invoice.GetQdInvoiceResult;
import cc.buyhoo.tax.entity.invoice.GetQdInvoiceResultData;
import cc.buyhoo.tax.entity.invoice.QuerySdInvoiceFileEntity;
import cc.buyhoo.tax.enums.InvoiceStatusEnum;
import cc.buyhoo.tax.params.invoice.NotifyInvoiceResultParams;
import cc.buyhoo.tax.util.ImageMergeUtil;
import cc.buyhoo.tax.util.InvoiceUrlUtil;
import cc.buyhoo.tax.util.InvoiceUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * 子线程实现开票后查询开票状态
 * 由于开票需要时间，所以需要演示实现查询
 */
public class GetQdInvoiceTask extends Thread {

    private final GetQdInvoiceEntity getQdInvoiceEntity;

    private final BusShopInvoiceEntity invoiceEntity;

    private final MinioUploadHelper minioUploadHelper;

    private final SmsgProperties smsgProperties;

    private final BusShopInvoiceMapper busShopInvoiceMapper;

    private InvoiceProperties invoiceProperties;

    private final InvoiceNotifyConfig invoiceNotifyConfig;
    private final BusSaleListMapper busSaleListMapper;
    private final BusShopMapper busShopMapper;

    private final InvoiceUrlUtil invoiceUrlUtil;


    public GetQdInvoiceTask(GetQdInvoiceEntity getQdInvoiceEntity, BusShopInvoiceEntity invoiceEntity, InvoiceProperties invoiceProperties,
                            MinioUploadHelper minioUploadHelper, SmsgProperties smsgProperties, BusShopInvoiceMapper busShopInvoiceMapper,
                            InvoiceNotifyConfig invoiceNotifyConfig, BusSaleListMapper busSaleListMapper, BusShopMapper busShopMapper, InvoiceUrlUtil invoiceUrlUtil) {
        this.invoiceProperties = invoiceProperties;
        this.getQdInvoiceEntity = getQdInvoiceEntity;
        this.invoiceEntity = invoiceEntity;
        this.minioUploadHelper = minioUploadHelper;
        this.smsgProperties = smsgProperties;
        this.busShopInvoiceMapper = busShopInvoiceMapper;
        this.invoiceNotifyConfig = invoiceNotifyConfig;
        this.busSaleListMapper = busSaleListMapper;
        this.busShopMapper = busShopMapper;
        this.invoiceUrlUtil = invoiceUrlUtil;
    }

    public void run() {
        String faPiaoPath = invoiceProperties.getFilePath();
        try {
            //此处必须延时处理，开发票需要时间
            Thread.sleep(5000L);
            //校验文件夹是否存在，如果不存在，创建
            ImageMergeUtil.checkFilePath(faPiaoPath);

            GetQdInvoiceResult getQdInvoiceResult = InvoiceUtil.getQdInvoice(getQdInvoiceEntity);
            if (getQdInvoiceResult.getCode().equals("200")) {
                //获取开票信息成功
                List<GetQdInvoiceResultData> dataList = getQdInvoiceResult.getData();
                //正常会有多张发票吗？
                for (GetQdInvoiceResultData d : dataList) {

                    //验证开票状态
                    String statusCode = d.getStatusCode();
                    if (!statusCode.equals("030000")) {
                        continue;
                    }
                    //更新发票状态为开票成功
                    QuerySdInvoiceFileEntity querySdInvoiceFileEntity = new QuerySdInvoiceFileEntity();
                    querySdInvoiceFileEntity.setNsrsbh(getQdInvoiceEntity.getNsrsbh());
                    querySdInvoiceFileEntity.setFphm(d.getInvoiceNumber());
                    querySdInvoiceFileEntity.setWjlx("PDF");
                    QuerySdInvoiceFileTask querySdInvoiceFileTask = new QuerySdInvoiceFileTask(getQdInvoiceEntity.getBillNumber(), querySdInvoiceFileEntity,invoiceEntity,invoiceProperties,minioUploadHelper,smsgProperties,busShopInvoiceMapper,invoiceNotifyConfig, busSaleListMapper, busShopMapper, invoiceUrlUtil);
                    querySdInvoiceFileTask.start();
                }
            }

        } catch (Exception e) {
            System.out.println("开票失败zz");
            e.printStackTrace();
            //更新发票状态为开票失败
            invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_4.getValue());
            busShopInvoiceMapper.updateById(invoiceEntity);
            try {
                //查询店铺类型
                BusShopEntity busShopEntity = busShopMapper.selectOne(Wrappers.<BusShopEntity>lambdaQuery().eq(BusShopEntity::getShopUnique, invoiceEntity.getShopUnique()));
                String url = invoiceUrlUtil.buildUrl(busShopEntity.getShopType());
                NotifyInvoiceResultParams params = new NotifyInvoiceResultParams();
                params.setSaleListUnique(invoiceEntity.getSaleListUnique());
                params.setBillNumber(invoiceEntity.getBillNumber());
                params.setStatus(4);
                params.setReviewComments(e.getMessage());
                System.out.println("通知开票状态:" + url + "["+JSONUtil.toJsonStr(params)+"]");
                HttpUtil.post(url, JSONUtil.toJsonStr(params));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
    }
}
