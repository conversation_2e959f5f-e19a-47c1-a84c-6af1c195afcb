package cc.buyhoo.tax.task;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.*;
import cc.buyhoo.tax.facade.BusShopApi;
import cc.buyhoo.tax.facade.params.busShop.ShopPayChangeParams;
import cc.buyhoo.tax.result.saleList.SaleListDailyAverageTurnoverDto;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName CompanyMigrateIoTask
 * <AUTHOR>
 * @Date 2024/9/26 14:40
 */
@Service
@Slf4j
public class CompanyMigrateIoTask {

    @Autowired
    private SysMarketMapper sysMarketMapper;
    @Resource
    private SysCompanyMapper sysCompanyMapper;
    @Resource
    private BusSaleListMapper busSaleListMapper;
    @Resource
    private BusShopMapper busShopMapper;
    @Resource
    private SysMigrateMapper sysMigrateMapper;
    @Resource
    private BusShopApi busShopApi;

    /**
     * 自动迁入迁出
     */
    @XxlJob("companyMigrateIoJobHandler")
    public void companyMigrateIo() {
        LambdaQueryWrapper<SysMarketEntity> marketQuery = new LambdaQueryWrapper<>();
        marketQuery.eq(SysMarketEntity::getManagementModel, ManagementModelEnum.MANAGEMENT_MODEL_1.getValue());
        marketQuery.eq(SysMarketEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        marketQuery.eq(SysMarketEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        // 经营模式为“企业联合”的市场
        List<SysMarketEntity> marketList = sysMarketMapper.selectList(marketQuery);
        if (ObjectUtil.isNotEmpty(marketList)) {
            for (SysMarketEntity market : marketList) {
                LambdaQueryWrapper<SysCompanyEntity> companyQuery = new LambdaQueryWrapper<>();
                companyQuery.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
                companyQuery.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
                companyQuery.eq(SysCompanyEntity::getMarketId, market.getId());
                companyQuery.orderByAsc(SysCompanyEntity::getStatisticGrade);
                List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyQuery);
                if (ObjectUtil.isNotEmpty(companyList)) {
                    for (SysCompanyEntity sysCompanyEntity : companyList) {
                        // 正在突击中的企业
                        if (ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_1.getValue(), sysCompanyEntity.getStatisticStatus())) {
                            long allDay = 0L;
                            long day = 0L;
                            long surplusDay = 0L;
                            QueryWrapper<BusSaleListEntity> saleListQuery = new QueryWrapper<>();
                            // 纳统类型,1-按月,2-按季度,3-按年
                            if (ObjectUtil.equals(TaxTypeEnum.YEAR.getValue(), sysCompanyEntity.getTaxType())) {
                                allDay = DateUtil.lengthOfYear(DateUtil.year(DateUtil.date()));
                                day = DateUtil.betweenDay(DateUtil.beginOfYear(DateUtil.date()), DateUtil.date(), true);
                                surplusDay = allDay - day;
                                saleListQuery.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfYear(new Date()));
                            } else if (ObjectUtil.equals(TaxTypeEnum.SEASON.getValue(), sysCompanyEntity.getTaxType())) {
                                allDay = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.endOfQuarter(DateUtil.date()), true);
                                day = DateUtil.betweenDay(DateUtil.beginOfQuarter(DateUtil.date()), DateUtil.date(), true);
                                surplusDay = allDay - day;
                                saleListQuery.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfQuarter(new Date()));
                            } else if (ObjectUtil.equals(TaxTypeEnum.MONTH.getValue(), sysCompanyEntity.getTaxType())) {
                                allDay = DateUtil.lengthOfMonth(DateUtil.month(DateUtil.date()), DateUtil.isLeapYear(DateUtil.year(DateUtil.date())));
                                day = DateUtil.betweenDay(DateUtil.beginOfMonth(DateUtil.date()), DateUtil.date(), true);
                                surplusDay = allDay - day;
                                saleListQuery.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfMonth(new Date()));
                            }
                            saleListQuery.lambda().eq(BusSaleListEntity::getCompanyId, sysCompanyEntity.getId());
                            saleListQuery.lambda().eq(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_2.getCode());
                            saleListQuery.select("shop_unique as shopUnique", "ROUND(IFNULL(sum(sale_list_actually_received),0),2) as saleListActuallyReceivedSum", "COUNT(DISTINCT DATE_FORMAT(sale_list_datetime,'%Y-%m-%d')) as saleListDatetimeCount");
                            saleListQuery.lambda().groupBy(BusSaleListEntity::getShopUnique);
                            //企业周期内联营订单
                            List<Map<String, Object>> saleList = busSaleListMapper.selectMaps(saleListQuery);

                            List<BusShopEntity> busShopList = busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, sysCompanyEntity.getId()).eq(BusShopEntity::getCooperateType,BusShopCooperateTypeEnum.COOPERATE_TYPE_2.getValue()));
                            if (ObjectUtil.isNotEmpty(saleList)) {
                                // 企业周期内联营订单总金额
                                BigDecimal allAmount = saleList.stream().map(m -> (ObjectUtil.isNotNull(m.get("saleListActuallyReceivedSum")) ? new BigDecimal(String.valueOf(m.get("saleListActuallyReceivedSum"))) : BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                if (ObjectUtil.isNotEmpty(saleList) && ObjectUtil.isNotNull(allAmount)
                                        && ObjectUtil.isNotNull(sysCompanyEntity.getVatRate()) && ObjectUtil.isNotNull(sysCompanyEntity.getStatisticAmount())) {
                                    // 当企业（周期内联营订单总营业额/（1+企业增值税税率））达到其突击金额时
                                    if ((allAmount.divide((BigDecimal.valueOf(1).add(sysCompanyEntity.getVatRate().divide(BigDecimal.valueOf(100),2,RoundingMode.HALF_UP))), 2, RoundingMode.HALF_UP))
                                            .compareTo(sysCompanyEntity.getStatisticAmount()) >= 0) {
                                        // 企业内“联营”方式供货商日均营业额倒序（大→小）
                                        List<SaleListDailyAverageTurnoverDto> saleListDailyAverageTurnoverDtoList = saleList.stream().filter(
                                                        v -> (ObjectUtil.isNotEmpty(busShopList) ? busShopList.stream().anyMatch(s -> Long.valueOf(String.valueOf(v.get("shopUnique"))).equals(s.getShopUnique())) : true))
                                                .map(m -> {
                                                    SaleListDailyAverageTurnoverDto dailyAverageTurnoverDto = new SaleListDailyAverageTurnoverDto();
                                                    dailyAverageTurnoverDto.setShopUnique(Long.valueOf(String.valueOf(m.get("shopUnique"))));
                                                    if (ObjectUtil.isNotNull(m.get("saleListActuallyReceivedSum")) && ObjectUtil.isNotNull(m.get("saleListDatetimeCount"))
                                                            && ObjectUtil.notEqual(BigDecimal.ZERO, (ObjectUtil.isNotNull(m.get("saleListDatetimeCount")) ? new BigDecimal(String.valueOf(m.get("saleListDatetimeCount"))) : BigDecimal.ZERO))) {
                                                        dailyAverageTurnoverDto.setDailyAverageTurnover((ObjectUtil.isNotNull(m.get("saleListActuallyReceivedSum")) ? new BigDecimal(String.valueOf(m.get("saleListActuallyReceivedSum"))) : BigDecimal.ZERO).divide(new BigDecimal(String.valueOf(m.get("saleListDatetimeCount"))), 2, RoundingMode.HALF_UP));
                                                    } else {
                                                        dailyAverageTurnoverDto.setDailyAverageTurnover(BigDecimal.ZERO);
                                                    }
                                                    return dailyAverageTurnoverDto;
                                                }).sorted(Comparator.comparing(SaleListDailyAverageTurnoverDto::getDailyAverageTurnover, Comparator.reverseOrder())).collect(Collectors.toList());
                                        if (ObjectUtil.isNotEmpty(saleListDailyAverageTurnoverDtoList)) {
                                            BigDecimal finallyAmount = allAmount;
                                            Long migrateCompanyId = null;
                                            BigDecimal allAmountMonth = allAmount;
                                            for (SaleListDailyAverageTurnoverDto saleListDailyAverageTurnoverDto : saleListDailyAverageTurnoverDtoList) {
                                                allAmountMonth = allAmountMonth.add(saleListDailyAverageTurnoverDto.getDailyAverageTurnover().multiply(BigDecimal.valueOf(surplusDay)));
                                            }
                                            int j = 0;
                                            for (SaleListDailyAverageTurnoverDto saleListDailyAverageTurnoverDto : saleListDailyAverageTurnoverDtoList) {
                                                if (ObjectUtil.isNotNull(sysCompanyEntity.getTargetAmount())) {
                                                    // 当前企业期末预计完成金额
                                                    // 如果当前企业期末预计完成金额小于（经营目标*110%）且大于等于经营目标金额
                                                    if (finallyAmount.compareTo(sysCompanyEntity.getTargetAmount()) >= 0 || allAmountMonth.compareTo(sysCompanyEntity.getTargetAmount().multiply(BigDecimal.valueOf(1.1))) >= 0) {
                                                        if (saleList.stream().anyMatch(m -> Long.valueOf(String.valueOf(m.get("shopUnique"))).equals(saleListDailyAverageTurnoverDto.getShopUnique()))) {
                                                            // 获取当前企业去除掉已迁出供货商的营业额
                                                            BigDecimal residueAmountMonth = allAmountMonth.subtract(saleListDailyAverageTurnoverDto.getDailyAverageTurnover().multiply(BigDecimal.valueOf(surplusDay)));
                                                            if (j == saleListDailyAverageTurnoverDtoList.size() - 1) {
                                                                break;
                                                            }
                                                            LambdaQueryWrapper<BusShopEntity> shopQueryWrapper = new LambdaQueryWrapper<>();
                                                            shopQueryWrapper.eq(BusShopEntity::getShopUnique, saleListDailyAverageTurnoverDto.getShopUnique());
                                                            shopQueryWrapper.eq(BusShopEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
                                                            shopQueryWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
                                                            shopQueryWrapper.eq(BusShopEntity::getCooperateType, BusShopCooperateTypeEnum.COOPERATE_TYPE_2.getValue());
                                                            BusShopEntity shopEntity = busShopMapper.selectOne(shopQueryWrapper);
                                                            if (ObjectUtil.isNotEmpty(shopEntity)) {
                                                                boolean statisticGradeFlag = false;
                                                                Long firstMigrateCompanyId = null;
                                                                int i = 0;
                                                                for (SysCompanyEntity sysCompany : companyList) {
                                                                    if (ObjectUtil.isNotNull(sysCompany.getStatisticGrade())
                                                                            && ObjectUtil.isNull(firstMigrateCompanyId)
                                                                            && ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_0.getValue(), sysCompany.getStatisticStatus())) {
                                                                        firstMigrateCompanyId = sysCompany.getId();
                                                                    }
                                                                    if (ObjectUtil.isNotNull(sysCompany.getStatisticGrade()) && ObjectUtil.equals(shopEntity.getCompanyId(), sysCompany.getId())) {
                                                                        if (i == companyList.size() - 1) {
                                                                            migrateCompanyId = firstMigrateCompanyId;
                                                                        }
                                                                        statisticGradeFlag = true;
                                                                    } else if (ObjectUtil.isNotNull(sysCompany.getStatisticGrade()) && statisticGradeFlag && ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_0.getValue(), sysCompany.getStatisticStatus())) {
                                                                        // 获取下一顺位企业
                                                                        migrateCompanyId = sysCompany.getId();
                                                                        break;
                                                                    }
                                                                    i++;
                                                                }
                                                                // 如果存在下一顺位企业，则将该供货商迁入下一顺位企业
                                                                if (ObjectUtil.isNotNull(migrateCompanyId)) {
                                                                    SysMigrateEntity sysMigrateEntity = new SysMigrateEntity();
                                                                    sysMigrateEntity.setShopUnique(saleListDailyAverageTurnoverDto.getShopUnique());
                                                                    sysMigrateEntity.setOutCompanyId(sysCompanyEntity.getId());
                                                                    shopEntity.setModifyTime(DateUtil.date());
                                                                    shopEntity.setCompanyId(migrateCompanyId);
                                                                    busShopMapper.updateById(shopEntity);

                                                                    sysMigrateEntity.setInCompanyId(migrateCompanyId);
                                                                    sysMigrateEntity.setAuditStatus(SysMigrateAuditStatusEnm.AUDIT_SUCCESS.getValue());
                                                                    sysMigrateEntity.setAuditTime(DateUtil.date());
                                                                    sysMigrateMapper.insert(sysMigrateEntity);

                                                                    ShopPayChangeParams shopPayChangeParams = new ShopPayChangeParams();
                                                                    shopPayChangeParams.setShopUnique(saleListDailyAverageTurnoverDto.getShopUnique());
                                                                    shopPayChangeParams.setCompanyId(migrateCompanyId);
                                                                    busShopApi.shopPayChangeList(shopPayChangeParams);
                                                                    allAmountMonth = residueAmountMonth;
                                                                }
                                                            }
                                                            if (residueAmountMonth.compareTo(sysCompanyEntity.getTargetAmount()) >= 0
                                                                    && residueAmountMonth.compareTo(sysCompanyEntity.getTargetAmount().multiply(BigDecimal.valueOf(1.1))) < 0) {
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                                j++;
                                            }
                                            if (ObjectUtil.isNotNull(migrateCompanyId)) {
                                                // 将状态置为已突击
                                                sysCompanyEntity.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_2.getValue());
                                                sysCompanyMapper.updateById(sysCompanyEntity);
                                                // 将下一顺位企业置为待突击
                                                SysCompanyEntity migrateSysCompany = sysCompanyMapper.selectById(migrateCompanyId);
                                                migrateSysCompany.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_1.getValue());
                                                sysCompanyMapper.updateById(migrateSysCompany);
                                            }
                                        }
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 月初代收代付供货商迁入迁出定时任务
     */
    @XxlJob("companyMigrateIoMonthBeginJobHandler")
    private void companyMigrateIoMonthBegin() {
        // 查询“企业联合”类型的市场
        LambdaQueryWrapper<SysMarketEntity> marketQuery = new LambdaQueryWrapper<>();
        marketQuery.eq(SysMarketEntity::getManagementModel, ManagementModelEnum.MANAGEMENT_MODEL_1.getValue());
        marketQuery.eq(SysMarketEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        marketQuery.eq(SysMarketEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysMarketEntity> marketList = sysMarketMapper.selectList(marketQuery);
        if (ObjectUtil.isNotEmpty(marketList)) {
            for (SysMarketEntity market : marketList) {
                // 查询市场下的企业
                LambdaQueryWrapper<SysCompanyEntity> companyQuery = new LambdaQueryWrapper<>();
                companyQuery.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
                companyQuery.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
                companyQuery.eq(SysCompanyEntity::getMarketId, market.getId());
                companyQuery.orderByAsc(SysCompanyEntity::getStatisticGrade);
                List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyQuery);
                if (ObjectUtil.isNotEmpty(companyList)) {
                    List<Long> outCompanyIdList = new ArrayList<>();
                    Long nextCompanyId = null;
                    Long inCompanyId = null;
                    boolean statisticGradeFlag = false;
                    for (SysCompanyEntity sysCompanyEntity : companyList) {
                        // 将正在突击企业内合作方式为“代收代付”供货商迁移至下一顺位企业，生成迁入迁出记录，并将这些供货商的合作方式修改为“联营”
                        if (ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_1.getValue(), sysCompanyEntity.getStatisticStatus())) {
                            if (ObjectUtil.isNotNull(sysCompanyEntity.getStatisticGrade())) {
                                inCompanyId = sysCompanyEntity.getId();
                                statisticGradeFlag = true;
                            }
                        } else {
                            outCompanyIdList.add(sysCompanyEntity.getId());
                            if (ObjectUtil.isNotNull(sysCompanyEntity.getStatisticGrade()) && statisticGradeFlag
                                    && ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_0.getValue(),sysCompanyEntity.getStatisticStatus())) {
                                // 获取下一顺位企业
                                nextCompanyId = sysCompanyEntity.getId();
                                statisticGradeFlag = false;
                            }
                        }
                    }
                    for (Long outCompanyId : outCompanyIdList) {
                        migrateCompany(outCompanyId, inCompanyId);
                    }
                    migrateCompany(inCompanyId, nextCompanyId);
                }
            }
        }
    }

    /**
     * 月初修改已突击企业的新经营周期状态为待突击定时任务
     */
    @XxlJob("modifyCompanyStatisticStatusBeginMonthJobHandler")
    private void modifyCompanyStatisticStatusBeginMonth() {
        // 查询“企业联合”类型的市场
        LambdaQueryWrapper<SysMarketEntity> marketQuery = new LambdaQueryWrapper<>();
        marketQuery.eq(SysMarketEntity::getManagementModel, ManagementModelEnum.MANAGEMENT_MODEL_1.getValue());
        marketQuery.eq(SysMarketEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        marketQuery.eq(SysMarketEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysMarketEntity> marketList = sysMarketMapper.selectList(marketQuery);
        if (ObjectUtil.isNotEmpty(marketList)) {
            for (SysMarketEntity market : marketList) {
                // 查询市场下的企业
                LambdaQueryWrapper<SysCompanyEntity> companyQuery = new LambdaQueryWrapper<>();
                companyQuery.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
                companyQuery.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
                companyQuery.eq(SysCompanyEntity::getMarketId, market.getId());
                companyQuery.eq(SysCompanyEntity::getStatisticStatus, SysCompanyStatisticStatusEnum.STATUS_2.getValue());
                companyQuery.orderByAsc(SysCompanyEntity::getStatisticGrade);
                List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyQuery);
                if (ObjectUtil.isNotEmpty(companyList)) {
                    List<SysCompanyEntity> companyUpdateList = new ArrayList<>();
                    for (SysCompanyEntity sysCompanyEntity : companyList) {
                        if (ObjectUtil.equals(TaxTypeEnum.YEAR.getValue(), sysCompanyEntity.getTaxType())) {
                            if (DateUtil.beginOfYear(DateUtil.date()).equals(DateUtil.beginOfDay(DateUtil.date()))) {
                                sysCompanyEntity.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_0.getValue());
                                companyUpdateList.add(sysCompanyEntity);
                            }
                        } else if (ObjectUtil.equals(TaxTypeEnum.SEASON.getValue(), sysCompanyEntity.getTaxType())) {
                            if (DateUtil.beginOfQuarter(DateUtil.date()).equals(DateUtil.beginOfDay(DateUtil.date()))) {
                                sysCompanyEntity.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_0.getValue());
                                companyUpdateList.add(sysCompanyEntity);
                            }
                        } else if (ObjectUtil.equals(TaxTypeEnum.MONTH.getValue(), sysCompanyEntity.getTaxType())) {
                            sysCompanyEntity.setStatisticStatus(SysCompanyStatisticStatusEnum.STATUS_0.getValue());
                            companyUpdateList.add(sysCompanyEntity);
                        }
                    }
                    if (ObjectUtil.isNotEmpty(companyUpdateList)) {
                        sysCompanyMapper.updateBatchById(companyUpdateList);
                    }
                }
            }
        }
    }

    /**
     * 迁入迁出
     * @param companyId
     * @param migrateCompanyId
     */
    private void migrateCompany(Long companyId, Long migrateCompanyId){
        LambdaQueryWrapper<BusShopEntity> shopQuery = new LambdaQueryWrapper<>();
        shopQuery.eq(BusShopEntity::getCompanyId, companyId);
        shopQuery.eq(BusShopEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        shopQuery.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        shopQuery.eq(BusShopEntity::getCooperateType, BusShopCooperateTypeEnum.COOPERATE_TYPE_1.getValue());
        List<BusShopEntity> shopList = busShopMapper.selectList(shopQuery);
        for (BusShopEntity shopEntity : shopList) {
            if (ObjectUtil.isNotEmpty(migrateCompanyId)) {
                SysMigrateEntity sysMigrateEntity = new SysMigrateEntity();
                sysMigrateEntity.setShopUnique(shopEntity.getShopUnique());
                sysMigrateEntity.setOutCompanyId(companyId);
                shopEntity.setModifyTime(DateUtil.date());
                shopEntity.setCooperateType(BusShopCooperateTypeEnum.COOPERATE_TYPE_2.getValue());
                shopEntity.setCompanyId(migrateCompanyId);
                busShopMapper.updateById(shopEntity);

                sysMigrateEntity.setInCompanyId(migrateCompanyId);
                sysMigrateEntity.setAuditStatus(SysMigrateAuditStatusEnm.AUDIT_SUCCESS.getValue());
                sysMigrateEntity.setAuditTime(DateUtil.date());
                sysMigrateMapper.insert(sysMigrateEntity);

                ShopPayChangeParams shopPayChangeParams = new ShopPayChangeParams();
                shopPayChangeParams.setShopUnique(shopEntity.getShopUnique());
                shopPayChangeParams.setCompanyId(migrateCompanyId);
                busShopApi.shopPayChangeList(shopPayChangeParams);
            }
        }
    }
}
