package cc.buyhoo.tax.task;

import cc.buyhoo.common.bankpay.CmbPayHelper;
import cc.buyhoo.common.bankpay.enums.BB6BTHHL.ChlflgEmnus;
import cc.buyhoo.common.bankpay.enums.BB6BTHHL.TrstypEnums;
import cc.buyhoo.common.bankpay.enums.BankTypeCodeEnum;
import cc.buyhoo.common.bankpay.enums.CmbBusCodEnum;
import cc.buyhoo.common.bankpay.enums.CmbBusModEnum;
import cc.buyhoo.common.bankpay.params.CmbPayConfigParams;
import cc.buyhoo.common.bankpay.params.bb1paybh.Bb1paybhx1Params;
import cc.buyhoo.common.bankpay.params.bb1payop.Bb1payopx1Params;
import cc.buyhoo.common.bankpay.params.bb6bthhl.*;
import cc.buyhoo.common.bankpay.result.bb1paybh.Bb1paybhz1Result;
import cc.buyhoo.common.bankpay.result.bb1payop.Bb1payopz1Result;
import cc.buyhoo.common.bankpay.result.bb6bthhl.Bb6cdcbhz1Detail;
import cc.buyhoo.common.bankpay.result.bb6bthhl.Bb6cdcbhz1Result;
import cc.buyhoo.common.bankpay.result.bb6bthhl.CdcMatchCardBinZ1Detail;
import cc.buyhoo.common.bankpay.result.bb6bthhl.CdcMatchCardBinZ1Result;
import cc.buyhoo.common.datasource.entity.BaseEntity;
import cc.buyhoo.common.dingdingtalk.utils.SendDingDingTalkUtils;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.exception.BusinessException;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.tax.config.properties.PayCenterProperties;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.*;
import cc.buyhoo.tax.enums.cmb.CmbWithHoldReqstaEnum;
import cc.buyhoo.tax.result.minsheng.CmbcTranferParams;
import cc.buyhoo.tax.temporary.PayCenterPayHelper;
import cc.buyhoo.tax.temporary.params.CmbcBatchTransferParams;
import cc.buyhoo.tax.temporary.params.CmbcTransferParams;
import cc.buyhoo.tax.temporary.result.PayCenterBaseResp;
import cc.buyhoo.tax.util.CommonUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AutomaticTransferAccountTask {
    @Autowired
    private CmbPayHelper cmbPayHelper;
    @Autowired
    private BusShopBillMapper busShopBillMapper;
    @Autowired
    private BusShopBillDetailMapper busShopBillDetailMapper;
    @Autowired
    private SysCompanyBankAccountMapper sysCompanyBankAccountMapper;
    @Resource
    private SysCompanyMapper sysCompanyMapper;
    @Resource
    private BusShopSaleListMonitorMapper busShopSaleListMonitorMapper;
    @Resource
    private RedisCache redisCache;
    @Autowired
    private BusShopMapper busShopMapper;
    @Resource
    private SendDingDingTalkUtils sendDingDingTalkUtils;
    @Resource
    private SysBankListMapper sysBankListMapper;
    @Resource
    private CmbcTranferRecordMapper cmbcTranferRecordMapper;
    @Resource
    private PayCenterPayHelper payCenterPayHelper;
    @Resource
    private PayCenterProperties payCenterProperties;
    @Resource
    private BusShopBankMapper busShopBankMapper;

    /**
     * 实现自动打款
     */
    @XxlJob("automaticTransferAccountDoingJobHandler")
    public void automaticTransferAccountDoing() {
        /**
         * 1、查询需要打款的账户名单
         * 2、循环打款
         */
        List<Long> companyIdList = busShopBillMapper.queryCompanyListForPay();
        if (ObjectUtil.isNotEmpty(companyIdList)) {
            StringBuffer str = new StringBuffer();
            for (Long companyId : companyIdList) {
                try {
                    //        //查询所有的待结算数据
                    LambdaQueryWrapper<BusShopBillEntity> billWrapper = new LambdaQueryWrapper<>();
                    billWrapper.eq(BusShopBillEntity::getCompanyId, companyId);
                    billWrapper.gt(BusShopBillEntity::getUnsettledAmount, BigDecimal.ZERO);
                    List<BusShopBillEntity> billList = busShopBillMapper.selectList(billWrapper);
                    if (ObjectUtil.isEmpty(billList)) throw new BusinessException(BusShopBillErrorEnum.EMPTY_ACCOUT_ERROR);

                    LambdaQueryWrapper<SysCompanyBankAccountEntity> bankWrapper = new LambdaQueryWrapper<>();
                    bankWrapper.eq(SysCompanyBankAccountEntity::getCompanyId, companyId).last("limit 1");
                    SysCompanyBankAccountEntity bankAccount = sysCompanyBankAccountMapper.selectOne(bankWrapper);
                    if (ObjectUtil.isNotEmpty(bankAccount)) {
                        if (bankAccount.getBillType() == BillTypeEnums.TRANSFER.getMode()) {
                            //转账，有手续费
                            doTransfer(companyId, billList, null, "", null);
                        } else if (bankAccount.getBillType() == BillTypeEnums.WITHHOLD.getMode()) {
                            //代发，有手续费，可减免
                            doBB6BTHHL(companyId, billList, null, "", null);
                        }
                    } else {
                        XxlJobHelper.log("账户【" + companyId + "】无银行账户信息");
                    }
                } catch (Exception e) {
                    str.append("公司编码【").append(companyId).append("】打款失败，原因：【").append(e.getMessage()).append("】；");
                    XxlJobHelper.log("账户" + companyId + "自动打款失败" + e.getMessage());
                }
            }
            try {
                //增加打款失败后钉钉通知
                if (str.length() > 0) {
                    String body = str.toString();
                    sendDingDingTalkUtils.sendDingDingTalkSettleMsg(null, "纳统打款异常", body);
                }
            } catch (Exception e) {
                log.error("钉钉发送异常", e);
            }
        }
    }

    /**
     * 企业打款
     * @param companyId 需要打款的企业ID
     * @param billList
     * @param transferMoney
     * @param remark 转账用途
     */
    private void doTransfer(Long companyId, List<BusShopBillEntity> billList, BigDecimal transferMoney, String remark,Long userId) {
        if (StrUtil.isBlank(remark)) {
            remark = "订单结算";
        }
        SysCompanyEntity company = sysCompanyMapper.selectById(companyId);
        if (ObjectUtil.isEmpty(company.getBankCard()) || ObjectUtil.isEmpty(company.getBankName())){
            throw new BusinessException(BusShopBillErrorEnum.COMP_SETTING_ERROR);
        }

        CmbPayConfigParams cmbPayConfigParams = getCompanyPayConfigById(companyId);
        //获取民生银行的账单
        List<BusShopBillEntity> cmbcBill = getCmbcBill(billList);
        if (ObjectUtil.isNotEmpty(cmbcBill)){
            cmbcTransfer(transferMoney, remark, userId, cmbcBill, company);
        }
        if (CollectionUtil.isEmpty(billList)){
            return;
        }
        //招商打款
        Map<String, Object> transferMap = handleTransferParam(billList,company,transferMoney,remark,userId, BankTypeCodeEnum.CMB.getCode());
        List<BusShopBillDetailEntity> billDetailList = (List<BusShopBillDetailEntity>) transferMap.get("billDetailList");
        String busMode = cmbPayConfigParams.getBUSMOD() == null ? company.getTransferAccountAudit() == 1 ? CmbBusModEnum.BUSMODS400C.getMode() : CmbBusModEnum.BUSMODS4008.getMode() : cmbPayConfigParams.getBUSMOD();
        String bthNbr = "";
        if (billList.size() == 1) { //单笔转账
            Bb1payopx1Params singleTransferParams = (Bb1payopx1Params) transferMap.get("singleTransferParams");
            Bb1payopz1Result result = cmbPayHelper.bb1payop(singleTransferParams, busMode, cmbPayConfigParams);
            XxlJobHelper.log("招行单笔转账:resp:{},req:{}", JSON.toJSONString(result),JSON.toJSON(singleTransferParams));
        }else { //批量转账
            List<Bb1paybhx1Params> transferList = (List<Bb1paybhx1Params>) transferMap.get("transferList");
            Bb1paybhz1Result payResult = cmbPayHelper.batchPay(transferList, busMode, cmbPayConfigParams);
            XxlJobHelper.log("招行批量转账:resp:{},req:{}",JSON.toJSONString(payResult),JSON.toJSON(transferList));
            if (ObjectUtil.isEmpty(payResult) || ObjectUtil.isEmpty(payResult.getBthNbr())) { //支付失败
                throw new BusinessException(BusShopBillErrorEnum.TRANSFER_REQ_ERROR);
            }
            bthNbr = payResult.getBthNbr();
        }

        //设置打款状态与批次号
        for (BusShopBillDetailEntity bill : billDetailList) {
            bill.setSettledStatus(CmbTransferStatusEnum.YTJ.getStatus());
            bill.setBthNbr(bthNbr);
        }

        //保存结算明细
        busShopBillDetailMapper.insertBatch(billDetailList);
        ThreadUtil.execAsync(() -> updateSaleListMonitorList(billDetailList));
        //修改结算中金额
        List<BusShopBillEntity> updShopBillList = new ArrayList<>();
        for (BusShopBillEntity bill : billList) {
            BusShopBillEntity upd = new BusShopBillEntity();
            upd.setId(bill.getId());
            upd.setSettledingAmount(NumberUtil.add(bill.getSettledingAmount(),ObjectUtil.isEmpty(transferMoney) ? bill.getUnsettledAmount() : transferMoney));
            upd.setUnsettledAmount(ObjectUtil.isEmpty(transferMoney) ? BigDecimal.ZERO : NumberUtil.sub(bill.getUnsettledAmount(),transferMoney));
            upd.setModifyUser(userId);
            updShopBillList.add(upd);
        }
        busShopBillMapper.updateBatchById(updShopBillList);

    }

    /**
     * 企业批量代发款
     * @param companyId 需要打款的企业ID
     * @param billList 待结算的用户列表
     * @param transferMoney 打款金额
     * @param remark 备注信息
     */
    public void doBB6BTHHL(Long companyId,List<BusShopBillEntity> billList, BigDecimal transferMoney,String remark,Long userId) {
        if (StrUtil.isBlank(remark)) {
            remark = "订单结算";
        }
        SysCompanyEntity company = sysCompanyMapper.selectById(companyId);
        if (ObjectUtil.isEmpty(company.getBankCard()) || ObjectUtil.isEmpty(company.getBankName())){
            throw new BusinessException(BusShopBillErrorEnum.COMP_SETTING_ERROR);
        }

        //获取打款企业的账户信息
        LambdaQueryWrapper<SysCompanyBankAccountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCompanyBankAccountEntity::getCompanyId,companyId);
        wrapper.eq(SysCompanyBankAccountEntity::getIsDefault,1);
        SysCompanyBankAccountEntity bankAccountEntity = sysCompanyBankAccountMapper.selectOne(wrapper);

        //查询店铺信息
        Set<Long> shopUniqueList = billList.stream().map(BusShopBillEntity::getShopUnique).collect(Collectors.toSet());
        LambdaQueryWrapper<BusShopEntity> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper.eq(BusShopEntity::getCompanyId,companyId);
        shopWrapper.in(BusShopEntity::getShopUnique,shopUniqueList);
        List<BusShopEntity> shopList = busShopMapper.selectList(shopWrapper);

        //修改结算中金额
        List<BusShopBillEntity> updShopBillList = new ArrayList<>();

        //根据记录信息，依次代扣
        for (BusShopBillEntity bill : billList) {
            //未结算金额必须大于0
            if (bill.getUnsettledAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //订单是否提交成功
            boolean flag = true;
            String flagReason = "";
            CmbPayConfigParams cmbPayConfigParams = getCompanyPayConfigById(companyId);
            //过滤店铺
            BusShopEntity shop = shopList.stream().filter(s -> s.getShopUnique().equals(bill.getShopUnique())).findFirst().orElse(null);
            Bb6bthhlParams params = new Bb6bthhlParams();
            List<Bb6bthhlParamsBb6busmod> bb6busmodList = new ArrayList<>();
            List<Bb6bthhlParamsBb6cdcbhx1> bb6cdcbhx1List = new ArrayList<>();
            List<Bb6bthhlParamsBb6cdcdlx1> bb6cdcdlx1List = new ArrayList<>();

            Bb6bthhlParamsBb6busmod bb6busmod = new Bb6bthhlParamsBb6busmod();
            Bb6bthhlParamsBb6cdcbhx1 bb6cdcbhx1 = new Bb6bthhlParamsBb6cdcbhx1();
            Bb6bthhlParamsBb6cdcdlx1 bb6cdcdlx1 = new Bb6bthhlParamsBb6cdcdlx1();

            bb6busmod.setBusmod(cmbPayConfigParams.getBUSMOD());
            bb6busmod.setBuscod(CmbBusCodEnum.N03010.getMode());

            bb6busmodList.add(bb6busmod);
            params.setBb6busmod(bb6busmodList);

            bb6cdcbhx1.setBbknbr(bankAccountEntity.getBankBranch());
            bb6cdcbhx1.setBegtag("Y");
            bb6cdcbhx1.setEndtag("Y");
//            //流程实例号不需要上传，续传的时候才需要
//            bb6cdcbhx1.setReqnbr(CommonUtil.createCmbYurref(redisCache));
            BigDecimal money = new BigDecimal(bill.getUnsettledAmount().stripTrailingZeros().toPlainString());
            if (ObjectUtil.isNotNull(transferMoney)) {
                money = new BigDecimal(transferMoney.stripTrailingZeros().toPlainString());
            }

            //校验银行卡是否标准银行卡，如果非标准银联卡，需要单独上传银行卡信息
            CdcMatchCardBinParams cdcMatchCardBinParams = new CdcMatchCardBinParams();
            List<CdcMatchCardBinParamsDetail> cdcMatchCardBinDetailList = new ArrayList<>();
            CdcMatchCardBinParamsDetail cdcMatchCardBinDetail = new CdcMatchCardBinParamsDetail();
            System.out.println("" + shop.getBankCard());
            cdcMatchCardBinDetail.setAccNbr(shop.getBankCard());
            cdcMatchCardBinDetailList.add(cdcMatchCardBinDetail);
            cdcMatchCardBinParams.setCdcMatchCardBinX1(cdcMatchCardBinDetailList);

            bb6cdcbhx1.setAccnbr(bankAccountEntity.getBankCard());
            bb6cdcbhx1.setAccnam(shop.getLegalPerson());
            bb6cdcbhx1.setTtlamt(money);
            bb6cdcbhx1.setTtlcnt(1);
            bb6cdcbhx1.setTtlnum(1);
            bb6cdcbhx1.setCuramt(money);
            bb6cdcbhx1.setCurcnt(1);
            bb6cdcbhx1.setCcynbr("10");
            bb6cdcbhx1.setTrstyp(TrstypEnums.BYSA.getMode());
            bb6cdcbhx1.setNusage("代发劳务收入");
            bb6cdcbhx1.setEptdat(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
            bb6cdcbhx1.setYurref(CommonUtil.createCmbYurref(redisCache));
            bb6cdcbhx1.setChlflg(ChlflgEmnus.M.getMode());

            bb6cdcbhx1List.add(bb6cdcbhx1);
            params.setBb6cdcbhx1(bb6cdcbhx1List);

            //判断一个卡是否银联卡，如果是银联卡，不需要上传他行开户行和开户地
            CdcMatchCardBinZ1Result cdcMatchCardBinZ1Result = cmbPayHelper.cdcMatchCardBin(cdcMatchCardBinParams,cmbPayConfigParams);
            if (ObjectUtil.isNotNull(cdcMatchCardBinZ1Result) && ObjectUtil.isNotNull(cdcMatchCardBinZ1Result.getCdcMatchCardBinZ1()) && cdcMatchCardBinZ1Result.getCdcMatchCardBinZ1().size() > 0) {
                CdcMatchCardBinZ1Detail cdcMatchCardBinZ1Detail = cdcMatchCardBinZ1Result.getCdcMatchCardBinZ1().get(0);
                if (cdcMatchCardBinZ1Detail.getUniFlg().equals("Y") || (cdcMatchCardBinZ1Detail.getBnkFlg() != null && cdcMatchCardBinZ1Detail.getBnkFlg().equals("Y"))) {
                    //是银联标准卡或者是招商银行的内部卡，不需要上传他行开户行（eacbnk）和开户地（eaccty）
                } else {
                    if (null == shop.getBankName() || null == shop.getBankCity()) {
                        //此处如果银行卡有问题，直接添加失败记录，不扣未结算余额，不增加结算中余额
                        flag = false;
                        flagReason = "银行卡不是标准银联卡，请上传银行卡开户行和开户地信息";
                    }
                    bb6cdcdlx1.setEacbnk(shop.getBankName());
                    bb6cdcdlx1.setEaccty(shop.getBankCity());
                }
            }

            bb6cdcdlx1.setTrxseq(CommonUtil.createTrxseq(redisCache, bb6cdcbhx1.getYurref()));
            bb6cdcdlx1.setAccnbr(shop.getBankCard());
            bb6cdcdlx1.setAccnam(shop.getLegalPerson());
            bb6cdcdlx1.setTrsamt(money);
            bb6cdcdlx1List.add(bb6cdcdlx1);
            params.setBb6cdcdlx1(bb6cdcdlx1List);

            //批量打款
            Bb6cdcbhz1Result bb6cdcbhz1Result = cmbPayHelper.bb6bthhl(params,cmbPayConfigParams);
            System.out.println("打款申请结果" + bb6cdcbhz1Result);
            if (ObjectUtil.isNotNull(bb6cdcbhz1Result) && ObjectUtil.isNotNull(bb6cdcbhz1Result.getBb6cdcbhz1()) && bb6cdcbhz1Result.getBb6cdcbhz1().size() > 0) {
                List<Bb6cdcbhz1Detail> bb6cdcbhz1DetailList = bb6cdcbhz1Result.getBb6cdcbhz1();
                for (Bb6cdcbhz1Detail bb6cdcbhz1Detail : bb6cdcbhz1DetailList) {
                    if (bb6cdcbhz1Detail.getReqsta().equals("")) {

                    }
                }
            }

            //存储申请记录和记录详情
            BusShopBillDetailEntity billDetail = new BusShopBillDetailEntity();
            billDetail.setBillId(bill.getId());
            billDetail.setCompanyId(companyId);
            billDetail.setShopUnique(shop.getShopUnique());
            billDetail.setBillNo(bb6cdcbhx1.getYurref());
            billDetail.setBankName(shop.getBankName());
            billDetail.setBankCard(shop.getBankCard());
            billDetail.setSettledAmount(money);
            billDetail.setSettledType("1");
            billDetail.setCreateUser(userId);
            if (flag) {
                //如果成功
                billDetail.setSettledStatus(CmbWithHoldReqstaEnum.YTJ.getStatus());
            } else {
                //如果失败
                billDetail.setSettledStatus(CmbWithHoldReqstaEnum.FINF.getStatus());
                billDetail.setFailReason(flagReason);
            }
            billDetail.setCompanyBankName(bankAccountEntity.getBankName());
            billDetail.setCompanyBankCard(bankAccountEntity.getBankCard());
            billDetail.setBthNbr(bb6cdcdlx1.getTrxseq());
            billDetail.setBillType(BillTypeEnums.WITHHOLD.getMode());
            busShopBillDetailMapper.insert(billDetail);
            ThreadUtil.execAsync(() -> updateSaleListMonitor(billDetail));
            if (flag) {
                //修改店铺余额表，将打款金额增加，未打款金额扣除（只有提交成功了才修改数据，如果失败了，直接添加失败记录）
                BusShopBillEntity upd = new BusShopBillEntity();
                upd.setId(bill.getId());
                System.out.println("现有的待结算金额" + bill.getSettledingAmount());
                System.out.println("新增的待结算金额" + money);
                upd.setSettledingAmount(NumberUtil.add(bill.getSettledingAmount(),money));
                upd.setUnsettledAmount(ObjectUtil.isEmpty(transferMoney) ? BigDecimal.ZERO : NumberUtil.sub(bill.getUnsettledAmount(),transferMoney));
                upd.setModifyUser(null);
                updShopBillList.add(upd);
            }
        }
        if (ObjectUtil.isNotEmpty(updShopBillList)) {
            busShopBillMapper.updateBatchById(updShopBillList);
        }
    }

    /**
     * 获取企业打款配置
     * @param companyId
     * @return
     */
    public CmbPayConfigParams getCompanyPayConfigById(Long companyId) {
        //获取打款企业的账户信息
        LambdaQueryWrapper<SysCompanyBankAccountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCompanyBankAccountEntity::getCompanyId,companyId);
        wrapper.eq(SysCompanyBankAccountEntity::getIsDefault,1);
        SysCompanyBankAccountEntity bankAccountEntity = sysCompanyBankAccountMapper.selectOne(wrapper);

        if (null == bankAccountEntity) {
            return null;
        }
        CmbPayConfigParams cmbPayConfigParams = new CmbPayConfigParams();
        cmbPayConfigParams.setURL(bankAccountEntity.getUrl());
        cmbPayConfigParams.setPUBLIC_KEY(bankAccountEntity.getPublicKey());
        cmbPayConfigParams.setPRIVATE_KEY(bankAccountEntity.getPrivateKey());
        cmbPayConfigParams.setSYM_KEY(bankAccountEntity.getSymmetricKey());
        cmbPayConfigParams.setUID(bankAccountEntity.getUid());
        cmbPayConfigParams.setBUSMOD(bankAccountEntity.getBusmod());

        return cmbPayConfigParams;
    }

    /**
     * 批量打款参数构建
     * @return
     */
    private Map<String,Object> handleTransferParam(List<BusShopBillEntity> billList,SysCompanyEntity company,BigDecimal transferMoney, String remark,Long userId, String bankType) {
        Map<String,Object> resp = new HashMap<>();

        //企业信息
        Long companyId = company.getId();

        //查询店铺信息
        Set<Long> shopUniqueSet = billList.stream().map(BusShopBillEntity::getShopUnique).collect(Collectors.toSet());
        if (null == shopUniqueSet || shopUniqueSet.size() < 1) {
            throw new BusinessException(CommonErrorEnum.PARAM_ERROR.getCode(), "订单未绑定对应店铺信息");
        }
        LambdaQueryWrapper<BusShopBankEntity> bankWrapper = new LambdaQueryWrapper<>();
        bankWrapper.eq(BusShopBankEntity::getCompanyId, companyId);
        bankWrapper.in(BusShopBankEntity::getShopUnique, shopUniqueSet);
        bankWrapper.eq(BusShopBankEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        bankWrapper.eq(BusShopBankEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        List<BusShopBankEntity> bankList = busShopBankMapper.selectList(bankWrapper);
        if (ObjectUtil.isNotEmpty(bankList)) {
            List<SysBankListEntity> banks = sysBankListMapper.selectList(Wrappers.lambdaQuery(SysBankListEntity.class).select(SysBankListEntity::getId, SysBankListEntity::getBankCode));
            Map<Long, String> bankMap = banks.stream().collect(Collectors.toMap(SysBankListEntity::getId, SysBankListEntity::getBankCode));
            bankList.stream().forEach(v -> {
                v.setBankNo(MapUtil.getStr(bankMap, v.getBankId()));
            });
        }
        resp.put("bankList", bankList);

        //单笔转账参数
        Bb1payopx1Params singleTransferParams = new Bb1payopx1Params();
        //批量转账参数
        List<Bb1paybhx1Params> transferList = new ArrayList<>();
        List<BusShopBillDetailEntity> billDetailList = new ArrayList<>();
        for (BusShopBillEntity bill : billList) {
            //过滤店铺
            BusShopBankEntity shopBank = bankList.stream().filter(s -> s.getShopUnique().equals(bill.getShopUnique())).findFirst().orElse(null);
            if (ObjectUtil.isNull(shopBank) || ObjectUtil.isEmpty(shopBank.getBankCard()) || ObjectUtil.isEmpty(shopBank.getLegalPerson())) {
                throw new BusinessException(BusShopBillErrorEnum.SHOP_SETTING_EMPTY_ERROR);
            }

            //转账参数构建
            String yurRef = StringUtils.join(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN), RandomUtil.randomNumbers(5));
            if (billList.size() == 1) { //单笔转账
                singleTransferParams.setDbtAcc(company.getBankCard());
                singleTransferParams.setCrtAcc(shopBank.getBankCard());
                singleTransferParams.setCrtNam(shopBank.getLegalPerson());
                singleTransferParams.setCrtBnk(shopBank.getBankName());
                singleTransferParams.setCcyNbr("10");
                singleTransferParams.setTrsAmt(ObjectUtil.isEmpty(transferMoney) ? bill.getUnsettledAmount().toPlainString() : transferMoney.toPlainString());
                singleTransferParams.setNusAge(ObjectUtil.isNotEmpty(remark) ? remark : "订单结算");
                singleTransferParams.setYurRef(yurRef);
                singleTransferParams.setBrdNbr(shopBank.getBankCode());
                if (StrUtil.equals(bankType, shopBank.getBankNo())) {
                    singleTransferParams.setBnkFlg("Y");
                } else {
                    singleTransferParams.setBnkFlg("N");
                }
            }else {
                //批量转账
                // 获取总行联行号
                List<SysBankListEntity> sysBankList = sysBankListMapper.selectList(Wrappers.lambdaQuery(SysBankListEntity.class).select(SysBankListEntity::getId, SysBankListEntity::getInterbankNumber));
                Map<Long, String> bankMap = sysBankList.stream().collect(Collectors.toMap(SysBankListEntity::getId, SysBankListEntity::getInterbankNumber));
                Bb1paybhx1Params p = new Bb1paybhx1Params();
                p.setDbtAcc(company.getBankCard());
                p.setCrtAcc(shopBank.getBankCard());
                p.setCrtNam(shopBank.getLegalPerson());
                p.setCrtBnk(shopBank.getBankName());
                p.setCcyNbr("10");
                p.setTrsAmt(ObjectUtil.isEmpty(transferMoney) ? bill.getUnsettledAmount().toPlainString() : transferMoney.toPlainString());
                p.setNusAge(ObjectUtil.isNotEmpty(remark) ? remark : "订单结算");
                p.setYurRef(yurRef);
                p.setCtrNbr("N/A");
                p.setInvNbr("N/A");
                if (StrUtil.equals(bankType, shopBank.getBankNo())) {
                    p.setBnkFlg("Y");
                } else {
                    p.setBnkFlg("N");
                }
                p.setBrdNbr(MapUtil.getStr(bankMap, shopBank.getBankId()));
                transferList.add(p);
            }

            //转账详情参数构建
            BusShopBillDetailEntity d = new BusShopBillDetailEntity();
            d.setBillId(bill.getId());
            d.setCompanyId(companyId);
            d.setShopUnique(bill.getShopUnique());
            d.setBillNo(yurRef);
            d.setBankName(shopBank.getBankName());
            d.setBankCard(shopBank.getBankCard());
            d.setSettledAmount(ObjectUtil.isEmpty(transferMoney) ? bill.getUnsettledAmount() : transferMoney);
            d.setSettledType(ObjectUtil.isEmpty(transferMoney) ? "1" : "0");
            d.setSettledStatus("YTJ");
            d.setCompanyBankName(company.getBankName());
            d.setCompanyBankCard(company.getBankCard());
            d.setCreateUser(userId);
            d.setModifyUser(userId);
            billDetailList.add(d);
        }
        if (billList.size() == 1) {
            resp.put("singleTransferParams",singleTransferParams);
        }else {
            resp.put("transferList",transferList);
        }
        resp.put("billDetailList",billDetailList);
        return resp;
    }

    /**
     * 批量更新订单监控状态为结算中
     * @param billDetailList
     */
    private void updateSaleListMonitorList(List<BusShopBillDetailEntity> billDetailList) {
        try {
            if (ObjectUtil.isNotEmpty(billDetailList)) {
                List<BusShopSaleListMonitorEntity> updateList = new ArrayList<>();
                for (BusShopBillDetailEntity billDetail : billDetailList) {
                    LambdaQueryWrapper<BusShopSaleListMonitorEntity> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(BusShopSaleListMonitorEntity::getShopUnique, billDetail.getShopUnique());
                    queryWrapper.in(BusShopSaleListMonitorEntity::getSettledStatus, SaleListMonitorSettledStatusEnum.SETTLED_FAIL.getValue(), SaleListMonitorSettledStatusEnum.UNSETTLED.getValue());
                    List<BusShopSaleListMonitorEntity> list = busShopSaleListMonitorMapper.selectList(queryWrapper);
                    if (ObjectUtil.isNotEmpty(list)) {
                        BigDecimal settledAmount = list.stream().map(BusShopSaleListMonitorEntity::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (billDetail.getSettledAmount().compareTo(settledAmount) != 0) {
                            log.error("商户【" + billDetail.getShopUnique() + "】的结算金额【" + billDetail.getSettledAmount() + "】与订单监控中的金额【" + settledAmount + "】不一致");
                        }
                        for (BusShopSaleListMonitorEntity monitor : list) {
                            monitor.setSettledStatus(SaleListMonitorSettledStatusEnum.SETTLEING.getValue());
                            monitor.setBillNo(billDetail.getBillNo());
                            monitor.setModifyUser(billDetail.getModifyUser());
                            monitor.setModifyTime(new Date());
                            updateList.add(monitor);
                        }
                    }
                }
                if (ObjectUtil.isNotEmpty(updateList)) {
                    busShopSaleListMonitorMapper.updateBatchById(updateList);
                }
            }
        } catch (Exception e) {
            log.error("更新订单监控列表结算状态异常", e);
        }

    }

    private void updateSaleListMonitor(BusShopBillDetailEntity billDetail) {
        try {
            List<BusShopSaleListMonitorEntity> updateList = new ArrayList<>();
            LambdaQueryWrapper<BusShopSaleListMonitorEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusShopSaleListMonitorEntity::getShopUnique, billDetail.getShopUnique());
            queryWrapper.in(BusShopSaleListMonitorEntity::getSettledStatus, SaleListMonitorSettledStatusEnum.SETTLED_FAIL.getValue(), SaleListMonitorSettledStatusEnum.UNSETTLED.getValue());
            List<BusShopSaleListMonitorEntity> list = busShopSaleListMonitorMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(list)) {
                BigDecimal settledAmount = list.stream().map(BusShopSaleListMonitorEntity::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (billDetail.getSettledAmount().compareTo(settledAmount) != 0) {
                    log.error("商户【" + billDetail.getShopUnique() + "】的结算金额【" + billDetail.getSettledAmount() + "】与订单监控中的金额【" + settledAmount + "】不一致");
                }
                for (BusShopSaleListMonitorEntity monitor : list) {
                    monitor.setSettledStatus(SaleListMonitorSettledStatusEnum.SETTLEING.getValue());
                    monitor.setBillNo(billDetail.getBillNo());
                    monitor.setModifyUser(billDetail.getModifyUser());
                    monitor.setModifyTime(new Date());
                    updateList.add(monitor);
                }
            }
            if (ObjectUtil.isNotEmpty(updateList)) {
                busShopSaleListMonitorMapper.updateBatchById(updateList);
            }
        } catch (Exception e) {
            log.error("更新监控列表异常", e);
        }
    }

    private List<BusShopBillEntity> getCmbcBill(List<BusShopBillEntity> billList) {
        List<BusShopBillEntity> cmbcBill = new ArrayList<>();
        Set<Long> companyIds = billList.stream().map(BusShopBillEntity::getCompanyId).collect(Collectors.toSet());
        if(CollectionUtil.isNotEmpty(companyIds)){
            //获取企业信息
            List<SysCompanyBankAccountEntity> sysCompanyBankAccountEntities = sysCompanyBankAccountMapper.selectList(new LambdaQueryWrapper<SysCompanyBankAccountEntity>().in(SysCompanyBankAccountEntity::getCompanyId, companyIds));
            if (CollectionUtil.isNotEmpty(sysCompanyBankAccountEntities)){
                //获取银行编号
                List<Integer> bankIds = sysCompanyBankAccountEntities.stream().map(SysCompanyBankAccountEntity::getBankId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(bankIds)) {
                    List<SysBankListEntity> sysBankListEntities = sysBankListMapper.selectList(new LambdaQueryWrapper<SysBankListEntity>().in(SysBankListEntity::getId, bankIds));
                    if (CollectionUtil.isNotEmpty(sysBankListEntities)){
                        //民生银行标识
                        List<Long> cmbcBankId = sysBankListEntities.stream().filter(item -> ObjectUtil.equals(item.getBankCode(), "CMBC")).map(BaseEntity::getId).collect(Collectors.toList());
                        //民生银行的企业Id
                        List<Long> cmbcCompanyId = sysCompanyBankAccountEntities.stream().filter(item -> cmbcBankId.contains(item.getBankId().longValue())).map(SysCompanyBankAccountEntity::getCompanyId).collect(Collectors.toList());
                        //民生银行账单
                        cmbcBill = billList.stream().filter(item -> cmbcCompanyId.contains(item.getCompanyId())).collect(Collectors.toList());
                        log.info("民生银行的账单：{}", JSONUtil.toJsonStr(cmbcBill));
                        //非民生银行的账单走原逻辑 民生银行的账单走支付中心
                        billList.removeAll(cmbcBill);
                    }
                }
            }
        }
        return cmbcBill;
    }

    /**
     * 民生银行转账功能
     * @param transferMoney
     * @param remark
     * @param userId
     * @param cmbcBill
     * @param company
     */
    private void cmbcTransfer(BigDecimal transferMoney, String remark, Long userId, List<BusShopBillEntity> cmbcBill, SysCompanyEntity company) {
        //打款
        Map<String, Object> transferMap = handleTransferParam(cmbcBill, company, transferMoney, remark, userId, BankTypeCodeEnum.CMBC.getCode());
        if (CollectionUtil.isNotEmpty(cmbcBill)) {
            if (ObjectUtil.isEmpty(company.getMchBankId())) {
                throw new BusinessException(BusShopBillErrorEnum.BANK_CODE_ERROR);
            }
            String bthNbr="";
            List<BusShopBillDetailEntity> billDetailList = (List<BusShopBillDetailEntity>) transferMap.get("billDetailList");
            //单条转账
            if (cmbcBill.size() == 1){
                Bb1payopx1Params singleTransferParams = (Bb1payopx1Params) transferMap.get("singleTransferParams");
                CmbcTransferParams params = new CmbcTransferParams();
                params.setBankKey(BankTypeCodeEnum.CMBC.getCode());
                params.setAcntToNo(singleTransferParams.getCrtAcc());
                params.setAcntNo(company.getBankCard());
                params.setAcntToName(singleTransferParams.getCrtNam());
                params.setMchBankId(company.getMchBankId());
                params.setAmount(singleTransferParams.getTrsAmt());
                params.setMchId(company.getMchId());
                if (StrUtil.equals("N", singleTransferParams.getBnkFlg())) {
                    // 跨行转账
                    params.setExternBank("1");
                    params.setLocalFlag("9");
                }
                params.setBankName(singleTransferParams.getCrtBnk());
                params.setBankCode(singleTransferParams.getBrdNbr());
                List<BusShopBankEntity> busShopBankEntityList = (List<BusShopBankEntity>) transferMap.get("bankList");
                if (ObjectUtil.isNotEmpty(busShopBankEntityList)) {
                    BusShopBankEntity busShopBankEntity = busShopBankEntityList.stream().filter(b -> ObjectUtil.equals(cmbcBill.get(0).getShopUnique(), b.getShopUnique())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(busShopBankEntity)) {
                        params.setRcvCustType(String.valueOf(busShopBankEntity.getRcvCustType()));
                    }
                }
                PayCenterBaseResp payCenterBaseResp = payCenterPayHelper.transferPay(payCenterProperties.getUrl(),params, company.getPayCenterSecretKey());
                if (ObjectUtil.equals(payCenterBaseResp.getCode(), HttpStatus.HTTP_OK)){
                    String data = payCenterBaseResp.getData();
                    CmbcTranferParams bean = JSONUtil.toBean(data, CmbcTranferParams.class);
                    CmbcTranferRecordEntity record = new CmbcTranferRecordEntity();
                    record.setInsId(bean.getInsId());
                    record.setTrnId(bean.getTrnId());
                    if (ObjectUtil.equals(bean.getCode(),String.valueOf(CmbcTranferRecordEnum.BEGIN.getValue()))){
                        record.setCompanyId(company.getId());
                        record.setYurRef(singleTransferParams.getYurRef());
                        record.setMultipleFlg(0);
                        record.setResult(bean.getMessage());
                        record.setStatus(CmbcTranferRecordEnum.BEGIN.getValue());
                        bthNbr = bean.getInsId();
                        record.setTranferAmount(new BigDecimal(singleTransferParams.getTrsAmt()));
                        cmbcTranferRecordMapper.insert(record);
                    }else {
                        record.setCompanyId(company.getId());
                        record.setYurRef(singleTransferParams.getYurRef());
                        record.setMultipleFlg(0);
                        record.setResult(bean.getMessage());
                        record.setStatus(CmbcTranferRecordEnum.TRANSFER_FAIL.getValue());
                        cmbcTranferRecordMapper.insert(record);
                        throw new BusinessException(BusShopBillErrorEnum.TRANSFER_REQ_ERROR);
                    }


                }else {
                    CmbcTranferRecordEntity record = new CmbcTranferRecordEntity();
                    record.setStatus(CmbcTranferRecordEnum.FAIL.getValue());
                    record.setMultipleFlg(0);
                    record.setResult(payCenterBaseResp.getMsg());
                    cmbcTranferRecordMapper.insert(record);
                    throw new BusinessException(BusShopBillErrorEnum.TRANSFER_REQ_ERROR);
                }

                log.info("民生银行单笔打款:resp:{},req:{}",JSON.toJSONString(payCenterBaseResp),JSON.toJSON(params));
            }else {
                CmbcBatchTransferParams params = new CmbcBatchTransferParams();
                params.setBankKey(BankTypeCodeEnum.CMBC.getCode());
                params.setPayerAcNo(company.getBankCard());
                List<CmbcBatchTransferParams.BillInfo> billInfoList = new ArrayList<>();
                List<Bb1paybhx1Params> transferList = (List<Bb1paybhx1Params>) transferMap.get("transferList");
                //总转账金额
                BigDecimal totalAmount = BigDecimal.ZERO;
                //总转账笔数
                int totalCount = 0;
                if (CollectionUtil.isNotEmpty(transferList)) {
                    for (Bb1paybhx1Params bb1paybhx1Params : transferList) {
                        totalCount++;
                        CmbcBatchTransferParams.BillInfo billInfo = new CmbcBatchTransferParams.BillInfo();
                        billInfo.setTransactionReferenceNumber(bb1paybhx1Params.getYurRef());
                        billInfo.setReceivingAccountNumber(bb1paybhx1Params.getCrtAcc());
                        billInfo.setReceivingAccountName(bb1paybhx1Params.getCrtNam());
                        billInfo.setAmount(bb1paybhx1Params.getTrsAmt());

                        billInfo.setReceivingBankCode(bb1paybhx1Params.getBrdNbr());
                        billInfo.setReceivingBankName(bb1paybhx1Params.getCrtBnk());
                        if (!StrUtil.equals("Y", bb1paybhx1Params.getBnkFlg())) {
                            //跨行转账
                            billInfo.setBankFlag("1");
                            /**
                             * 非必输， 本行转账时不输入；跨行转账
                             * 时，6：小额，7：大额， 11：网上支付跨
                             * 行清算系统（网银互联），若不输入 100
                             * 万以上默认走大额汇路，100 万以下走网
                             * 银互联汇路
                             */
                            billInfo.setRemittanceRoute("11");
                        }
                        totalAmount = totalAmount.add(new BigDecimal(billInfo.getAmount()));
                        billInfoList.add(billInfo);
                    }
                }
                params.setBillInfos(JSONUtil.toJsonStr(billInfoList));
                params.setTotalAmt(totalAmount.toPlainString());
                params.setTotalRow(String.valueOf(totalCount));
                params.setPayerAcNo(company.getBankCard());
                params.setMchBankId(company.getMchBankId());
                params.setMchId(company.getMchId());
                PayCenterBaseResp payCenterBaseResp = payCenterPayHelper.batchTransferPay(payCenterProperties.getUrl(),params, company.getPayCenterSecretKey());
                if (ObjectUtil.equals(payCenterBaseResp.getCode(),HttpStatus.HTTP_OK)){
                    String data = payCenterBaseResp.getData();
                    CmbcTranferParams bean = JSONUtil.toBean(data, CmbcTranferParams.class);
                    CmbcTranferRecordEntity record = new CmbcTranferRecordEntity();
                    record.setInsId(bean.getInsId());
                    record.setTrnId(bean.getTrnId());
                    if (ObjectUtil.equals(bean.getCode(),String.valueOf(CmbcTranferRecordEnum.BEGIN.getValue()))){
                        record.setStatus(CmbcTranferRecordEnum.BEGIN.getValue());
                        bthNbr = bean.getInsId();
                    }else {
                        record.setStatus(CmbcTranferRecordEnum.TRANSFER_FAIL.getValue());
                    }
                    record.setMultipleFlg(1);
                    record.setResult(bean.getMessage());
                    record.setCompanyId(company.getId());
                    cmbcTranferRecordMapper.insert(record);
                }else {
                    CmbcTranferRecordEntity record = new CmbcTranferRecordEntity();
                    record.setStatus(CmbcTranferRecordEnum.FAIL.getValue());
                    record.setMultipleFlg(1);
                    record.setResult(payCenterBaseResp.getMsg());
                    cmbcTranferRecordMapper.insert(record);
                    throw new BusinessException(BusShopBillErrorEnum.TRANSFER_REQ_ERROR);
                }

                log.info("民生银行批量打款:resp:{},req:{}",JSON.toJSONString(payCenterBaseResp),JSON.toJSON(params));
            }
            //设置打款状态与批次号
            for (BusShopBillDetailEntity bill : billDetailList) {
                bill.setSettledStatus(CmbTransferStatusEnum.YTJ.getStatus());
                bill.setBthNbr(bthNbr);
            }

            //保存结算明细
            busShopBillDetailMapper.insertBatch(billDetailList);
            //异步更新订单监控表中结算状态为结算中
            ThreadUtil.execAsync(() -> updateSaleListMonitorList(billDetailList));
            //修改结算中金额
            List<BusShopBillEntity> updShopBillList = new ArrayList<>();
            for (BusShopBillEntity bill : cmbcBill) {
                BusShopBillEntity upd = new BusShopBillEntity();
                upd.setId(bill.getId());
                upd.setSettledingAmount(NumberUtil.add(bill.getSettledingAmount(),ObjectUtil.isEmpty(transferMoney) ? bill.getUnsettledAmount() : transferMoney));
                upd.setUnsettledAmount(ObjectUtil.isEmpty(transferMoney) ? BigDecimal.ZERO : NumberUtil.sub(bill.getUnsettledAmount(),transferMoney));
                upd.setModifyUser(userId);
                updShopBillList.add(upd);
            }
            busShopBillMapper.updateBatchById(updShopBillList);
        }
    }
}
