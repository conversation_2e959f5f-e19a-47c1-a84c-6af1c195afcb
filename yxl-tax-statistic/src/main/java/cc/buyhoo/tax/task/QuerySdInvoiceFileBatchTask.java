package cc.buyhoo.tax.task;


import cc.buyhoo.common.minio.MinioUploadHelper;
import cc.buyhoo.common.minio.result.MinioUploadResult;
import cc.buyhoo.common.smsg.config.properties.SmsgProperties;
import cc.buyhoo.common.smsg.core.AliyunSmsTemplate;
import cc.buyhoo.tax.config.InvoiceNotifyConfig;
import cc.buyhoo.tax.config.properties.InvoiceProperties;
import cc.buyhoo.tax.dao.BusSaleListMapper;
import cc.buyhoo.tax.dao.BusShopInvoiceMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusSaleListEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.BusShopInvoiceEntity;
import cc.buyhoo.tax.entity.invoice.QuerySdInvoiceFileEntity;
import cc.buyhoo.tax.entity.invoice.QuerySdInvoiceFileResult;
import cc.buyhoo.tax.entity.invoice.QuerySdInvoiceFileResultData;
import cc.buyhoo.tax.enums.InvoiceStatusEnum;
import cc.buyhoo.tax.enums.PeriodNumberEnum;
import cc.buyhoo.tax.enums.SaleListOrderTypeEnum;
import cc.buyhoo.tax.params.invoice.NotifyInvoiceResultParams;
import cc.buyhoo.tax.util.ImageMergeUtil;
import cc.buyhoo.tax.util.InvoiceUrlUtil;
import cc.buyhoo.tax.util.InvoiceUtil;
import cc.buyhoo.tax.util.SystemUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.mail.MailUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 子线程实现开票后查询开票文件
 * 由于开票需要时间，所以需要演示实现查询
 */
public class QuerySdInvoiceFileBatchTask extends Thread {

    private final String billNumber;
    private final QuerySdInvoiceFileEntity querySdInvoiceFileEntity;

    private final List<BusShopInvoiceEntity> invoiceEntityList;

    private final MinioUploadHelper minioUploadHelper;

    private final SmsgProperties smsgProperties;

    private final BusShopInvoiceMapper busShopInvoiceMapper;

    private InvoiceProperties invoiceProperties;

    private final InvoiceNotifyConfig invoiceNotifyConfig;
    private final BusSaleListMapper busSaleListMapper;
    private final BusShopMapper busShopMapper;
    private final InvoiceUrlUtil invoiceUrlUtil;

    public QuerySdInvoiceFileBatchTask(String billNumber, QuerySdInvoiceFileEntity querySdInvoiceFileEntity, List<BusShopInvoiceEntity> invoiceEntityList, InvoiceProperties invoiceProperties,
                                       MinioUploadHelper minioUploadHelper, SmsgProperties smsgProperties, BusShopInvoiceMapper busShopInvoiceMapper,
                                       InvoiceNotifyConfig invoiceNotifyConfig, BusSaleListMapper busSaleListMapper, BusShopMapper busShopMapper, InvoiceUrlUtil invoiceUrlUtil) {
        this.billNumber = billNumber;
        this.invoiceProperties = invoiceProperties;
        this.querySdInvoiceFileEntity = querySdInvoiceFileEntity;
        this.invoiceEntityList = invoiceEntityList;
        this.minioUploadHelper = minioUploadHelper;
        this.smsgProperties = smsgProperties;
        this.busShopInvoiceMapper = busShopInvoiceMapper;
        this.invoiceNotifyConfig = invoiceNotifyConfig;
        this.busSaleListMapper = busSaleListMapper;
        this.busShopMapper = busShopMapper;
        this.invoiceUrlUtil = invoiceUrlUtil;
    }

    public void run() {
        String faPiaoPath = invoiceProperties.getFilePath();
        try {
            //此处必须延时处理，开发票需要时间
            Thread.sleep(5000L);
            //校验文件夹是否存在，如果不存在，创建
            ImageMergeUtil.checkFilePath(faPiaoPath);

            QuerySdInvoiceFileResult querySdInvoiceFileResult = InvoiceUtil.querySdInvoiceFile(querySdInvoiceFileEntity);
            if (querySdInvoiceFileResult.getCode().equals("200")) {
                //获取开票信息成功
                List<QuerySdInvoiceFileResultData> dataList = querySdInvoiceFileResult.getData();
                //正常会有多张发票吗？
                for (QuerySdInvoiceFileResultData d : dataList) {
                    BusShopInvoiceEntity invoiceEntity = invoiceEntityList.get(0);
                    //查询店铺 取店铺类型
                    BusShopEntity busShopEntity = busShopMapper.selectOne(Wrappers.<BusShopEntity>lambdaQuery().eq(BusShopEntity::getShopUnique, invoiceEntity.getShopUnique()));
                    String url = invoiceUrlUtil.buildUrl(busShopEntity.getShopType());
                    String fileContent = d.getFileContent();
                    File file = InvoiceUtil.base64ToFile(fileContent, faPiaoPath + invoiceEntity.getShopUnique() + "_" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_FORMATTER) + "." + d.getWjlx().toLowerCase());
                    System.out.println("本地存储的绝对路径为=====" + file.getAbsolutePath());

                    //转换图片格式
                    InvoiceUtil.pdfToImage(file.getAbsolutePath(), faPiaoPath);

                    MinioUploadResult minioUploadResult = minioUploadHelper.uploadImg(file);
                    invoiceEntityList.stream().forEach(v -> {
                        v.setStatus(InvoiceStatusEnum.INVOICE_STATUS_1.getValue());
                        v.setImageUrl(minioUploadResult.getUrl());
                        v.setPeriodsLevel(PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel());
                        v.setBillNumber(billNumber);
                        ThreadUtil.execAsync(() -> {
                            try {
                                NotifyInvoiceResultParams params = new NotifyInvoiceResultParams();
                                params.setSaleListUnique(v.getSaleListUnique());
                                params.setBillNumber(billNumber);
                                params.setStatus(3);
                                params.setImageUrl(minioUploadResult.getUrl());
                                System.out.println("通知开票状态:" + url + "[" + JSONUtil.toJsonStr(params) + "]");
                                HttpUtil.post(url, JSONUtil.toJsonStr(params));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        });
                    });

                    // 更新订单 为联营订单
                    Set<String> saleListUniqueList = invoiceEntityList.stream().map(BusShopInvoiceEntity::getSaleListUnique).collect(Collectors.toSet());
                    busSaleListMapper.update(null, new LambdaUpdateWrapper<BusSaleListEntity>().set(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_2.getCode()).eq(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_1.getCode()).in(BusSaleListEntity::getSaleListUnique, saleListUniqueList));
                    ThreadUtil.execAsync(() -> {
                        //将发票发送给客户
                        String receiveMsg = invoiceEntity.getReceiveMsg();
                        if (ObjectUtil.isNotEmpty(receiveMsg)) {
                            //校验地址格式是手机号
                            if (SystemUtil.isValidPhoneNumber(receiveMsg)) {
                                //发送短信
                                Map<String, String> map = new HashMap<>();
                                //注意,imgurl不包含域名信息
                                map.put("recordId", invoiceEntity.getId().toString());

                                AliyunSmsTemplate smsTemplate = new AliyunSmsTemplate(smsgProperties);

                                smsTemplate.send(receiveMsg, smsgProperties.getTemplatedIdForInvoice(), map);

                            } else if (SystemUtil.isValidEmail(receiveMsg)) {
                                //发送邮件
                                MailUtil.send(receiveMsg, "电子发票", "谢谢您的光临", false, file);
                            } else {
                                //未上传数据，不发送
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            System.out.println("开票失败zz");
            e.printStackTrace();
            //更新发票状态为开票失败
            invoiceEntityList.stream().forEach(v -> {
                v.setStatus(InvoiceStatusEnum.INVOICE_STATUS_4.getValue());
                BusShopEntity busShopEntity = busShopMapper.selectOne(Wrappers.<BusShopEntity>lambdaQuery().eq(BusShopEntity::getShopUnique, v.getShopUnique()));
                String url = invoiceUrlUtil.buildUrl(busShopEntity.getShopType());
                try {
                    NotifyInvoiceResultParams params = new NotifyInvoiceResultParams();
                    params.setSaleListUnique(v.getSaleListUnique());
                    params.setBillNumber(v.getBillNumber());
                    params.setStatus(4);
                    params.setReviewComments(e.getMessage());
                    System.out.println("通知开票状态:" + url + "[" + JSONUtil.toJsonStr(params) + "]");
                    HttpUtil.post(url, JSONUtil.toJsonStr(params));
                } catch (Exception e1) {
                    e1.printStackTrace();
                }
            });
        }
        busShopInvoiceMapper.updateBatchById(invoiceEntityList);
    }
}
