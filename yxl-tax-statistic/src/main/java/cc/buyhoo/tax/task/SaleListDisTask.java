package cc.buyhoo.tax.task;

import cc.buyhoo.tax.dao.BusSaleListDetailMapper;
import cc.buyhoo.tax.dao.BusSaleListDisassembleMapper;
import cc.buyhoo.tax.dao.BusSaleListMapper;
import cc.buyhoo.tax.dao.BusSaleListPayDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class SaleListDisTask {
    @Resource
    private BusSaleListMapper busSaleListMapper;
    @Resource
    private BusSaleListDetailMapper saleListDetailMapper;
    @Resource
    private BusSaleListPayDetailMapper saleListPayDetailMapper;
    @Resource
    private BusSaleListDisassembleMapper saleListDisassembleMapper;

}
