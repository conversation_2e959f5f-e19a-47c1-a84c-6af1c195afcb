package cc.buyhoo.tax.task;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.tax.dao.*;
import cc.buyhoo.tax.entity.*;
import cc.buyhoo.tax.enums.sys.CompanyTypeEnum;
import cc.buyhoo.tax.facade.enums.SaleListPayMethodEnum;
import cc.buyhoo.tax.facade.enums.SaleListProfitStatusEnum;
import cc.buyhoo.tax.service.BusInventoryOrderService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 成本计算
 * @ClassName InventoryTask
 * <AUTHOR>
 * @Date 2024/6/25 15:10
 **/
@Service
@RefreshScope
@RequiredArgsConstructor
public class InventoryTask {

    private final SysCompanyMapper sysCompanyMapper;
    private final BusShopMapper busShopMapper;
    private final BusSaleListMapper busSaleListMapper;
    private final BusCompanyProfitRuleMapper busCompanyProfitRuleMapper;
    private final BusInventoryOrderService busInventoryOrderService;
    private final BusShopCoverChargeMapper busShopCoverChargeMapper;

    @XxlJob("inventoryOrderAndBatchJobHandler")
    public void inventoryJobHandler() {
        //查询数据
        Date currentTime = DateUtil.date();
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(currentTime, -1));
        Date endTime = DateUtil.endOfDay(startTime);
        Date startMonthTime = DateUtil.beginOfMonth(currentTime);
        try {
            LambdaQueryWrapper<SysCompanyEntity> companyWrapper = new LambdaQueryWrapper<>();
            companyWrapper.ne(SysCompanyEntity::getCompanyType, CompanyTypeEnum.PLATFORM.getValue());
            companyWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
            List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyWrapper);
            QueryWrapper<BusSaleListEntity> saleListSumQueryWrapper = new QueryWrapper<>();
            // 2、查询本月已计算成本总订单金额
            saleListSumQueryWrapper.select("company_id, sum(sale_list_actually_received) as monthSaleListMoney, sum(profit_total) as monthTotalCostMoney");
            saleListSumQueryWrapper.lambda()
                    .isNotNull(BusSaleListEntity::getCompanyId)
                    .eq(BusSaleListEntity::getProfitStatus, SaleListProfitStatusEnum.DONE.getValue())
                    .between(BusSaleListEntity::getSaleListDatetime, startMonthTime, currentTime)
                    .groupBy(BusSaleListEntity::getCompanyId);
            List<Map<String, Object>> saleListMoneyList = busSaleListMapper.selectMaps(saleListSumQueryWrapper);
            for (SysCompanyEntity companyEntity : companyList) {
                List<BusShopEntity> shopEntityList = busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, companyEntity.getId()).eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
                if (CollectionUtil.isEmpty(shopEntityList)) {
                    continue;
                }
                Map<Long, BusShopEntity> shopMap = shopEntityList.stream().collect(Collectors.toMap(BusShopEntity::getShopUnique, v -> v));
                List<BusSaleListEntity> saleListEntityList = busSaleListMapper.selectList(new LambdaQueryWrapper<BusSaleListEntity>().eq(BusSaleListEntity::getCompanyId, companyEntity.getId()).eq(BusSaleListEntity::getProfitStatus, SaleListProfitStatusEnum.NEW.getValue()).between(BusSaleListEntity::getSaleListDatetime, startTime, endTime).orderByAsc(BusSaleListEntity::getSaleListDatetime));
                if (CollectionUtil.isEmpty(saleListEntityList)) {
                    continue;
                }
                Map<Long, List<BusSaleListEntity>> saleListMap = saleListEntityList.stream().collect(Collectors.groupingBy(BusSaleListEntity::getShopUnique));
                // 查询企业成本计算方法
                BusCompanyProfitRuleEntity profitRule = busCompanyProfitRuleMapper.selectOne(new LambdaQueryWrapper<BusCompanyProfitRuleEntity>().eq(BusCompanyProfitRuleEntity::getCompanyId, companyEntity.getId()).eq(BusCompanyProfitRuleEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode()).orderByDesc(BusCompanyProfitRuleEntity::getId).last("limit 1"));

                if (ObjectUtil.isNull(profitRule)) {
                    busInventoryOrderService.createInventoryOrder(saleListMap, shopMap, companyEntity);
                } else {
                    List<Map<String, Object>> list = saleListMoneyList.stream().filter(v -> ObjectUtil.equals(v.get("company_id"), companyEntity.getId())).collect(Collectors.toList());
                    BigDecimal monthSaleListMoney = BigDecimal.ZERO;
                    BigDecimal monthTotalCostMoney = BigDecimal.ZERO;
                    if (CollectionUtil.isNotEmpty(list) && ObjectUtil.isNotNull(list.get(0))) {
                        monthSaleListMoney = (BigDecimal) list.get(0).getOrDefault("monthSaleListMoney", BigDecimal.ZERO);
                        monthTotalCostMoney = (BigDecimal) list.get(0).getOrDefault("monthTotalCostMoney", BigDecimal.ZERO);
                    }
                    busInventoryOrderService.createInventoryOrderProfit(monthSaleListMoney, monthTotalCostMoney, profitRule, saleListMap, shopMap, companyEntity);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @XxlJob("coverChargeStatJobHandler")
    public void coverChargeStatJobHandler() {
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -1));
        Date endTime = DateUtil.endOfDay(startTime);
        List<BusShopCoverChargeEntity> list = new ArrayList<>();
        LambdaQueryWrapper<SysCompanyEntity> companyWrapper = new LambdaQueryWrapper<>();
        companyWrapper.ne(SysCompanyEntity::getCompanyType, CompanyTypeEnum.PLATFORM.getValue());
        companyWrapper.eq(SysCompanyEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<SysCompanyEntity> companyList = sysCompanyMapper.selectList(companyWrapper);
        for (SysCompanyEntity companyEntity : companyList) {
            List<BusShopEntity> shopEntityList = busShopMapper.selectList(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getCompanyId, companyEntity.getId()).eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode()));
            if (CollectionUtil.isEmpty(shopEntityList)) {
                continue;
            }

            LambdaQueryWrapper<BusShopCoverChargeEntity> coverChargeWrapper = new LambdaQueryWrapper<>();
            coverChargeWrapper.eq(BusShopCoverChargeEntity::getCompanyId, companyEntity.getId());
            coverChargeWrapper.eq(BusShopCoverChargeEntity::getStatDate, DateUtil.format(startTime, "yyyy-MM-dd"));
            List<BusShopCoverChargeEntity> coverChargeList = busShopCoverChargeMapper.selectList(coverChargeWrapper);

            LambdaQueryWrapper<BusSaleListEntity> saleListWrapper = new LambdaQueryWrapper<>();
            saleListWrapper.eq(BusSaleListEntity::getCompanyId, companyEntity.getId());
            saleListWrapper.between(BusSaleListEntity::getSaleListDatetime, startTime, endTime);
            saleListWrapper.in(BusSaleListEntity::getSaleListState, 3, 22);
            saleListWrapper.select(BusSaleListEntity::getSaleListActuallyReceived, BusSaleListEntity::getProfitTotal, BusSaleListEntity::getSaleListUnique,
                    BusSaleListEntity::getProfitStatus, BusSaleListEntity::getSaleListServiceFee, BusSaleListEntity::getShopUnique);
            // 线上支付，服务费都是0， 所以排除掉线上支付订单
            saleListWrapper.notIn(BusSaleListEntity::getSaleListPayment, SaleListPayMethodEnum.JUHE_PAY.getCode(), SaleListPayMethodEnum.EASY_PASS_PAYMENT.getCode(), SaleListPayMethodEnum.HELIPAY.getCode());
            List<BusSaleListEntity> saleListEntityList = busSaleListMapper.selectList(saleListWrapper);

            shopEntityList.forEach(shop -> {
                BusShopCoverChargeEntity entity = new BusShopCoverChargeEntity();
                entity.setCompanyId(companyEntity.getId());
                entity.setShopUnique(shop.getShopUnique());
                entity.setStatDate(DateUtil.format(startTime, "yyyy-MM-dd"));
                entity.setCoverCharge(BigDecimal.ZERO);
                if (ObjectUtil.isNotEmpty(coverChargeList) &&
                        coverChargeList.stream().anyMatch(v -> ObjectUtil.equals(v.getShopUnique(), shop.getShopUnique()))) {
                    entity = coverChargeList.stream().filter(v -> ObjectUtil.equals(v.getShopUnique(), shop.getShopUnique())).findFirst().get();
                    entity.setCoverCharge(BigDecimal.ZERO);
                    entity.setModifyTime(DateUtil.date());
                }
                if (ObjectUtil.isNotEmpty(shop) && ObjectUtil.isNotEmpty(shop.getHasServiceFee())) {
                    if (ObjectUtil.equals(1, shop.getHasServiceFee())) {
                        if (CollectionUtil.isNotEmpty(saleListEntityList) && saleListEntityList.stream().anyMatch(v -> ObjectUtil.equals(v.getShopUnique(), shop.getShopUnique()))) {
                            for (BusSaleListEntity saleListEntity : saleListEntityList.stream().filter(v -> ObjectUtil.equals(v.getShopUnique(), shop.getShopUnique())).collect(Collectors.toList())) {
                                if (ObjectUtil.isNotEmpty(saleListEntity.getProfitStatus()) && ObjectUtil.equals(0, saleListEntity.getProfitStatus())) {
                                    XxlJobHelper.log("订单:{}还未计算成本", saleListEntity.getSaleListUnique());
                                    continue;
                                }
                                if (ObjectUtil.isNotEmpty(saleListEntity.getSaleListActuallyReceived()) && saleListEntity.getSaleListActuallyReceived().compareTo(BigDecimal.ZERO) > 0) {
                                    BigDecimal saleListCoverCharge = saleListEntity.getSaleListActuallyReceived().subtract(saleListEntity.getProfitTotal()).subtract(saleListEntity.getSaleListServiceFee());
                                    if (saleListCoverCharge.compareTo(BigDecimal.ZERO) < 0) {
                                        XxlJobHelper.log("订单:{}的服务费为{}", saleListEntity.getSaleListUnique(), saleListCoverCharge);
                                    } else {
                                        entity.setCoverCharge(entity.getCoverCharge().add(saleListCoverCharge));
                                    }
                                }
                            }
                        }
                    } else {
                        entity.setCoverCharge(BigDecimal.ZERO);
                    }
                }
                list.add(entity);
            });
        }
        busShopCoverChargeMapper.insertOrUpdateBatch(list);
    }
}
