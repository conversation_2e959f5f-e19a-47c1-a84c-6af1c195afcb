package cc.buyhoo.tax.task;


import cc.buyhoo.tax.service.impl.CmbcQueryTranferResultServiceImpl;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;


/**
 * @Description 民生银行查询转账结果
 * @ClassName BillStatisticTask
 * <AUTHOR>
 * @Date 2024/6/13 19:43
 **/
@Service
@Slf4j
public class CmbcTranferResultTask {


    @Autowired
    private CmbcQueryTranferResultServiceImpl cmbcQueryTranferResultService;

    /**
     * 单笔转账查询结果
     */
    @XxlJob("cmbcTranferJobHandler")
    public void cmbcTranfer() {
        log.info("开始查询单笔转账结果");
        cmbcQueryTranferResultService.queryTransferAndUpdate();
    }

    /**
     * 批量转账查询结果
     */
    @XxlJob("cmbcBatchTranferJobHandler")
    public void cmbcBatchTranfer() {
        cmbcQueryTranferResultService.queryBatchTransferAndUpdate();
    }
}
