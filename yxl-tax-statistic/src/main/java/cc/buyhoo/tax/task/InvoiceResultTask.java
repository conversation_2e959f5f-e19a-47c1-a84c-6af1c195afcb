package cc.buyhoo.tax.task;

import cc.buyhoo.common.minio.MinioUploadHelper;
import cc.buyhoo.common.minio.result.MinioUploadResult;
import cc.buyhoo.common.smsg.config.properties.SmsgProperties;
import cc.buyhoo.common.smsg.core.AliyunSmsTemplate;
import cc.buyhoo.tax.config.InvoiceNotifyConfig;
import cc.buyhoo.tax.config.properties.InvoiceProperties;
import cc.buyhoo.tax.dao.BusSaleListMapper;
import cc.buyhoo.tax.dao.BusShopInvoiceMapper;
import cc.buyhoo.tax.dao.BusShopMapper;
import cc.buyhoo.tax.entity.BusSaleListEntity;
import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.entity.BusShopInvoiceEntity;
import cc.buyhoo.tax.entity.invoice.GetQdInvoiceEntity;
import cc.buyhoo.tax.entity.invoice.GetQdInvoiceResult;
import cc.buyhoo.tax.entity.invoice.GetQdInvoiceResultData;
import cc.buyhoo.tax.enums.InvoiceStatusEnum;
import cc.buyhoo.tax.enums.PeriodNumberEnum;
import cc.buyhoo.tax.enums.SaleListOrderTypeEnum;
import cc.buyhoo.tax.params.invoice.NotifyInvoiceResultParams;
import cc.buyhoo.tax.util.ImageMergeUtil;
import cc.buyhoo.tax.util.InvoiceUrlUtil;
import cc.buyhoo.tax.util.InvoiceUtil;
import cc.buyhoo.tax.util.SystemUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.mail.MailUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Description 发票结果查询
 * @ClassName InvoiceResultTask
 * <AUTHOR>
 * @Date 2024-7-10 16:24:19
 **/
@Service
@RefreshScope
public class InvoiceResultTask {

    @Autowired
    private BusShopInvoiceMapper busShopInvoiceMapper;
    @Autowired
    private MinioUploadHelper minioUploadHelper;

    @Autowired
    private InvoiceNotifyConfig invoiceNotifyConfig;
    @Autowired
    private InvoiceProperties invoiceProperties;
    @Autowired
    private BusSaleListMapper busSaleListMapper;
    @Autowired
    private BusShopMapper busShopMapper;
    @Autowired
    private InvoiceUrlUtil invoiceUrlUtil;
    /**
     * 短信配置
     */
    @Autowired
    private SmsgProperties smsgProperties;
    @XxlJob("invoiceResultJobHandler")
    public void getInvoiceResult() {
        LambdaQueryWrapper<BusShopInvoiceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopInvoiceEntity::getStatus,InvoiceStatusEnum.INVOICE_STATUS_2.getValue());
        queryWrapper.isNotNull(BusShopInvoiceEntity::getBillNumber);
        List<BusShopInvoiceEntity> invoiceEntityList = busShopInvoiceMapper.selectList(queryWrapper);
        if(ObjectUtil.isNotEmpty(invoiceEntityList)){
            for (BusShopInvoiceEntity invoiceEntity : invoiceEntityList) {
                GetQdInvoiceEntity getQdInvoiceEntity = new GetQdInvoiceEntity();
                getQdInvoiceEntity.setBillNumber(invoiceEntity.getBillNumber());
                getQdInvoiceEntity.setNsrsbh(invoiceEntity.getSaleIdentity());
                String faPiaoPath = invoiceProperties.getFilePath();
                //查询店铺信息
                BusShopEntity busShopEntity = busShopMapper.selectOne(Wrappers.<BusShopEntity>lambdaQuery().eq(BusShopEntity::getShopUnique, invoiceEntity.getShopUnique()));
                String url = invoiceUrlUtil.buildUrl(busShopEntity.getShopType());
                try {
                    ImageMergeUtil.checkFilePath(faPiaoPath);
                    GetQdInvoiceResult getQdInvoiceResult = InvoiceUtil.getQdInvoice(getQdInvoiceEntity);
                    XxlJobHelper.log("开票结果:" + "["+ JSONUtil.toJsonStr(getQdInvoiceResult)+"]");
                    if (getQdInvoiceResult.getCode().equals("200")) {
                        //获取开票信息成功
                        List<GetQdInvoiceResultData> dataList = getQdInvoiceResult.getData();
                        //正常会有多张发票吗？
                        for (GetQdInvoiceResultData d : dataList) {
                            //验证开票状态
                            String statusCode = d.getStatusCode();
                            if (!statusCode.equals("030000")) {
                                continue;
                            }
                            invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_1.getValue());
                            String fileContent = d.getFileContent();
                            File file = InvoiceUtil.base64ToFile(fileContent, faPiaoPath + invoiceEntity.getSaleListUnique() + "." + d.getFileType());

                            //转换图片格式
                            InvoiceUtil.pdfToImage(file.getAbsolutePath(), faPiaoPath);
                            MinioUploadResult minioUploadResult = minioUploadHelper.uploadImg(file);
                            invoiceEntity.setImageUrl(minioUploadResult.getUrl());
                            invoiceEntity.setPeriodsLevel(PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel());
                            //将发票发送给客户
                            String receiveMsg = invoiceEntity.getReceiveMsg();
                            if (ObjectUtil.isNotEmpty(receiveMsg)) {
                                //校验地址格式是手机号
                                if (SystemUtil.isValidPhoneNumber(receiveMsg)) {
                                    //发送短信
                                    Map<String, String> map = new HashMap<>();
                                    //注意,imgurl不包含域名信息
                                    map.put("recordId", invoiceEntity.getId().toString());
                                    AliyunSmsTemplate smsTemplate = new AliyunSmsTemplate(smsgProperties);
                                    smsTemplate.send(receiveMsg, smsgProperties.getTemplatedIdForInvoice(), map);
                                } else if (SystemUtil.isValidEmail(receiveMsg)) {
                                    //发送邮件
                                    MailUtil.send(receiveMsg, "电子发票", "谢谢您的光临", false, file);
                                }
                            }
                            try {
                                NotifyInvoiceResultParams notifyInvoiceResultParams = new NotifyInvoiceResultParams();
                                notifyInvoiceResultParams.setSaleListUnique(invoiceEntity.getSaleListUnique());
                                notifyInvoiceResultParams.setBillNumber(getQdInvoiceEntity.getBillNumber());
                                notifyInvoiceResultParams.setStatus(3);
                                notifyInvoiceResultParams.setImageUrl(minioUploadResult.getUrl());
                                XxlJobHelper.log("通知开票成功状态:" + url + "["+ JSONUtil.toJsonStr(notifyInvoiceResultParams)+"]");
                                HttpUtil.post(url, JSONUtil.toJsonStr(notifyInvoiceResultParams));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    invoiceEntity.setBillNumber(getQdInvoiceEntity.getBillNumber());
                    busShopInvoiceMapper.updateById(invoiceEntity);
                    // 更新订单为联营订单
                    busSaleListMapper.update(null, new LambdaUpdateWrapper<BusSaleListEntity>().set(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_2.getCode()).eq(BusSaleListEntity::getCompanyId, invoiceEntity.getCompanyId()).eq(BusSaleListEntity::getSaleListUnique, invoiceEntity.getSaleListUnique()).eq(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_1.getCode()));
                } catch (Exception e) {
                    e.printStackTrace();
                    //更新发票状态为开票失败
                    invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_4.getValue());
                    busShopInvoiceMapper.updateById(invoiceEntity);
                    try {
                        NotifyInvoiceResultParams notifyInvoiceResultParams = new NotifyInvoiceResultParams();
                        notifyInvoiceResultParams.setSaleListUnique(invoiceEntity.getSaleListUnique());
                        notifyInvoiceResultParams.setBillNumber(invoiceEntity.getBillNumber());
                        notifyInvoiceResultParams.setStatus(4);
                        notifyInvoiceResultParams.setReviewComments(e.getMessage());
                        XxlJobHelper.log("通知开票失败状态:" + url + "["+JSONUtil.toJsonStr(notifyInvoiceResultParams)+"]");
                        HttpUtil.post(url, JSONUtil.toJsonStr(notifyInvoiceResultParams));
                    } catch (Exception e1) {
                        e1.printStackTrace();
                    }
                }
            }
        }

    }
}
