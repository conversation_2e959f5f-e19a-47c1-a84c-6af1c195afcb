package cc.buyhoo.tax.task;

import cc.buyhoo.common.bankpay.CmbPayHelper;
import cc.buyhoo.common.bankpay.config.Constant;
import cc.buyhoo.common.bankpay.enums.BB6BTHHL.Bb6bpdqyZ1RtnflgEnums;
import cc.buyhoo.common.bankpay.enums.BB6BTHHL.Bb6bpdqyZ2Stscod;
import cc.buyhoo.common.bankpay.enums.CmbBusCodEnum;
import cc.buyhoo.common.bankpay.enums.CmbResultCodeEnum;
import cc.buyhoo.common.bankpay.params.CmbPayConfigParams;
import cc.buyhoo.common.bankpay.params.bb1payqr.Bb1payqrx1Params;
import cc.buyhoo.common.bankpay.params.bb1qrybd.Bb1qrybdy1Params;
import cc.buyhoo.common.bankpay.params.bb6bthhl.Bb6bpdqyDetail;
import cc.buyhoo.common.bankpay.params.bb6bthhl.Bb6bpdqyParams;
import cc.buyhoo.common.bankpay.result.bb1payqr.Bb1payqrz1Result;
import cc.buyhoo.common.bankpay.result.bb1qrybd.Bb1qrybdz1Result;
import cc.buyhoo.common.bankpay.result.bb6bthhl.Bb6bpdqyResult;
import cc.buyhoo.common.bankpay.result.bb6bthhl.Bb6bpdqyResultBb6bpdqyz1;
import cc.buyhoo.common.bankpay.result.bb6bthhl.Bb6bpdqyResultBb6bpdqyz2;
import cc.buyhoo.tax.dao.BusShopBillDetailMapper;
import cc.buyhoo.tax.dao.BusShopBillMapper;
import cc.buyhoo.tax.dao.SysCompanyBankAccountMapper;
import cc.buyhoo.tax.entity.BusShopBillDetailEntity;
import cc.buyhoo.tax.entity.BusShopBillEntity;
import cc.buyhoo.tax.entity.SysCompanyBankAccountEntity;
import cc.buyhoo.tax.enums.BillTypeEnums;
import cc.buyhoo.tax.enums.CmbTransferStatusEnum;
import cc.buyhoo.tax.enums.cmb.CmbWithHoldReqstaEnum;
import cc.buyhoo.tax.util.CommonUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CmbTransferAccountTask {

    @Autowired
    private CmbPayHelper cmbPayHelper;

    @Autowired
    private BusShopBillMapper shopBillMapper;

    @Autowired
    private BusShopBillDetailMapper shopBillDetailMapper;
    @Autowired
    private SysCompanyBankAccountMapper sysCompanyBankAccountMapper;

    /**
     * 招行转账状态同步
     */
    @XxlJob("cmbTransferAccountStatusJobHandler")
    public void cmbTransferAccountStatus() {
        //查询未完结的转账记录
        LambdaQueryWrapper<BusShopBillDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(BusShopBillDetailEntity::getSettledStatus,getSettledStatus());
        List<BusShopBillDetailEntity> detailList = shopBillDetailMapper.selectList(detailWrapper);
        if (ObjectUtil.isEmpty(detailList)) return;

        //根据detailList获取需要打款的所有企业的打款信息
        List<Long> companyIds = detailList.stream().map(d -> d.getCompanyId()).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<SysCompanyBankAccountEntity> accountWrapper = new LambdaQueryWrapper<>();
        accountWrapper.in(SysCompanyBankAccountEntity::getCompanyId,companyIds);
        List<SysCompanyBankAccountEntity> accountList = sysCompanyBankAccountMapper.selectList(accountWrapper);


        List<BusShopBillDetailEntity> updDetailList = new ArrayList<>();
        //先区分是转账还是代发代扣
        List<BusShopBillDetailEntity> transferList = detailList.stream().filter(d -> BillTypeEnums.TRANSFER.getMode() == d.getBillType()).collect(Collectors.toList());
        List<BusShopBillDetailEntity> withHoldList = detailList.stream().filter(d -> BillTypeEnums.WITHHOLD.getMode() == d.getBillType()).collect(Collectors.toList());
        System.out.println("需要查询的转账信息" + transferList);
        System.out.println("需要查询的代扣信息" + withHoldList);
        if (ObjectUtil.isNotEmpty(transferList)) {
            //单笔支付查询结果
            List<BusShopBillDetailEntity> singlePayList = detailList.stream().filter(d -> "".equals(d.getBthNbr())).collect(Collectors.toList());
            for (BusShopBillDetailEntity d : singlePayList) {
                System.out.println("处理转账业务查询" + d);
                //获取企业打款信息
                Long companyId = d.getCompanyId();
                SysCompanyBankAccountEntity account = accountList.stream().filter(acc -> acc.getCompanyId().equals(companyId)).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(account)) {
                    XxlJobHelper.log("代扣业务号:{}，未找到企业打款信息",d);
                    continue;
                }
                CmbPayConfigParams cmbPayConfigParams = CommonUtil.convertCmbPayInfo(account);
                //获取支付配置
                if (ObjectUtil.isEmpty(cmbPayConfigParams)) {
                    //如果店铺未配置打款信息，跳过继续下一笔打款
                    continue;
                }
                //结果查询
                Bb1payqrx1Params params = new Bb1payqrx1Params();
                params.setBusCod(Constant.ZHIFU);
                params.setYurRef(d.getBillNo());
                Bb1payqrz1Result r = cmbPayHelper.bb1payqr(params,cmbPayConfigParams);
                XxlJobHelper.log("单笔支付查询接口返回:{}", JSON.toJSONString(r));

                if (ObjectUtil.isEmpty(r)) continue;

                //参数转换
                BusShopBillDetailEntity upd = new BusShopBillDetailEntity();
                upd.setId(d.getId());
                upd.setBillId(d.getBillId());
                upd.setSettledAmount(new BigDecimal(r.getTrsAmt()));
                if (!CmbTransferStatusEnum.FIN.getStatus().equals(r.getReqSts())) { //转账操作未完成，继续等待
                    upd.setSettledStatus(r.getReqSts().toUpperCase());
                }else { //转账操作已完结
                    String settledStatus = convertSettledStatus(r.getRtnFlg());
                    if (ObjectUtil.isNotEmpty(settledStatus)) {
                        upd.setSettledStatus(settledStatus);
                    }
                }
                upd.setFailReason(ObjectUtil.isEmpty(r.getRtnNar()) ? "" : r.getRtnNar());
                updDetailList.add(upd);
            }

            //批量转账：批次编号
            Set<String> bthNbrSet = withHoldList.stream().filter(d -> ObjectUtil.isNotEmpty(d.getBthNbr())).map(BusShopBillDetailEntity::getBillNo).distinct().collect(Collectors.toSet());
            System.out.println("需要查询的代扣的企业信息列表" + bthNbrSet);
            for (String btnNbr : bthNbrSet) {
                System.out.println("处理代扣业务查询" + btnNbr);
                BusShopBillDetailEntity busShopBillDetailEntity = withHoldList.stream().filter(d -> d.getBillNo().equals(btnNbr)).findFirst().get();
                //获取企业打款信息
                Long companyId = busShopBillDetailEntity.getCompanyId();
                SysCompanyBankAccountEntity account = accountList.stream().filter(acc -> acc.getCompanyId().equals(companyId)).findFirst().orElse(null);
                System.out.println("当前企业的账户信息" + account);
                if (ObjectUtil.isEmpty(account)) {
                    XxlJobHelper.log("代扣业务号:{}，未找到企业打款信息",btnNbr);
                    continue;
                }
                CmbPayConfigParams cmbPayConfigParams = CommonUtil.convertCmbPayInfo(account);
                System.out.println("当前企业转换后的查询信息");
                //获取支付配置
                if (ObjectUtil.isEmpty(cmbPayConfigParams)) {
                    //如果店铺未配置打款信息，跳过继续下一笔打款
                    continue;
                }

                Bb1qrybdy1Params params = new Bb1qrybdy1Params();
                params.setBthNbr(btnNbr);
                List<Bb1qrybdz1Result> results = cmbPayHelper.bb1qrybd(params, null,cmbPayConfigParams);
                XxlJobHelper.log("批量支付查询接口返回:{}", JSON.toJSONString(results));

                if (ObjectUtil.isEmpty(results)) continue;
                for (Bb1qrybdz1Result r : results) {
                    if (detailList.stream().noneMatch(d -> d.getBillNo().equals(r.getYurRef()))) {
                        continue;
                    }
                    BusShopBillDetailEntity detail = detailList.stream().filter(d -> d.getBillNo().equals(r.getYurRef())).findFirst().get();
                    BusShopBillDetailEntity upd = new BusShopBillDetailEntity();
                    upd.setId(detail.getId());
                    upd.setBillId(detail.getBillId());
                    upd.setSettledAmount(new BigDecimal(r.getTrsAmt()));
                    if (!CmbTransferStatusEnum.FIN.getStatus().equals(r.getReqSts())) { //转账操作未完成，继续等待
                        upd.setSettledStatus(r.getReqSts().toUpperCase());
                    }else { //转账操作已完结
                        String settledStatus = convertSettledStatus(r.getRtnFlg());
                        if (ObjectUtil.isNotEmpty(settledStatus)) {
                            upd.setSettledStatus(settledStatus);
                        }
                    }
                    upd.setFailReason(ObjectUtil.isEmpty(r.getErrTxt()) ? "" : r.getErrTxt());
                    updDetailList.add(upd);
                }
            }
        }

        //代扣的待处理数据不为空
        if (ObjectUtil.isNotEmpty(withHoldList)) {
            //业务号有可能重复，
            Set<String> bthNbrSet = withHoldList.stream().filter(d -> ObjectUtil.isNotEmpty(d.getBillNo())).map(BusShopBillDetailEntity::getBillNo).distinct().collect(Collectors.toSet());
            System.out.println("待处理的业务列表" + bthNbrSet);
            //根据业务号以此处理相同的业务编号的代扣数据
            for (String yurref : bthNbrSet) {
                //各个查询业务之间应该相互独立，不受其他业务失败的影响
                try {
                    System.out.println("处理代发业务信息" + yurref);
                    BusShopBillDetailEntity detail = withHoldList.stream().filter(d -> d.getBillNo().equals(yurref)).findFirst().get();
                    //获取企业打款信息
                    Long companyId = detail.getCompanyId();
                    SysCompanyBankAccountEntity account = accountList.stream().filter(acc -> acc.getCompanyId().equals(companyId)).findFirst().orElse(null);
                    if (ObjectUtil.isEmpty(account)) {
                        XxlJobHelper.log("代扣业务号:{}，未找到企业打款信息", yurref);
                        continue;
                    }
                    CmbPayConfigParams cmbPayConfigParams = CommonUtil.convertCmbPayInfo(account);
                    //获取支付配置
                    if (ObjectUtil.isEmpty(cmbPayConfigParams)) {
                        //如果店铺未配置打款信息，跳过继续下一笔打款
                        continue;
                    }
                    Bb6bpdqyParams params = new Bb6bpdqyParams();
                    List<Bb6bpdqyDetail> bpdqyDetailArrayList = new ArrayList<>();
                    Bb6bpdqyDetail bpdqyDetail = new Bb6bpdqyDetail();
                    bpdqyDetail.setBuscod(CmbBusCodEnum.N03010.getMode());
                    bpdqyDetail.setYurref(yurref);
                    bpdqyDetailArrayList.add(bpdqyDetail);
                    params.setBb6bpdqyy1(bpdqyDetailArrayList);

                    //调用代扣批次与明细查询 BB6BPDQY，获取该批次所有的请求结果
                    Bb6bpdqyResult bb6bpdqyResult = cmbPayHelper.bb6bpdqy(params, cmbPayConfigParams);

                    //此处需要先判断是否请求成功，如果失败，要根据失败内容进行判断，如果没有满足条件的数据，判定为失败
                    XxlJobHelper.log("代扣批次与明细查询接口返回:{}", JSON.toJSONString(bb6bpdqyResult));

                    if (ObjectUtil.isEmpty(bb6bpdqyResult)) {
                        //没有请求数据，不操作
                        continue;
                    } else if (bb6bpdqyResult.getResultcode().equals(CmbResultCodeEnum.FAIL.getMode())) {
                        //如果失败了，记录失败信息，并进入下一次循环
                        List<BusShopBillDetailEntity> busShopBillDetailEntities = withHoldList.stream().filter(d -> d.getBillNo().equals(yurref)).collect(Collectors.toList());
                        for (BusShopBillDetailEntity busShopBillDetailEntity : busShopBillDetailEntities) {
                            //全部添加到失败列表
                            BusShopBillDetailEntity upd = new BusShopBillDetailEntity();
                            upd.setId(busShopBillDetailEntity.getId());
                            upd.setBillId(busShopBillDetailEntity.getBillId());
                            upd.setSettledAmount(detail.getSettledAmount());
                            upd.setSettledStatus("FINF");
                            upd.setFailReason(bb6bpdqyResult.getResultmsg());
                            updDetailList.add(upd);
                        }
                        continue;
                    }

                    List<Bb6bpdqyResultBb6bpdqyz1> bb6bpdqyz1List = bb6bpdqyResult.getBb6bpdqyz1();
                    List<Bb6bpdqyResultBb6bpdqyz2> bb6bpdqyz2List = bb6bpdqyResult.getBb6bpdqyz2();
                    if (ObjectUtil.isEmpty(bb6bpdqyz1List) || ObjectUtil.isEmpty(bb6bpdqyz2List)) continue;
                    //实际使用过程中，此处只会返回一条记录，所以此处直接取第一项
                    boolean flag = true;
                    String settledStatus = "FIN";
                    for (Bb6bpdqyResultBb6bpdqyz1 z1 : bb6bpdqyz1List) {
                        //先判断业务请求状态，只有FIN状态，才继续处理
                        if (!CmbWithHoldReqstaEnum.FIN.getStatus().equals(z1.getReqsta())) {
                            flag = false;
                            break;
                        }
                        settledStatus = convertCmbWithHoldReqstaEnum(z1.getRtnflg());
                    }
                    System.out.println("获取到订单的最终状态为" + settledStatus);
                    if (!flag) {
                        //继续等待，处理下一个订单
                        continue;
                    }

                    System.out.println("本笔订单处理状态" + settledStatus);
                    if (settledStatus.equals(CmbWithHoldReqstaEnum.FINS.getStatus())) {
                        //当前请求为成功，不代表每一笔都成功了，需每一笔都判断一下
                        for (Bb6bpdqyResultBb6bpdqyz2 z2 : bb6bpdqyz2List) {
                            settledStatus = convertBb6bpdqyZ2Stscod(z2.getStscod());
                            if (z2.getStscod().equals(Bb6bpdqyZ2Stscod.S.getMode()) ) {
                                BusShopBillDetailEntity busShopBillDetailEntity = withHoldList.stream().filter(d -> d.getBthNbr().equals(z2.getTrxseq()) && d.getBillNo().equals(yurref)).findFirst().get();
                                BusShopBillDetailEntity upd = new BusShopBillDetailEntity();
                                upd.setId(busShopBillDetailEntity.getId());
                                upd.setBillId(busShopBillDetailEntity.getBillId());
                                upd.setSettledAmount(new BigDecimal(z2.getTrsamt()));
                                upd.setSettledStatus(settledStatus);
                                updDetailList.add(upd);
                            } else if  (z2.getStscod().equals(Bb6bpdqyZ2Stscod.E.getMode()) || z2.getStscod().equals(Bb6bpdqyZ2Stscod.A.getMode())) {
                                //添加到失败列表
                                BusShopBillDetailEntity busShopBillDetailEntity = withHoldList.stream().filter(d -> d.getBthNbr().equals(z2.getTrxseq()) && d.getBillNo().equals(yurref)).findFirst().get();
                                BusShopBillDetailEntity upd = new BusShopBillDetailEntity();
                                upd.setId(busShopBillDetailEntity.getId());
                                upd.setBillId(busShopBillDetailEntity.getBillId());
                                upd.setSettledAmount(new BigDecimal(z2.getTrsamt()));
                                upd.setSettledStatus(settledStatus);
                                upd.setFailReason(z2.getErrtxt());
                                updDetailList.add(upd);
                            }
                        }
                    } else {
                        //当前请求为失败、超时、取消、拒绝时，直接修改状态
                        for (Bb6bpdqyResultBb6bpdqyz2 z2 : bb6bpdqyz2List) {
                            BusShopBillDetailEntity busShopBillDetailEntity = withHoldList.stream().filter(d -> d.getBthNbr().equals(z2.getTrxseq()) && d.getBillNo().equals(yurref)).findFirst().get();
                            //全部添加到失败列表
                            BusShopBillDetailEntity upd = new BusShopBillDetailEntity();
                            upd.setId(busShopBillDetailEntity.getId());
                            upd.setBillId(busShopBillDetailEntity.getBillId());
                            upd.setSettledAmount(new BigDecimal(z2.getTrsamt()));
                            upd.setSettledStatus(settledStatus);
                            upd.setFailReason(z2.getErrtxt());
                            updDetailList.add(upd);
                        }
                    }
                } catch (Exception e) {
                    XxlJobHelper.log("代扣业务号:{}，查询代扣批次与明细查询接口异常", yurref);
                    e.printStackTrace();
                }
            }
        }

        //修改支付结果
        doUpdateDetail(updDetailList);
    }


    /**
     * 修改支付结果
     */
    private void doUpdateDetail(List<BusShopBillDetailEntity> updDetailList) {
        if (ObjectUtil.isEmpty(updDetailList)) return;
        //更新详情
        shopBillDetailMapper.updateBatchById(updDetailList);
        //更新对账单
        //转账成功： key:billId,value:金额
        Map<Long,BigDecimal> succTransfer = new HashMap<>();
        //转账失败： key:billId,value:金额
        Map<Long,BigDecimal> failTransfer = new HashMap<>();
        for (BusShopBillDetailEntity upd : updDetailList) {
            Long billId = upd.getBillId();
            BigDecimal money = upd.getSettledAmount();
            if (CmbTransferStatusEnum.FINS.getStatus().equals(upd.getSettledStatus())) { //银行支付成功
                if (succTransfer.containsKey(billId)) {
                    money = NumberUtil.add(succTransfer.get(billId),money);
                }
                succTransfer.put(billId,money);
            }else if (CmbTransferStatusEnum.FINF.getStatus().equals(upd.getSettledStatus()) || CmbTransferStatusEnum.FINB.getStatus().equals(upd.getSettledStatus()) ||
                    CmbTransferStatusEnum.FINR.getStatus().equals(upd.getSettledStatus()) || CmbTransferStatusEnum.FIND.getStatus().equals(upd.getSettledStatus()) ||
                    CmbTransferStatusEnum.FINC.getStatus().equals(upd.getSettledStatus()) || CmbTransferStatusEnum.FINU.getStatus().equals(upd.getSettledStatus())) {
                if (failTransfer.containsKey(billId)) {
                    money = NumberUtil.add(failTransfer.get(billId),money);
                }
                failTransfer.put(billId,money);
            }
        }

        //更新转账单
        Set<Long> succBillIds = succTransfer.keySet();
        if (ObjectUtil.isNotEmpty(succBillIds)) {
            List<BusShopBillEntity> succBillList = shopBillMapper.selectBatchIds(succBillIds);
            //转账成功
            List<BusShopBillEntity> succUpd = new ArrayList<>();
            for (BusShopBillEntity succ : succBillList) {
                BusShopBillEntity upd = new BusShopBillEntity();
                upd.setId(succ.getId());
                BigDecimal money = succTransfer.get(succ.getId());
                upd.setSettledAmount(NumberUtil.add(succ.getSettledAmount(),money));
                upd.setSettledingAmount(NumberUtil.sub(succ.getSettledingAmount(),money));
                succUpd.add(upd);
            }
            shopBillMapper.updateBatchById(succUpd);
        }

        //转账失败
        Set<Long> failBillIds = failTransfer.keySet();
        if (ObjectUtil.isNotEmpty(failBillIds)) {
            List<BusShopBillEntity> failBillList = shopBillMapper.selectBatchIds(failBillIds);
            List<BusShopBillEntity> failUpd = new ArrayList<>();
            for (BusShopBillEntity fail : failBillList) {
                BusShopBillEntity upd = new BusShopBillEntity();
                upd.setId(fail.getId());
                BigDecimal money = failTransfer.get(fail.getId());
                upd.setUnsettledAmount(NumberUtil.add(fail.getUnsettledAmount(),money));
                upd.setSettledingAmount(NumberUtil.sub(fail.getSettledingAmount(),money));
                failUpd.add(upd);
            }
            shopBillMapper.updateBatchById(failUpd);
        }

        try {
            //两次查询类请求最小调用间隔为2s
            TimeUnit.SECONDS.sleep(3);
        } catch (Exception e) {
            XxlJobHelper.log("任务执行异常:{}", ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 未完结的转账状态
     * @return
     */
    private List<String> getSettledStatus() {
        List<String> settledStatus = new ArrayList<>();
        settledStatus.add(CmbTransferStatusEnum.YTJ.getStatus());
        settledStatus.add(CmbTransferStatusEnum.AUT.getStatus());
        settledStatus.add(CmbTransferStatusEnum.NTE.getStatus());
        settledStatus.add(CmbTransferStatusEnum.BNK.getStatus());
        settledStatus.add(CmbTransferStatusEnum.FIN.getStatus());
        settledStatus.add(CmbTransferStatusEnum.OPR.getStatus());
        settledStatus.add(CmbTransferStatusEnum.APW.getStatus());
        settledStatus.add(CmbTransferStatusEnum.WRF.getStatus());

        return settledStatus;
    }

    /**
     * 转换结算状态
     * @return
     */
    private String convertSettledStatus(String rtnFlg) {
        String settledStatus = "";
        if ("S".equalsIgnoreCase(rtnFlg)) { //银行支付成功
            settledStatus = CmbTransferStatusEnum.FINS.getStatus();
        }else if ("F".equalsIgnoreCase(rtnFlg)) {
            settledStatus = CmbTransferStatusEnum.FINF.getStatus();
        }else if ("B".equalsIgnoreCase(rtnFlg)) {
            settledStatus = CmbTransferStatusEnum.FINB.getStatus();
        }else if ("R".equalsIgnoreCase(rtnFlg)) {
            settledStatus = CmbTransferStatusEnum.FINR.getStatus();
        }else if ("D".equalsIgnoreCase(rtnFlg)) {
            settledStatus = CmbTransferStatusEnum.FIND.getStatus();
        }else if ("C".equalsIgnoreCase(rtnFlg)) {
            settledStatus = CmbTransferStatusEnum.FINC.getStatus();
        }
        return settledStatus;
    }

    /**
     * 代扣结果转换
     * @param rtnFlg
     * @return
     */
    private String convertCmbWithHoldReqstaEnum(String rtnFlg) {
        String settledType = "";
        if (Bb6bpdqyZ1RtnflgEnums.S.getMode().equalsIgnoreCase(rtnFlg)) { //银行支付成功
            settledType = CmbWithHoldReqstaEnum.FINS.getStatus();
        }else if (Bb6bpdqyZ1RtnflgEnums.F.getMode().equalsIgnoreCase(rtnFlg)) {
            settledType = CmbWithHoldReqstaEnum.FINF.getStatus();
        }else if (Bb6bpdqyZ1RtnflgEnums.C.getMode().equalsIgnoreCase(rtnFlg)) {
            settledType =CmbWithHoldReqstaEnum.FINC.getStatus();
        }else if (Bb6bpdqyZ1RtnflgEnums.D.getMode().equalsIgnoreCase(rtnFlg)) {
            settledType = CmbWithHoldReqstaEnum.FIND.getStatus();
        }else if (Bb6bpdqyZ1RtnflgEnums.R.getMode().equalsIgnoreCase(rtnFlg)) {
            settledType = CmbWithHoldReqstaEnum.FINR.getStatus();
        }
        return settledType;
    }

    /**
     * 代扣结果转换
     * @param stscod
     * @return
     */
    private String convertBb6bpdqyZ2Stscod(String stscod) {
        String settledType = "";
        if (Bb6bpdqyZ2Stscod.S.getMode().equalsIgnoreCase(stscod)) { //银行支付成功
            settledType = CmbWithHoldReqstaEnum.FINS.getStatus();
        }else if (Bb6bpdqyZ2Stscod.A.getMode().equalsIgnoreCase(stscod)) {
            settledType = CmbWithHoldReqstaEnum.FINF.getStatus();
        }else if (Bb6bpdqyZ2Stscod.E.getMode().equals(stscod)) {
            settledType = CmbWithHoldReqstaEnum.FINF.getStatus();
        }
        return settledType;
    }
}
