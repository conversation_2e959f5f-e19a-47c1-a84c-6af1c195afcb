package cc.buyhoo.tax.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Description
 * @ClassName ShopProperties
 * <AUTHOR>
 * @Date 2023/7/31 8:51
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "shop")
public class ShopProperties {

    /**
     * 百货商家端注册地址
     */
    private String registerUrl;
}
