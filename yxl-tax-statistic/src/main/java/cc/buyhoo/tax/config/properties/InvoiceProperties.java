package cc.buyhoo.tax.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "file")
public class InvoiceProperties {
    /**
     * 文件服务器地址
     */
    private String fileHost;

    /**
     * 临时文件夹，文件上传至此并转化格式上传
     */
    private String filePath;
}
