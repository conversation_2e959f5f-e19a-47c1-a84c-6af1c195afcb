package cc.buyhoo.tax.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Description 百度API
 * @ClassName BaiduProperties
 * <AUTHOR>
 * @Date 2024/7/26 16:02
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "baidu")
public class BaiduProperties {
    private String ak;
    private String sk;
}
