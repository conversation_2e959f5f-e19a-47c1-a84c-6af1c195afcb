package cc.buyhoo.tax.config.FTP;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "ftp")
public class FTPConfig {

    public static String host;
    public static int port;
    public static String username;
    public static String password;
    public static String base_path;
    public static String fapiao_path;
}
