package cc.buyhoo.tax.config.cmb;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "cmb")
public class CmbConfig {
    //招商银行解密公钥，非招商银行加密公钥
    public String bankPubkey;
    //招商银行解密用字符串
    public String signStr;
    //招商银行用解密字符串，固定值
    public String USERID;
}