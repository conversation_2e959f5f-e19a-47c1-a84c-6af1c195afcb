package cc.buyhoo.tax.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Description 金圈配置
 * @ClassName PayCenterProperties
 * <AUTHOR>
 * @Date 2025/03/20 8:51
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "all-scm")
public class AllScmProperties {

    /**
     * 支付中心地址
     */
    private String url;
}
