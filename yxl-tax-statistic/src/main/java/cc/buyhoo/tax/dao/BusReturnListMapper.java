package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.BusReturnListEntity;
import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.result.returnList.ReturnListSyncDto;
import cc.buyhoo.tax.result.saleList.SaleListSyncDto;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BusReturnListMapper extends BaseMapperPlus<BusReturnListMapper, BusReturnListEntity> {


    List<Map<String, Object>> selectRetOnlineMoney(@Param("companyId") Long companyId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    int updateSettledStatus(@Param("companyId") Long companyId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Map<String, Object> selectTaxCount(@Param("shopUnique") Long shopUnique, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<Map<String, Object>> selectTaxCountGroup(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<ReturnListSyncDto> returnListSyncInfo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}