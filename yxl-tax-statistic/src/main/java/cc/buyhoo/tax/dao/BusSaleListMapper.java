package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.BusSaleListEntity;
import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.params.indexData.IndexDataParams;
import cc.buyhoo.tax.result.saleList.SaleListSyncDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BusSaleListMapper extends BaseMapperPlus<BusSaleListMapper, BusSaleListEntity> {


    BigDecimal selectTotalAmount(@Param("params") IndexDataParams params);

    List<Map<String, Object>> selectOnlineMoney(@Param("companyId") Long companyId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    int updateSettledStatus(@Param("companyId") Long companyId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    int updateProfitStatus(@Param("companyId") Long companyId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    int updateBatchProfitRecords(@Param("list") List<BusSaleListEntity> updateSaleList);

    Map<String, Object> selectTaxSyncCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Map<String, Object> selectTaxCount(@Param("shopUnique") Long shopUnique, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<Map<String, Object>> selectTaxCountGroup(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SaleListSyncDto> saleListSyncInfo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}