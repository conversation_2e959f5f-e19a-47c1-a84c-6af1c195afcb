package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.SysRoleEntity;
import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.entity.SysRoleMenuEntity;
import cc.buyhoo.tax.entity.SysUserRoleEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysRoleMapper extends BaseMapperPlus<SysRoleMapper, SysRoleEntity> {

    /**
     * 删除角色权限
     * @param roleId
     */
    public void deleteRoleMenuByRoleId(Long roleId);

    /**
     * 根据角色编号查询用户
     * @return
     */
    public List<SysUserRoleEntity> findUserByRoleId(List<Long> list);

    /**
     * 保存用户角色
     * @param userRole
     */
    public void insertUserRole(SysUserRoleEntity userRole);

    /**
     * 根据用户id删除用户角色
     * @param userId
     */
    public void deleteUserRoleByUserId(Long userId);

    /**
     * 根据菜单编号删除菜单
     * @param menuId
     */
    public void deleteRoleMenuByMenuId(Long menuId);

}