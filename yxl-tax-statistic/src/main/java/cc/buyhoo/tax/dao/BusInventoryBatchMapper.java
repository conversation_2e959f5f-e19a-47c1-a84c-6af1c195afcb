package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.BusInventoryBatchEntity;
import cc.buyhoo.common.datasource.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BusInventoryBatchMapper extends BaseMapperPlus<BusInventoryBatchMapper, BusInventoryBatchEntity> {


    @Select("select ib.shop_unique, ib.profit_money from bus_shop s, bus_inventory_batch ib where s.company_id = #{companyId} and s.company_id = ib.company_id and s.shop_unique = ib.shop_unique and s.has_service_fee = 0 and ib.batch_date = #{batchDate}")
    List<Map<String, Object>> selectShopServiceFee(@Param("companyId") Long companyId, @Param("batchDate") String batchDate);
}