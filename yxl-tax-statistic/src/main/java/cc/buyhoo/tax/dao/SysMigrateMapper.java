package cc.buyhoo.tax.dao;

import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.entity.SysMigrateEntity;
import cc.buyhoo.tax.params.sysMigrate.SysMigrateExportParams;
import cc.buyhoo.tax.params.sysMigrate.SysMigratePageParams;
import cc.buyhoo.tax.result.sysMigrate.SysMigrateDetailResult;
import cc.buyhoo.tax.result.sysMigrate.SysMigrateDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @Description 迁入迁出管理
* @ClassName SysMigrate
* <AUTHOR> 
* @Date 2024-08-29
**/
public interface SysMigrateMapper extends BaseMapperPlus<SysMigrateMapper, SysMigrateEntity> {

    List<SysMigrateDto> selectPageList(@Param("params") SysMigratePageParams pageParams);

    List<SysMigrateDto> selectList(@Param("params") SysMigrateExportParams pageParams);

    SysMigrateDetailResult selectDetailById(@Param("id") Long id);
}