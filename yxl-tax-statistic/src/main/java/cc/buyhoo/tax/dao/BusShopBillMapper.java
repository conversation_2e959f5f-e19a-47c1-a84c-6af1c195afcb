package cc.buyhoo.tax.dao;

import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.entity.BusShopBillEntity;
import cc.buyhoo.tax.result.busShopBill.BusShopBillDto;
import cc.buyhoo.tax.result.busShopBill.BusShopBillExcel;
import cc.buyhoo.tax.params.busShopBill.BusShopBillParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* @Description 供货商账单表
* @ClassName BusShopBill
* <AUTHOR> 
* @Date 2023-07-27
**/
public interface BusShopBillMapper extends BaseMapperPlus<BusShopBillMapper, BusShopBillEntity> {

    List<Long> queryCompanyListForPay();

    List<BusShopBillDto> selectPageList(@Param("params") BusShopBillParams busShopBillParams);

    List<BusShopBillExcel> selectExportList(@Param("params") BusShopBillParams busShopBillParams);
}