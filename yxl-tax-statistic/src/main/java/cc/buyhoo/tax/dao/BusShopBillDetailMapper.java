package cc.buyhoo.tax.dao;


import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.entity.BusShopBillDetailEntity;
import cc.buyhoo.tax.params.busShopBill.BusShopBillDetailParams;
import cc.buyhoo.tax.result.busShopBill.BusShopBillDetailDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @Description 供货商账单明细表
* @ClassName BusShopBillDetail
* <AUTHOR> 
* @Date 2023-07-27
**/
public interface BusShopBillDetailMapper extends BaseMapperPlus<BusShopBillDetailMapper, BusShopBillDetailEntity> {

    List<BusShopBillDetailDto> selectPageList(@Param("params") BusShopBillDetailParams busShopBillDetailParams);
}