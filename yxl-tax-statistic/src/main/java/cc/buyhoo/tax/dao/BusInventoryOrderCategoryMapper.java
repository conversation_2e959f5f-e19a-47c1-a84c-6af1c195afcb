package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.BusInventoryOrderCategoryEntity;
import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.params.inventoryOrderCategory.InventoryOrderCategoryParams;
import cc.buyhoo.tax.result.inventoryOrderCategory.InventoryOrderCategoryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusInventoryOrderCategoryMapper extends BaseMapperPlus<BusInventoryOrderCategoryMapper, BusInventoryOrderCategoryEntity> {


    List<InventoryOrderCategoryDto> selectPageList(@Param("params") InventoryOrderCategoryParams params);
}