package cc.buyhoo.tax.dao;

import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.entity.SysCompanyEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface SysCompanyMapper extends BaseMapperPlus<SysCompanyMapper, SysCompanyEntity> {

    @Select("SELECT sm.id as marketId,Max(sc.statistic_grade) as maxStatisticGrade from sys_company sc right join sys_market sm on sc.market_id = sm.id group by sm.id")
    List<Map<String, Object>> queryMaxStatisticGrade();
}