package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.BusShopEntity;
import cc.buyhoo.tax.result.enterpriseDataStatistics.Statistics;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface EnterpriseDataStatisticsMapper extends BaseMapper<BusShopEntity> {

    /**
     *  //查询每个门店下的bus_shop的 : 商户数,今日新增商户数,一般纳税人数,今日新增一般纳税人数,小规模纳税人数,今日新增小规模纳税人数,个体工商户数,今日新增个体工商户数
     * @param
     * @return
     */
    List<Statistics> selectMerchant(@Param("shopUnique") List<Long> shopUnique,
                                    @Param("createTime") LocalDate startTime,
                                    @Param("modifyTime") LocalDate endTime,
                                    @Param("companyId")Long companyId
    );

    /**
     *   //查询每个门店下的sale_list:销售额,今日销售额,订单数,今日订单数
     * @param
     * @return
     */
    <startTime> List<Statistics> selectOrderForm(@Param("shopUnique") List<Long> shopUnique,
                                                 @Param("createTime") LocalDate startTime,
                                                 @Param("modifyTime") LocalDate endTime,
                                                 @Param("companyId")Long companyId);


    /**
     *  //查询每个门店下的bus_shop_invoice,开票额,今日开票额,开票单数,今日开票单数
     * @param
     * @return
     */
    List<Statistics> selectmakeOutAnInvoice(@Param("shopUnique") List<Long> shopUnique,
                                            @Param("createTime") LocalDate startTime,
                                            @Param("modifyTime") LocalDate modifyTime,
                                            @Param("companyId")Long companyId);


    /**
     * 获取企业下的所有的区县和街道编码
     * @param companyId
     * @return
     */

    List<String> DistrictAndCountyStreet(Long companyId);

}
