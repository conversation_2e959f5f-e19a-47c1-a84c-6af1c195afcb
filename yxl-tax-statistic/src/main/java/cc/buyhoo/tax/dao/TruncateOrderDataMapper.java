package cc.buyhoo.tax.dao;

public interface TruncateOrderDataMapper {

    /**
     * 清空订单数据
     */
    public void truncate1();
    public void truncate2();
    public void truncate3();
    public void truncate4();
    public void truncate5();
    public void truncate6();
    public void truncate7();
    public void truncate8();
    public void truncate9();
    public void truncate10();
    public void truncate11();
    public void truncate12();
    public void truncate13();
    public void truncate14();
    public void truncate15();
    public void truncate16();
    public void truncate17();
    public void truncate18();
    public void truncate19();

    public void truncate20();

    public void truncate21();

}
