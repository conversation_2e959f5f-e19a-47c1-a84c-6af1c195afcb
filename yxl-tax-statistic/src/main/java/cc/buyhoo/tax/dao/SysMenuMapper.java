package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.SysMenuEntity;
import cc.buyhoo.common.datasource.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysMenuMapper extends BaseMapperPlus<SysMenuMapper, SysMenuEntity> {

    /**
     * 根据角色查询权限
     * @param roleId
     * @return
     */
    List<Long> getMenuIdByRoleId(@Param("roleId") Long roleId);

}