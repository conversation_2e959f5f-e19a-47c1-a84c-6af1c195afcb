package cc.buyhoo.tax.dao;

import cc.buyhoo.tax.entity.BusSaleListDetailEntity;
import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.entity.invoice.InvoiceGoodsExternalEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusSaleListDetailMapper extends BaseMapperPlus<BusSaleListDetailMapper, BusSaleListDetailEntity> {

    List<InvoiceGoodsExternalEntity> querySaleListDetailForInvoice(@Param("saleListUnique") String saleListUnique);

}