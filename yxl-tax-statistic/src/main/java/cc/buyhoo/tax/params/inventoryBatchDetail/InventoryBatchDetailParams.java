package cc.buyhoo.tax.params.inventoryBatchDetail;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 入库批次明细请求参数
 * @ClassName InventoryBatchDetailParams
 * <AUTHOR>
 * @Date 2023/8/3 8:29
 **/
@Data
public class InventoryBatchDetailParams extends PageParams implements Serializable {

    /**
     * 入库批次ID
     */
    @NotNull(message = "请选择批次")
    private Long batchId;
}
