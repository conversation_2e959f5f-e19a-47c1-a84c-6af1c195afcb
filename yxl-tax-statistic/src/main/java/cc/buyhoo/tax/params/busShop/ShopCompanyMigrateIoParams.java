package cc.buyhoo.tax.params.busShop;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ShopCompanyMigrateIoParams
 * <AUTHOR>
 * @Date 2024/9/23 15:39
 */
@Data
public class ShopCompanyMigrateIoParams implements Serializable {
    /**
     * id
     */
    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 迁入企业
     */
    @NotNull(message = "迁入企业不能为空")
    private Long migrateInCompanyId;
    /**
     * 合作方式
     */
    @NotNull(message = "合作方式不能为空")
    @Range(min = 1, max = 2, message = "合作方式不合法")
    private Integer cooperateType;
}
