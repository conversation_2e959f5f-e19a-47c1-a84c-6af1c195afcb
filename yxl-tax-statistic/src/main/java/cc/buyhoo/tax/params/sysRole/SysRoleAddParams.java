package cc.buyhoo.tax.params.sysRole;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 角色新增参数
 */
@Data
public class SysRoleAddParams implements Serializable {
    private static final long serialVersionUID = 7911344683204657711L;
    /**
     * 角色名称
     */
    @NotBlank(message = "请输入角色名称")
    private String roleName; //角色名称

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long companyId;

    /**
     * 备注
     */
    private String remark; //备注

    /**
     * 菜单ID数组
     */
    @NotEmpty(message = "请选择菜单")
    private List<Long> menuIds; //菜单集合

}
