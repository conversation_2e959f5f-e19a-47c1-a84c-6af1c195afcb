package cc.buyhoo.tax.params.cmb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 创建订单所需要的参数信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateNewOrderParams implements Serializable {
    //银行ID
    private Integer bankId;
    //银行内部交易码
    private String bankTrscode;
    //虚拟主账户信息
    private String accnbr;
    //支付金额
    private BigDecimal payAmount;
    //支付流水号
    private String tradeNo;
    //虚拟子商户信息（就是哪个店打的款，订单要关联到哪个店）
    private String frmcod;
    //订单生成时间，如果不传，按当前的系统时间
    private String createTime;
}
