package cc.buyhoo.tax.params.busShopBill;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class ManualTransferAccountsParams {

    @NotNull(message = "请选择要转账的供货商")
    private Long shopUnique; //供货商编号

    @NotNull(message = "请填写转账金额")
    @DecimalMin(value = "0.01",message = "转账金额需大于0")
    private BigDecimal money; //转账金额

    private String remark; //用途，默认值：订单结算

}
