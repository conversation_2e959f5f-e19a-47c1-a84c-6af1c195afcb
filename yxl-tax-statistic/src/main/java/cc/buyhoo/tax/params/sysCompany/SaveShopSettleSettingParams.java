package cc.buyhoo.tax.params.sysCompany;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SaveShopSettleSettingParams {

    @NotBlank(message = "请输入开户行")
    private String bankName; //开户行

    @NotBlank(message = "请输入对公账户")
    private String bankCard; //银行账号

    @NotNull(message = "请选择是否需要审批")
    private Integer transferAccountAudit; //转账是否要审批:1无需审批2需审批

    @NotNull(message = "请选择是否支持即时到账")
    private Integer instantTransfer;

}
