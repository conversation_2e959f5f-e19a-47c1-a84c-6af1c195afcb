package cc.buyhoo.tax.params.cmb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateBusInventoryOrderParams implements Serializable {
    //企业ID
    private Long companyId;
    //三方订单号
    private String thirdTradeNo;
    //交易金额
    private BigDecimal totalMoney;
    //订单类型
    private Integer inventoryType;
    //对应店铺编号
    private Long shopUnique;
}
