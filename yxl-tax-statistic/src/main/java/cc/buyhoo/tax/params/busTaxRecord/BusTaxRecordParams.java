package cc.buyhoo.tax.params.busTaxRecord;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 申报记录请求参数
 * @ClassName BusTaxRecordParams
 * <AUTHOR>
 * @Date 2023/8/11 11:32
 **/
@Data
public class BusTaxRecordParams extends PageParams {

    private Long id;
    /**
     * 企业ID
     */
    private Long companyId;
    /**
     * 纳统类型,1-按月,2-按季度,2-按年
     */
    private String taxType;
    /**
     * 申报金额
     */
    private BigDecimal targetAmount;
    /**
     * 营业金额
     */
    private BigDecimal taxAmount;
    /**
     * 申报日期
     */
    private String taxDate;
    /**
     * 申报状态：0-未完成，1-已完成
     */
    private String taxStatus;
    /**
     * 备注
     */
    private String remark;

}
