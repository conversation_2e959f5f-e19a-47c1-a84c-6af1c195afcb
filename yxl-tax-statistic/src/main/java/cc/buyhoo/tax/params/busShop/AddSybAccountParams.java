package cc.buyhoo.tax.params.busShop;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ShopCompanyMigrateIoParams
 * <AUTHOR>
 * @Date 2025/03/08 15:39
 */
@Data
public class AddSybAccountParams implements Serializable {


    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 银行编号 TODO
     */

    private String bankCode="CMBC";

    /**
     * 子账号 有则传
     */
    private String subAccount;

    /**
     * 子账簿名称
     */
    @NotNull(message = "子账簿名称不能为空")
    private String subAccountName;

    /**
     * 联系人名称
     */
    @NotNull(message = "联系人名称不能为空")
    private String contactUserName;

    /**
     * 联系人手机号
     */
    @NotNull(message = "联系人手机号不能为空")
    private String contactPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 子账簿状态  0-正常 1-只入不出、2-只出不入、3-不出不入、
     */
    @NotNull(message = "子账簿状态不能为空")
    private String subAccountStatus="0";

    /**
     * 推送通知  00-不通知、01-短信、02-邮箱
     */
    @NotNull(message = "推送通知不能为空")
    private String pubNotice="01";
    /**
     * 店铺
     */
    private String shopUnique;
}
