package cc.buyhoo.tax.params.busShop;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

/** 供应商查询参数
 * @Description
 * @ClassName ShopListParams
 * <AUTHOR>
 * @Date 2023/7/26 15:32
 **/
@Data
public class ShopListParams extends PageParams {

    /**
     * 企业ID
     */
    private Long companyId;


    /**
     * 供货商名称
     */
    private String shopName;

    /**
     * 联系电话
     */
    private String shopPhone;

    /**
     * 所属市场ID
     */
    private Long marketId;

    /**
     * 供货商id
     */
    private Long shopId;

    /**
     * 区编码
     */
    private Long countyCode;
    /**
     * 镇编码
     */
    private Long townCode;
    /**
     * 商户性质
     */
    private Integer shopNature;
}
