package cc.buyhoo.tax.params.busGoods;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 商品分类绑定参数
 * @ClassName GoodsCategoryBindParams
 * <AUTHOR>
 * @Date 2023/7/30 13:53
 **/
@Data
public class GoodsCategoryBindParams implements Serializable {

    /**
     * 一级分类
     */
    @NotNull(message = "一级分类不能为空")
    private Long categoryId;
    /**
     * 二级分类
     */
    @NotNull(message = "二级分类不能为空")
    private Long categoryTwoId;
    /**
     * 商品ID数组
     */
    @NotNull(message = "商品ID数组不能为空")
    private List<Long> goodsIds;
}
