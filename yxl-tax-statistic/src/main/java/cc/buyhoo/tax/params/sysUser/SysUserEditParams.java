package cc.buyhoo.tax.params.sysUser;

import cc.buyhoo.tax.result.userArea.AreaDictQueryDto;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户修改参数
 */
@Data
public class SysUserEditParams implements Serializable {

    private static final long serialVersionUID = -2773825401145198486L;
    /**
     * 用户Id
     */
    @NotNull(message = "请选择用户")
    private Long id;

    /**
     * 用户名
     */
    @NotBlank(message = "请输入用户名")
    private String username;
    /**
     * 角色ID
     */
    @NotNull(message = "请选择角色")
    private Long roleId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 数据权限树
     */
    private List<AreaDictQueryDto> dataScopeList;
    /**
     * 数据维度
     */
    private Integer level;
}
