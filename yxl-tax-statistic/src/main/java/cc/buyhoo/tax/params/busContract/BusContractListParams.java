package cc.buyhoo.tax.params.busContract;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 合同列表查询参数
 * @ClassName BusContractListParams
 * <AUTHOR>
 * @Date 2024/7/13 11:17
 **/
@Data
public class BusContractListParams extends PageParams implements Serializable {
    private static final long serialVersionUID = 1118491824564394804L;

    /**
     * 合同名称，模糊查询
     */
    private String contractName;
    /**
     * 合同编码，模糊查询
     */
    private String contractNo;
    /**
     * 商户编码
     */
    private Long shopUnique;
    /**
     * 订单编码，模糊查询
     */
    private String orderNo;
    /**
     * 合同状态：0-草拟，1-待审，2-已签署，3-执行中，4-已完成，5-终止
     */
    private Integer contractStatus;
    /**
     * 合同类型：1-销售合同，2-采购合同，3-联营合同，4-其它
     */
    private Integer contractType;
    /**
     * 签署起止时间
     */
    private String[] signTime;
}
