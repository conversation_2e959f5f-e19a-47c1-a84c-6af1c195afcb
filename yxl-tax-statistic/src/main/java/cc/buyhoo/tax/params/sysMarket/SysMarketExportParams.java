package cc.buyhoo.tax.params.sysMarket;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 市场分页查询参数
 * @ClassName BusMarketPageParams
 * <AUTHOR>
 * @Date 2024/8/23 15:32
 **/
@Data
public class SysMarketExportParams implements Serializable {
    private static final long serialVersionUID = 1495566056892634421L;

    /**
     * 市场名称(模糊查询)
     */
    private String marketName;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
}
