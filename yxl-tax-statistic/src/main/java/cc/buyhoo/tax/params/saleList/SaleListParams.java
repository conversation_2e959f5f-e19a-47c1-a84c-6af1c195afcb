package cc.buyhoo.tax.params.saleList;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

@Data
public class SaleListParams extends PageParams {

    private Long shopUnique; //店铺名称

    private String dateStart; //开始时间

    private String dateEnd; //结束时间

    private String saleListUnique; //订单编号

    private String orderType; //订单类型

    private String shopName; //商户名称

    private String countyCode; //区县编码

    private String townCode; //乡镇编码

}
