package cc.buyhoo.tax.params.sysMarket;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 市场新增参数
 * @ClassName BusMarketAddParams
 * <AUTHOR>
 * @Date 2024/8/23 15:05
 **/
@Data
public class SysMarketAddParams implements Serializable {
    private static final long serialVersionUID = -6835004696076130100L;
    /**
     * 市场名称
     */
    @NotBlank(message = "市场名称不能为空")
    @Size(max = 100, message = "市场名称长度不能超过100个字符")
    private String marketName;

    /**
     * 所在地区ID数组
     */
    @NotNull(message = "所在地区不能为空")
    private Long[] cityInfoIds;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    @Size(max = 500, message = "详细地址长度不能超过500个字符")
    private String address;

    /**
     * 经营模式：0-企业独营，1-企业联营
     */
    @NotNull(message = "经验模式不能为空")
    private Integer managementModel;

    /**
     * 负责人
     */
    private String legalPerson;

    /**
     * 负责人电话
     */
    private String personMobile;

    /**
     * 企业介绍
     */
    private String introduction;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
}
