package cc.buyhoo.tax.params.sysUser;

import cc.buyhoo.tax.result.userArea.AreaDictQueryDto;
import cc.buyhoo.tax.result.userArea.AreaDictQueryList;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 新增用户参数
 */
@Data
public class SysUserAddParams implements Serializable {

    private static final long serialVersionUID = -3012941603235845302L;
    /**
     * 用户名
     */
    @NotBlank(message = "请输入用户名")
    private String username;
    /**
     * 密码
     */
    @NotBlank(message = "请输入密码")
    private String password;
    /**
     * 角色ID
     */
    @NotNull(message = "请选择角色")
    private Long roleId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
    /**
     * 备注
     */
    private String remark;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 数据权限树
     */
    private List<AreaDictQueryDto> dataScopeList;
    /**
     * 数据维度
     */
    private Integer level;

}
