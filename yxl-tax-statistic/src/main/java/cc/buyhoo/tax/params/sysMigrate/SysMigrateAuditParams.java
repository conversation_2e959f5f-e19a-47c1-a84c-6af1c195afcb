package cc.buyhoo.tax.params.sysMigrate;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 迁入迁出审核参数
 * @ClassName SysMigrateAuditParams
 * <AUTHOR>
 * @Date 2024/8/29 14:31
 **/
@Data
public class SysMigrateAuditParams implements Serializable {
    private static final long serialVersionUID = 6264245331637521275L;
    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 审核状态：0-待审核，1-通过，2-不通过
     */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    /**
     * 审核备注
     */
    private String auditContent;
}
