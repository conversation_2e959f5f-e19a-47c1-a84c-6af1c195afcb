package cc.buyhoo.tax.params.busShopBank;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 企业修改参数
 */
@Data
public class BusShopBankUpdateParams implements Serializable {
    private static final long serialVersionUID = 1576466662405137255L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 商家唯一标识
     */
    @NotNull(message = "店铺编码不能为空")
    private Long shopUnique;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long companyId;
    /**
     * 银行名称
     */
    @NotBlank(message = "支行名称不能为空")
    private String bankName;
    /**
     * 支行联行号
     */
    @NotBlank(message = "支行联行号不能为空")
    private String bankCode;
    /**
     * 开户行所在地（非银联卡必传）
     */
    private String bankCity;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    private String bankCard;
    /**
     * 银行类型
     */
    @NotNull(message = "所属银行类型不能为空")
    private Long bankId;
    /**
     * 法人姓名
     */
    private String legalPerson;
    /**
     * 法人手机号
     */
    private String legalPhone;
    /**
     * 账户类型：1:对公；2:对私；
     */
    private Integer rcvCustType;
}
