package cc.buyhoo.tax.params.inventoryBatch;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 入库批次请求参数
 * @ClassName InventoryBatchParams
 * <AUTHOR>
 * @Date 2023/7/31 15:40
 **/
@Data
public class InventoryBatchParams extends PageParams implements Serializable {

    private static final long serialVersionUID = -9068879539451568414L;
    /**
     * 所属企业ID
     */
    private Long companyId;
    /**
     * 供应商编码
     */
    private Long shopUnique;
    /**
     * 入库单号
     */
    private String batchNo;
    /**
     * 开始时间
     */
    private String[] createTime;
    /**
     * 入库单类型:1收银订单同步2餐饮订单同步(仅统计数据使用)3手动录入
     */
    private Integer inventoryType;
}
