package cc.buyhoo.tax.params.saleList;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AutoDisassembleSaleListParams implements Serializable {
    @NotNull(message = "最小金额不能为空")
    private BigDecimal minValue;
    @NotNull(message = "最大金额不能为空")
    private BigDecimal maxValue;
    //最小的订单金额
    @NotNull(message = "请输入拆单的最小订单金额")
    private BigDecimal minOrderAmount;
    //最大的拆单金额
    private BigDecimal maxOrderAmount;
    //需要拆单的订单开始日期
    private String startDate;
    //需要拆单的订单结束日期
    private String endDate;
}
