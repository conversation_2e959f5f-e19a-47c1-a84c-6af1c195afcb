package cc.buyhoo.tax.params.sysMigrate;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 迁入迁出新增参数
 * @ClassName SysMigrateAddParams
 * <AUTHOR>
 * @Date 2024/8/29 14:30
 **/
@Data
public class SysMigrateAddParams implements Serializable {
    private static final long serialVersionUID = -7102638245103602518L;

    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long companyId;

    /**
     * 商户编码
     */
    @NotNull(message = "商户编码不能为空")
    private Long shopUnique;

    /**
     * 迁入迁出类型：0-迁入，1-迁出
     */
    @NotNull(message = "迁入迁出类型不能为空")
    private Integer migrateType;

    /**
     * 备注
     */
    private String remark;

}
