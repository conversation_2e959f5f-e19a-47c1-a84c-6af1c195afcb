package cc.buyhoo.tax.params.busContract;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 合同新增参数
 * @ClassName BusContractParams
 * <AUTHOR>
 * @Date 2024/7/13 11:36
 **/
@Data
public class BusContractCheckParams implements Serializable {
    private static final long serialVersionUID = 2155850471202375690L;

    /**
     * 合作方编码
     */
    @NotNull(message = "合作方不能为空")
    private Long shopUnique;

    /**
     * 关联单号,多个逗号(,)分割
     */
    @NotBlank(message = "关联单号不能为空")
    private String orderNo;

}
