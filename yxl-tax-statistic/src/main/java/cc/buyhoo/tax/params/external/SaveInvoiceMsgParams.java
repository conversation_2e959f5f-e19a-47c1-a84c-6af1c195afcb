package cc.buyhoo.tax.params.external;

import cc.buyhoo.tax.params.busShopInvoice.ShopInvoiceAddParams;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 存储新的发票信息
 */
@Data
public class SaveInvoiceMsgParams {

    /**
     * 企业ID，开票时记录，方式开票期间，店铺被转移
     */
    @NotNull
    private Long companyId;
    /**
     * 开票订单号
     */
    private String saleListUnique;
    /**
     * 发票类型
     */
    private Integer invoiceType;
    /**
     * 开票店铺
     */
    private Long shopUnique;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 机器编码
     */
    private String machineCode;
    /**
     * 发票号码
     */
    @NotBlank(message = "请输入发票号码")
    private String invoiceNumber;

    /**
     * 发票介质：1、电子发票；2、纸质发票
     */
    @NotNull(message = "请选择发票介质：1、电子发票；2、纸质发票")
    private Integer mediumType;
    /**
     * 发票种类:1普通发票2专用发票
     */
    @NotNull(message = "请选择发票类型")
    private Integer invoiceKind;
    /**
     * 开票日期
     */
    @NotBlank(message = "请填写开票日期")
    private String invoiceDate;
    /**
     * 校验码
     */
    private String checkCode;

    /**
     * 购买方类型：1、个人；2、企业。个人用户不需要上购买方信息
     */
    @NotNull(message = "请选择购买方类型")
    private Integer purchaseType;
    /**
     * 购买方名称
     */
    @NotBlank(message = "请输入购买方名称")
    private String purchaseName;
    /**
     * 购买方纳税人识别号
     */
    private String purchaseIdentity;
    /**
     * 购买方地址
     */
    private String purchaseAddress;
    /**
     * 购买方电话
     */
    private String purchasePhone;
    /**
     * 购买方开户行
     */
    private String purchaseBank;
    /**
     * 购买方开户行账号
     */
    private String purchaseBankNo;
    /**
     * 销售方名称
     */
    private String saleName;
    /**
     * 销售方纳税人识别号
     */
    private String saleIdentity;
    /**
     * 销售方地址
     */
    private String saleAddress;
    /**
     * 销售方电话
     */
    private String salePhone;
    /**
     * 销售方开户行
     */
    private String saleBank;
    /**
     * 销售方开户行账号
     */
    private String saleBankNo;
    /**
     * 合计订单金额
     */
    @NotNull(message = "请输入合计金额")
    private BigDecimal orderMoney;
    /**
     * 合计税额
     */
    @NotNull(message = "请输入合计税额")
    private BigDecimal taxMoney;
    /**
     * 价税合计
     */
    @NotNull(message = "请输入价税合计")
    private BigDecimal orderTaxMoney;
    /**
     * 收款人
     */
    private String payee;
    /**
     * 复核人
     */
    private String checker;
    /**
     * 开票人
     */
    private String invoiceMan;

    //开票详情
    List<SaveInvoiceDetailMsg> detailEntityList;
}
