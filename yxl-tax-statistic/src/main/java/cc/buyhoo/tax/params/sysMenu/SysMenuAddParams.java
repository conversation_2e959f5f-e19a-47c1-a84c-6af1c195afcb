package cc.buyhoo.tax.params.sysMenu;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 菜单新增参数
 */
@Data
public class SysMenuAddParams implements Serializable {

    private static final long serialVersionUID = 5911727824132651042L;
    /**
     * 父id
     */
    @NotNull(message = "请选择父分类")
    private Long parentId;
    /**
     * 菜单名称
     */
    @NotBlank(message = "请输入菜单名称")
    private String menuName;
    /**
     * 菜单权限
     */
    private String permission;
    /**
     * 菜单类型:1目录2菜单3按钮
     */
    @NotNull(message = "请选择菜单类型")
    private Integer type;
    /**
     * 组件地址
     */
    private String component;
    /**
     * 组件名称
     */
    private String componentName;
    /**
     * 排序
     */
    @NotNull(message = "请输入菜单排序")
    private Integer sort;
    /**
     * 组件图标
     */
    private String icon;
    /**
     * 1隐藏2显示
     */
    @NotNull(message = "请选择菜单状态")
    private Integer hidden;
    /**
     * 菜单级别:0-普通,1-平台,2-内置
     */
    private Integer menuLevel;

}
