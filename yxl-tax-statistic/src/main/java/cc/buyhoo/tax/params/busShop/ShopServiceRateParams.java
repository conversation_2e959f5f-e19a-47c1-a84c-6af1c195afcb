package cc.buyhoo.tax.params.busShop;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 批量修改结算费率
 * @ClassName ShopServiceRateParams
 * <AUTHOR>
 * @Date 2024/6/19 17:55
 **/
@Data
public class ShopServiceRateParams implements Serializable {

    @NotNull(message = "结算费率不能为空")
    @DecimalMin(value = "0", message = "结算费率不能小于0")
    @DecimalMax(value = "100", message = "结算费率不能大于100")
    private BigDecimal serviceFeeRate;
}
