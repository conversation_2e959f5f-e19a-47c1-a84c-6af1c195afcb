package cc.buyhoo.tax.params.busShop;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/** 供应商查询参数
 * @Description
 * @ClassName ShopListParams
 * <AUTHOR>
 * @Date 2023/7/26 15:32
 **/
@Data
public class ShopListExportParams {



    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 供货商名称
     */
    private String shopName;

    /**
     * 联系电话
     */
    private String shopPhone;

    /**
     * 所属市场ID
     */
    private Long marketId;

    /**
     * 所属区县
     */
    private String countyCode;
    /**
     * 所属乡镇
     */
    private String townCode;

    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 供货商性质
     */
    private String shopNature;

    /**
     * 开始时间
     */
    private LocalDate createTime;
    /**
     * 结束时间
     */
    private LocalDate modifyTime;
}
