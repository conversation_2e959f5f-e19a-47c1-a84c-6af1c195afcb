package cc.buyhoo.tax.params.invoice;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateInvoiceStaffParams {

    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空")
    private Long id;
    /**
     * 员工名称
     */
    @NotBlank(message = "员工名称不能为空")
    private String staffName;

    /**
     * 全电4期登录账户
     */
    @NotBlank(message = "员工登录账户不能为空")
    private String staffAccount;

    /**
     * 全电4期密码
     */
    @NotBlank(message = "员工登录密码不能为空")
    private String staffPwd;

    /**
     * 责任人类型：01、法人；02、财务负责人；03、办税员；05、管理员；08、社保经办人；09、开票员；10、销售人员
     */
    @NotBlank(message = "责任人类型不能为空")
    private String dutyType;

    /**
     * 1、有效；0、无效
     */
    private Integer validType;
}
