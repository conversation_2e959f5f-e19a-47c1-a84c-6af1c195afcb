package cc.buyhoo.tax.params.external;

import lombok.Data;

import java.io.Serializable;

/**
 * 新版请求
 */
@Data
public class SaveInvoiceMsgPublicParams implements Serializable {
    //订单编号
    private String saleListUnique;
    //店铺编号
    private Long shopUnique;
    //发票类型
    private Integer invoiceType;
    //客户类型
    private Integer purchaseType;
    //客户名称
    private String purchaseName;
    //客户地址
    private String purchaseAddress;
    //客户电话
    private String purchasePhone;
    //客户银行
    private String purchaseBank;
    //客户银行账号
    private String purchaseBankNo;
    //客户税号
    private String purchaseIdentity;
    //接受发票的方式，手机号或邮箱
    private String receiveMsg;
}
