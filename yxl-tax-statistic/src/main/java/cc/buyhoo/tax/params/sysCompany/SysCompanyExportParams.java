package cc.buyhoo.tax.params.sysCompany;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业列表导出参数
 */
@Data
public class SysCompanyExportParams implements Serializable {

    private static final long serialVersionUID = 2117120467157695604L;
    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 所属行业ID
     */
    private Long industryId;

    /**
     * 所属市场ID
     */
    private Long marketId;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

}
