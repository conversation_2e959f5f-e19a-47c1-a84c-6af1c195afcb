package cc.buyhoo.tax.params.sysIndustry;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 行业新增参数
 * @ClassName SysIndustryAddParams
 * <AUTHOR>
 * @Date 2024/8/23 15:05
 **/
@Data
public class SysIndustryAddParams implements Serializable {
    private static final long serialVersionUID = -6835004696076130100L;
    /**
     * 市场名称
     */
    @NotBlank(message = "行业名称不能为空")
    @Size(max = 100, message = "行业名称长度不能超过100个字符")
    private String industryName;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
}
