package cc.buyhoo.tax.params.invoice;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ManualBatchInvoiceApplyParams
 * <AUTHOR>
 * @Date 2024/6/14 9:35
 */
@Data
public class ManualBatchInvoiceApplyParams implements Serializable {
    /**
     * 店铺发票明细Id数组
     */
    @NotNull(message = "请选择待开票记录")
    private List<Long> idList;
    /**
     * 发票种类:1普通发票2专用发票
     */
    @NotNull(message = "请选择发票类型")
    private Integer invoiceKind;
    /**
     * 购买方类型：1、个人；2、企业。个人用户不需要上购买方信息
     */
    @NotNull(message = "请选择购买方类型")
    private Integer purchaseType;
    /**
     * 购买方名称
     */
    @NotBlank(message = "请输入购买方名称")
    private String purchaseName;
    /**
     * 购买方纳税人识别号
     */
    private String purchaseIdentity;
    /**
     * 购买方地址
     */
    private String purchaseAddress;
    /**
     * 购买方电话
     */
    private String purchasePhone;
    /**
     * 购买方开户行
     */
    private String purchaseBank;
    /**
     * 购买方开户行账号
     */
    private String purchaseBankNo;

    /**
     * 是否展示购方地址、电话信息：1-是；2-否
     */
    private Integer purchasePersonFlag;

    /**
     * 是否展示销方地址、电话信息：1-是；2-否
     */
    private Integer salePersonFlag;
    /**
     * 是否展示购方开户银行、银行账号信息：1-是；2-否
     */
    private Integer purchaseBankFlag;

    /**
     * 是否展示销方开户银行、银行账号信息：1-是；2-否
     */
    private Integer saleBankFlag;

    /**
     * 销售方名称
     */
    @NotBlank(message = "请输入销售方名称")
    private String saleName;
    /**
     * 销售方纳税人识别号
     */
    @NotBlank(message = "请输入销售方纳税人识别号")
    private String saleIdentity;
    /**
     * 销售方地址
     */
    @NotBlank(message = "请输入销售方地址")
    private String saleAddress;
    /**
     * 销售方电话
     */
    @NotBlank(message = "请输入销售方电话")
    private String salePhone;
    /**
     * 销售方开户行
     */
    @NotBlank(message = "请输入销售方开户行")
    private String saleBank;
    /**
     * 销售方开户行账号
     */
    @NotBlank(message = "请输入销售方开户行账号")
    private String saleBankNo;
    /**
     * 商品明细
     */
    private List<ManualInvoiceApplyGoodListParams> goodList;
    /**
     * 备注
     */
    private String notes;
    /**
     * 收款人
     */
    @NotBlank(message = "请输入收款人")
    private String payee;
    /**
     * 复核人
     */
    @NotBlank(message = "请输入复核人")
    private String checker;
    /**
     * 开票人
     */
    @NotBlank(message = "请输入开票人")
    private String invoiceMan;
}
