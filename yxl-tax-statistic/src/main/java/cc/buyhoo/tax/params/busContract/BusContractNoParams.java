package cc.buyhoo.tax.params.busContract;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description
 * @ClassName BusContractNoParams
 * <AUTHOR>
 * @Date 2024/7/15 9:13
 **/
@Data
public class BusContractNoParams implements Serializable {

    private static final long serialVersionUID = -7083932191269075693L;

    /**
     * 合同类型：1-销售合同，2-采购合同，3-联营合同，4-其它
     */
    @NotNull(message = "合同类型不能为空")
    private Integer contractType;

    /**
     * 合作方编码
     */
    @NotNull(message = "合作方不能为空")
    private Long shopUnique;
}
