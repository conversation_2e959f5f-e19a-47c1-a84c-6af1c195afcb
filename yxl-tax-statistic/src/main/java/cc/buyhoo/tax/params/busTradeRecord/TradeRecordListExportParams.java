package cc.buyhoo.tax.params.busTradeRecord;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName TradeRecordListImportParams
 * <AUTHOR>
 * @Date 2024/8/31 16:27
 */
@Data
public class TradeRecordListExportParams implements Serializable {
    /**
     * 交易时间
     */
    private List<String> tradeTime;
    /**
     * 交易类型：1、入账；2、出账；
     */
    private Integer tradeWay;
    /**
     * 收（付）方名称
     */
    private String tradeOtherName;
    /**
     * 收（付）方账号
     */
    private String tradeOtherAccount;
    /**
     * 流水号
     */
    private String serialNumber;
    /**
     * 摘要
     */
    private String tradeAbstract;

}
