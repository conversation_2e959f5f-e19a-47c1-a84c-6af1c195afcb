package cc.buyhoo.tax.params.goodsCategory;

import lombok.Data;

/**
 * @Description
 * @ClassName BusGoodsCategoryParams
 * <AUTHOR>
 * @Date 2023/7/26 18:24
 **/
@Data
public class BusGoodsCategoryParams {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 所属父类ID
     */
    private Long parentId;

    /**
     * 祖籍列表
     */
    private String ancestors;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分类类型:1默认分类2普通分类
     */
    private Integer categoryType;

    /**
     * 税目
     */
    private String categoryNo;

    /**
     * 开票商品名称
     */
    private String goodsName;

    /**
     * 开票税率
     */
    private String taxRate;
    /**
     * 商品计量单位
     */
    private String unit;
}
