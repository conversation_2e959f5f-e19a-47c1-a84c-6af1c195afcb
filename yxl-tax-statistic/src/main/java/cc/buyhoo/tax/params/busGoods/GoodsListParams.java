package cc.buyhoo.tax.params.busGoods;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 商品管理列表参数
 * @ClassName GoodsListParams
 * <AUTHOR>
 * @Date 2023/7/29 18:03
 **/
@Data
public class GoodsListParams extends PageParams implements Serializable {

    private static final long serialVersionUID = 4544986348393695211L;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 供应商编码
     */
    private Long shopUnique;

    /**
     * 一级分类
     */
    private Long categoryId;

    /**
     * 二级分类
     */
    private Long categoryTwoId;

    /**
     * 分类筛选
     */
    private Long[] categoryTwoName;
}
