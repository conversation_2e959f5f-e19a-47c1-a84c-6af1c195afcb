package cc.buyhoo.tax.params.busShopInvoice;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ShopInvoiceListParams extends PageParams {

    @NotNull(message = "请选择发票类型")
    private Integer invoiceType; //发票类型:1进项票2销项票

    private Integer invoiceKind; //发票种类:1普通发票2增值税发票3专用发票

    private String invoiceNumber; //发票号码

    private Long shopUnique; //店铺编号

    private List<String> invoiceDate;

    //订单状态：1、已开票；2、未开票；3、开票中；4、开票失败
    private Integer status;

    /**
     * 申请状态
     */
    private Integer applyFlag;
    // 订单编码，多个以逗号“,"分割
    private String saleListUnique;

}
