package cc.buyhoo.tax.params.busShop;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 供应商请求参数
 * @Description
 * @ClassName ShopListParams
 * <AUTHOR>
 * @Date 2023/7/26 15:32
 **/
@Data
public class ShopParams {

    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * 法人姓名
     */
    private String legalPerson;

    /**
     * 法人手机号
     */
    private String legalPhone;

    /**
     * 结算费率
     */
    @NotNull(message = "结算费率不能为空")
    private BigDecimal serviceFeeRate;

    /**
     * 是否有服务费：0-无，1-有
     */
    private Integer hasServiceFee;

    /**
     * 合同附件地址
     */
    private String contractUrl;


    /**
     * 子账簿账号
     */
    private String subAccNo;


    /**
     * 合作商成本占比
     */
    private BigDecimal cooperatorCostProportion;

    /**
     * 合作商编号
     */
    private Long supplierNo;
    /**
     * 店铺
     */
    private String shopUnique;
}
