package cc.buyhoo.tax.params.inventoryBatchDetail;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 入库批次明细
 * @ClassName InventoryBatchDetailAddParams
 * <AUTHOR>
 * @Date 2023/8/3 8:26
 **/
@Data
public class InventoryBatchDetailAddParams implements Serializable {

    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 批次数量
     */
    private BigDecimal goodsCount;
    /**
     * 批次金额
     */
    private BigDecimal totalMoney;

}
