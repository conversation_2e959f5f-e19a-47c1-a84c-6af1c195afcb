package cc.buyhoo.tax.params.login;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class LoginParams {

    @NotBlank(message = "请输入uuid")
    private String uuid; //uuid

    @NotBlank(message = "请输入验证码")
    private String code; //code

    @NotBlank(message = "请输入用户名")
    @Length(min = 2, max = 20, message = "请输入2-20位用户名")
    private String username; //用户名

    @NotBlank(message = "请输入密码")
    @Length(min = 5, max = 20, message = "请输入5-20位密码")
    private String password; //密码

}
