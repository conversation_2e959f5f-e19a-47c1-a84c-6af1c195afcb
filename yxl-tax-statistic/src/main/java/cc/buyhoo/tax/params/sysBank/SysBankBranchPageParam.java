package cc.buyhoo.tax.params.sysBank;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 支行信息查询参数
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 14:00
 **/
@Data
public class SysBankBranchPageParam extends PageParams implements Serializable {
    private static final long serialVersionUID = -334135156727852424L;

    /**
     * 支行名称
     */
    private String branchName;
    /**
     * 支行行号
     */
    private String branchCode;
    /**
     * 总行行号
     */
    private String bankCode;
}
