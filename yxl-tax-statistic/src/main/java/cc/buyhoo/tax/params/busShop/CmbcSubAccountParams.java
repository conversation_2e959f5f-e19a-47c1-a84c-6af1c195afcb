package cc.buyhoo.tax.params.busShop;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 创建子账簿入参
 * @ClassName CreateSubAccountRequest
 * <AUTHOR>
 **/
@Data
public class CmbcSubAccountParams implements Serializable {

    private static final long serialVersionUID = 7078149329296829305L;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 商户d对应的银行编号
     */
    private String mchBankId;

    /**
     * 银行标识
     */
    private String bankKey;

    /**
     * 终端IP
     * 示例：*******
     */
    private String payClientIp;


    /**
     *外部操作流水号
     * 商户操作流水号，每次请求唯一，32位定长支持数字或字母组合
     */
    private String merSerialNo;



    /**
     *付款方子账簿名称
     * 新增时必输，用于标识子账簿的归属
     */
    private String partnerName;

    /**
     * 子账簿账号
     *修改和注销时必输，新增时为空
     */
    private String accNo;

    /**
     *子账簿状态
     * 5-只入不出、6-只出不入、7-不出不入、0-正常，仅修改时可输入；
     * no
     */
    private String stat;

    /**
     *联系人名称
     * 新增时必输
     */
    private String accName;

    /**
     *手机号
     * 新增时必输
     */
    private String phoneId;

    /**
     * 邮箱
     *no
     */
    private String mail;

    /**
     *推送通知
     * 00-不通知、01-短信、02-邮件、03-短信与邮件，不输入默认不通知
     * no
     */
    private String isPush;


}
