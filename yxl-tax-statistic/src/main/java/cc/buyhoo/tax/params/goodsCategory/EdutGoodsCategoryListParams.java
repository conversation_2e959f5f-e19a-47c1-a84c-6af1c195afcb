package cc.buyhoo.tax.params.goodsCategory;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;


@Data
public class EdutGoodsCategoryListParams {
    /**
     * 税目
     */
    @NotBlank(message = "请输入税目信息")
    private String categoryNo;

    /**
     * 开票商品名称
     */
    @NotBlank(message = "请输入开票商品名称")
    private String goodsName;

    /**
     * 开票税率
     */
    @NotNull(message = "请输入开票税率")
    private BigDecimal taxRate;

    /**
     * 商品单位信息
     */
    @NotBlank(message = "请输入商品单位信息")
    private String unit;
    /**
     * 要删除的记录
     */
    @NotEmpty(message = "请选择要编辑的记录")
    private List<Long> ids;
}
