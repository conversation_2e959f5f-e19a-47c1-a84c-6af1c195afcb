package cc.buyhoo.tax.params.cmb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 生成busInvo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateNewInventoryParams implements Serializable {
    //打款的账号，供应商账号
    private String accnbr;
    //支付流水号（供应商账户）
    private String tradeNo;
    //银行内部交易码（供应商账户）
    private String bankTrscode;
    //银行ID（供应商）
    private Integer bankId;
    //收款的账户，供应商账户
    private String tradingAccount;
    //交易金额
    private BigDecimal payAmount;
}
