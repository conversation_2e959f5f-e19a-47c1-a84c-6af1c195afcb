package cc.buyhoo.tax.params.busShop;

import lombok.Data;

import java.io.Serializable;

@Data
public class PayCenterPayParams implements Serializable {
    //用户ID
    private String mchId;
    //用户ID地址
    private String clientIp;
    //商户订单号，唯一
    private String mchTradeNo;
    //支付金额，单位元
    private String payFee;
    /**
     * 货币类型
     */
    private String feeType;
    //授权码
    private String authCode;
    //商品名称
    private String subject;
    private String body;
    //自定义参数，原样返回
    private String customJson;
    //回调地址
    private String callBackUrl;
    //POS终端号
    private String posTerminalId;
    /**
     * 打印设备类型
     * 1-打印商家联与客户联
     * 2-只打印客户联
     * 3-都不打印
     */
    private String receiptType;


}
