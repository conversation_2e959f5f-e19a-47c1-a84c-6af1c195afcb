package cc.buyhoo.tax.params.busShopBill;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description
 * @ClassName BusShopBillParams
 * <AUTHOR>
 * @Date 2023/7/27 18:24
 **/
@Data
public class BusShopBillDetailParams extends PageParams {
    /**
     * 账单ID
     */
    @NotNull(message = "账单ID不能为空")
    private Long billId;

    /**
     * 开始时间
     */
    private String[] createTime;
    /**
     * 打款方式 0-手动，1-自动
     */
    private String settledType;
    /**
     * 结算状态: 1转账成功、2转账中、3转账失败
     */
    private Integer transferStatus;

}
