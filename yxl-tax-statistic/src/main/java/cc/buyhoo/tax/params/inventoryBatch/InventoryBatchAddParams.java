package cc.buyhoo.tax.params.inventoryBatch;

import cc.buyhoo.tax.params.inventoryBatchDetail.InventoryBatchDetailAddParams;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 添加入库批次
 * @ClassName InventoryBatchAddParams
 * <AUTHOR>
 * @Date 2023/9/2 17:51
 **/
@Data
public class InventoryBatchAddParams implements Serializable {

    /**
     * 批次ID
     */
    private Long id;
    /**
     * 发票金额
     */
    @NotNull(message = "发票金额不能为空")
    private BigDecimal totalMoney;
    /**
     * 发票日期
     */
    private String batchDate;

    /**
     * 入库商品明细
     */
    private List<InventoryBatchDetailAddParams> detailList;
}
