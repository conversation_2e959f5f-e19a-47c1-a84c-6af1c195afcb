package cc.buyhoo.tax.params.external;

import cc.buyhoo.tax.params.busShopInvoice.ShopInvoiceGoodListAddParams;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName ShopInvoiceSaveParams
 * <AUTHOR>
 * @Date 2024/6/14 8:23
 */
@Data
public class ShopInvoiceAddParams implements Serializable {
    /**
     * 订单编号
     */
    @NotBlank(message = "请输入订单编号")
    private String saleListUnique;
    /**
     * 发票种类:1普通发票2专用发票
     */
    @NotNull(message = "请选择发票类型")
    private Integer invoiceKind;
    /**
     * 购买方类型：1、个人；2、企业。个人用户不需要上购买方信息
     */
    @NotNull(message = "请选择购买方类型")
    private Integer purchaseType;
    /**
     * 购买方名称
     */
    @NotBlank(message = "请输入购买方名称")
    private String purchaseName;
    /**
     * 购买方纳税人识别号
     */
    private String purchaseIdentity;
    /**
     * 购买方地址
     */
    private String purchaseAddress;
    /**
     * 购买方电话
     */
    private String purchasePhone;
    /**
     * 购买方开户行
     */
    private String purchaseBank;
    /**
     * 购买方开户行账号
     */
    private String purchaseBankNo;

    /**
     * 是否展示购方地址、电话信息：1-是；2-否
     */
    private Integer purchasePersonFlag;

    /**
     * 是否展示销方地址、电话信息：1-是；2-否
     */
    private Integer salePersonFlag;
    /**
     * 是否展示购方开户银行、银行账号信息：1-是；2-否
     */
    private Integer purchaseBankFlag;

    /**
     * 是否展示销方开户银行、银行账号信息：1-是；2-否
     */
    private Integer saleBankFlag;
    /**
     * 销售方名称
     */
    @NotBlank(message = "请输入销售方名称")
    private String saleName;
    /**
     * 销售方纳税人识别号
     */
    @NotBlank(message = "请输入销售方纳税人识别号")
    private String saleIdentity;
    /**
     * 销售方地址
     */
    @NotBlank(message = "请输入销售方地址")
    private String saleAddress;
    /**
     * 销售方电话
     */
    @NotBlank(message = "请输入销售方电话")
    private String salePhone;
    /**
     * 销售方开户行
     */
    @NotBlank(message = "请输入销售方开户行")
    private String saleBank;
    /**
     * 销售方开户行账号
     */
    @NotBlank(message = "请输入销售方开户行账号")
    private String saleBankNo;
    /**
     * 商品明细
     */
    @NotEmpty(message = "请选择商品明细")
    private List<ShopInvoiceGoodListAddParams> goodList;
    /**
     * 备注
     */
    private String notes;
    /**
     * 收款人
     */
    @NotBlank(message = "请输入收款人")
    private String payee;
    /**
     * 复核人
     */
    @NotBlank(message = "请输入复核人")
    private String checker;
    /**
     * 开票人
     */
    @NotBlank(message = "请输入开票人")
    private String invoiceMan;

    /**
     * 接收发票的邮箱
     */
    @NotBlank(message = "请输入邮箱地址")
    private String emailAddress;

    private BigDecimal orderMoney;

    private BigDecimal orderTaxMoney;

    private BigDecimal taxMoney;

    private Integer status;
    /**
     * 发票类型:1进项票2销项票
     */
    private Integer invoiceType;
    /**
     * 公司编号
     */
    private Long companyId;
    /**
     * 店铺编号
     */
    private Long shopUnique;
}
