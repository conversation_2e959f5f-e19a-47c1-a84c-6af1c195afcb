package cc.buyhoo.tax.params.busInvoiceGoodsService;

import lombok.Data;

import java.io.Serializable;

/**
 * 开票服务保存参数
 * @ClassName ShopGoodsServiceSaveParams
 * <AUTHOR>
 * @Date 2023/9/6 14:24
 **/
@Data
public class InvoiceGoodsServiceSaveParams implements Serializable {
    private static final long serialVersionUID = 7674678282624768485L;

    /**
     * 开票服务ID
     */
    private Long id;
    /**
     * 税收分类编码
     */
    private String categoryNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品税目
     */
    private String taxItem;
    /**
     * 商品规格
     */
    private String goodsSpec;
    /**
     * 商品单位
     */
    private String goodsUnit;
    /**
     * 税率
     */
    private String taxRate;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;
}
