package cc.buyhoo.tax.params.invoice;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class InvoiceResultQueryParams  implements Serializable {
    @NotNull(message = "请选择发票信息")
    private Long invoiceId;
    //销货方纳税人识别号（必选）
    @NotEmpty(message = "请输入销货方纳税人识别号")
    private String nsrsbh;
    //订单请求流水号（查询开票时必选）
    @NotEmpty(message = "请输入订单请求流水号")
    private String billNumber;
    //红字确认单编号（红冲时必选）
    private String hzqrdbh;
}
