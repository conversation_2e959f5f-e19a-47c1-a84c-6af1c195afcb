package cc.buyhoo.tax.params.sysCompany;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业新增参数
 */
@Data
public class SysCompanyAddParams implements Serializable {
    private static final long serialVersionUID = 1576466662405137255L;
    /**
     * 公司名称
     */
    @NotBlank(message = "企业名称不能为空")
    private String companyName;
    /**
     * 营业执照编码
     */
    @NotBlank(message = "营业执照编码不能为空")
    private String licenseNumber;
    /**
     * 注册地址
     */
    @NotBlank(message = "公司注册地址不能为空")
    private String address;

    /**
     * 企业邀请码
     */
    @NotBlank(message = "邀请码不能为空")
    private String invitationCode;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系电话
     */
    private String contactMobile;
    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

    /**
     * 经营目标金额(万元)
     */
    @NotNull(message = "经营目标金额不能为空")
    @Min(value = 0, message = "经营目标金额不能小于0")
    private BigDecimal targetAmount;
    /**
     * 经营周期,1-按月,2-按季度,3-按年
     */
    @NotBlank(message = "经营周期不能为空")
    private String taxType;
    /**
     * 转账审批状态:1无需审批2需审批
     */
    @NotNull(message = "转账审批状态不能为空")
    private Integer transferAccountAudit;

    /**
     * 行业ID
     */
    @NotNull(message = "所属行业不能为空")
    private Long industryId;

    /**
     * 市场ID
     */
    @NotNull(message = "所属市场不能为空")
    private Long marketId;

    /**
     * 突击顺序，由大到小
     */
    private Integer statisticGrade;

    /**
     * 突击金额(万元)
     */
    private BigDecimal statisticAmount;

    /**
     * 未申请订单是否可以开票：0-否1-是
     */
    @NotNull(message = "开票方式不能为空")
    private Integer invoiceFlag;

    /**
     * 交易费率(%)
     */
    @NotNull(message = "交易费率不能为空")
    @Min(value = 0, message = "交易费率不能小于0")
    @Max(value = 100, message = "交易费率不能大于100")
    private BigDecimal payFeeRate;

    /**
     * 增值税率(%)
     */
    @NotNull(message = "增值税率不能为空")
    @Min(value = 0, message = "增值税率不能小于0")
    @Max(value = 100, message = "增值税率不能大于100")
    private BigDecimal vatRate;

    /**
     * 支付商户号
     */
    private String mchId;

    /**
     * 支付中心密钥
     */
    private String payCenterSecretKey;


    /**
     * 子账簿密钥
     */
    private String subAccountSecretKey;
    /**
     *商户对应的银行编号
     */
    private String mchBankId;
}
