package cc.buyhoo.tax.params.sysMigrate;

import cc.buyhoo.common.standard.PageParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 迁入迁出分页查询参数
 * @ClassName SysMigratePageParams
 * <AUTHOR>
 * @Date 2024/8/29 14:27
 **/
@Data
public class SysMigratePageParams extends PageParams implements Serializable {
    private static final long serialVersionUID = 6040330305600978084L;

    /**
     * 迁入企业ID
     */
    private Long inCompanyId;
    /**
     * 迁出企业ID
     */
    private Long outCompanyId;

    /**
     * 商户名称（模糊查询）
     */
    private String shopName;

    /**
     * 审核状态，0-待审核，1-通过，2-不通过
     */
    private Integer auditStatus;
}
