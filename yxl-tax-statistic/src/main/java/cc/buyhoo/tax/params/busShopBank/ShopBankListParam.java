package cc.buyhoo.tax.params.busShopBank;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 供货商银行卡查询参数
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-23 08:57
 **/
@Data
public class ShopBankListParam implements Serializable {
    private static final long serialVersionUID = -107380078826834530L;

    /**
     * 供货商编码
     */
    @NotNull(message = "供货商编码不能为空")
    private Long shopUnique;

    /**
     * 启用状态 1正常0无效
     */
    private Integer enableStatus;
}
