package cc.buyhoo.tax.params.busContract;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同新增参数
 * @ClassName BusContractParams
 * <AUTHOR>
 * @Date 2024/7/13 11:36
 **/
@Data
public class BusContractAddParams implements Serializable {
    private static final long serialVersionUID = 2155850471202375690L;

    /**
     * 合同编码
     */
    @NotBlank(message = "合同编码不能为空")
    private String contractNo;

    /**
     * 合作方编码
     */
    @NotNull(message = "合作方不能为空")
    private Long shopUnique;

    /**
     * 合同名称
     */
    @NotBlank(message = "合同名称不能为空")
    private String contractName;

    /**
     * 合同类型：1-销售合同，2-采购合同，3-联营合同，4-其它
     */
    @NotNull(message = "合同类型不能为空")
    private Integer contractType;

    /**
     * 合同金额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 合同状态：0-草拟，1-待审，2-已签署，3-执行中，4-已完成，5-终止
     */
    @NotNull(message = "合同状态不能为空")
    private Integer contractStatus;

    /**
     * 起止日期
     */
    private String[] dateArray;

    /**
     * 合同签订日期
     */
    private String signTime;

    /**
     * 关联单号,多个逗号(,)分割
     */
    private String orderNo;

    /**
     * 附件地址
     */
    private String fileUrl;

}
