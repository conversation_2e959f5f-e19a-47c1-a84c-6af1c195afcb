package cc.buyhoo.tax.params.sysCompanyConfig;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UpdateSysCompanyConfigParams implements Serializable {
    @NotNull(message = "请设置是否开启功能")
    //是否开启拆解功能:1、开启；0、关闭，如果未设置，默认关闭
    private Integer disassembleStatus;
    @NotNull(message = "请设置拆解订单设置的最小订单金额")
    //拆解订单设置的最小订单金额
    private BigDecimal disassembleMinMoney;
    @NotNull(message = "请设置拆解订单设置的最大订单金额")
    //拆解订单设置的最大订单金额
    private BigDecimal disassembleMaxMoney;
    @NotNull(message = "请设置拆解订单的起拆金额")
    //拆解订单的起拆金额
    private BigDecimal disassembleStartMoney;
}
