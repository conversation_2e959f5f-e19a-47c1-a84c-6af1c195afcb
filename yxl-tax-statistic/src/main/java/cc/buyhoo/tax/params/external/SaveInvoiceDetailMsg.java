package cc.buyhoo.tax.params.external;

import cc.buyhoo.tax.entity.BusShopInvoiceDetailEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class SaveInvoiceDetailMsg extends BusShopInvoiceDetailEntity {

    //商品税收分类编码
    @NotBlank
    private String taxClassificationCode;
    //商品名称
    @NotBlank
    private String goodsName;
    //金额
    @NotNull
    private BigDecimal amount;
    //税率
    @NotNull
    private BigDecimal taxRate;
}
