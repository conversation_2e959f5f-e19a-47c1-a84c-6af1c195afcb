# 税务统计系统业务流程文档

## 目录

1. [系统概述](#1-系统概述)
   - 1.1 [技术架构](#11-技术架构)
   - 1.2 [主要模块](#12-主要模块)
2. [业务流程概览](#2-业务流程概览)
   - 2.0 [整体业务流程图](#20-整体业务流程图)
3. [核心业务流程](#3-核心业务流程)
   - 3.1 [商户管理流程](#31-商户管理流程)
   - 3.2 [销售订单管理流程](#32-销售订单管理流程)
   - 3.3 [库存管理流程](#33-库存管理流程)
   - 3.4 [商品管理流程](#34-商品管理流程)
   - 3.5 [合同管理流程](#35-合同管理流程)
   - 3.6 [发票管理流程](#36-发票管理流程)
   - 3.7 [税务申报流程](#37-税务申报流程)
   - 3.8 [银行转账流程](#38-银行转账流程)
   - 3.9 [财务结算流程](#39-财务结算流程)
   - 3.10 [数据迁移流程](#310-数据迁移流程)
   - 3.11 [现金升级流程](#311-现金升级流程)
4. [权限管理](#4-权限管理)
5. [外部系统集成](#5-外部系统集成)
6. [定时任务](#6-定时任务)
7. [系统监控](#7-系统监控)
8. [业务流程总结](#8-业务流程总结)

---

## 1. 系统概述

税务统计系统（yxl-tax-statistic）是一个基于Spring Boot的企业级税务管理平台，主要用于管理商户的销售订单、库存、发票开具、税务申报、银行转账等业务流程。

### 1.1 技术架构
- **框架**: Spring Boot + MyBatis Plus + Dubbo
- **数据库**: MySQL
- **缓存**: Redis
- **消息队列**: RocketMQ
- **文件存储**: MinIO
- **权限管理**: Sa-Token
- **任务调度**: XXL-Job

### 1.2 主要模块
- 商户管理
- 销售订单管理
- 库存管理
- 商品管理
- 合同管理
- 发票管理
- 税务申报
- 银行转账
- 财务结算
- 数据迁移
- 现金升级
- 权限管理

## 2. 业务流程概览

### 2.0 整体业务流程图

```mermaid
graph TD
    A[商户注册] --> B[企业认证]
    B --> C[银行账户配置]
    C --> D[商品管理]
    D --> E[合同签署]
    E --> F[销售订单同步]
    F --> G[订单拆分处理]
    G --> H[库存管理]
    H --> I[发票开具]
    I --> J[税务申报]
    J --> K[财务结算]
    K --> L[银行转账]

    F --> M[退货处理]
    M --> H

    L --> N[账单管理]
    N --> O[利润分成]

    P[数据迁移] --> B
    Q[现金升级] --> C
```

## 3. 核心业务流程

### 3.1 商户管理流程

#### 3.1.1 商户注册与认证
**流程描述**: 商户通过邀请码注册，完成企业信息认证

**主要实体**:
- [`BusShopEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopEntity.java) - 商户基本信息
- [`SysCompanyEntity`](../src/main/java/cc/buyhoo/tax/entity/SysCompanyEntity.java) - 企业信息

**核心服务**:
- [`BusShopServiceImpl`](../src/main/java/cc/buyhoo/tax/service/impl/BusShopServiceImpl.java) - 商户管理服务
- [`SysCompanyServiceImpl`](../src/main/java/cc/buyhoo/tax/service/impl/SysCompanyServiceImpl.java) - 企业管理服务

**API接口**:
- [`BusShopController`](../src/main/java/cc/buyhoo/tax/controller/BusShopController.java) - 商户管理接口

**业务步骤**:
1. 商户使用邀请码注册
2. 填写企业基本信息（统一社会信用代码、企业名称、地址等）
3. 配置银行账户信息
4. 系统验证企业信息有效性
5. 完成商户认证，激活账户

#### 3.1.2 商户银行账户管理
**流程描述**: 管理商户的银行账户信息，支持多银行账户

**主要实体**:
- [`BusShopBankEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopBankEntity.java) - 商户银行账户

**核心服务**:
- [`BusShopBankServiceImpl`](../src/main/java/cc/buyhoo/tax/service/impl/BusShopBankServiceImpl.java) - 银行账户管理

**API接口**:
- [`BusShopBankController`](../src/main/java/cc/buyhoo/tax/controller/BusShopBankController.java) - 银行账户管理接口

### 3.2 销售订单管理流程

#### 3.2.1 销售订单同步
**流程描述**: 从外部收银系统同步销售订单数据

**主要实体**:
- [`BusSaleListEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSaleListEntity.java) - 销售订单主表
- [`BusSaleListDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSaleListDetailEntity.java) - 销售订单明细
- [`BusSaleListPayDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSaleListPayDetailEntity.java) - 支付明细

**核心服务**:
- [`BusSaleListService`](../src/main/java/cc/buyhoo/tax/service/BusSaleListService.java) - 销售订单服务

**API接口**:
- [`SaleListController`](../src/main/java/cc/buyhoo/tax/controller/SaleListController.java) - 销售订单接口

**业务步骤**:
1. 外部收银系统产生销售订单
2. 通过API接口同步订单数据到税务系统
3. 系统验证订单数据完整性
4. 计算订单税额和服务费
5. 更新商户账单信息

#### 3.2.2 销售订单拆分
**流程描述**: 将大额订单拆分为多个小额订单，便于税务处理

**主要实体**:
- [`BusSaleListDisassembleEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSaleListDisassembleEntity.java) - 拆分订单
- [`DisassembleListEntity`](../src/main/java/cc/buyhoo/tax/entity/DisassembleListEntity.java) - 拆分记录

**核心服务**:
- [`DisassembleListService`](../src/main/java/cc/buyhoo/tax/service/DisassembleListService.java) - 订单拆分服务

**定时任务**:
- [`SaleListDisTask`](../src/main/java/cc/buyhoo/tax/task/SaleListDisTask.java) - 订单拆分定时任务

### 3.3 库存管理流程

#### 3.3.1 入库单管理
**流程描述**: 管理商户的商品入库信息，用于成本核算

**主要实体**:
- [`BusInventoryOrderEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInventoryOrderEntity.java) - 入库单
- [`BusInventoryBatchEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInventoryBatchEntity.java) - 入库批次
- [`BusInventoryBatchDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInventoryBatchDetailEntity.java) - 入库明细

**核心服务**:
- [`BusInventoryOrderService`](../src/main/java/cc/buyhoo/tax/service/BusInventoryOrderService.java) - 入库单服务
- [`BusInventoryBatchService`](../src/main/java/cc/buyhoo/tax/service/BusInventoryBatchService.java) - 入库批次服务

**API接口**:
- [`BusInventoryOrderController`](../src/main/java/cc/buyhoo/tax/controller/BusInventoryOrderController.java) - 入库单管理接口
- [`BusInventoryBatchController`](../src/main/java/cc/buyhoo/tax/controller/BusInventoryBatchController.java) - 入库批次接口

**定时任务**:
- [`InventoryTask`](../src/main/java/cc/buyhoo/tax/task/InventoryTask.java) - 库存统计定时任务

#### 3.3.2 退货管理
**流程描述**: 处理商户的退货订单和退货入库

**主要实体**:
- [`BusReturnListEntity`](../src/main/java/cc/buyhoo/tax/entity/BusReturnListEntity.java) - 退货订单
- [`BusReturnBatchEntity`](../src/main/java/cc/buyhoo/tax/entity/BusReturnBatchEntity.java) - 退货批次

**核心服务**:
- [`BusReturnListService`](../src/main/java/cc/buyhoo/tax/service/BusReturnListService.java) - 退货服务

**API接口**:
- [`ReturnListController`](../src/main/java/cc/buyhoo/tax/controller/ReturnListController.java) - 退货管理接口

### 3.4 商品管理流程

#### 3.4.1 商品信息管理
**流程描述**: 管理商户的商品基础信息和分类

**主要实体**:
- [`BusGoodsEntity`](../src/main/java/cc/buyhoo/tax/entity/BusGoodsEntity.java) - 商品基础信息
- [`BusGoodsCategoryEntity`](../src/main/java/cc/buyhoo/tax/entity/BusGoodsCategoryEntity.java) - 商品分类

**核心服务**:
- [`BusGoodsService`](../src/main/java/cc/buyhoo/tax/service/BusGoodsService.java) - 商品管理服务
- [`BusGoodsCategoryService`](../src/main/java/cc/buyhoo/tax/service/BusGoodsCategoryService.java) - 商品分类服务

**API接口**:
- [`BusGoodsController`](../src/main/java/cc/buyhoo/tax/controller/BusGoodsController.java) - 商品管理接口
- [`BusGoodsCategoryController`](../src/main/java/cc/buyhoo/tax/controller/BusGoodsCategoryController.java) - 商品分类接口

**业务步骤**:
1. 从外部系统同步商品基础信息
2. 设置商品分类和属性
3. 配置商品价格和库存信息
4. 绑定商品与分类关系
5. 维护商品生命周期状态

### 3.5 合同管理流程

#### 3.5.1 合同签署管理
**流程描述**: 管理与商户的各类合同签署和执行

**主要实体**:
- [`BusContractEntity`](../src/main/java/cc/buyhoo/tax/entity/BusContractEntity.java) - 合同信息

**核心服务**:
- [`BusContractService`](../src/main/java/cc/buyhoo/tax/service/BusContractService.java) - 合同管理服务

**API接口**:
- [`BusContractController`](../src/main/java/cc/buyhoo/tax/controller/BusContractController.java) - 合同管理接口

**业务步骤**:
1. 创建合同草稿（销售合同、采购合同、联营合同等）
2. 填写合同基本信息和条款
3. 提交合同审核
4. 合同签署确认
5. 合同执行状态跟踪
6. 合同完成或终止处理

### 3.6 发票管理流程

#### 3.6.1 发票开具流程
**流程描述**: 为销售订单开具电子发票

**主要实体**:
- [`BusShopInvoiceEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopInvoiceEntity.java) - 发票记录
- [`BusInvoiceStaffEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInvoiceStaffEntity.java) - 开票员工信息
- [`GenerateQdInvoiceEntity`](../src/main/java/cc/buyhoo/tax/entity/invoice/GenerateQdInvoiceEntity.java) - 开票请求参数

**核心服务**:
- [`BusShopInvoiceServiceImpl`](../src/main/java/cc/buyhoo/tax/service/impl/BusShopInvoiceServiceImpl.java) - 发票服务

**API接口**:
- [`BusShopInvoiceController`](../src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java) - 发票管理接口

**定时任务**:
- [`InvoiceResultTask`](../src/main/java/cc/buyhoo/tax/task/InvoiceResultTask.java) - 发票结果查询任务
- [`GetQdInvoiceTask`](../src/main/java/cc/buyhoo/tax/task/GetQdInvoiceTask.java) - 发票开具任务
- [`QuerySdInvoiceFileTask`](../src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileTask.java) - 发票文件查询任务

**业务步骤**:
1. 商户选择销售订单申请开票
2. 填写购买方信息（企业或个人）
3. 系统调用税务局开票接口
4. 开票成功后生成PDF发票文件
5. 转换为图片格式并上传到文件服务器
6. 通过短信或邮件发送给客户

### 3.7 税务申报流程

#### 3.7.1 税务记录管理
**流程描述**: 记录和管理企业的税务申报信息

**主要实体**:
- [`BusTaxRecordEntity`](../src/main/java/cc/buyhoo/tax/entity/BusTaxRecordEntity.java) - 税务申报记录

**核心服务**:
- [`BusTaxRecordServiceImpl`](../src/main/java/cc/buyhoo/tax/service/impl/BusTaxRecordServiceImpl.java) - 税务记录服务

**API接口**:
- [`BusTaxRecordController`](../src/main/java/cc/buyhoo/tax/controller/BusTaxRecordController.java) - 税务申报接口

**业务步骤**:
1. 系统根据销售数据计算应纳税额
2. 生成税务申报记录
3. 财务人员确认申报信息
4. 提交税务申报
5. 更新申报状态为已完成

### 3.8 银行转账流程

#### 3.8.1 自动转账
**流程描述**: 系统自动向商户转账销售款项

**主要实体**:
- [`BusShopBillEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopBillEntity.java) - 商户账单
- [`CmbcTranferRecordEntity`](../src/main/java/cc/buyhoo/tax/entity/CmbcTranferRecordEntity.java) - 转账记录

**核心服务**:
- [`BusShopBillServiceImpl`](../src/main/java/cc/buyhoo/tax/service/impl/BusShopBillServiceImpl.java) - 账单服务
- [`CmbService`](../src/main/java/cc/buyhoo/tax/service/CmbService.java) - 银行服务

**定时任务**:
- [`AutomaticTransferAccountTask`](../src/main/java/cc/buyhoo/tax/task/AutomaticTransferAccountTask.java) - 自动转账任务
- [`CmbTransferAccountTask`](../src/main/java/cc/buyhoo/tax/task/CmbTransferAccountTask.java) - 招商银行转账任务

**业务步骤**:
1. 系统计算商户应收款项
2. 扣除税费、服务费等
3. 生成转账指令
4. 调用银行API执行转账
5. 记录转账结果
6. 更新商户账单状态

#### 3.8.2 手动转账
**流程描述**: 财务人员手动发起转账操作

**API接口**:
- [`CmbController`](../src/main/java/cc/buyhoo/tax/controller/CmbController.java) - 银行操作接口

**业务步骤**:
1. 财务人员选择待转账账单
2. 确认转账金额和银行账户
3. 发起转账请求
4. 银行处理转账
5. 更新转账状态

### 3.9 财务结算流程

#### 3.9.1 利润分成
**流程描述**: 根据配置的分成规则计算各方利润

**主要实体**:
- [`BusCompanyProfitRuleEntity`](../src/main/java/cc/buyhoo/tax/entity/BusCompanyProfitRuleEntity.java) - 利润分成规则
- [`BusShopServiceFeeEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopServiceFeeEntity.java) - 服务费记录

**核心服务**:
- [`BusCompanyProfitRuleEntityService`](../src/main/java/cc/buyhoo/tax/service/BusCompanyProfitRuleEntityService.java) - 分成规则服务
- [`BusShopServiceFeeService`](../src/main/java/cc/buyhoo/tax/service/BusShopServiceFeeService.java) - 服务费服务

#### 3.9.2 账单管理
**流程描述**: 生成和管理商户的账单信息

**主要实体**:
- [`BusShopBillDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopBillDetailEntity.java) - 账单明细
- [`BusShopBillLogEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopBillLogEntity.java) - 账单日志

**API接口**:
- [`BusShopBillController`](../src/main/java/cc/buyhoo/tax/controller/BusShopBillController.java) - 账单管理接口

### 3.10 数据迁移流程

#### 3.10.1 商户迁移管理
**流程描述**: 处理商户在不同企业间的迁入迁出操作

**主要实体**:
- [`SysMigrateEntity`](../src/main/java/cc/buyhoo/tax/entity/SysMigrateEntity.java) - 迁移记录

**核心服务**:
- [`SysMigrateService`](../src/main/java/cc/buyhoo/tax/service/SysMigrateService.java) - 迁移管理服务

**定时任务**:
- [`CompanyMigrateIoTask`](../src/main/java/cc/buyhoo/tax/task/CompanyMigrateIoTask.java) - 企业迁移处理任务

**业务步骤**:
1. 发起商户迁移申请（迁入或迁出）
2. 填写迁移原因和相关信息
3. 系统审核迁移申请
4. 审核通过后执行数据迁移
5. 更新商户所属企业关系
6. 同步相关业务数据

### 3.11 现金升级流程

#### 3.11.1 现金升级管理
**流程描述**: 管理商户的现金升级申请和处理

**核心服务**:
- [`CashUpgradeService`](../src/main/java/cc/buyhoo/tax/service/CashUpgradeService.java) - 现金升级服务
- [`CashUpgradeForbidShopService`](../src/main/java/cc/buyhoo/tax/service/CashUpgradeForbidShopService.java) - 禁止升级商户管理
- [`CashUpgradeLogService`](../src/main/java/cc/buyhoo/tax/service/CashUpgradeLogService.java) - 升级日志服务

**API接口**:
- [`CashUpgradeController`](../src/main/java/cc/buyhoo/tax/controller/CashUpgradeController.java) - 现金升级接口
- [`CashUpgradeForbidShopController`](../src/main/java/cc/buyhoo/tax/controller/CashUpgradeForbidShopController.java) - 禁止升级管理接口
- [`CashUpgradeLogController`](../src/main/java/cc/buyhoo/tax/controller/CashUpgradeLogController.java) - 升级日志接口

**业务步骤**:
1. 商户提交现金升级申请
2. 系统检查商户是否在禁止升级名单中
3. 验证商户资质和条件
4. 审核升级申请
5. 升级成功后更新商户等级
6. 记录升级日志和相关操作

## 4. 权限管理

### 4.1 用户权限体系
**主要实体**:
- [`SysUserEntity`](../src/main/java/cc/buyhoo/tax/entity/SysUserEntity.java) - 系统用户
- [`SysRoleEntity`](../src/main/java/cc/buyhoo/tax/entity/SysRoleEntity.java) - 系统角色
- [`SysMenuEntity`](../src/main/java/cc/buyhoo/tax/entity/SysMenuEntity.java) - 系统菜单

**核心服务**:
- [`SysUserService`](../src/main/java/cc/buyhoo/tax/service/SysUserService.java) - 用户管理服务
- [`LoginService`](../src/main/java/cc/buyhoo/tax/service/LoginService.java) - 登录服务

### 4.2 数据权限
**主要实体**:
- [`StbusUserDataPermissionRelEntity`](../src/main/java/cc/buyhoo/tax/entity/StbusUserDataPermissionRelEntity.java) - 用户数据权限关系

## 5. 外部系统集成

### 5.1 税务局接口
- 电子发票开具
- 发票状态查询
- 发票文件下载

### 5.2 银行接口
- 招商银行转账接口
- 转账状态查询
- 账户余额查询

### 5.3 短信邮件服务
- 发票发送通知
- 系统异常告警

## 6. 定时任务

### 6.1 业务定时任务
- **库存统计任务**: 每日统计库存和成本数据
- **发票处理任务**: 查询发票开具结果
- **自动转账任务**: 自动向商户转账
- **订单拆分任务**: 处理大额订单拆分

### 6.2 数据同步任务
- **销售数据同步**: 同步外部收银系统数据
- **退货数据同步**: 同步退货信息
- **库存数据同步**: 同步库存变动

## 7. 系统监控

### 7.1 业务监控
- 订单处理状态监控
- 发票开具成功率监控
- 转账成功率监控
- 税务申报状态监控

### 7.2 技术监控
- 系统性能监控
- 数据库连接监控
- 外部接口调用监控
- 异常日志监控

## 8. 业务流程总结

### 8.1 核心业务链路
1. **商户入驻链路**: 注册 → 认证 → 银行配置 → 商品管理 → 合同签署
2. **订单处理链路**: 订单同步 → 数据验证 → 订单拆分 → 库存更新 → 账单生成
3. **发票开具链路**: 申请开票 → 税务局接口 → 文件生成 → 客户通知
4. **财务结算链路**: 利润计算 → 税费扣除 → 银行转账 → 账单更新
5. **税务申报链路**: 数据统计 → 申报生成 → 审核确认 → 状态更新

### 8.2 关键业务规则
- **订单拆分**: 大额订单自动拆分为小额订单，便于税务处理
- **发票管理**: 支持企业和个人开票，自动生成PDF和图片格式
- **转账方式**: 支持转账和代发两种模式，根据银行配置自动选择
- **利润分成**: 根据配置的分成规则自动计算各方利润
- **数据权限**: 基于企业和用户角色的多级数据权限控制

### 8.3 系统集成点
- **外部收银系统**: 销售订单和退货数据同步
- **税务局系统**: 电子发票开具和查询
- **银行系统**: 转账和代发业务处理
- **短信邮件系统**: 发票通知和系统告警
- **文件存储系统**: MinIO对象存储和SFTP文件传输
- **缓存系统**: Redis缓存和会话管理

> 📖 **相关文档**: 详细的IO操作和数据流转请参考 [IO业务流程文档](./IO.md)

### 8.4 监控告警
- **业务监控**: 订单处理、发票开具、转账成功率
- **系统监控**: 接口调用、数据库性能、异常日志
- **告警机制**: 钉钉群通知、邮件告警、短信通知

### 8.5 数据安全
- **权限控制**: 基于Sa-Token的细粒度权限管理
- **数据加密**: 敏感数据加密存储和传输
- **审计日志**: 关键操作的完整审计记录
- **备份恢复**: 定期数据备份和灾难恢复机制

## 9. 快速参考

### 9.1 主要API接口列表
| 模块 | 控制器 | 主要功能 |
|------|--------|----------|
| 商户管理 | BusShopController | 商户注册、认证、信息管理 |
| 订单管理 | SaleListController | 销售订单同步、查询、统计 |
| 库存管理 | BusInventoryOrderController | 入库单管理、库存统计 |
| 商品管理 | BusGoodsController | 商品信息、分类管理 |
| 合同管理 | BusContractController | 合同签署、状态跟踪 |
| 发票管理 | BusShopInvoiceController | 发票开具、状态查询 |
| 税务申报 | BusTaxRecordController | 税务记录、申报管理 |
| 银行转账 | CmbController | 转账操作、状态查询 |
| 账单管理 | BusShopBillController | 账单生成、结算管理 |

### 9.2 关键定时任务
| 任务名称 | 执行频率 | 主要功能 |
|----------|----------|----------|
| InventoryTask | 每日 | 库存统计和成本核算 |
| InvoiceResultTask | 每小时 | 发票开具结果查询 |
| AutomaticTransferAccountTask | 每日 | 自动转账处理 |
| SaleListDisTask | 实时 | 大额订单拆分 |
| CompanyMigrateIoTask | 每日 | 企业迁移数据处理 |

### 9.3 重要业务枚举
- **订单类型**: 实体店销售、APP订单、微信小程序、外卖订单等
- **合同类型**: 销售合同、采购合同、联营合同、其他
- **发票状态**: 待开票、开票中、开票成功、开票失败
- **转账方式**: 转账、代发
- **税务申报状态**: 待申报、申报中、申报完成

### 9.4 常见问题处理
1. **订单同步失败**: 检查外部系统连接状态和数据格式
2. **发票开具失败**: 确认税务局接口状态和开票信息完整性
3. **转账失败**: 验证银行账户信息和余额状态
4. **数据权限问题**: 检查用户角色和数据权限配置
5. **定时任务异常**: 查看任务执行日志和系统资源状态

---

**文档版本**: v1.0
**最后更新**: 2024年12月
**维护人员**: 系统开发团队

**变更记录**:
- v1.0 (2024-12): 初始版本，包含完整业务流程文档
