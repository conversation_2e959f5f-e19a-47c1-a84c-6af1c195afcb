# 税务统计系统消息队列与通信业务流程文档

## 1. 概述

本文档描述了税务统计系统(yxl-tax-statistic)中所有消息队列、WebSocket、Socket、MQTT、RabbitMQ等MQ相关的业务逻辑和通信机制。系统采用多种通信方式实现异步处理、状态通知和系统解耦。

### 1.1 技术架构
- **消息队列**: RocketMQ (配置但未实际使用)
- **短信通知**: 阿里云短信服务
- **邮件通知**: SMTP邮件服务
- **HTTP通知**: Webhook回调机制
- **钉钉通知**: 企业内部通知
- **异步处理**: 线程池异步执行

### 1.2 通信机制分类
- 短信通知 (发票推送)
- 邮件通知 (发票推送)
- HTTP Webhook通知 (开票状态回调)
- 钉钉机器人通知 (业务异常告警)
- 银行转账通知 (招商银行回调)

## 2. 短信通知服务

### 2.1 阿里云短信服务配置

系统使用阿里云短信服务进行发票通知，主要配置通过`SmsgProperties`管理。

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/BusShopInvoiceServiceImpl.java" mode="EXCERPT">
````java
@Resource
private SmsgProperties smsgProperties;

//校验地址格式是手机号
if (SystemUtil.isValidPhoneNumber(receiveMsg)) {
    //发送短信
    Map<String, String> map = new HashMap<>();
    //注意,imgurl不包含域名信息
    map.put("recordId", invoiceEntity.getId().toString());

    AliyunSmsTemplate smsTemplate = new AliyunSmsTemplate(smsgProperties);
    SmsResult smsResult = smsTemplate.send(receiveMsg, smsgProperties.getTemplatedIdForInvoice(), map);
    System.out.println("短信发送结果" + smsResult);
}
````
</augment_code_snippet>

### 2.2 短信发送业务流程

```mermaid
graph TD
    A[发票开具成功] --> B[检查接收方式]
    B --> C{是否为手机号}
    C -->|是| D[构建短信参数]
    D --> E[创建阿里云短信模板]
    E --> F[发送短信]
    F --> G[记录发送结果]
    C -->|否| H[检查是否为邮箱]
    H -->|是| I[发送邮件]
    H -->|否| J[跳过通知]
```

### 2.3 短信发送触发场景

1. **发票开具成功** - 自动发送发票链接短信
2. **发票重新发送** - 手动触发重发短信
3. **批量开票完成** - 批量发送短信通知

相关代码文件：
- [BusShopInvoiceServiceImpl.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/BusShopInvoiceServiceImpl.java) - 主要短信发送逻辑
- [QuerySdInvoiceFileTask.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileTask.java) - 异步任务中的短信发送
- [InvoiceResultTask.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/InvoiceResultTask.java) - 发票结果处理任务

## 3. 邮件通知服务

### 3.1 SMTP邮件配置

<augment_code_snippet path="yxl-tax-statistic/src/main/resources/mail.setting" mode="EXCERPT">
````properties
# 邮件服务器的SMTP地址，可选，默认为smtp.<发件人邮箱后缀>
host = smtp.126.com
# 邮件服务器的SMTP端口，可选，默认25
port = 25
# 发件人（必须正确，否则发送失败）
from = <EMAIL>
# 用户名，默认为发件人邮箱前缀
user = 13054908225
# 密码（注意，某些邮箱需要为SMTP服务单独设置授权码，详情查看相关帮助）
pass = TBAIKBQONBGWAVPJ
````
</augment_code_snippet>

### 3.2 邮件发送逻辑

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/BusShopInvoiceServiceImpl.java" mode="EXCERPT">
````java
} else if (SystemUtil.isValidEmail(receiveMsg)) {
    //发送邮件
    MailUtil.send(receiveMsg, "电子发票", "谢谢您的光临", false, file);
}
````
</augment_code_snippet>

### 3.3 邮件发送业务场景

1. **发票文件推送** - 将PDF发票作为附件发送
2. **业务通知** - 重要业务状态变更通知

## 4. HTTP Webhook通知机制

### 4.1 发票状态回调通知

系统通过HTTP POST方式向外部系统发送发票状态变更通知。

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/config/InvoiceNotifyConfig.java" mode="EXCERPT">
````java
@Data
@Configuration
@ConfigurationProperties(prefix = "invoicenotify")
public class InvoiceNotifyConfig {
    public String url;
    public String urlRes;
}
````
</augment_code_snippet>

### 4.2 通知URL构建逻辑

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/util/InvoiceUrlUtil.java" mode="EXCERPT">
````java
@Component
public class InvoiceUrlUtil {
    @Resource
    private InvoiceNotifyConfig invoiceNotifyConfig;

    public String buildUrl(Integer type) {
        ShopTypeEnum shopType = ShopTypeEnum.getEnumByValue(type);
        return shopType == ShopTypeEnum.RESTAURANT_SHOP ?
                invoiceNotifyConfig.urlRes : invoiceNotifyConfig.url;
    }
}
````
</augment_code_snippet>

### 4.3 HTTP通知发送实现

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileTask.java" mode="EXCERPT">
````java
try {
    //查询店铺类型
    BusShopEntity busShopEntity = busShopMapper.selectOne(Wrappers.<BusShopEntity>lambdaQuery().eq(BusShopEntity::getShopUnique, invoiceEntity.getShopUnique()));
    String url = invoiceUrlUtil.buildUrl(busShopEntity.getShopType());
    NotifyInvoiceResultParams params = new NotifyInvoiceResultParams();
    params.setSaleListUnique(invoiceEntity.getSaleListUnique());
    params.setBillNumber(billNumber);
    params.setStatus(3);
    params.setImageUrl(minioUploadResult.getUrl());
    System.out.println("通知开票状态:" + url + "["+JSONUtil.toJsonStr(params)+"]");
    HttpUtil.post(url, JSONUtil.toJsonStr(params));
} catch (Exception e) {
    e.printStackTrace();
}
````
</augment_code_snippet>

### 4.4 HTTP通知业务流程

```mermaid
graph TD
    A[发票状态变更] --> B[获取商户信息]
    B --> C[根据商户类型构建通知URL]
    C --> D[构建通知参数]
    D --> E[发送HTTP POST请求]
    E --> F{请求是否成功}
    F -->|成功| G[记录通知成功]
    F -->|失败| H[记录异常信息]
```

### 4.5 通知状态说明

- **状态3**: 开票成功
- **状态4**: 开票失败

相关代码文件：
- [QuerySdInvoiceFileTask.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileTask.java) - 发票文件查询任务通知
- [QuerySdInvoiceFileBatchTask.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileBatchTask.java) - 批量发票文件处理通知
- [GetQdInvoiceBatchTask.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/GetQdInvoiceBatchTask.java) - 批量开票任务通知
- [InvoiceResultTask.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/InvoiceResultTask.java) - 发票结果处理通知

## 5. 钉钉机器人通知

### 5.1 钉钉通知配置

系统集成钉钉机器人用于业务异常和重要事件通知。

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/dubbo/BusShopSaleListMonitorFacadeImpl.java" mode="EXCERPT">
````java
@Resource
private SendDingDingTalkUtils sendDingDingTalkUtils;
````
</augment_code_snippet>

### 5.2 钉钉通知业务场景

#### 5.2.1 销售数据监控异常通知

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/dubbo/BusShopSaleListMonitorFacadeImpl.java" mode="EXCERPT">
````java
String body = str.toString();
sendDingDingTalkUtils.sendDingDingTalkMsgText(null, DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd"), body);
````
</augment_code_snippet>

#### 5.2.2 自动转账异常通知

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/AutomaticTransferAccountTask.java" mode="EXCERPT">
````java
try {
    //增加打款失败后钉钉通知
    if (str.length() > 0) {
        String body = str.toString();
        sendDingDingTalkUtils.sendDingDingTalkSettleMsg(null, "纳统打款异常", body);
    }
} catch (Exception e) {
    log.error("钉钉发送异常", e);
}
````
</augment_code_snippet>

### 5.3 钉钉通知类型

1. **数据监控通知** - 销售数据异常监控
2. **打款异常通知** - 自动转账失败告警
3. **业务异常通知** - 其他业务流程异常

相关代码文件：
- [BusShopSaleListMonitorFacadeImpl.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/dubbo/BusShopSaleListMonitorFacadeImpl.java) - 销售监控通知
- [AutomaticTransferAccountTask.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/AutomaticTransferAccountTask.java) - 转账异常通知
- [PayCenterPayHelper.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/temporary/PayCenterPayHelper.java) - 支付中心通知

## 6. 银行转账通知机制

### 6.1 招商银行转账通知

系统接收招商银行的转账通知回调，用于处理入账业务。

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CmbController.java" mode="EXCERPT">
````java
//收到的信息为转账通知
if (cmbNote.getNottyp().equals(NottypEnums.YQN36110.getMode())) {
    String notdat = cmbNote.getNotdat();
    CMBNoteData msg = JSONObject.parseObject(notdat, CMBNoteData.class);

    //收到的信息为入账通知
    if (msg.getMsgtyp().equals(MsgtypEnums.BHNTRRCV.getMode())) {
        Yqn36110BhntrrcvEntity yqn36110BhntrrcvEntity = JSONObject.parseObject(msg.getMsgdat().toString(), Yqn36110BhntrrcvEntity.class);
        //只有收到转账通知才生成订单，且生成销售订单
        yqn36110Bhntrrcv(yqn36110BhntrrcvEntity);
    }
}
````
</augment_code_snippet>

### 6.2 银行通知处理流程

```mermaid
graph TD
    A[接收银行通知] --> B[解析通知类型]
    B --> C{是否为转账通知}
    C -->|是| D[解析通知数据]
    D --> E{是否为入账通知}
    E -->|是| F[处理入账业务]
    F --> G[生成销售订单]
    E -->|否| H[忽略通知]
    C -->|否| H
```

相关代码文件：
- [CmbController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CmbController.java) - 招商银行通知接收

## 7. 异步处理机制

### 7.1 线程池配置

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/config/thread/AsyncConfiguration.java" mode="EXCERPT">
````java
@Bean("async")
public Executor doSomethingExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    // 核心线程数：线程池创建时候初始化的线程数
    executor.setCorePoolSize(10);
    // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
    executor.setMaxPoolSize(20);
    // 缓冲队列：用来缓冲执行任务的队列
    executor.setQueueCapacity(500);
    // 允许线程的空闲时间60秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
    executor.setKeepAliveSeconds(60);
    // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
    executor.setThreadNamePrefix("async-");
    // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.initialize();
    return executor;
}
````
</augment_code_snippet>

### 7.2 异步通知实现

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileBatchTask.java" mode="EXCERPT">
````java
ThreadUtil.execAsync(() -> {
    try {
        NotifyInvoiceResultParams params = new NotifyInvoiceResultParams();
        params.setSaleListUnique(v.getSaleListUnique());
        params.setBillNumber(billNumber);
        params.setStatus(3);
        params.setImageUrl(minioUploadResult.getUrl());
        System.out.println("通知开票状态:" + url + "[" + JSONUtil.toJsonStr(params) + "]");
        HttpUtil.post(url, JSONUtil.toJsonStr(params));
    } catch (Exception e) {
        e.printStackTrace();
    }
});
````
</augment_code_snippet>

## 8. RocketMQ消息队列

### 8.1 RocketMQ配置

虽然项目中配置了RocketMQ依赖，但在当前代码中未找到具体的生产者和消费者实现。

<augment_code_snippet path="yxl-tax-statistic/pom.xml" mode="EXCERPT">
````xml
<properties>
    <rocketmq.version>2.3.1</rocketmq.version>
</properties>
````
</augment_code_snippet>

### 8.2 消息队列应用场景

根据IO.md文档描述，RocketMQ主要用于：
- 发票处理消息
- 数据同步消息
- 业务通知消息

*注：当前版本中RocketMQ的具体实现可能在外部依赖包中，或者计划在后续版本中实现。*

### 8.3 消息队列集成建议

基于当前系统架构，建议按以下方式集成RocketMQ：

1. **生产者配置** - 在发票处理、数据同步等关键业务点发送消息
2. **消费者配置** - 创建专门的消费者处理异步通知任务
3. **消息分类** - 按业务类型划分Topic（发票、转账、监控等）
4. **失败重试** - 配置消息重试机制和死信队列

## 9. 消息通信监控与故障处理

### 9.1 当前监控机制

#### 9.1.1 日志监控
系统通过控制台输出和日志文件记录通知发送状态：

```java
System.out.println("短信发送结果" + smsResult);
System.out.println("通知开票状态:" + url + "["+JSONUtil.toJsonStr(params)+"]");
```

#### 9.1.2 异常处理
所有通知发送都包含异常捕获机制：

```java
try {
    HttpUtil.post(url, JSONUtil.toJsonStr(params));
} catch (Exception e) {
    e.printStackTrace();
}
```

### 9.2 故障处理策略

1. **短信发送失败** - 记录日志，不影响主业务流程
2. **邮件发送失败** - 记录异常，可考虑降级为短信通知
3. **HTTP通知失败** - 记录异常，建议增加重试机制
4. **钉钉通知失败** - 记录日志，不影响业务告警的及时性

### 9.3 监控改进建议

1. **统一日志格式** - 建立标准的通知日志格式
2. **状态码标准化** - 定义统一的通知状态码体系
3. **监控大盘** - 建立通知成功率、失败率监控面板
4. **告警机制** - 通知失败率超过阈值时自动告警

## 10. 整体通信架构

### 10.1 通信架构图

```mermaid
graph TB
    subgraph "税务统计系统"
        A[业务处理层]
        B[通知服务层]
        C[异步任务层]
    end

    subgraph "通信方式"
        D[短信通知]
        E[邮件通知]
        F[HTTP Webhook]
        G[钉钉通知]
        H[银行回调]
    end

    subgraph "外部系统"
        I[阿里云短信]
        J[SMTP服务器]
        K[商户系统]
        L[钉钉机器人]
        M[招商银行]
    end

    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G

    D --> I
    E --> J
    F --> K
    G --> L
    M --> H
    H --> A
```

### 10.2 发票通知业务时序图

```mermaid
sequenceDiagram
    participant B as 业务系统
    participant I as 发票服务
    participant T as 异步任务
    participant S as 短信服务
    participant E as 邮件服务
    participant H as HTTP通知
    participant D as 钉钉通知

    B->>I: 发票开具请求
    I->>I: 调用税务API开票
    I->>T: 创建异步通知任务

    par 并行通知处理
        T->>S: 发送短信通知
        S-->>T: 返回发送结果
    and
        T->>E: 发送邮件通知
        E-->>T: 返回发送结果
    and
        T->>H: 发送HTTP回调
        H-->>T: 返回回调结果
    end

    alt 通知失败
        T->>D: 发送钉钉告警
        D-->>T: 返回告警结果
    end

    T->>I: 更新通知状态
    I->>B: 返回处理结果
```

### 10.3 消息流转说明

1. **业务触发** - 业务处理层产生通知需求
2. **通知分发** - 通知服务层根据类型分发消息
3. **异步执行** - 异步任务层执行具体通知逻辑
4. **外部投递** - 向外部系统投递消息
5. **状态反馈** - 接收外部系统的回调通知

## 11. 最佳实践与优化建议

### 11.1 当前实现优点

1. **多样化通信方式** - 支持短信、邮件、HTTP、钉钉等多种通信方式
2. **异步处理** - 使用线程池实现异步通知，提高系统响应性能
3. **容错处理** - 通知失败时有异常捕获和日志记录
4. **灵活配置** - 通过配置文件管理不同的通知地址和参数

### 11.2 优化建议

1. **引入真正的消息队列** - 建议实际使用RocketMQ替代当前的异步线程池
2. **重试机制** - 为HTTP通知添加重试机制，提高通知成功率
3. **通知状态跟踪** - 建立通知状态表，跟踪每次通知的发送状态
4. **监控告警** - 增加通知失败率监控和告警机制
5. **批量处理** - 对于大量通知，考虑批量发送以提高效率

### 11.3 扩展方向

1. **WebSocket实时通知** - 为前端页面添加实时状态推送
2. **消息模板管理** - 建立统一的消息模板管理系统
3. **多渠道路由** - 根据用户偏好自动选择通知渠道
4. **消息去重** - 避免重复发送相同内容的通知

## 12. 相关配置文件

- [application.yml](yxl-tax-statistic/src/main/resources/application.yml) - 主配置文件
- [mail.setting](yxl-tax-statistic/src/main/resources/mail.setting) - 邮件服务配置
- [pom.xml](yxl-tax-statistic/pom.xml) - Maven依赖配置

## 13. 总结

税务统计系统的消息通信机制涵盖了短信、邮件、HTTP回调、钉钉通知等多种方式，通过异步处理保证了系统的响应性能。虽然配置了RocketMQ但未实际使用，建议在后续版本中引入真正的消息队列机制以提高系统的可靠性和扩展性。整体架构设计合理，但在重试机制、状态跟踪和监控告警方面还有优化空间。
