# 税务统计系统数据库ER图文档

## 1. 概述

本文档描述了税务统计系统(yxl-tax-statistic)的数据库表结构及其实体关系图(ER图)。系统采用MySQL数据库，使用MyBatis Plus作为ORM框架，主要用于管理商户、订单、库存、发票、税务申报等业务数据。

### 1.1 技术架构
- **数据库**: MySQL
- **ORM框架**: MyBatis Plus
- **实体基类**: BaseEntity (包含id、createTime、modifyTime等公共字段)

### 1.2 命名规范
- 表名：使用下划线分隔的小写字母
- 实体类：使用驼峰命名，以Entity结尾
- 字段名：使用下划线分隔的小写字母

## 2. 核心业务实体关系图

```mermaid
erDiagram
    %% 系统管理模块
    SYS_COMPANY ||--o{ SYS_USER : "拥有"
    SYS_COMPANY ||--o{ SYS_COMPANY_CONFIG : "配置"
    SYS_COMPANY ||--o{ SYS_COMPANY_BANK_ACCOUNT : "银行账户"
    SYS_USER ||--o{ SYS_USER_ROLE : "角色关联"
    SYS_ROLE ||--o{ SYS_USER_ROLE : "用户关联"
    SYS_ROLE ||--o{ SYS_ROLE_MENU : "菜单权限"
    SYS_MENU ||--o{ SYS_ROLE_MENU : "角色权限"
    SYS_BANK_LIST ||--o{ SYS_BANK_BRANCH_LIST : "支行"
    SYS_BANK_LIST ||--o{ SYS_COMPANY_BANK_ACCOUNT : "银行账户"

    %% 商户管理模块
    SYS_COMPANY ||--o{ BUS_SHOP : "管理"
    BUS_SHOP ||--o{ BUS_CONTRACT : "合同"
    BUS_SHOP ||--o{ BUS_SALE_LIST : "销售订单"
    BUS_SHOP ||--o{ BUS_SHOP_BILL : "账单"
    BUS_SHOP ||--o{ BUS_SHOP_INVOICE : "发票"

    %% 商品管理模块
    SYS_COMPANY ||--o{ BUS_GOODS_CATEGORY : "商品分类"
    BUS_GOODS_CATEGORY ||--o{ BUS_GOODS : "商品"
    BUS_SHOP ||--o{ BUS_GOODS : "供应商品"

    %% 订单管理模块
    BUS_SALE_LIST ||--o{ BUS_SALE_LIST_DETAIL : "订单明细"
    BUS_SALE_LIST ||--o{ BUS_SALE_LIST_PAY_DETAIL : "支付明细"
    BUS_GOODS ||--o{ BUS_SALE_LIST_DETAIL : "商品明细"

    %% 库存管理模块
    SYS_COMPANY ||--o{ BUS_INVENTORY_ORDER : "入库单"
    BUS_INVENTORY_ORDER ||--o{ BUS_INVENTORY_BATCH : "入库批次"
    BUS_INVENTORY_BATCH ||--o{ BUS_INVENTORY_BATCH_DETAIL : "批次明细"
    BUS_GOODS ||--o{ BUS_INVENTORY_BATCH_DETAIL : "商品库存"

    %% 发票管理模块
    BUS_SHOP_INVOICE ||--o{ BUS_SHOP_INVOICE_DETAIL : "发票明细"
    SYS_COMPANY ||--o{ BUS_SHOP_INVOICE_SETTING : "开票设置"
    SYS_COMPANY ||--o{ BUS_SHOP_INVOICE_GOODS_SERVICE : "开票服务"

    %% 财务管理模块
    BUS_SHOP_BILL ||--o{ BUS_SHOP_BILL_DETAIL : "账单明细"
    SYS_COMPANY ||--o{ BUS_TAX_RECORD : "税务记录"
    SYS_COMPANY ||--o{ TRADE_RECORD : "交易记录"

    %% 数据同步模块
    SYS_COMPANY ||--o{ BUS_SYNC_RECORD : "同步记录"
    SYS_COMPANY ||--o{ BUS_INVENTORY_NOT_SYNC : "未同步数据"
```

## 3. 简化业务关系图

```mermaid
erDiagram
    SYS_COMPANY {
        bigint id PK
        string company_name
        string license_number
        int company_type
    }

    BUS_SHOP {
        bigint id PK
        bigint shop_unique UK
        bigint company_id FK
        string shop_name
        string bank_card
    }

    BUS_SALE_LIST {
        bigint id PK
        string sale_list_unique UK
        bigint company_id FK
        bigint shop_unique FK
        decimal sale_list_total
    }

    BUS_SHOP_INVOICE {
        bigint id PK
        bigint company_id FK
        bigint shop_unique FK
        string sale_list_unique FK
        decimal order_tax_money
    }

    BUS_SHOP_BILL {
        bigint id PK
        bigint company_id FK
        bigint shop_unique FK
        decimal settled_amount
    }

    SYS_COMPANY ||--o{ BUS_SHOP : "管理"
    BUS_SHOP ||--o{ BUS_SALE_LIST : "销售"
    BUS_SALE_LIST ||--|| BUS_SHOP_INVOICE : "开票"
    BUS_SHOP ||--|| BUS_SHOP_BILL : "结算"
```

## 4. 详细表结构说明

### 4.1 系统管理模块

#### 4.1.1 企业信息表 (sys_company)
**表名**: `sys_company`  
**实体类**: [`SysCompanyEntity`](../src/main/java/cc/buyhoo/tax/entity/SysCompanyEntity.java)  
**描述**: 存储企业基本信息，支持平台、总公司、分公司的层级结构

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| parent_company_id | bigint(20) | 父公司ID | FK |
| company_type | int(11) | 公司类型:1系统平台2总公司3分公司 | NOT NULL |
| company_name | varchar(100) | 公司名称 | NOT NULL |
| license_number | varchar(50) | 统一社会信用代码 | |
| dept_id | bigint(20) | 所属部门 | |
| address | varchar(200) | 公司地址 | |
| contact_name | varchar(50) | 联系人 | |
| contact_mobile | varchar(20) | 联系电话 | |
| enable_status | int(11) | 有效状态:1正常0无效 | DEFAULT 1 |
| invitation_code | varchar(50) | 邀请码 | |
| bank_name | varchar(100) | 开户行 | |
| bank_card | varchar(30) | 银行账号 | |

#### 4.1.2 系统用户表 (sys_user)
**表名**: `sys_user`
**实体类**: [`SysUserEntity`](../src/main/java/cc/buyhoo/tax/entity/SysUserEntity.java)
**描述**: 系统用户信息，关联企业和角色

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| username | varchar(50) | 用户名 | NOT NULL, UNIQUE |
| password | varchar(100) | 密码 | NOT NULL |
| user_type | int(11) | 用户类型：1超级管理员2普通管理员 | |
| mobile | varchar(20) | 手机号 | |
| email | varchar(100) | 邮箱 | |
| avatar | varchar(200) | 用户头像 | |
| enable_status | int(11) | 有效状态:1正常0无效 | DEFAULT 1 |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

#### 4.1.3 系统角色表 (sys_role)
**表名**: `sys_role`
**实体类**: [`SysRoleEntity`](../src/main/java/cc/buyhoo/tax/entity/SysRoleEntity.java)
**描述**: 系统角色定义，支持数据权限控制

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| role_type | int(11) | 角色类型:1超级管理员2普通管理员 | |
| role_name | varchar(50) | 角色名称 | NOT NULL |
| data_scope | int(11) | 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） | |
| data_scope_dept_ids | varchar(500) | 数据范围(指定部门数组) | |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

#### 4.1.4 系统菜单表 (sys_menu)
**表名**: `sys_menu`
**实体类**: [`SysMenuEntity`](../src/main/java/cc/buyhoo/tax/entity/SysMenuEntity.java)
**描述**: 系统菜单权限管理，支持树形结构

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| parent_id | bigint(20) | 父菜单ID | |
| menu_name | varchar(50) | 菜单名称 | NOT NULL |
| permission | varchar(100) | 菜单权限 | |
| type | int(11) | 菜单类型:1目录2菜单3按钮 | |
| path | varchar(200) | 路由地址 | |
| component | varchar(200) | 组件地址 | |
| component_name | varchar(50) | 组件名称 | |
| sort | int(11) | 排序 | DEFAULT 0 |
| icon | varchar(50) | 组件图标 | |
| hidden | int(11) | 1隐藏2显示 | DEFAULT 2 |
| menu_level | int(11) | 菜单级别:0-普通,1-平台,2-内置 | DEFAULT 0 |
| built_in_system | int(11) | 是否系统内置：0-否，1-是 | DEFAULT 0 |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

#### 4.1.5 用户角色关联表 (sys_user_role)
**表名**: `sys_user_role`
**描述**: 用户与角色的多对多关联表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| user_id | bigint(20) | 用户ID | FK → sys_user.id |
| role_id | bigint(20) | 角色ID | FK → sys_role.id |

#### 4.1.6 角色菜单关联表 (sys_role_menu)
**表名**: `sys_role_menu`
**描述**: 角色与菜单权限的多对多关联表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| role_id | bigint(20) | 角色ID | FK → sys_role.id |
| menu_id | bigint(20) | 菜单ID | FK → sys_menu.id |

#### 4.1.7 企业配置表 (sys_company_config)
**表名**: `sys_company_config`
**描述**: 纳统企业配置信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| config_key | varchar(100) | 配置键 | |
| config_value | varchar(500) | 配置值 | |
| config_desc | varchar(200) | 配置描述 | |
| enable_status | int(11) | 有效状态:1正常0无效 | DEFAULT 1 |

### 4.2 银行管理模块

#### 4.2.1 银行列表表 (sys_bank_list)
**表名**: `sys_bank_list`
**实体类**: [`SysBankListEntity`](../src/main/java/cc/buyhoo/tax/entity/SysBankListEntity.java)
**描述**: 银行基础信息管理

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| bank_name | varchar(100) | 银行名称 | |
| bank_code | varchar(20) | 银行编码 | |
| interbank_number | varchar(20) | 银行联行号 | |
| bank_logo | varchar(255) | 银行logo存放地址 | |
| del_flag | int(1) | 删除标记：0、删除；1、未删除 | DEFAULT 1 |

#### 4.2.2 银行支行表 (sys_bank_branch_list)
**表名**: `sys_bank_branch_list`
**描述**: 银行分支机构信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| branch_id | varchar(20) | 银行内部分配的分支ID | |
| bank_id | bigint(20) | 分支所属银行ID | FK → sys_bank_list.id |
| branch_name | varchar(100) | 银行分支的名称 | |
| del_flag | smallint(1) | 删除标记：1、未删除；0、已删除 | DEFAULT 1 |

#### 4.2.3 企业银行账户表 (sys_company_bank_account)
**表名**: `sys_company_bank_account`
**实体类**: [`SysCompanyBankAccountEntity`](../src/main/java/cc/buyhoo/tax/entity/SysCompanyBankAccountEntity.java)
**描述**: 企业银行账户信息，支持多银行账户管理

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 所属企业ID | FK → sys_company.id |
| is_default | smallint(1) | 1、默认账户；0、非默认账户 | |
| bank_id | bigint(20) | 所属银行ID | FK → sys_bank_list.id |
| bank_card | varchar(30) | 银行账号 | |
| bank_name | varchar(50) | 开户行名称 | |
| bank_lhh | varchar(12) | 联行号 | |
| bank_branch | varchar(20) | 所在银行的支行号 | NOT NULL |
| symmetric_key | varchar(255) | 对称秘钥 | |
| private_key | varchar(255) | 银行私钥 | |
| public_key | varchar(255) | 银行公钥 | |
| uid | varbinary(50) | 银行分配的员工ID | |
| busmod | varchar(50) | 业务ID | |
| bill_type | int(11) | 转账方式：1、转账；2、代发工资 | DEFAULT 1 |

### 4.3 商户管理模块

#### 4.3.1 商户信息表 (bus_shop)
**表名**: `bus_shop`
**实体类**: [`BusShopEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopEntity.java)
**描述**: 商户基本信息，包含银行账户、合作方式等

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| shop_unique | bigint(20) | 商家唯一标识 | UNIQUE |
| shop_name | varchar(100) | 商家名称 | NOT NULL |
| shop_type | int(11) | 店铺类型 | |
| address | varchar(200) | 详细地址 | |
| shop_phone | varchar(20) | 联系电话 | |
| county_code | varchar(20) | 区县编码 | |
| town_code | varchar(20) | 村编码 | |
| enable_status | int(11) | 有效状态:1正常0无效 | DEFAULT 1 |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| invitation_code | varchar(50) | 邀请码 | |
| bank_name | varchar(100) | 银行名称 | |
| bank_city | varchar(100) | 开户行所在地 | |
| bank_card | varchar(30) | 银行卡号 | |
| legal_person | varchar(50) | 法人姓名 | |
| legal_phone | varchar(20) | 法人手机号 | |
| bind_status | int(11) | 绑定状态:0未绑定1已绑定 | DEFAULT 0 |
| cooperate_type | int(11) | 合作方式 | |
| sub_account | varchar(50) | 子商户号 | |
| service_fee_rate | decimal(10,3) | 结算服务费率（千分比）‰ | |
| has_service_fee | int(11) | 是否有服务费：0-无，1-有 | DEFAULT 0 |
| sync_canyin_data | int(11) | 是否同步餐饮订单: 0-否，1-是 | DEFAULT 0 |
| sync_buyhoo_data | int(11) | 是否同步零售订单: 0-否，1-是 | DEFAULT 0 |
| contract_url | varchar(500) | 合同附件地址 | |
| shop_nature | int(11) | 店铺性质（0-一般纳税人，1-个体工商户，2-小规模纳税人） | |
| province | bigint(20) | 所在省 | |
| city | bigint(20) | 所在市 | |
| industry | varchar(100) | 行业 | |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

### 4.4 合同管理模块

#### 4.4.1 合同管理表 (bus_contract)
**表名**: `bus_contract`
**实体类**: [`BusContractEntity`](../src/main/java/cc/buyhoo/tax/entity/BusContractEntity.java)
**描述**: 商户合同信息管理

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| contract_no | varchar(50) | 合同编码 | UNIQUE |
| shop_unique | bigint(20) | 商户编码 | FK → bus_shop.shop_unique |
| contract_name | varchar(100) | 合同名称 | |
| contract_type | int(11) | 合同类型：1-销售合同，2-采购合同，3-联营合同，4-其它 | |
| total_amount | decimal(15,2) | 合同金额（元） | |
| contract_status | int(11) | 合同状态：0-草拟，1-待审，2-已签署，3-执行中，4-已完成，5-终止 | |
| start_time | datetime | 合同开始时间 | |
| end_time | datetime | 合同结束时间 | |
| sign_time | varchar(20) | 合同签订日期 | |
| order_no | text | 关联单号,多个逗号(,)分割 | |
| file_url | varchar(500) | 附件地址 | |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

### 4.5 商品管理模块

#### 4.5.1 商品分类表 (bus_goods_category)
**表名**: `bus_goods_category`
**实体类**: [`BusGoodsCategoryEntity`](../src/main/java/cc/buyhoo/tax/entity/BusGoodsCategoryEntity.java)
**描述**: 商品分类管理，支持树形结构和税务信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| parent_id | bigint(20) | 所属父类ID | |
| ancestors | varchar(500) | 祖籍列表 | |
| category_name | varchar(100) | 类别名称 | NOT NULL |
| enable_status | int(11) | 有效状态:1正常0无效 | DEFAULT 1 |
| company_id | bigint(20) | 所属企业ID | FK → sys_company.id |
| category_type | int(11) | 分类类型:1默认分类2普通分类 | |
| category_no | varchar(50) | 税目 | |
| goods_name | varchar(100) | 开票商品名称 | |
| tax_rate | decimal(5,4) | 开票税率 | |
| unit | varchar(20) | 单位 | |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

#### 4.5.2 商品管理表 (bus_goods)
**表名**: `bus_goods`
**实体类**: [`BusGoodsEntity`](../src/main/java/cc/buyhoo/tax/entity/BusGoodsEntity.java)
**描述**: 商品基本信息管理

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| goods_id | bigint(20) | 百货端商品ID | |
| shop_unique | bigint(20) | 供应商编码 | FK → bus_shop.shop_unique |
| goods_barcode | varchar(50) | 商品编码 | |
| goods_name | varchar(100) | 商品名称 | NOT NULL |
| goods_alias | varchar(100) | 商品别名 | |
| goods_in_price | decimal(10,2) | 商品进价 | |
| goods_sale_price | decimal(10,2) | 商品售价 | |
| goods_web_sale_price | decimal(10,2) | 商品线上售价 | |
| goods_life | int(11) | 商品保质天数 | |
| goods_standard | varchar(50) | 商品规格 | |
| goods_unit | varchar(20) | 商品计价单位 | |
| category_id | bigint(20) | 一级分类ID | FK → bus_goods_category.id |
| category_two_id | bigint(20) | 二级分类ID | FK → bus_goods_category.id |
| company_id | bigint(20) | 所属企业ID | FK → sys_company.id |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

### 4.6 订单管理模块

#### 4.6.1 销售订单表 (sale_list)
**表名**: `sale_list`
**实体类**: [`BusSaleListEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSaleListEntity.java)
**描述**: 销售订单主表，记录订单基本信息和支付状态

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 公司编号 | FK → sys_company.id |
| shop_unique | bigint(20) | 店铺编号 | FK → bus_shop.shop_unique |
| shop_name | varchar(100) | 店铺名称 | |
| sale_type | int(11) | 收银订单类型 | |
| sale_list_unique | varchar(50) | 订单编号 | UNIQUE |
| cus_unique | varchar(50) | 消费者唯一编号 | |
| sale_list_name | varchar(100) | 收货人姓名 | |
| sale_list_total | decimal(15,2) | 应收金额 | |
| sale_list_actually_received | decimal(15,2) | 实收金额 | |
| sale_list_service_fee | decimal(10,2) | 服务费 | |
| trade_no | varchar(100) | 交易流水号 | |
| pay_time | datetime | 支付时间 | |
| pay_fee | decimal(10,2) | 交易手续费 | |
| sale_list_datetime | datetime | 销售单日期 | |
| sale_list_state | int(11) | 收银付款状态 | |
| sale_list_payment | int(11) | 收银支付方式 | |
| settled_status | int(11) | 计算结算状态：0-待计算，1-已计算 | DEFAULT 0 |
| profit_status | int(11) | 计算成本状态：0-待计算，1-已计算 | DEFAULT 0 |
| profit_total | decimal(15,2) | 计算成本金额 | |
| contract_no | varchar(50) | 合同编码 | |
| order_type | varchar(10) | 订单类型：1-代收代付，2-联营订单 | |
| parent_list_unique | varchar(50) | 已拆解的订单的父订单编号 | |

#### 4.6.2 销售订单明细表 (sale_list_detail)
**表名**: `sale_list_detail`
**实体类**: [`BusSaleListDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSaleListDetailEntity.java)
**描述**: 销售订单商品明细

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| sale_list_detail_id | bigint(20) | 详情id | |
| sale_list_unique | varchar(50) | 销售单唯一标识 | FK → sale_list.sale_list_unique |
| goods_id | bigint(20) | 商品id | FK → bus_goods.goods_id |
| goods_barcode | varchar(50) | 商品条形码 | |
| goods_name | varchar(100) | 商品名称 | |
| sale_list_detail_count | decimal(10,2) | 商品数量 | |
| sale_list_detail_price | decimal(10,2) | 商品购买的价格 | |
| sale_list_detail_subtotal | decimal(15,2) | 金额小计 | |

#### 4.6.3 销售订单支付明细表 (sale_list_pay_detail)
**表名**: `sale_list_pay_detail`
**实体类**: [`BusSaleListPayDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSaleListPayDetailEntity.java)
**描述**: 销售订单支付方式明细

### 4.7 库存管理模块

#### 4.7.1 入库单表 (bus_inventory_order)
**表名**: `bus_inventory_order`
**实体类**: [`BusInventoryOrderEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInventoryOrderEntity.java)
**描述**: 入库单主表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 公司编号 | FK → sys_company.id |
| order_no | varchar(50) | 入库单号 | UNIQUE |
| third_trade_no | varchar(100) | 三方交易单号 | |
| total_money | decimal(15,2) | 入库金额 | |
| cost_money | decimal(15,2) | 成本金额 | |
| profit_money | decimal(15,2) | 利润金额 | |
| inventory_type | int(11) | 入库单类型:1收银订单同步2餐饮订单同步3手动录入 | |

#### 4.7.2 入库单批次表 (bus_inventory_batch)
**表名**: `bus_inventory_batch`
**实体类**: [`BusInventoryBatchEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInventoryBatchEntity.java)
**描述**: 入库单批次信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 公司编号 | FK → sys_company.id |
| shop_unique | bigint(20) | 店铺编号 | FK → bus_shop.shop_unique |
| batch_no | varchar(50) | 批次单号 | |
| total_money | decimal(15,2) | 总金额 | |
| cost_money | decimal(15,2) | 成本金额 | |
| profit_money | decimal(15,2) | 利润金额 | |
| order_id | bigint(20) | 入库单ID | FK → bus_inventory_order.id |
| batch_date | varchar(20) | 实际订单时间 | |
| inventory_type | int(11) | 入库单类型:1收银订单同步2餐饮订单同步3手动录入 | |

#### 4.7.3 入库单批次明细表 (bus_inventory_batch_detail)
**表名**: `bus_inventory_batch_detail`
**实体类**: [`BusInventoryBatchDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInventoryBatchDetailEntity.java)
**描述**: 入库单批次商品明细

### 4.8 发票管理模块

#### 4.8.1 商户发票表 (bus_shop_invoice)
**表名**: `bus_shop_invoice`
**实体类**: [`BusShopInvoiceEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopInvoiceEntity.java)
**描述**: 商户发票主表，记录发票基本信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 公司编号 | FK → sys_company.id |
| shop_unique | bigint(20) | 店铺编号 | FK → bus_shop.shop_unique |
| sale_list_unique | varchar(50) | 订单编号 | FK → sale_list.sale_list_unique |
| invoice_type | int(11) | 发票类型:1进项票2销项票 | |
| invoice_code | varchar(50) | 发票代码 | |
| machine_code | varchar(50) | 机器编码 | |
| invoice_number | varchar(50) | 发票号码 | |
| medium_type | int(11) | 发票介质：1、电子发票；2、纸质发票 | |
| invoice_kind | int(11) | 发票种类:1普通发票2专用发票 | |
| invoice_date | datetime | 开票日期 | |
| check_code | varchar(50) | 校验码 | |
| purchase_type | int(11) | 购买方类型：1、个人；2、企业 | |
| purchase_name | varchar(100) | 购买方名称 | |
| purchase_identity | varchar(50) | 购买方纳税人识别号 | |
| purchase_address | varchar(200) | 购买方地址 | |
| purchase_phone | varchar(20) | 购买方电话 | |
| purchase_bank | varchar(100) | 购买方开户行 | |
| purchase_bank_no | varchar(50) | 购买方开户行账号 | |
| sale_name | varchar(100) | 销售方名称 | |
| sale_identity | varchar(50) | 销售方纳税人识别号 | |
| sale_address | varchar(200) | 销售方地址 | |
| sale_phone | varchar(20) | 销售方电话 | |
| sale_bank | varchar(100) | 销售方开户行 | |
| sale_bank_no | varchar(50) | 销售方开户行账号 | |
| order_money | decimal(15,2) | 合计订单金额 | |
| tax_money | decimal(15,2) | 合计税额 | |
| order_tax_money | decimal(15,2) | 价税合计 | |
| payee | varchar(50) | 收款人 | |
| checker | varchar(50) | 复核人 | |
| invoice_man | varchar(50) | 开票人 | |
| status | int(11) | 是否开票：1已开票2未开票；3、开票中；4、开票失败 | |
| image_url | varchar(500) | 发票保存完整地址 | |
| bill_number | varchar(50) | 发票申请号 | |
| periods_level | int(11) | 期数等级 | |
| receive_msg | varchar(100) | 接收发票的邮箱或者手机号 | |
| notes | varchar(500) | 备注 | |
| purchase_person_flag | int(11) | 是否展示购方地址、电话信息：1-是；2-否 | |
| sale_person_flag | int(11) | 是否展示销方地址、电话信息：1-是；2-否 | |
| purchase_bank_flag | int(11) | 是否展示购方银行信息：1-是；2-否 | |
| sale_bank_flag | int(11) | 是否展示销方银行信息：1-是；2-否 | |
| apply_flag | int(11) | 是否申请开票：0-否1-是 | |

#### 4.8.2 商户发票明细表 (bus_shop_invoice_detail)
**表名**: `bus_shop_invoice_detail`
**实体类**: [`BusShopInvoiceDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopInvoiceDetailEntity.java)
**描述**: 商户发票商品明细

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| invoice_id | bigint(20) | 发票ID | FK → bus_shop_invoice.id |
| tax_classification_code | varchar(50) | 商品税收分类编码 | |
| goods_name | varchar(100) | 销售商品名称 | |
| goods_sm | varchar(50) | 商品税目 | |
| spec | varchar(50) | 商品规格型号 | |
| unit | varchar(20) | 商品计量单位 | |
| price | decimal(10,2) | 销售单价（含税） | |
| quantity | decimal(10,2) | 销售数量 | |
| amount | decimal(15,2) | 销售小计 | |
| tax_rate | decimal(5,4) | 税率 | |
| tax_amount | decimal(15,2) | 税额 | |
| tax_classification_name | varchar(100) | 商品税收分类名称 | |

#### 4.8.3 企业开票设置表 (bus_shop_invoice_setting)
**表名**: `bus_shop_invoice_setting`
**实体类**: [`BusShopInvoiceSettingEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopInvoiceSettingEntity.java)
**描述**: 企业开票信息配置

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 公司编号 | FK → sys_company.id |
| company_name | varchar(100) | 开票企业 | |
| company_tax_no | varchar(50) | 开票企业税号 | |
| company_address | varchar(200) | 开票企业地址 | |
| company_phone | varchar(20) | 开票企业电话 | |
| invoice_man | varchar(50) | 开票人 | |
| checker | varchar(50) | 复核人 | |
| payee | varchar(50) | 收款人 | |
| invoice_bank_name | varchar(100) | 开票企业银行 | |
| invoice_bank_card | varchar(50) | 开票企业银行账号 | |
| periods_level | int(11) | 期数 | |

### 4.9 财务管理模块

#### 4.9.1 商户账单表 (bus_shop_bill)
**表名**: `bus_shop_bill`
**实体类**: [`BusShopBillEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopBillEntity.java)
**描述**: 供货商账单汇总表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 公司编号 | FK → sys_company.id |
| shop_unique | bigint(20) | 商家唯一标识 | FK → bus_shop.shop_unique |
| settled_amount | decimal(15,2) | 已结算金额 | |
| unsettled_amount | decimal(15,2) | 未结算金额 | |
| settleding_amount | decimal(15,2) | 结算中金额 | |
| settled_type | varchar(10) | 结算方式，0-手动结算，1-自动结算 | |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

#### 4.9.2 商户账单明细表 (bus_shop_bill_detail)
**表名**: `bus_shop_bill_detail`
**实体类**: [`BusShopBillDetailEntity`](../src/main/java/cc/buyhoo/tax/entity/BusShopBillDetailEntity.java)
**描述**: 供货商账单明细表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| bill_id | bigint(20) | 供应商账单表ID | FK → bus_shop_bill.id |
| company_id | bigint(20) | 企业唯一ID | FK → sys_company.id |
| shop_unique | bigint(20) | 供货商唯一ID | FK → bus_shop.shop_unique |
| bill_no | varchar(50) | 打款单号 | |
| bank_name | varchar(100) | 银行名称 | |
| bank_card | varchar(50) | 银行卡号 | |
| settled_amount | decimal(15,2) | 结算金额 | |
| settled_type | varchar(10) | 结算方式，0-手动结算，1-自动结算 | |
| bill_type | smallint(2) | 转账方式：1、转账；2、代发工资 | DEFAULT 1 |

#### 4.9.3 税务申报记录表 (bus_tax_record)
**表名**: `bus_tax_record`
**实体类**: [`BusTaxRecordEntity`](../src/main/java/cc/buyhoo/tax/entity/BusTaxRecordEntity.java)
**描述**: 纳统申报记录

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| tax_type | varchar(10) | 纳统类型,1-按月,2-按季度,3-按年 | |
| target_amount | decimal(15,2) | 申报金额 | |
| tax_amount | decimal(15,2) | 营业金额 | |
| tax_date | varchar(20) | 申报日期 | |
| tax_status | varchar(10) | 申报状态：0-未完成，1-已完成 | |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

#### 4.9.4 交易记录表 (trade_record)
**表名**: `trade_record`
**实体类**: [`TradeRecordEntity`](../src/main/java/cc/buyhoo/tax/entity/TradeRecordEntity.java)
**描述**: 银行交易流水记录

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| company_bank_name | varchar(100) | 企业银行账户名称 | |
| company_bank_account | varchar(50) | 企业银行账户账号 | |
| trade_date | varchar(20) | 交易日期 | |
| trade_time | varchar(20) | 交易时间 | |
| trade_amount | decimal(15,2) | 交易金额 | |
| trade_way | int(11) | 交易类型：1、入账；2、出账 | |
| trade_abstract | varchar(200) | 交易摘要 | |
| trade_other_account | varchar(50) | 交易另一方账户 | |
| trade_other_name | varchar(100) | 交易另一方账户名称 | |
| company_balance | decimal(15,2) | 交易后账户余额 | |
| serial_number | varchar(100) | 银行交易流水号 | |
| trade_type | varchar(50) | 交易类型 | |
| credit_mark | int(11) | 交易借贷标记 | |
| business_num | varchar(100) | 业务参考号 | |
| bank_branch_id | varchar(50) | 交易另一方所属银行内部支行号 | |

### 3.10 数据同步模块

#### 3.10.1 订单同步记录表 (bus_sync_record)
**表名**: `bus_sync_record`
**实体类**: [`BusSyncRecordEntity`](../src/main/java/cc/buyhoo/tax/entity/BusSyncRecordEntity.java)
**描述**: 订单同步记录

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 企业ID | FK → sys_company.id |
| sync_type | int(11) | 同步类型，0-订单 | |
| start_time | datetime | 开始时间 | |
| end_time | datetime | 结束时间 | |
| sale_list_count | bigint(20) | 同步数量 | |
| status | int(11) | 同步状态，0-成功，1-失败 | |
| del_flag | int(11) | 删除标记:0未删除1已删除 | DEFAULT 0 |

#### 3.10.2 未同步订单数据表 (bus_inventory_not_sync)
**表名**: `bus_inventory_not_sync`
**实体类**: [`BusInventoryNotSyncEntity`](../src/main/java/cc/buyhoo/tax/entity/BusInventoryNotSyncEntity.java)
**描述**: 未同步的订单数据

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| company_id | bigint(20) | 公司编号 | FK → sys_company.id |
| sync_type | int(11) | 同步类型:1商品未同步2库存未同步 | |
| sale_list_unique | varchar(50) | 销售单唯一标识 | |
| sale_list_detail_id | bigint(20) | 百货详情id | |
| goods_id | bigint(20) | 商品编号 | |
| status | int(11) | 库存同步状态:1待同步2已同步 | |

### 3.11 辅助数据表

#### 3.11.1 中国行政地区表 (cnarea_2023)
**表名**: `cnarea_2023`
**实体类**: [`Cnarea2023Entity`](../src/main/java/cc/buyhoo/tax/entity/Cnarea2023Entity.java)
**描述**: 中国行政地区信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | PK, AUTO_INCREMENT |
| level | int(11) | 层级 | |
| parent_code | bigint(20) | 父级行政代码 | |
| area_code | bigint(20) | 行政代码 | |
| short_parent_code | bigint(20) | 父级行政代码简称 | |
| short_area_code | bigint(20) | 行政区简写 | |
| zip_code | varchar(10) | 邮政编码 | |
| city_code | varchar(10) | 区号 | |
| name | varchar(50) | 名称 | |
| short_name | varchar(50) | 简称 | |
| merger_name | varchar(200) | 组合名 | |
| pinyin | varchar(200) | 拼音 | |
| lng | decimal(10,6) | 经度 | |
| lat | decimal(10,6) | 纬度 | |

## 4. 核心业务流程图

```mermaid
flowchart TD
    A[企业注册] --> B[商户管理]
    B --> C[商品分类管理]
    C --> D[商品信息管理]
    D --> E[合同签署]
    E --> F[订单同步]
    F --> G[订单拆分处理]
    G --> H[库存管理]
    H --> I[发票开具]
    I --> J[税务申报]
    J --> K[财务结算]
    K --> L[银行转账]

    F --> M[退货处理]
    M --> H

    L --> N[账单管理]
    N --> O[利润分成]

    P[数据迁移] --> B
    Q[现金升级] --> E

    style A fill:#e1f5fe
    style J fill:#fff3e0
    style K fill:#f3e5f5
    style L fill:#e8f5e8
```

## 5. 关键业务关系说明

### 4.1 企业与商户关系
- 一个企业(sys_company)可以管理多个商户(bus_shop)
- 商户通过company_id关联到企业
- 企业可以配置多个银行账户(sys_company_bank_account)

### 4.2 商户与订单关系
- 一个商户可以有多个销售订单(sale_list)
- 订单通过shop_unique关联到商户
- 每个订单可以有多个商品明细(sale_list_detail)

### 4.3 商品与分类关系
- 商品(bus_goods)属于商品分类(bus_goods_category)
- 支持二级分类：category_id(一级分类)和category_two_id(二级分类)
- 商品分类支持树形结构，通过parent_id建立层级关系

### 4.4 发票与订单关系
- 发票(bus_shop_invoice)通过sale_list_unique关联到销售订单
- 一个订单可以开具一张发票
- 发票明细(bus_shop_invoice_detail)记录具体的商品税务信息

### 5. 财务结算关系
- 商户账单(bus_shop_bill)汇总商户的结算信息
- 账单明细(bus_shop_bill_detail)记录具体的转账记录
- 支持自动结算和手动结算两种方式

## 6. 索引建议

### 6.1 主要外键索引
```sql
-- 商户表
CREATE INDEX idx_bus_shop_company_id ON bus_shop(company_id);
CREATE INDEX idx_bus_shop_shop_unique ON bus_shop(shop_unique);

-- 销售订单表
CREATE INDEX idx_sale_list_company_id ON sale_list(company_id);
CREATE INDEX idx_sale_list_shop_unique ON sale_list(shop_unique);
CREATE INDEX idx_sale_list_unique ON sale_list(sale_list_unique);
CREATE INDEX idx_sale_list_datetime ON sale_list(sale_list_datetime);

-- 商品表
CREATE INDEX idx_bus_goods_company_id ON bus_goods(company_id);
CREATE INDEX idx_bus_goods_shop_unique ON bus_goods(shop_unique);
CREATE INDEX idx_bus_goods_category_id ON bus_goods(category_id);

-- 发票表
CREATE INDEX idx_bus_shop_invoice_company_id ON bus_shop_invoice(company_id);
CREATE INDEX idx_bus_shop_invoice_shop_unique ON bus_shop_invoice(shop_unique);
CREATE INDEX idx_bus_shop_invoice_sale_list_unique ON bus_shop_invoice(sale_list_unique);
```

### 6.2 业务查询索引
```sql
-- 按时间范围查询订单
CREATE INDEX idx_sale_list_date_status ON sale_list(sale_list_datetime, sale_list_state);

-- 按商户查询账单
CREATE INDEX idx_bus_shop_bill_shop_unique ON bus_shop_bill(shop_unique);

-- 按企业查询税务记录
CREATE INDEX idx_bus_tax_record_company_date ON bus_tax_record(company_id, tax_date);
```

## 7. 数据完整性约束

### 7.1 外键约束
系统中的主要外键关系应该通过数据库约束或应用层验证来保证数据完整性：

1. **企业关联约束**: 所有业务数据都应该关联到有效的企业
2. **商户关联约束**: 订单、发票、账单等都应该关联到有效的商户
3. **订单关联约束**: 订单明细、支付明细、发票等都应该关联到有效的订单

### 7.2 业务规则约束
1. **金额字段**: 所有金额字段应该大于等于0
2. **状态字段**: 状态字段应该在预定义的枚举值范围内
3. **删除标记**: 使用软删除，通过del_flag字段标记

## 8. 性能优化建议

### 8.1 分区策略
对于大数据量的表，建议按时间进行分区：
- sale_list: 按sale_list_datetime分区
- trade_record: 按trade_date分区
- bus_sync_record: 按create_time分区

### 8.2 归档策略
定期归档历史数据：
- 订单数据保留2年
- 交易记录保留5年
- 同步记录保留1年

## 9. 文档维护说明

### 9.1 更新频率
- 当数据库表结构发生变更时，应及时更新本文档
- 建议每季度检查一次文档的准确性和完整性

### 9.2 版本控制
- 文档版本号采用语义化版本控制
- 重大结构变更时升级主版本号
- 新增表或字段时升级次版本号
- 文档修正时升级修订版本号

### 9.3 相关文档
- [业务流程文档](./BIZ.md) - 详细的业务流程说明
- [IO业务流程文档](./IO.md) - 文件读写、存储、缓存等IO操作说明
- [API接口文档] - 系统API接口说明
- [部署文档] - 系统部署和配置说明

---

**文档版本**: 1.0
**最后更新**: 2024年12月
**维护人员**: 系统开发团队
