# 税务统计系统API接口文档

## 1. 概述

本文档描述了税务统计系统(yxl-tax-statistic)的所有REST API接口。系统基于Spring Boot框架构建，提供完整的税务管理、商户管理、订单处理、发票开具等功能。

### 1.1 技术架构
- **框架**: Spring Boot + MyBatis Plus + Dubbo
- **数据库**: MySQL
- **缓存**: Redis
- **消息队列**: RocketMQ
- **文件存储**: MinIO
- **权限管理**: Sa-Token
- **任务调度**: XXL-Job

### 1.2 接口规范
- **基础路径**: `/taxStatisticBack`
- **端口**: `14000`
- **响应格式**: 统一使用 `Result<T>` 包装
- **权限验证**: 基于Sa-Token的注解式权限控制
- **参数验证**: 使用 `@Validated` 注解进行参数校验

### 1.3 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

## 2. 认证授权模块

### 2.1 用户登录认证

#### 获取验证码
- **接口**: `GET /login/code`
- **功能**: 获取图形验证码
- **权限**: 无需认证
- **响应**: `CaptchaResult` - 包含验证码图片和key
- **代码**: [LoginController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/LoginController.java#L26-L29)

#### 用户登录
- **接口**: `POST /login/login`
- **功能**: 用户登录验证
- **权限**: 无需认证
- **参数**: `LoginParams` - 用户名、密码、验证码
- **响应**: `LoginResult` - 登录成功返回token和用户信息
- **代码**: [LoginController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/LoginController.java#L36-L39)

#### 退出登录
- **接口**: `POST /login/logout`
- **功能**: 用户退出登录
- **权限**: 需要认证
- **响应**: 无数据返回
- **代码**: [LoginController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/LoginController.java#L45-L48)

## 3. 系统管理模块

### 3.1 企业管理 (SysCompanyController)

#### 企业分页列表
- **接口**: `GET /sysCompany/pageList`
- **功能**: 获取纳统企业分页列表
- **权限**: `sysCompany:list`
- **参数**: `SysCompanyPageParams` - 分页查询参数
- **响应**: `SysCompanyPageResult` - 企业分页数据
- **代码**: [SysCompanyController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysCompanyController.java#L40-L44)

#### 新增企业
- **接口**: `POST /sysCompany/addCompany`
- **功能**: 新增纳统企业
- **权限**: `sysCompany:add`
- **参数**: `SysCompanyAddParams` - 企业信息
- **响应**: 无数据返回
- **代码**: [SysCompanyController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysCompanyController.java#L51-L55)

#### 企业详情
- **接口**: `GET /sysCompany/detail/{id}`
- **功能**: 获取企业详细信息
- **权限**: `sysCompany:detail` 或 `sysCompany:edit`
- **参数**: `id` - 企业ID
- **响应**: `SysCompanyDetailResult` - 企业详细信息
- **代码**: [SysCompanyController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysCompanyController.java)

#### 修改企业
- **接口**: `POST /sysCompany/updateCompany`
- **功能**: 修改企业信息
- **权限**: `sysCompany:edit`
- **参数**: `SysCompanyUpdateParams` - 企业更新信息
- **响应**: 无数据返回
- **代码**: [SysCompanyController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysCompanyController.java)

#### 删除企业
- **接口**: `POST /sysCompany/deleteCompany`
- **功能**: 删除企业
- **权限**: `sysCompany:delete`
- **参数**: `DeleteIdsParams` - 企业ID列表
- **响应**: 无数据返回
- **代码**: [SysCompanyController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysCompanyController.java)

#### 企业下拉选择
- **接口**: `GET /sysCompany/selectList`
- **功能**: 获取企业下拉选择列表
- **权限**: 无特殊权限要求
- **响应**: `List<SelectDataDto>` - 企业选择数据
- **代码**: [SysCompanyController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysCompanyController.java)

### 3.2 用户管理 (SysUserController)

#### 用户分页列表
- **接口**: `GET /sysUser/pageList`
- **功能**: 获取用户分页列表
- **权限**: `sysUser:list`
- **参数**: `UserPageParams` - 分页查询参数
- **响应**: `SysUserPageResult` - 用户分页数据
- **代码**: [SysUserController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysUserController.java#L33-L37)

#### 新增用户
- **接口**: `POST /sysUser/addUser`
- **功能**: 新增系统用户
- **权限**: `sysUser:add`
- **参数**: `SysUserAddParams` - 用户信息
- **响应**: 无数据返回
- **代码**: [SysUserController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysUserController.java#L44-L48)

### 3.3 角色管理 (SysRoleController)

#### 角色分页列表
- **接口**: `GET /sysRole/pageList`
- **功能**: 获取角色分页列表
- **权限**: `sysRole:list`
- **参数**: `SysRolePageParams` - 分页查询参数
- **响应**: `SysRolePageResult` - 角色分页数据
- **代码**: [SysRoleController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysRoleController.java#L29-L33)

#### 新增角色
- **接口**: `POST /sysRole/addRole`
- **功能**: 新增系统角色
- **权限**: `sysRole:add`
- **参数**: `SysRoleAddParams` - 角色信息
- **响应**: 无数据返回
- **代码**: [SysRoleController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysRoleController.java#L40-L44)

### 3.4 菜单管理 (SysMenuController)

#### 路由菜单列表
- **接口**: `GET /sysMenu/routerList`
- **功能**: 获取用户路由菜单
- **权限**: 需要认证
- **响应**: `RouterMenuListResult` - 路由菜单树
- **代码**: [SysMenuController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMenuController.java#L31-L34)

#### 菜单管理列表
- **接口**: `GET /sysMenu/menuList`
- **功能**: 获取菜单管理列表
- **权限**: `sysMenu:list`
- **参数**: `MenuListParams` - 查询参数
- **响应**: `MenuListResult` - 菜单列表
- **代码**: [SysMenuController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMenuController.java#L40-L44)

#### 角色菜单列表
- **接口**: `GET /sysMenu/roleMenuList`
- **功能**: 角色管理选择菜单
- **权限**: `sysRole:list`
- **响应**: `RoleMenuListResult` - 角色菜单列表
- **代码**: [SysMenuController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMenuController.java#L50-L54)

#### 新增菜单
- **接口**: `POST /sysMenu/addMenu`
- **功能**: 新增系统菜单
- **权限**: `sysMenu:list`
- **参数**: `SysMenuAddParams` - 菜单信息
- **响应**: 无数据返回
- **代码**: [SysMenuController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMenuController.java#L59-L63)

#### 修改菜单
- **接口**: `POST /sysMenu/updateMenu`
- **功能**: 修改系统菜单
- **权限**: `sysMenu:edit`
- **参数**: `UpdateMenuAddParams` - 菜单更新信息
- **响应**: 无数据返回
- **代码**: [SysMenuController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMenuController.java#L68-L72)

### 3.5 基础数据管理

#### 城市信息 (SysCityInfoController)
- **接口**: `GET /sysCityInfo/list/{level}/{pid}`
- **功能**: 查询城市列表
- **权限**: 无特殊权限要求
- **参数**: `level` - 级别, `pid` - 父级ID
- **响应**: `List<SysCityInoDto>` - 城市列表
- **代码**: [SysCityInfoController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysCityInfoController.java#L28-L31)

#### 行业管理 (SysIndustryController)
- **接口**: `GET /sysIndustry/pageList`
- **功能**: 分页查询行业列表
- **权限**: `sysIndustry:list`
- **参数**: `SysIndustryPageParams` - 分页参数
- **响应**: `SysIndustryPageResult` - 行业分页数据
- **代码**: [SysIndustryController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysIndustryController.java#L38-L42)

#### 市场管理 (SysMarketController)
- **接口**: `GET /sysMarket/pageList`
- **功能**: 分页查询市场列表
- **权限**: 相应权限要求
- **参数**: 分页查询参数
- **响应**: 市场分页数据
- **代码**: [SysMarketController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMarketController.java)

#### 银行管理 (SysBankListController)
- **接口**: 银行相关接口
- **功能**: 银行和支行信息管理
- **权限**: 相应权限要求
- **代码**: [SysBankListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysBankListController.java)

### 3.6 企业设置管理 (BusSettingController)

#### 获取企业配置
- **接口**: `GET /busSetting/getBusSettings`
- **功能**: 获取当前企业配置信息
- **权限**: 需要认证
- **响应**: `BusSettingDto` - 企业配置信息
- **代码**: [BusSettingController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusSettingController.java#L28-L31)

#### 保存企业设置
- **接口**: `POST /busSetting/saveBusSettings`
- **功能**: 保存企业配置设置
- **权限**: 需要认证
- **参数**: `SaveBusSettingParams` - 企业设置参数
- **响应**: 无数据返回
- **代码**: [BusSettingController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusSettingController.java#L37-L40)

### 3.7 迁移管理 (SysMigrateController)

#### 迁移分页列表
- **接口**: `GET /sysMigrate/pageList`
- **功能**: 分页查询迁入迁出列表
- **权限**: `sysMigrate:list`
- **参数**: `SysMigratePageParams` - 分页参数
- **响应**: `SysMigratePageResult` - 迁移分页数据
- **代码**: [SysMigrateController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMigrateController.java#L41-L45)

#### 迁移详情
- **接口**: `GET /sysMigrate/selectById/{id}`
- **功能**: 获取迁入迁出详情
- **权限**: `sysMigrate:detail`
- **参数**: `id` - 迁移记录ID
- **响应**: `SysMigrateDetailResult` - 迁移详情
- **代码**: [SysMigrateController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMigrateController.java#L52-L56)

#### 删除迁移记录
- **接口**: `POST /sysMigrate/deleteByIds`
- **功能**: 删除迁入迁出记录
- **权限**: `sysMigrate:delete`
- **参数**: `DeleteIdsParams` - ID列表
- **响应**: 删除结果
- **代码**: [SysMigrateController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMigrateController.java#L85-L89)

#### 导出迁移列表
- **接口**: `POST /sysMigrate/exportMigrate`
- **功能**: 导出迁移列表
- **权限**: 相应权限要求
- **参数**: `SysMigrateExportParams` - 导出参数
- **响应**: Excel文件流
- **代码**: [SysMigrateController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMigrateController.java#L98-L101)

#### 查询企业列表
- **接口**: `GET /sysMigrate/queryCompanyList`
- **功能**: 查询企业列表
- **权限**: 无特殊权限要求
- **响应**: `List<SysCompanyQueryDto>` - 企业列表
- **代码**: [SysMigrateController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/sys/SysMigrateController.java#L103-L106)

## 4. 业务核心模块

### 4.1 商户管理 (BusShopController)

#### 同步商家数据
- **接口**: `POST /busShop/syncShopData`
- **功能**: 同步商家数据
- **权限**: 无特殊权限要求
- **参数**: `BusShopEntity` - 商家实体信息
- **响应**: 无数据返回
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L42-L45)

#### 商家分页列表
- **接口**: `GET /busShop/list`
- **功能**: 获取商家分页列表
- **权限**: `busShop:list`
- **参数**: `ShopListParams` - 分页查询参数
- **响应**: `BusShopListResult` - 商家分页数据
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L50-L54)

#### 保存商户
- **接口**: `POST /busShop/saveShop`
- **功能**: 新增或修改商户信息
- **权限**: `busShop:save`
- **参数**: `ShopParams` - 商户信息
- **响应**: `BusShopListResult` - 保存结果
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L59-L63)

#### 删除商户
- **接口**: `POST /busShop/deleteShop`
- **功能**: 删除商户
- **权限**: `busShop:delete`
- **参数**: `DeleteIdsParams` - 商户ID列表
- **响应**: `BusShopListResult` - 删除结果
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L68-L72)

#### 商家下拉选择
- **接口**: `GET /busShop/selectList`
- **功能**: 获取商家下拉选择列表
- **权限**: 无特殊权限要求
- **参数**: `ShopListParams` - 查询参数
- **响应**: `List<BusShopSelectDto>` - 商家选择数据
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L77-L80)

#### 导出商户列表
- **接口**: `POST /busShop/exportShop`
- **功能**: 导出商户列表
- **权限**: `busShop:export`
- **参数**: `ShopListExportParams` - 导出参数
- **响应**: Excel文件流
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L107-L111)

#### 迁入企业下拉框
- **接口**: `GET /busShop/queryMigrateInCompanyList`
- **功能**: 获取迁入企业下拉框列表
- **权限**: 无特殊权限要求
- **参数**: `BusShopMigrateInCompanyListQueryParams` - 查询参数
- **响应**: `BusShopMigrateInCompanyListQueryResult` - 迁入企业列表
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L116-L119)

#### 获取银行信息
- **接口**: `GET /busShop/getBankInfo`
- **功能**: 获取银行相关信息
- **权限**: 无特殊权限要求
- **参数**: `GetBankInfoParams` - 银行信息查询参数
- **响应**: `GetBankInfoResult` - 银行信息
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L85-L88)

#### 批量修改结算费率
- **接口**: `POST /busShop/saveServiceRateBatch`
- **功能**: 批量修改商户结算费率
- **权限**: `busShop:updateServiceRate`
- **参数**: `ShopServiceRateParams` - 费率参数
- **响应**: `BusShopListResult` - 修改结果
- **代码**: [BusShopController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopController.java#L94-L98)

### 4.1.1 商户银行卡管理 (BusShopBankController)

#### 银行卡分页列表
- **接口**: `GET /shopBank/pageList`
- **功能**: 获取供货商银行卡分页列表
- **权限**: 无特殊权限要求
- **参数**: `ShopBankListParam` - 分页查询参数
- **响应**: `List<BusShopBankDto>` - 银行卡列表
- **代码**: [BusShopBankController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBankController.java#L35-L38)

#### 新增银行卡
- **接口**: `POST /shopBank/addShopBank`
- **功能**: 新增供货商银行卡
- **权限**: 无特殊权限要求
- **参数**: `BusShopBankAddParams` - 银行卡信息
- **响应**: 新增结果
- **代码**: [BusShopBankController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBankController.java#L45-L48)

#### 修改银行卡
- **接口**: `POST /shopBank/updateShopBank`
- **功能**: 修改供货商银行卡信息
- **权限**: 无特殊权限要求
- **参数**: `BusShopBankUpdateParams` - 银行卡更新信息
- **响应**: 修改结果
- **代码**: [BusShopBankController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBankController.java#L55-L58)

#### 删除银行卡
- **接口**: `POST /shopBank/deleteById`
- **功能**: 删除供货商银行卡
- **权限**: 无特殊权限要求
- **参数**: `BusShopBankIdParams` - 银行卡ID
- **响应**: 删除结果
- **代码**: [BusShopBankController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBankController.java#L65-L68)

#### 银行卡详情
- **接口**: `GET /shopBank/selectById/{id}`
- **功能**: 查看银行卡详情
- **权限**: 无特殊权限要求
- **参数**: `id` - 银行卡ID
- **响应**: `BusShopBankDto` - 银行卡详情
- **代码**: [BusShopBankController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBankController.java#L75-L78)

### 4.1.2 商户服务费管理 (BusShopServiceFeeController)

#### 服务费列表
- **接口**: `GET /busShopServiceFee/list`
- **功能**: 获取商户服务费列表
- **权限**: `busShopServiceFee:list`
- **参数**: `ShopServiceFeeListParams` - 查询参数
- **响应**: `ShopServiceFeeListResult` - 服务费列表
- **代码**: [BusShopServiceFeeController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopServiceFeeController.java#L26-L30)

### 4.1.3 商户服务费统计 (BusShopCoverChargeController)

#### 服务费统计列表
- **接口**: `GET /busShopCoverCharge/list`
- **功能**: 获取服务费统计列表
- **权限**: `busShopCoverCharge:list`
- **参数**: `ShopCoverChargeListParams` - 查询参数
- **响应**: `ShopCoverChargeListResult` - 服务费统计列表
- **代码**: [BusShopCoverChargeController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopCoverChargeController.java#L29-L33)

### 4.2 订单管理 (SaleListController)

#### 获取拆单状态列表
- **接口**: `GET /saleList/getDisassembleStatus`
- **功能**: 获取拆单状态枚举列表
- **权限**: 无特殊权限要求
- **响应**: `List<DisassembleStatusDto>` - 拆单状态列表
- **代码**: [SaleListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/SaleListController.java#L35-L40)

#### 拆解订单
- **接口**: `POST /saleList/disassembleSaleList`
- **功能**: 手动拆解销售订单
- **权限**: `saleList:list`
- **参数**: `DisassembleSaleListParams` - 拆单参数
- **响应**: 无数据返回
- **代码**: [SaleListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/SaleListController.java#L47-L51)

#### 自动拆解订单
- **接口**: `POST /saleList/autoDisassembleSaleList`
- **功能**: 自动执行订单拆解
- **权限**: 无特殊权限要求
- **参数**: `AutoDisassembleSaleListParams` - 自动拆单参数
- **响应**: 无数据返回
- **代码**: [SaleListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/SaleListController.java#L58-L61)

#### 订单分页列表
- **接口**: `GET /saleList/saleList`
- **功能**: 获取销售订单分页列表
- **权限**: `saleList:list`
- **参数**: `SaleListParams` - 分页查询参数
- **响应**: `SaleListResult` - 订单分页数据
- **代码**: [SaleListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/SaleListController.java#L68-L72)

#### 订单详情
- **接口**: `GET /saleList/saleListDetail`
- **功能**: 获取订单详细信息
- **权限**: `saleList:list`
- **参数**: `SaleListDetailParams` - 订单详情参数
- **响应**: `SaleListDetailResult` - 订单详情
- **代码**: [SaleListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/SaleListController.java#L79-L83)

### 4.3 库存管理 (BusInventoryOrderController)

#### 入库分页列表
- **接口**: `GET /inventoryOrder/pageList`
- **功能**: 获取入库单分页列表
- **权限**: `busInventoryOrder:list`
- **参数**: `InventoryOrderParams` - 分页查询参数
- **响应**: `InventoryOrderListResult` - 入库单分页数据
- **代码**: [BusInventoryOrderController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusInventoryOrderController.java#L33-L37)

### 4.4 商品管理 (BusGoodsController)

#### 商品分页列表
- **接口**: `GET /busGoods/pageList`
- **功能**: 获取商品分页列表
- **权限**: `busGoods:list`
- **参数**: `GoodsListParams` - 分页查询参数
- **响应**: `BusGoodsListResult` - 商品分页数据
- **代码**: [BusGoodsController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusGoodsController.java#L35-L39)

#### 商品绑定分类
- **接口**: `POST /busGoods/updateGoodsCategory`
- **功能**: 更新商品分类绑定
- **权限**: `busGoods:list`
- **参数**: `GoodsCategoryBindParams` - 分类绑定参数
- **响应**: 无数据返回
- **代码**: [BusGoodsController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusGoodsController.java#L46-L50)

### 4.5 合同管理 (BusContractController)

#### 合同分页列表
- **接口**: `GET /busContract/pageList`
- **功能**: 获取合同分页列表
- **权限**: `busContract:list`
- **参数**: `BusContractListParams` - 分页查询参数
- **响应**: `BusContractListResult` - 合同分页数据
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L35-L39)

#### 新增合同
- **接口**: `POST /busContract/addContract`
- **功能**: 新增合同
- **权限**: `busContract:add`
- **参数**: `BusContractAddParams` - 合同信息
- **响应**: 无数据返回
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L47-L51)

#### 修改合同
- **接口**: `POST /busContract/updateContract`
- **功能**: 修改合同信息
- **权限**: `busContract:update`
- **参数**: `BusContractUpdateParams` - 合同更新信息
- **响应**: 无数据返回
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L59-L63)

#### 删除合同
- **接口**: `POST /busContract/deleteContract`
- **功能**: 删除合同
- **权限**: `busContract:delete`
- **参数**: `DeleteIdsParams` - 合同ID列表
- **响应**: 无数据返回
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L71-L75)

#### 生成合同编码与名称
- **接口**: `GET /busContract/selectContractNoAndName`
- **功能**: 生成合同编码与名称
- **权限**: 无特殊权限要求
- **参数**: `BusContractNoParams` - 合同编码参数
- **响应**: `BusContractNoDto` - 合同编码和名称
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L83-L86)

#### 校验订单号
- **接口**: `POST /busContract/checkOrderNo`
- **功能**: 校验订单号是否有效
- **权限**: 无特殊权限要求
- **参数**: `BusContractCheckParams` - 校验参数
- **响应**: `BigDecimal` - 校验结果
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L94-L97)

#### 查询合同详情
- **接口**: `GET /busContract/selectByContractNo/{contractNo}`
- **功能**: 通过合同编码查询合同详情
- **权限**: 无特殊权限要求
- **参数**: `contractNo` - 合同编码
- **响应**: `BusContractDto` - 合同详情
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L104-L107)

#### 导出合同列表
- **接口**: `POST /busContract/export`
- **功能**: 导出合同列表
- **权限**: `busContract:export`
- **参数**: `BusContractListParams` - 导出参数
- **响应**: Excel文件流
- **代码**: [BusContractController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusContractController.java#L116-L120)

### 4.5.1 发票设置管理 (BusShopInvoiceSettingController)

#### 获取开票周期列表
- **接口**: `GET /busShopInvoiceSetting/getInvoicePeriods`
- **功能**: 获取开票周期枚举列表
- **权限**: 无特殊权限要求
- **响应**: `InvoicePeriodsResult` - 开票周期列表
- **代码**: [BusShopInvoiceSettingController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceSettingController.java)

#### 获取开票设置
- **接口**: `GET /busShopInvoiceSetting/getInvoiceSetting`
- **功能**: 获取当前开票设置
- **权限**: 无特殊权限要求
- **响应**: `GetInvoiceSettingResult` - 开票设置信息
- **代码**: [BusShopInvoiceSettingController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceSettingController.java#L54-L57)

#### 保存开票设置
- **接口**: `POST /busShopInvoiceSetting/saveInvoiceSetting`
- **功能**: 保存开票设置
- **权限**: `shopSettleSetting:update`
- **参数**: `BusShopInvoiceSettingEntity` - 开票设置信息
- **响应**: 无数据返回
- **代码**: [BusShopInvoiceSettingController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceSettingController.java#L64-L68)

### 4.6 发票管理 (BusShopInvoiceController)

#### 发票分页列表
- **接口**: `GET /busShopInvoice/pageList`
- **功能**: 获取发票分页列表
- **权限**: `busShopInvoice:list`
- **参数**: `ShopInvoiceListParams` - 分页查询参数
- **响应**: `ShopInvoiceListResult` - 发票分页数据
- **代码**: [BusShopInvoiceController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java)

#### 发票详情
- **接口**: `GET /busShopInvoice/detail/{id}`
- **功能**: 获取发票详细信息
- **权限**: `busShopInvoice:detail`
- **参数**: `id` - 发票ID
- **响应**: `ShopInvoiceDetailResult` - 发票详情
- **代码**: [BusShopInvoiceController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java)

#### 申请开票
- **接口**: `POST /busShopInvoice/applyInvoice`
- **功能**: 申请开具发票
- **权限**: `busShopInvoice:apply`
- **参数**: `InvoiceApplyParams` - 开票申请参数
- **响应**: 无数据返回
- **代码**: [BusShopInvoiceController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java)

#### 手动开票
- **接口**: `POST /busShopInvoice/manualInvoiceApply`
- **功能**: 手动申请开票
- **权限**: `busShopInvoice:manual`
- **参数**: `ManualInvoiceApplyParams` - 手动开票参数
- **响应**: 无数据返回
- **代码**: [BusShopInvoiceController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java)

#### 批量手动开票
- **接口**: `POST /busShopInvoice/manualBatchInvoiceApply`
- **功能**: 批量手动申请开票
- **权限**: `busShopInvoice:batchManual`
- **参数**: `ManualBatchInvoiceApplyParams` - 批量开票参数
- **响应**: 无数据返回
- **代码**: [BusShopInvoiceController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java)

#### 查询开票结果
- **接口**: `POST /busShopInvoice/queryInvoiceResult`
- **功能**: 查询发票开具结果
- **权限**: `busShopInvoice:query`
- **参数**: `InvoiceResultQueryParams` - 查询参数
- **响应**: 开票结果信息
- **代码**: [BusShopInvoiceController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java)

#### 获取发票状态列表
- **接口**: `GET /busShopInvoice/getInvoiceStatusList`
- **功能**: 获取发票状态枚举列表
- **权限**: 无特殊权限要求
- **响应**: `GetInvoiceStatusListResult` - 发票状态列表
- **代码**: [BusShopInvoiceController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopInvoiceController.java)

### 4.7 税务申报 (BusTaxRecordController)

#### 申报记录分页列表
- **接口**: `GET /busTaxRecord/pageList`
- **功能**: 获取税务申报记录分页列表
- **权限**: `busTaxRecord:list`
- **参数**: `BusTaxRecordParams` - 分页查询参数
- **响应**: `BusTaxRecordResult` - 申报记录分页数据
- **代码**: [BusTaxRecordController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusTaxRecordController.java#L32-L36)

#### 保存申报记录
- **接口**: `POST /busTaxRecord/save`
- **功能**: 新增或更新申报记录
- **权限**: `busTaxRecord:update`
- **参数**: `BusTaxRecordParams` - 申报记录信息
- **响应**: 无数据返回
- **代码**: [BusTaxRecordController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusTaxRecordController.java#L43-L47)

#### 删除申报记录
- **接口**: `POST /busTaxRecord/delete`
- **功能**: 删除税务申报记录
- **权限**: `busTaxRecord:delete`
- **参数**: `DeleteIdsParams` - 申报记录ID列表
- **响应**: 无数据返回
- **代码**: [BusTaxRecordController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusTaxRecordController.java#L54-L58)

#### 完成申报
- **接口**: `POST /busTaxRecord/success`
- **功能**: 标记申报完成
- **权限**: `busTaxRecord:update`
- **参数**: `BusTaxRecordParams` - 申报记录参数
- **响应**: 无数据返回
- **代码**: [BusTaxRecordController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusTaxRecordController.java#L65-L69)

### 4.8 账单管理 (BusShopBillController)

#### 账单分页列表
- **接口**: `GET /shopBill/pageList`
- **功能**: 获取供应商账单分页列表
- **权限**: `busShopBill:list`
- **参数**: `BusShopBillParams` - 分页查询参数
- **响应**: `BusShopBillPageResult` - 账单分页数据
- **代码**: [BusShopBillController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBillController.java#L38-L42)

#### 导出账单
- **接口**: `POST /shopBill/export`
- **功能**: 导出企业未结算账单
- **权限**: `busShopBill:export`
- **参数**: `BusShopBillParams` - 导出参数
- **响应**: Excel文件流
- **代码**: [BusShopBillController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBillController.java#L50-L54)

#### 导入结算明细
- **接口**: `POST /shopBill/import`
- **功能**: 导入企业结算明细
- **权限**: `busShopBill:import`
- **参数**: `MultipartFile` - Excel文件
- **响应**: 无数据返回
- **代码**: [BusShopBillController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBillController.java#L60-L64)

#### 一键打款
- **接口**: `POST /shopBill/transferAccounts`
- **功能**: 一键批量转账打款
- **权限**: `busShopBill:transferAccounts`
- **响应**: 无数据返回
- **代码**: [BusShopBillController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBillController.java#L70-L74)

#### 手动转账
- **接口**: `POST /shopBill/manualTransferAccounts`
- **功能**: 手动指定转账
- **权限**: `busShopBill:transferAccounts`
- **参数**: `ManualTransferAccountsParams` - 手动转账参数
- **响应**: 无数据返回
- **代码**: [BusShopBillController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusShopBillController.java#L80-L84)

## 5. 银行支付模块

### 5.1 招商银行接口 (CmbController)

#### 创建新订单
- **接口**: `POST /cmb/createNewOrder`
- **功能**: 创建招商银行新订单
- **权限**: 相应权限要求
- **参数**: `CreateNewOrderParams` - 订单创建参数
- **响应**: 订单创建结果
- **代码**: [CmbController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CmbController.java)

#### 创建新库存
- **接口**: `POST /cmb/createNewInventory`
- **功能**: 创建招商银行新库存记录
- **权限**: 相应权限要求
- **参数**: `CreateNewInventoryParams` - 库存创建参数
- **响应**: 库存创建结果
- **代码**: [CmbController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CmbController.java)

#### 交易记录查询
- **接口**: `GET /cmb/tradeRecordList`
- **功能**: 查询银行交易记录
- **权限**: 相应权限要求
- **参数**: `TradeRecordListQueryParams` - 查询参数
- **响应**: 交易记录列表
- **代码**: [CmbController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CmbController.java)

#### 导出交易记录
- **接口**: `POST /cmb/exportTradeRecord`
- **功能**: 导出银行交易记录
- **权限**: 相应权限要求
- **参数**: `TradeRecordListExportParams` - 导出参数
- **响应**: Excel文件流
- **代码**: [CmbController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CmbController.java)

## 6. 外部集成接口

### 6.1 外部系统接口 (ExternalController)

#### 获取开票企业信息
- **接口**: `POST /external/getInvoiceMsg`
- **功能**: 根据开票店铺信息获取对应的纳统企业开票信息
- **权限**: 无特殊权限要求
- **参数**: `GetInvoiceMsgParmas` - 开票信息查询参数
- **响应**: `GetInvoiceMsgResult` - 开票企业信息和开票内容
- **代码**: [ExternalController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/ExternalController.java#L36-L39)

#### 存储开票结果
- **接口**: `POST /external/saveInvoiceMsg`
- **功能**: 存储外部系统开票结果
- **权限**: 无特殊权限要求
- **参数**: `SaveInvoiceMsgParams` - 开票结果信息
- **响应**: 无数据返回
- **代码**: [ExternalController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/ExternalController.java#L46-L49)

#### 申请开票(公共接口)
- **接口**: `POST /external/saveInvoiceMsgPub`
- **功能**: 将开票功能存放到纳统平台，开票完成后回调到业务系统
- **权限**: 无特殊权限要求
- **参数**: `SaveInvoiceMsgPublicParams` - 公共开票申请参数
- **响应**: 无数据返回
- **代码**: [ExternalController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/ExternalController.java)

#### 保存商户发票
- **接口**: `POST /external/saveShopInvoice`
- **功能**: 保存商户发票信息
- **权限**: 无特殊权限要求
- **参数**: `ShopInvoiceAddParams` - 商户发票参数
- **响应**: `NotifyInvoiceResultParams` - 发票通知结果
- **代码**: [ExternalController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/ExternalController.java)

## 7. 工具类接口

### 7.1 通用功能 (CommonController)

#### 文件上传
- **接口**: `POST /common/upload`
- **功能**: 通用文件上传接口
- **权限**: 需要认证
- **参数**: `MultipartFile` - 上传文件
- **响应**: 文件访问URL
- **代码**: [CommonController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CommonController.java)

#### 获取字典数据
- **接口**: `GET /common/dict/{dictType}`
- **功能**: 获取字典数据
- **权限**: 无特殊权限要求
- **参数**: `dictType` - 字典类型
- **响应**: 字典数据列表
- **代码**: [CommonController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CommonController.java)

### 7.2 首页统计 (IndexController)

#### 首页数据统计
- **接口**: `GET /index/statistics`
- **功能**: 获取首页统计数据
- **权限**: 需要认证
- **响应**: 首页统计信息
- **代码**: [IndexController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/IndexController.java)

## 8. 现金升级模块

### 8.1 现金升级管理 (CashUpgradeController)

#### 升级列表
- **接口**: `GET /cashUpgrade/pageList`
- **功能**: 获取现金升级分页列表
- **权限**: 相应权限要求
- **参数**: 分页查询参数
- **响应**: 升级记录分页数据
- **代码**: [CashUpgradeController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CashUpgradeController.java)

#### 查询商户列表
- **接口**: `GET /cashUpgrade/queryShops`
- **功能**: 查询可升级的商户列表
- **权限**: 相应权限要求
- **响应**: `QueryShopsResult` - 商户列表
- **代码**: [CashUpgradeController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CashUpgradeController.java)

#### 获取最大版本
- **接口**: `GET /cashUpgrade/getMaxVersion`
- **功能**: 获取当前最大版本号
- **权限**: 相应权限要求
- **响应**: `GetMaxVersionResult` - 最大版本信息
- **代码**: [CashUpgradeController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CashUpgradeController.java)

#### 查询升级商户
- **接口**: `GET /cashUpgrade/queryUpgradeShop`
- **功能**: 查询升级商户信息
- **权限**: 相应权限要求
- **响应**: `QueryUpgradeShopResult` - 升级商户信息
- **代码**: [CashUpgradeController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/CashUpgradeController.java)

## 9. 其他业务模块

### 9.1 退货管理 (ReturnListController)

#### 退货订单列表
- **接口**: `GET /returnList/pageList`
- **功能**: 获取退货订单分页列表
- **权限**: 相应权限要求
- **参数**: 分页查询参数
- **响应**: 退货订单分页数据
- **代码**: [ReturnListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/ReturnListController.java)

### 9.2 拆单管理 (DisassembleListController)

#### 拆单记录列表
- **接口**: `GET /disassembleList/pageList`
- **功能**: 获取拆单记录分页列表
- **权限**: 相应权限要求
- **参数**: 分页查询参数
- **响应**: 拆单记录分页数据
- **代码**: [DisassembleListController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/DisassembleListController.java)

### 9.3 库存批次管理 (BusInventoryBatchController)

#### 库存批次列表
- **接口**: `GET /inventoryBatch/pageList`
- **功能**: 获取库存批次分页列表
- **权限**: `inventoryBatch:list`
- **参数**: 分页查询参数
- **响应**: 库存批次分页数据
- **代码**: [BusInventoryBatchController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusInventoryBatchController.java)

#### 添加入库批次
- **接口**: `POST /inventoryBatch/save`
- **功能**: 添加入库批次
- **权限**: `inventoryBatch:add`
- **参数**: `InventoryBatchAddParams` - 批次信息
- **响应**: 无数据返回
- **代码**: [BusInventoryBatchController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusInventoryBatchController.java#L47-L51)

#### 获取订单批次信息
- **接口**: `GET /inventoryBatch/getShopUniqueAndBatchIdByOrderId/{orderId}`
- **功能**: 获取订单对应店铺编码、批次ID
- **权限**: 无特殊权限要求
- **参数**: `orderId` - 订单ID
- **响应**: `List<BusInventoryBatchEntity>` - 批次信息列表
- **代码**: [BusInventoryBatchController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusInventoryBatchController.java#L56-L59)

#### 删除入库批次
- **接口**: `POST /inventoryBatch/delete`
- **功能**: 删除入库批次
- **权限**: 无特殊权限要求
- **参数**: `DeleteIdsParams` - 批次ID列表
- **响应**: 无数据返回
- **代码**: [BusInventoryBatchController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusInventoryBatchController.java#L64-L67)

### 9.4 商品分类管理 (BusGoodsCategoryController)

#### 商品分类相关接口
- **功能**: 商品分类的增删改查操作
- **权限**: 相应权限要求
- **代码**: [BusGoodsCategoryController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/BusGoodsCategoryController.java)

### 9.5 用户区域管理 (UserAreaController)

#### 用户区域相关接口
- **功能**: 用户区域权限管理
- **权限**: 相应权限要求
- **代码**: [UserAreaController.java](yxl-tax-statistic/src/main/java/cc/buyhoo/tax/controller/UserAreaController.java)

## 10. 接口总结

### 10.1 接口统计

| 模块分类 | 控制器数量 | 主要功能 |
|----------|------------|----------|
| 认证授权 | 1 | 用户登录、退出、验证码 |
| 系统管理 | 9 | 企业、用户、角色、菜单、基础数据管理 |
| 业务核心 | 8 | 商户、订单、库存、商品、合同、发票、税务、账单 |
| 银行支付 | 1 | 招商银行接口集成 |
| 外部集成 | 1 | 第三方系统接口 |
| 工具类 | 2 | 通用功能、首页统计 |
| 现金升级 | 3 | 现金升级管理 |
| 其他业务 | 5 | 退货、拆单、库存批次等 |
| **总计** | **30+** | **完整的税务统计管理系统** |

### 10.2 权限控制说明

系统采用Sa-Token进行权限控制，主要权限类型包括：

#### 系统管理权限
- `sysCompany:*` - 企业管理权限
- `sysUser:*` - 用户管理权限
- `sysRole:*` - 角色管理权限
- `sysMenu:*` - 菜单管理权限
- `sysIndustry:*` - 行业管理权限
- `sysMigrate:*` - 迁移管理权限

#### 业务模块权限
- `busShop:*` - 商户管理权限
- `saleList:*` - 订单管理权限
- `busGoods:*` - 商品管理权限
- `busContract:*` - 合同管理权限
- `busShopInvoice:*` - 发票管理权限
- `busTaxRecord:*` - 税务申报权限
- `busShopBill:*` - 账单管理权限
- `busInventoryOrder:*` - 库存管理权限
- `inventoryBatch:*` - 库存批次权限

### 10.3 接口调用注意事项

#### 认证要求
1. 除登录接口外，所有接口都需要在请求头中携带token
2. Token格式：`Authorization: Bearer {token}`
3. Token过期时间根据系统配置，需要及时刷新

#### 参数验证
1. 所有接口参数都使用`@Validated`注解进行校验
2. 必填参数不能为空，格式必须正确
3. 分页参数：`pageNum`(页码)、`pageSize`(每页大小)

#### 响应格式
1. 成功响应：`code=200`，`message="success"`
2. 业务异常：`code!=200`，`message`包含错误信息
3. 分页数据包含：`total`(总数)、`list`(数据列表)

#### 文件操作
1. 文件上传支持常见格式：jpg、png、pdf、xlsx等
2. 文件下载接口返回文件流，需要设置正确的Content-Type
3. 导出功能支持Excel格式，文件名包含时间戳

#### 数据权限
1. 普通用户只能查看所属企业的数据
2. 管理员可以查看所有企业数据
3. 部分接口支持企业ID参数进行数据过滤

### 10.4 集成建议

#### 前端集成
1. 建议使用axios等HTTP客户端库
2. 统一封装请求拦截器处理token和错误
3. 分页组件可复用，统一处理分页参数

#### 第三方系统集成
1. 外部系统调用需要申请API密钥
2. 建议使用HTTPS协议保证数据安全
3. 重要接口建议添加签名验证

#### 性能优化
1. 列表查询建议使用分页，避免一次性加载大量数据
2. 频繁查询的数据可以考虑缓存
3. 大文件上传建议使用分片上传

### 10.5 相关文档链接

- [业务流程文档](BIZ.md) - 详细的业务流程说明
- [数据库设计文档](ER.md) - 数据库表结构和关系
- [文件IO文档](IO.md) - 文件操作和存储说明
- [消息队列文档](MQ.md) - 消息队列使用说明
- [定时任务文档](JOB.md) - 定时任务配置说明

---

**文档版本**: v1.0
**最后更新**: 2024年12月
**维护人员**: 系统开发团队
