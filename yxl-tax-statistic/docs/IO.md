# 税务统计系统IO业务流程文档

## 1. 概述

本文档描述了税务统计系统(yxl-tax-statistic)中所有文件读取、写入、存储相关的业务逻辑和IO操作流程。系统采用多种存储方式和外部服务集成，实现了完整的数据流转和文件管理功能。

### 1.1 技术架构
- **数据库**: MySQL (MyBatis Plus ORM)
- **缓存**: Redis
- **文件存储**: MinIO对象存储
- **文件传输**: SFTP
- **消息队列**: RocketMQ
- **外部API**: 百度OCR、发票API、银行接口
- **短信服务**: 阿里云短信
- **任务调度**: XXL-Job

### 1.2 IO操作分类
- 数据库读写操作
- 文件上传下载
- 缓存读写
- 外部API调用
- 消息队列
- 日志记录
- 配置文件读取

## 2. 数据库IO操作

### 2.1 数据库配置

<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/YxlTaxStatisticApplication.java" mode="EXCERPT">
````java
@SpringBootApplication
@MapperScan("cc.buyhoo.tax.dao")
@EnableDiscoveryClient
@EnableDubbo
public class YxlTaxStatisticApplication {
    // 应用启动类，配置MyBatis扫描
}
````
</augment_code_snippet>

### 2.2 数据持久化业务流程

#### 2.2.1 用户登录数据读取
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/LoginServiceImpl.java" mode="EXCERPT">
````java
@Override
@Transactional(rollbackFor = Exception.class)
public Result<LoginResult> login(LoginParams params) {
    // 查询用户
    LambdaQueryWrapper<SysUserEntity> userWrapper = new LambdaQueryWrapper<>();
    userWrapper.eq(SysUserEntity::getUsername, params.getUsername());
    SysUserEntity sysUserEntity = sysUserMapper.selectOne(userWrapper);
    // 用户验证和登录逻辑
}
````
</augment_code_snippet>

#### 2.2.2 商户数据同步写入
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/BusShopServiceImpl.java" mode="EXCERPT">
````java
@Service
public class BusShopServiceImpl extends BaseService implements BusShopService {
    // 商户数据的增删改查操作
    // 包含复杂的业务逻辑和数据关联
}
````
</augment_code_snippet>

### 2.3 事务管理
系统使用Spring事务管理，确保数据一致性：
- `@Transactional(rollbackFor = Exception.class)` 注解
- 支持分布式事务
- 数据库连接池管理

## 3. 文件存储IO操作

### 3.1 文件上传业务流程

#### 3.1.1 通用文件上传
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/UploadServiceImpl.java" mode="EXCERPT">
````java
@Override
public Result<UploadFileDto> uploadFile(HttpServletRequest request) {
    //获取图片
    MultipartFile mfile = FileUtils.getFile(request, "file");
    
    //图片上传
    File file = FileUtils.transferToFile(mfile);
    MinioUploadResult minioUploadResult = minioUploadHelper.uploadImg(file);
    UploadFileDto dto = new UploadFileDto();
    dto.setName(mfile.getName());
    dto.setUrl(minioUploadResult.getUrl());
    return Result.ok(dto);
}
````
</augment_code_snippet>

#### 3.1.2 发票文件处理
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileTask.java" mode="EXCERPT">
````java
// 发票文件下载和存储
String fileContent = d.getFileContent();
File file = InvoiceUtil.base64ToFile(fileContent, faPiaoPath + invoiceEntity.getSaleListUnique() + "." + d.getWjlx().toLowerCase());

//转换图片格式
InvoiceUtil.pdfToImage(file.getAbsolutePath(), faPiaoPath);

MinioUploadResult minioUploadResult = minioUploadHelper.uploadImg(file);
invoiceEntity.setImageUrl(minioUploadResult.getUrl());
````
</augment_code_snippet>

### 3.2 SFTP文件传输

#### 3.2.1 SFTP配置
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/config/FTP/FTPConfig.java" mode="EXCERPT">
````java
@Data
@Configuration
@ConfigurationProperties(prefix = "ftp")
public class FTPConfig {
    public static String host;
    public static int port;
    public static String username;
    public static String password;
    public static String base_path;
    public static String fapiao_path;
}
````
</augment_code_snippet>

#### 3.2.2 SFTP文件操作
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/util/SFTPUtil.java" mode="EXCERPT">
````java
public void upload(String directory, String uploadFile) throws FileNotFoundException, SftpException{   
    File file = new File(uploadFile);   
    upload(directory, file.getName(), new FileInputStream(file));   
}

public boolean uploadByProgress( String directory,String sftpFileName,  MultipartFile file) {
    // 带进度监控的文件上传
    fileProgressMonitor = new FileProgressMonitor(file.getSize());
    sftp.put( in, sftpFileName, fileProgressMonitor, ChannelSftp.OVERWRITE);
}
````
</augment_code_snippet>

### 3.3 文件存储配置
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/config/properties/InvoiceProperties.java" mode="EXCERPT">
````java
@Data
@Configuration
@ConfigurationProperties(prefix = "file")
public class InvoiceProperties {
    /**
     * 文件服务器地址
     */
    private String fileHost;
    
    /**
     * 临时文件夹，文件上传至此并转化格式上传
     */
    private String filePath;
}
````
</augment_code_snippet>

## 4. 缓存IO操作

### 4.1 Redis缓存业务流程

#### 4.1.1 验证码缓存
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/LoginServiceImpl.java" mode="EXCERPT">
````java
@Override
public Result<CaptchaResult> captcha() {
    Result<GenCaptchaResult> capResult = captchaHelper.genCaptcha();
    GenCaptchaResult genResult = capResult.getData();
    
    //验证码存redis
    String key = StringUtils.join(captchaProperties.getRedisPrefixKey(), genResult.getUuid());
    redisCache.setCacheObject(key, genResult.getCode(), captchaProperties.getExpireMinute(), TimeUnit.MINUTES);
    
    return Result.ok(result);
}
````
</augment_code_snippet>

#### 4.1.2 业务序列号生成
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/util/CommonUtil.java" mode="EXCERPT">
````java
public static String createIndex(RedisCache redisCache, String key,String dayStr) {
    String redisKey = key + dayStr;
    Integer index = 10000000;
    if (ObjectUtil.isNull(redisCache.getCacheObject(redisKey))) {
        index = index + 1;
    } else {
        index = Integer.parseInt(redisCache.getCacheObject(redisKey).toString());
        index = index + 1;
    }
    redisCache.setCacheObject(redisKey, index, 24 * 3600 , TimeUnit.SECONDS);
    return dayStr + index + random.nextInt(9999);
}
````
</augment_code_snippet>

#### 4.1.3 Token缓存管理
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/UploadServiceImpl.java" mode="EXCERPT">
````java
String rediskey = baiduProperties.getAk() + "_" + baiduProperties.getSk();
String token = (String) redisCache.getCacheObject(rediskey);
if (StrUtil.isBlank(token)) {
    token = AuthUtil.getAuth(baiduProperties.getAk(), baiduProperties.getSk());
    redisCache.setCacheObject(rediskey, token, 10, TimeUnit.DAYS);
}
````
</augment_code_snippet>

## 5. 外部API调用

### 5.1 发票API调用

#### 5.1.1 发票开具API
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/util/InvoiceUtil.java" mode="EXCERPT">
````java
public static GenerateQdInvoiceResult generateQdInvoice(GenerateQdInvoiceEntity entity){
    //引用hututil
    HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.INVOICE_INVOICE_URL);
    //设置头格式
    postRequest.header("Content-Type","application/json;charset=UTF-8");
    //上传参数信息
    RequestParams requestParams = new RequestParams();
    requestParams.setNsrsbh(entity.getSellerTaxCode());
    requestParams.setApiKey(InvoiceConfig.APIKEY);
    String encodeString = Base64Util.encode(JSONUtil.toJsonStr(entity));
    requestParams.setRequestData(encodeString);
    requestParams.setSign(MD5Util.MD5Encode(JSONUtil.toJsonStr(entity) + InvoiceConfig.APISECRET,InvoiceConfig.CHARTSET));
}
````
</augment_code_snippet>

#### 5.1.2 发票查询API
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/util/InvoiceUtil.java" mode="EXCERPT">
````java
public static GetQdInvoiceResult getQdInvoice(GetQdInvoiceEntity entity){
    //引用hututil
    HttpRequest postRequest = HttpUtil.createPost(InvoiceConfig.INVOICE_INVOICE_INFO_URL);
    //设置头格式
    postRequest.header("Content-Type","application/json;charset=UTF-8");
    //上传参数信息
    RequestParams requestParams = new RequestParams();
    // API调用逻辑
}
````
</augment_code_snippet>

### 5.2 银行接口调用

#### 5.2.1 招商银行配置
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/config/cmb/CmbConfig.java" mode="EXCERPT">
````java
@Data
@Configuration
@ConfigurationProperties(prefix = "cmb")
public class CmbConfig {
    //招商银行解密公钥，非招商银行加密公钥
    public String bankPubkey;
    //招商银行解密用字符串
    public String signStr;
    //招商银行用解密字符串，固定值
    public String USERID;
}
````
</augment_code_snippet>

#### 5.2.2 HTTP客户端调用
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/util/OpenApiDemoSm2Service.java" mode="EXCERPT">
````java
public String httpClientPost(Map<String, List<String>> header, String body, String url) throws Exception {
    log.info("HttpVisitor body is {},header is {},url is {}", body, header, url);
    try {
        HttpResponse httpResp = HttpRequest.post(url)
                .body(body, ContentType.JSON.toString())
                .header(header)
                .timeout(10000)
                .setReadTimeout(30000)
                .execute();
        if (200 != httpResp.getStatus()) {
            log.error("请求失败：status is [{}],message is [{}]",
                    httpResp.getStatus(), httpResp.body());
        }
    }
}
````
</augment_code_snippet>

## 6. 消息通信IO操作

### 6.1 短信发送

#### 6.1.1 阿里云短信服务
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/service/impl/BusShopInvoiceServiceImpl.java" mode="EXCERPT">
````java
//校验地址格式是手机号
if (SystemUtil.isValidPhoneNumber(receiveMsg)) {
    //发送短信
    Map<String, String> map = new HashMap<>();
    //注意,imgurl不包含域名信息
    map.put("recordId", invoiceEntity.getId().toString());

    AliyunSmsTemplate smsTemplate = new AliyunSmsTemplate(smsgProperties);

    SmsResult smsResult = smsTemplate.send(receiveMsg, smsgProperties.getTemplatedIdForInvoice(), map);
    System.out.println("短信发送结果" + smsResult);
}
````
</augment_code_snippet>

### 6.2 消息队列配置
系统集成RocketMQ消息队列，用于异步处理和系统解耦：
- 发票处理消息
- 数据同步消息
- 业务通知消息

## 7. 日志IO操作

### 7.1 日志配置

#### 7.1.1 Logback主配置
<augment_code_snippet path="yxl-tax-statistic/src/main/resources/logback.xml" mode="EXCERPT">
````xml
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 日志存放路径 -->
    <property name="log.path" value="logs/yxl-tax-statistic"/>
    <!-- 日志输出格式 -->
    <property name="console.log.pattern"
              value="%red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}%n) - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>
</configuration>
````
</augment_code_snippet>

#### 7.1.2 日志文件分类
<augment_code_snippet path="yxl-tax-statistic/src/main/resources/logback-common.xml" mode="EXCERPT">
````xml
<!-- 系统日志输出 -->
<appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${log.path}/info.log</file>
    <!-- 循环政策：基于时间创建日志文件 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <!-- 日志文件名格式 -->
        <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
        <!-- 日志最大的历史 60天 -->
        <maxHistory>60</maxHistory>
    </rollingPolicy>
</appender>

<appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${log.path}/error.log</file>
    <!-- 错误日志配置 -->
</appender>
````
</augment_code_snippet>

## 8. 定时任务IO操作

### 8.1 XXL-Job定时任务

#### 8.1.1 公司迁移任务
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/CompanyMigrateIoTask.java" mode="EXCERPT">
````java
/**
 * 自动迁入迁出
 */
@XxlJob("companyMigrateIoJobHandler")
public void companyMigrateIo() {
    LambdaQueryWrapper<SysMarketEntity> marketQuery = new LambdaQueryWrapper<>();
    marketQuery.eq(SysMarketEntity::getManagementModel, ManagementModelEnum.MANAGEMENT_MODEL_1.getValue());
    // 经营模式为"企业联合"的市场
    List<SysMarketEntity> marketList = sysMarketMapper.selectList(marketQuery);
    // 定时任务处理逻辑
}
````
</augment_code_snippet>

#### 8.1.2 发票文件查询任务
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/task/QuerySdInvoiceFileTask.java" mode="EXCERPT">
````java
QuerySdInvoiceFileResult querySdInvoiceFileResult = InvoiceUtil.querySdInvoiceFile(querySdInvoiceFileEntity);
if (querySdInvoiceFileResult.getCode().equals("200")) {
    //获取开票信息成功
    List<QuerySdInvoiceFileResultData> dataList = querySdInvoiceFileResult.getData();
    for (QuerySdInvoiceFileResultData d : dataList) {
        String fileContent = d.getFileContent();
        File file = InvoiceUtil.base64ToFile(fileContent, faPiaoPath + invoiceEntity.getSaleListUnique() + "." + d.getWjlx().toLowerCase());
        // 文件处理和上传逻辑
    }
}
````
</augment_code_snippet>

## 9. 配置文件IO操作

### 9.1 应用配置
<augment_code_snippet path="yxl-tax-statistic/src/main/resources/application.yml" mode="EXCERPT">
````yaml
server:
  port: 14000
  servlet:
    context-path: /taxStatisticBack
spring:
  application:
    name: yxl-tax-statistic
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: @nacos.server-addr@
      username: @nacos.username@
      password: @nacos.password@
  config:
    import:
      - optional:nacos:application.common.yaml
      - optional:nacos:dubbo-config.yaml
      - optional:nacos:${spring.application.name}.yaml
````
</augment_code_snippet>

### 9.2 业务配置属性
系统包含多个配置类，用于管理不同业务模块的配置：
- `InvoiceProperties`: 发票文件配置
- `BaiduProperties`: 百度API配置
- `FTPConfig`: FTP服务器配置
- `CmbConfig`: 招商银行配置
- `ShopProperties`: 商户配置

## 10. 异步处理IO操作

### 10.1 线程池配置
<augment_code_snippet path="yxl-tax-statistic/src/main/java/cc/buyhoo/tax/config/thread/AsyncConfiguration.java" mode="EXCERPT">
````java
@Configuration
@EnableAsync
public class AsyncConfiguration {

    @Bean("async")
    public Executor doSomethingExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：线程池创建时候初始化的线程数
        executor.setCorePoolSize(10);
        // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(20);
        // 缓冲队列：用来缓冲执行任务的队列
        executor.setQueueCapacity(500);
        return executor;
    }
}
````
</augment_code_snippet>

### 10.2 异步任务处理
系统使用异步处理来优化IO密集型操作：
- 文件上传处理
- 发票生成和查询
- 短信发送
- 数据同步

## 11. IO操作业务流程图

### 11.1 文件上传流程
```mermaid
graph TD
    A[用户上传文件] --> B[文件格式验证]
    B --> C[临时文件存储]
    C --> D[文件格式转换]
    D --> E[MinIO对象存储]
    E --> F[数据库记录URL]
    F --> G[返回访问地址]
```

### 11.2 发票处理流程
```mermaid
graph TD
    A[发票申请] --> B[调用发票API]
    B --> C[获取发票文件]
    C --> D[Base64解码]
    D --> E[PDF转图片]
    E --> F[上传MinIO]
    F --> G[发送短信通知]
    G --> H[更新数据库状态]
```

### 11.3 数据同步流程
```mermaid
graph TD
    A[外部系统数据] --> B[API接口调用]
    B --> C[数据验证]
    C --> D[Redis缓存]
    D --> E[数据库写入]
    E --> F[消息队列通知]
    F --> G[业务处理]
```

## 12. 性能优化和监控

### 12.1 IO性能优化策略
- 数据库连接池优化
- Redis缓存策略
- 文件分片上传
- 异步处理机制
- 批量操作优化

### 12.2 监控和日志
- 详细的操作日志记录
- 性能监控指标
- 异常处理和告警
- 文件操作审计

## 13. 安全性考虑

### 13.1 数据安全
- 数据库访问权限控制
- 敏感数据加密存储
- API接口签名验证
- 文件上传安全检查

### 13.2 网络安全
- HTTPS通信加密
- API接口鉴权
- 防止SQL注入
- 文件类型限制

## 14. 总结

税务统计系统的IO操作涵盖了数据库、文件存储、缓存、外部API、消息队列等多个方面，形成了完整的数据流转体系。系统通过合理的架构设计和技术选型，实现了高效、安全、可靠的IO操作，支撑了复杂的税务业务流程。

主要特点：
- 多层次的存储架构
- 完善的异步处理机制
- 丰富的外部服务集成
- 全面的日志和监控
- 严格的安全控制

通过持续的优化和监控，系统能够稳定高效地处理各种IO操作，为税务统计业务提供可靠的技术支撑。

## 15. 相关文档

- [业务流程文档](./BIZ.md) - 详细的业务流程和功能说明
- [数据库ER图文档](./ER.md) - 数据库表结构和实体关系说明

---

**文档版本**: v1.0
**最后更新**: 2024年12月
**维护人员**: 系统开发团队

**变更记录**:
- v1.0 (2024-12): 初始版本，包含完整IO业务流程文档
